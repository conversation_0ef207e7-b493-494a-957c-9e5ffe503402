const express = require('express');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
const { getConnection, safeQuery } = require('../database');
const { requireAuth } = require('./admin');

// 设置moment.js使用北京时间
moment.locale('zh-cn');

const router = express.Router();

// 验证授权码
router.post('/verify', async (req, res) => {
    try {
        const { code, clientInfo, checkOnly = false, deviceId } = req.body;

        // 获取真实客户端IP
        const clientIp = getClientIP(req);

        if (!code) {
            return res.status(400).json({
                success: false,
                message: '授权码不能为空'
            });
        }

        const db = getConnection();

        // 查询授权码 - 使用安全查询
        const [rows] = await safeQuery(
            'SELECT * FROM auth_codes WHERE code = ?',
            [code]
        );
        
        if (rows.length === 0) {
            // 记录失败日志
            await logUsage(db, code, clientIp, clientInfo, false, '授权码不存在');
            return res.status(401).json({
                success: false,
                message: '授权码无效'
            });
        }
        
        const authCode = rows[0];
        
        // 检查状态
        if (authCode.status !== 'active') {
            await logUsage(db, code, clientIp, clientInfo, false, `授权码状态: ${authCode.status}`);
            return res.status(401).json({
                success: false,
                message: '授权码已被禁用或过期'
            });
        }
        
        // 检查过期时间
        if (authCode.expire_date) {
            // 使用JavaScript原生Date对象进行更精确的比较
            const expireTime = new Date(authCode.expire_date);
            const currentTime = new Date();

            // 计算时间差（毫秒）
            const timeDiff = expireTime.getTime() - currentTime.getTime();
            const secondsDiff = Math.floor(timeDiff / 1000);

            console.log(`🕐 授权码过期检查: ${code}`);
            console.log(`📅 数据库过期时间: ${authCode.expire_date}`);
            console.log(`📅 解析过期时间: ${expireTime.toLocaleString('zh-CN')}`);
            console.log(`📅 当前北京时间: ${currentTime.toLocaleString('zh-CN')}`);
            console.log(`⏰ 时间差: ${secondsDiff} 秒`);
            console.log(`⏰ 是否过期: ${currentTime > expireTime}`);

            // 只有当前时间真正超过过期时间时才判定为过期
            if (currentTime > expireTime) {
                // 更新状态为过期 - 使用安全查询
                await safeQuery(
                    'UPDATE auth_codes SET status = "expired", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [authCode.id]
                );
                await logUsage(db, code, clientIp, clientInfo, false, '授权码已过期');
                return res.status(401).json({
                    success: false,
                    message: '授权码已过期'
                });
            } else if (secondsDiff <= 30) {
                // 如果还有30秒或更少就要过期，给出警告但不阻止使用
                console.log(`⚠️ 授权码即将过期，剩余 ${secondsDiff} 秒`);
            }
        }
        
        // 设备绑定检查
        if (authCode.device_binding_enabled && deviceId) {
            const deviceCheckResult = await checkDeviceBinding(db, code, deviceId, clientIp, clientInfo, authCode.max_devices || 1);
            if (!deviceCheckResult.success) {
                await logUsage(db, code, clientIp, clientInfo, false, deviceCheckResult.message);
                return res.status(401).json({
                    success: false,
                    message: deviceCheckResult.message
                });
            }
        }

        // 检查使用次数（只有在实际使用时才检查，检查模式跳过）
        if (!checkOnly && authCode.max_uses > 0 && authCode.used_count >= authCode.max_uses) {
            await logUsage(db, code, clientIp, clientInfo, false, '授权码使用次数已达上限');
            return res.status(401).json({
                success: false,
                message: '授权码使用次数已达上限'
            });
        }

        // 只有在非检查模式时才更新使用次数 - 使用安全查询
        if (!checkOnly) {
            await safeQuery(
                'UPDATE auth_codes SET used_count = used_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [authCode.id]
            );
        }

        // 记录成功日志
        const logMessage = checkOnly ? '状态检查成功' : '验证成功';
        await logUsage(db, code, clientIp, clientInfo, true, logMessage);

        res.json({
            success: true,
            message: '授权验证成功',
            data: {
                name: authCode.name,
                remainingUses: authCode.max_uses > 0 ? authCode.max_uses - authCode.used_count : -1,
                expireDate: authCode.expire_date,
                checkOnly: checkOnly
            }
        });
        
    } catch (error) {
        console.error('授权验证错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 生成授权码 - 需要管理员权限
router.post('/generate', requireAuth, async (req, res) => {
    try {
        const {
            name,
            maxUses = 1,
            expireDays = 30,
            expireDate = null,
            notes = '',
            customCode = null,
            maxDevices = 1,
            deviceBindingEnabled = true
        } = req.body;

        if (!name) {
            return res.status(400).json({
                success: false,
                message: '授权码名称不能为空'
            });
        }

        const db = getConnection();

        // 生成授权码
        const code = customCode || generateAuthCode();

        // 计算过期时间
        let finalExpireDate = null;

        if (expireDate) {
            // 使用具体的过期时间
            const expireMoment = moment(expireDate);
            if (!expireMoment.isValid()) {
                return res.status(400).json({
                    success: false,
                    message: '过期时间格式无效'
                });
            }

            // 获取当前北京时间
            const nowBeijing = moment();
            console.log(`🕐 当前北京时间: ${nowBeijing.format('YYYY-MM-DD HH:mm:ss')}`);
            console.log(`🕐 设置过期时间: ${expireMoment.format('YYYY-MM-DD HH:mm:ss')}`);

            if (expireMoment.isBefore(nowBeijing)) {
                return res.status(400).json({
                    success: false,
                    message: '过期时间不能早于当前时间'
                });
            }

            finalExpireDate = expireMoment.format('YYYY-MM-DD HH:mm:ss');
        } else if (expireDays > 0) {
            // 使用天数计算过期时间
            const nowBeijing = moment();
            const expireBeijing = nowBeijing.add(expireDays, 'days');
            console.log(`🕐 当前北京时间: ${nowBeijing.format('YYYY-MM-DD HH:mm:ss')}`);
            console.log(`🕐 计算过期时间: ${expireBeijing.format('YYYY-MM-DD HH:mm:ss')} (${expireDays}天后)`);
            finalExpireDate = expireBeijing.format('YYYY-MM-DD HH:mm:ss');
        }
        // 如果 expireDays = 0 且 expireDate = null，则永不过期

        // 检查授权码是否已存在
        const [existing] = await db.execute(
            'SELECT id FROM auth_codes WHERE code = ?',
            [code]
        );

        if (existing.length > 0) {
            return res.status(400).json({
                success: false,
                message: '授权码已存在，请重新生成'
            });
        }

        // 插入授权码
        const [result] = await db.execute(
            `INSERT INTO auth_codes (code, name, max_uses, expire_date, notes, max_devices, device_binding_enabled)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [code, name, maxUses, finalExpireDate, notes, maxDevices, deviceBindingEnabled]
        );

        res.json({
            success: true,
            message: '授权码生成成功',
            data: {
                id: result.insertId,
                code: code,
                name: name,
                maxUses: maxUses,
                maxDevices: maxDevices,
                deviceBindingEnabled: deviceBindingEnabled,
                expireDate: finalExpireDate,
                notes: notes
            }
        });

    } catch (error) {
        console.error('生成授权码错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 获取授权码列表 - 需要管理员权限
router.get('/list', requireAuth, async (req, res) => {
    try {
        const { page = 1, limit = 20, status = '', search = '' } = req.query;

        // 确保参数是数字类型
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        const offset = (pageNum - 1) * limitNum;

        const db = getConnection();

        // 移除批量更新过期状态，改为在前端显示时实时检查

        let whereClause = 'WHERE 1=1';
        let params = [];

        if (status && status.trim()) {
            whereClause += ' AND status = ?';
            params.push(status.trim());
        }

        if (search && search.trim()) {
            whereClause += ' AND (code LIKE ? OR name LIKE ? OR notes LIKE ?)';
            const searchPattern = `%${search.trim()}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }

        // 获取总数
        const [countResult] = await db.execute(
            `SELECT COUNT(*) as total FROM auth_codes ${whereClause}`,
            params
        );
        const total = countResult[0].total;

        // 获取列表 - 分别构建查询参数
        const listParams = [...params];
        const listQuery = `SELECT id, code, name, status, max_uses, used_count, expire_date,
                    created_at, updated_at, creator, notes, max_devices, device_binding_enabled
             FROM auth_codes ${whereClause}
             ORDER BY created_at DESC
             LIMIT ${limitNum} OFFSET ${offset}`;

        const [rows] = await db.execute(listQuery, listParams);

        // 为每个授权码获取绑定的设备信息
        for (let authCode of rows) {
            if (authCode.device_binding_enabled) {
                const [devices] = await db.execute(
                    `SELECT device_id, device_name, client_ip, first_used_at, last_used_at, status
                     FROM auth_devices
                     WHERE auth_code = ? AND status = 'active'
                     ORDER BY first_used_at ASC`,
                    [authCode.code]
                );
                authCode.boundDevices = devices;
                authCode.deviceCount = devices.length;
            } else {
                authCode.boundDevices = [];
                authCode.deviceCount = 0;
            }
        }

        // 在前端显示时也检查过期状态
        const processedRows = rows.map(row => {
            const processedRow = { ...row };

            // 检查是否过期
            if (row.expire_date && moment().isAfter(moment(row.expire_date))) {
                processedRow.status = 'expired';
                processedRow.isExpiredNow = true; // 标记为刚刚过期
            }

            return processedRow;
        });

        res.json({
            success: true,
            data: {
                list: processedRows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: total,
                    pages: Math.ceil(total / limitNum)
                }
            }
        });

    } catch (error) {
        console.error('获取授权码列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 删除授权码 - 需要管理员权限
router.delete('/delete/:id', requireAuth, async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({
                success: false,
                message: '无效的授权码ID'
            });
        }

        const db = getConnection();

        // 检查授权码是否存在
        const [existing] = await db.execute(
            'SELECT id, code FROM auth_codes WHERE id = ?',
            [parseInt(id)]
        );

        if (existing.length === 0) {
            return res.status(404).json({
                success: false,
                message: '授权码不存在'
            });
        }

        // 删除授权码
        await db.execute(
            'DELETE FROM auth_codes WHERE id = ?',
            [parseInt(id)]
        );

        // 记录删除日志
        await logUsage(db, existing[0].code, req.ip, { action: 'delete' }, true, '授权码被删除');

        res.json({
            success: true,
            message: '授权码删除成功'
        });

    } catch (error) {
        console.error('删除授权码错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 获取使用记录 - 需要管理员权限
router.get('/usage-logs', requireAuth, async (req, res) => {
    try {
        const { page = 1, limit = 20, authCode = '', success = '', startDate = '', endDate = '' } = req.query;

        // 确保参数是数字类型
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        const offset = (pageNum - 1) * limitNum;

        const db = getConnection();

        let whereClause = 'WHERE 1=1';
        let params = [];

        // 按授权码筛选
        if (authCode && authCode.trim()) {
            whereClause += ' AND auth_code LIKE ?';
            params.push(`%${authCode.trim()}%`);
        }

        // 按成功状态筛选
        if (success !== '') {
            whereClause += ' AND success = ?';
            params.push(success === 'true');
        }

        // 按日期范围筛选
        if (startDate) {
            whereClause += ' AND used_at >= ?';
            params.push(startDate);
        }

        if (endDate) {
            whereClause += ' AND used_at <= ?';
            params.push(endDate + ' 23:59:59');
        }

        // 获取总数
        const [countResult] = await db.execute(
            `SELECT COUNT(*) as total FROM auth_usage_logs ${whereClause}`,
            params
        );
        const total = countResult[0].total;

        // 获取列表
        const listParams = [...params];
        const listQuery = `SELECT id, auth_code, client_ip, client_info, used_at, success, error_message
             FROM auth_usage_logs ${whereClause}
             ORDER BY used_at DESC
             LIMIT ${limitNum} OFFSET ${offset}`;

        const [rows] = await db.execute(listQuery, listParams);

        // 获取统计信息
        const [statsResult] = await db.execute(`
            SELECT
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_requests,
                COUNT(DISTINCT auth_code) as unique_codes,
                COUNT(DISTINCT client_ip) as unique_ips
            FROM auth_usage_logs ${whereClause}
        `, params);

        res.json({
            success: true,
            data: {
                list: rows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: total,
                    pages: Math.ceil(total / limitNum)
                },
                statistics: statsResult[0]
            }
        });

    } catch (error) {
        console.error('获取使用记录错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 记录使用日志 - 使用安全查询
async function logUsage(db, authCode, clientIp, clientInfo, success, errorMessage = null) {
    try {
        await safeQuery(
            `INSERT INTO auth_usage_logs (auth_code, client_ip, client_info, success, error_message)
             VALUES (?, ?, ?, ?, ?)`,
            [authCode, clientIp, JSON.stringify(clientInfo), success, errorMessage]
        );
    } catch (error) {
        console.error('记录使用日志失败:', error);
    }
}

// 更新过期的授权码状态
async function updateExpiredAuthCodes(db) {
    try {
        // 查找所有过期但状态仍为active的授权码
        const [expiredCodes] = await db.execute(
            `UPDATE auth_codes
             SET status = 'expired', updated_at = CURRENT_TIMESTAMP
             WHERE status = 'active'
             AND expire_date IS NOT NULL
             AND expire_date < NOW()`
        );

        if (expiredCodes.affectedRows > 0) {
            console.log(`✅ 自动更新了 ${expiredCodes.affectedRows} 个过期授权码的状态`);
        }

    } catch (error) {
        console.error('更新过期授权码状态失败:', error);
    }
}

// 检查设备绑定
async function checkDeviceBinding(db, authCode, deviceId, clientIp, clientInfo, maxDevices) {
    try {
        // 查询当前授权码绑定的设备 - 使用安全查询
        const [boundDevices] = await safeQuery(
            'SELECT * FROM auth_devices WHERE auth_code = ? AND status = "active"',
            [authCode]
        );

        // 检查当前设备是否已绑定
        const currentDevice = boundDevices.find(device => device.device_id === deviceId);

        if (currentDevice) {
            // 设备已绑定，更新最后使用时间 - 使用安全查询
            await safeQuery(
                'UPDATE auth_devices SET last_used_at = CURRENT_TIMESTAMP, client_ip = ? WHERE id = ?',
                [clientIp, currentDevice.id]
            );
            return { success: true, message: '设备验证通过' };
        }

        // 检查是否超过最大设备数量
        if (boundDevices.length >= maxDevices) {
            return {
                success: false,
                message: `授权码已绑定 ${boundDevices.length} 个设备，超过最大限制 ${maxDevices} 个`
            };
        }

        // 绑定新设备 - 使用安全查询
        const deviceName = clientInfo?.deviceName || `设备-${deviceId.substring(0, 8)}`;
        await safeQuery(
            `INSERT INTO auth_devices (auth_code, device_id, device_name, client_ip, user_agent)
             VALUES (?, ?, ?, ?, ?)`,
            [authCode, deviceId, deviceName, clientIp, JSON.stringify(clientInfo)]
        );

        console.log(`✅ 新设备绑定成功: ${authCode} -> ${deviceId}`);
        return { success: true, message: '设备绑定成功' };

    } catch (error) {
        console.error('设备绑定检查失败:', error);
        return { success: false, message: '设备验证失败' };
    }
}

// 获取客户端真实IP
function getClientIP(req) {
    // 对于桌面应用，显示为本地应用
    return '本地桌面应用';
}

// 生成授权码
function generateAuthCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 清空使用记录
router.delete('/usage-logs/clear', async (req, res) => {
    try {
        const { days = 0 } = req.query; // 保留最近几天的记录，0表示全部清空

        const db = getConnection();

        if (days > 0) {
            // 只清空指定天数之前的记录
            const [result] = await db.execute(
                'DELETE FROM auth_usage_logs WHERE used_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [days]
            );

            res.json({
                success: true,
                message: `已清空 ${days} 天前的使用记录`,
                deletedCount: result.affectedRows
            });
        } else {
            // 清空所有记录
            const [result] = await db.execute('DELETE FROM auth_usage_logs');

            res.json({
                success: true,
                message: '已清空所有使用记录',
                deletedCount: result.affectedRows
            });
        }

    } catch (error) {
        console.error('清空使用记录错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

module.exports = router;
