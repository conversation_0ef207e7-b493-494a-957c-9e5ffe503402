require('dotenv').config();
const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const { safeQuery } = require('../database');
const { requireAuth } = require('./admin');

// 从环境变量获取服务器URL
const SERVER_URL = process.env.SERVER_URL;

// 创建MSI更新包（包含更新逻辑的exe）
async function createMsiUpdatePackage() {
    try {
        const updatePackageName = 'magic-box-msi-update.exe';
        const updatePackagePath = path.join(__dirname, '../downloads', updatePackageName);
        const sourceExePath = path.join(__dirname, '../downloads', 'magic-box.exe');

        // 检查源文件是否存在
        await fs.access(sourceExePath);

        // 创建更新脚本内容
        const updateScript = `
@echo off
title Magic Box 更新程序
echo 正在更新 Magic Box...
echo.

REM 等待原程序完全退出
timeout /t 3 /nobreak >nul

REM 查找Magic Box的安装位置
set "INSTALL_PATH="
for /f "tokens=2*" %%a in ('reg query "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall" /s /f "Magic Box" 2^>nul ^| findstr "InstallLocation"') do (
    set "INSTALL_PATH=%%b"
    goto :found
)

REM 如果注册表中找不到，尝试常见路径
if not defined INSTALL_PATH (
    if exist "C:\\Program Files\\Magic Box\\magic-box.exe" (
        set "INSTALL_PATH=C:\\Program Files\\Magic Box\\"
        goto :found
    )
    if exist "C:\\Program Files (x86)\\Magic Box\\magic-box.exe" (
        set "INSTALL_PATH=C:\\Program Files (x86)\\Magic Box\\"
        goto :found
    )
)

REM 如果还是找不到，使用默认路径
if not defined INSTALL_PATH (
    set "INSTALL_PATH=C:\\Program Files\\Magic Box\\"
)

:found
echo 找到安装路径: %INSTALL_PATH%
echo 正在备份原文件...

REM 备份原文件
if exist "%INSTALL_PATH%magic-box.exe" (
    copy "%INSTALL_PATH%magic-box.exe" "%INSTALL_PATH%magic-box.exe.bak" >nul 2>&1
)

echo 正在复制新版本...

REM 复制新版本（从临时目录复制自己旁边的新版本）
set "NEW_EXE=%~dp0magic-box-new.exe"
if exist "%NEW_EXE%" (
    copy "%NEW_EXE%" "%INSTALL_PATH%magic-box.exe" >nul 2>&1
    if errorlevel 1 (
        echo 更新失败，正在恢复备份...
        if exist "%INSTALL_PATH%magic-box.exe.bak" (
            copy "%INSTALL_PATH%magic-box.exe.bak" "%INSTALL_PATH%magic-box.exe" >nul 2>&1
        )
        echo 更新失败，请手动更新。
        pause
        exit /b 1
    )
) else (
    echo 找不到新版本文件，更新失败。
    pause
    exit /b 1
)

echo 更新完成！正在启动新版本...

REM 删除备份文件
if exist "%INSTALL_PATH%magic-box.exe.bak" (
    del "%INSTALL_PATH%magic-box.exe.bak" >nul 2>&1
)

REM 启动新版本
start "" "%INSTALL_PATH%magic-box.exe"

REM 清理临时文件
timeout /t 2 /nobreak >nul
if exist "%NEW_EXE%" del "%NEW_EXE%" >nul 2>&1
if exist "%~f0" del "%~f0" >nul 2>&1

exit
`;

        // 将脚本保存为bat文件
        const batPath = path.join(__dirname, '../downloads', 'update.bat');
        await fs.writeFile(batPath, updateScript.trim());

        console.log('✅ MSI更新包创建完成');

    } catch (error) {
        console.error('❌ 创建MSI更新包失败:', error);
        throw error;
    }
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../downloads/'));
    },
    filename: function (req, file, cb) {
        // 保持原始文件名
        cb(null, file.originalname);
    }
});

// 统一的multer配置，支持多文件上传
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB限制
    },
    fileFilter: function (req, file, cb) {
        // 检查文件类型
        const allowedTypes = ['.exe', '.msi', '.dmg', '.deb', '.rpm'];
        const ext = path.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(ext)) {
            cb(null, true);
        } else {
            cb(new Error('不支持的文件类型'));
        }
    }
});

// 从环境变量获取当前版本信息
const CURRENT_VERSION = process.env.CURRENT_VERSION;

// 初始化当前版本到数据库
async function initializeCurrentVersion() {
    try {
        // 检查是否已有当前版本记录
        const [rows] = await safeQuery(
            'SELECT * FROM app_versions WHERE version = ?',
            [CURRENT_VERSION]
        );

        if (rows.length === 0) {
            console.log(`🔄 初始化当前版本 ${CURRENT_VERSION} 到数据库...`);

            // 不再自动插入默认版本，由管理员手动管理版本记录
            console.log('版本管理系统已初始化，版本记录由管理员手动管理');

            console.log(`✅ 当前版本 ${CURRENT_VERSION} 初始化完成`);
        } else {
            console.log(`✅ 当前版本 ${CURRENT_VERSION} 已存在，跳过初始化`);
        }
    } catch (error) {
        console.error('❌ 初始化当前版本失败:', error);
    }
}

// 在模块加载时初始化当前版本
initializeCurrentVersion();



// 获取最新版本信息
async function getLatestVersion() {
    try {
        const [rows] = await safeQuery(
            'SELECT * FROM app_versions ORDER BY release_date DESC LIMIT 1'
        );
        return rows[0] || null;
    } catch (error) {
        console.error('获取最新版本失败:', error);
        return null;
    }
}

// 获取当前版本信息
async function getCurrentVersion() {
    try {
        const [rows] = await safeQuery(
            'SELECT * FROM app_versions WHERE is_current = TRUE LIMIT 1'
        );
        return rows[0] || { version: CURRENT_VERSION };
    } catch (error) {
        console.error('获取当前版本失败:', error);
        return { version: CURRENT_VERSION };
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Tauri更新API端点 - 必须放在通配符路由之前
router.get('/tauri-update', async (req, res) => {
    try {
        console.log('🔍 Tauri更新检查请求');

        // 获取最新版本信息（按时间排序）
        const [latestRows] = await safeQuery(
            'SELECT * FROM app_versions ORDER BY created_at DESC LIMIT 1'
        );

        if (latestRows.length === 0) {
            console.log('❌ 未找到版本信息');
            res.status(204).send();
            return;
        }

        const latestVersion = latestRows[0];

        // 检查是否比当前应用版本新
        // 这里我们假设应用请求时会带上当前版本，但Tauri默认不带参数
        // 所以我们直接返回最新版本，让Tauri自己判断

        // 如果最新版本就是基础版本1.0.0，说明没有更新
        if (latestVersion.version === CURRENT_VERSION) {
            console.log('✅ 没有新版本可用');
            res.status(204).send();
            return;
        }

        // 构建正确的Tauri更新响应格式
        const pubDate = latestVersion.release_date || latestVersion.created_at;
        const formattedDate = new Date(pubDate).toISOString();

        const updateResponse = {
            version: latestVersion.version,
            notes: latestVersion.description || '新版本更新',
            pub_date: formattedDate,
            platforms: {
                "windows-x86_64": {
                    signature: "",
                    url: latestVersion.download_url || `${SERVER_URL}/downloads/${latestVersion.file_name}`
                }
            }
        };

        console.log('✅ 返回Tauri更新信息:', updateResponse);
        res.json(updateResponse);

    } catch (error) {
        console.error('❌ Tauri更新检查失败:', error);
        res.status(500).json({ error: '检查更新失败' });
    }
});

// 获取版本历史 - 需要管理员权限
router.get('/admin/history', requireAuth, async (req, res) => {
    try {
        const [rows] = await safeQuery(
            'SELECT * FROM app_versions ORDER BY release_date DESC LIMIT 10'
        );

        const versions = rows.map(row => {
            let features = [];
            if (row.features) {
                if (typeof row.features === 'string') {
                    try {
                        features = JSON.parse(row.features);
                    } catch (error) {
                        console.error('❌ 解析版本历史features JSON字符串失败:', error);
                        features = [];
                    }
                } else if (Array.isArray(row.features)) {
                    features = row.features;
                } else {
                    features = [];
                }
            }

            return {
                ...row,
                features: features,
                fileSize: formatFileSize(row.file_size || 0)
            };
        });

        res.json({
            success: true,
            data: versions
        });

    } catch (error) {
        console.error('❌ 获取版本历史失败:', error);
        res.status(500).json({
            success: false,
            message: '获取版本历史失败',
            error: error.message
        });
    }
});

// 检查更新 - Tauri更新器格式
router.get('/:target/:current_version', async (req, res) => {
    try {
        const { target, current_version } = req.params;
        const { type } = req.query; // 获取安装类型参数

        // 减少日志输出 - 只在开发环境或首次请求时输出
        if (process.env.NODE_ENV === 'development' || Math.random() < 0.1) {
            console.log(`🔍 检查更新请求: target=${target}, current_version=${current_version}, type=${type}`);
        }

        // 从数据库获取最新版本信息
        const latestVersion = await getLatestVersion();

        if (!latestVersion) {
            // 减少"未找到版本信息"的日志输出
            if (process.env.NODE_ENV === 'development' || Math.random() < 0.05) {
                console.log('❌ 未找到版本信息');
            }
            return res.status(204).send(); // 204 No Content 表示无更新
        }

        // 比较版本号
        if (current_version === latestVersion.version) {
            console.log(`✅ 当前已是最新版本: ${current_version}`);
            return res.status(204).send(); // 204 No Content 表示无更新
        }
        
        // 有新版本可用，返回Tauri更新器格式
        let features = [];
        if (latestVersion.features) {
            if (typeof latestVersion.features === 'string') {
                try {
                    features = JSON.parse(latestVersion.features);
                } catch (error) {
                    console.error('❌ 解析features JSON字符串失败:', error);
                    features = [];
                }
            } else if (Array.isArray(latestVersion.features)) {
                features = latestVersion.features;
            } else {
                console.log('features数据类型:', typeof latestVersion.features);
                features = [];
            }
        }



        // 根据安装类型和版本选择合适的更新文件
        let downloadUrl = latestVersion.download_url;
        let fileName = latestVersion.file_name;

        // 检查是否是从旧版本(1.0.0)更新到新版本
        const isOldVersionUpdate = current_version === '1.0.0' && latestVersion.version === '1.1.0';

        if (type === 'msi') {
            if (isOldVersionUpdate) {
                // 旧版本MSI更新：提供更新器批处理文件
                const updatePackageName = 'magic-box-updater.bat';
                const updatePackagePath = path.join(__dirname, '../downloads', updatePackageName);

                try {
                    await fs.access(updatePackagePath);
                    downloadUrl = `${SERVER_URL}/downloads/${updatePackageName}`;
                    fileName = updatePackageName;
                    console.log(`📦 旧版本MSI更新，使用更新器: ${updatePackageName}`);
                } catch (error) {
                    console.log(`⚠️ 更新器不存在，使用默认EXE文件: ${fileName}`);
                }
            } else {
                // 正常MSI更新流程
                const msiFileName1 = fileName.replace('.exe', '.msi');
                const msiFileName2 = 'magic-box.msi';

                const msiPath1 = path.join(__dirname, '../downloads', msiFileName1);
                const msiPath2 = path.join(__dirname, '../downloads', msiFileName2);

                try {
                    await fs.access(msiPath1);
                    downloadUrl = `${SERVER_URL}/downloads/${msiFileName1}`;
                    fileName = msiFileName1;
                    console.log(`📦 MSI安装版本，使用MSI更新文件: ${msiFileName1}`);
                } catch (error) {
                    try {
                        await fs.access(msiPath2);
                        downloadUrl = `${SERVER_URL}/downloads/${msiFileName2}`;
                        fileName = msiFileName2;
                        console.log(`📦 MSI安装版本，使用备选MSI更新文件: ${msiFileName2}`);
                    } catch (error) {
                        console.log(`⚠️ MSI文件不存在，使用默认EXE文件: ${fileName}`);
                    }
                }
            }
        } else {
            // 便携版本，确保使用EXE文件
            const exeFileName1 = fileName.replace('.msi', '.exe');
            const exeFileName2 = 'magic-box.exe';

            const exePath1 = path.join(__dirname, '../downloads', exeFileName1);
            const exePath2 = path.join(__dirname, '../downloads', exeFileName2);

            try {
                await fs.access(exePath1);
                downloadUrl = `${SERVER_URL}/downloads/${exeFileName1}`;
                fileName = exeFileName1;
                console.log(`📦 便携版本，使用EXE更新文件: ${exeFileName1}`);
            } catch (error) {
                try {
                    await fs.access(exePath2);
                    downloadUrl = `${SERVER_URL}/downloads/${exeFileName2}`;
                    fileName = exeFileName2;
                    console.log(`📦 便携版本，使用备选EXE更新文件: ${exeFileName2}`);
                } catch (error) {
                    console.log(`⚠️ EXE文件不存在，使用默认文件: ${fileName}`);
                }
            }
        }

        const updateResponse = {
            version: latestVersion.version,
            date: latestVersion.release_date.toISOString().split('T')[0],
            body: `${latestVersion.description}\n\n新功能:\n${features.map(f => `• ${f}`).join('\n')}`,
            signature: "untrusted comment: placeholder signature\nRWQ52XQHHGZDZENTVENVENVENVENVENVENVENVENVENVENVENVENVENVENVENVENVT\ntrusted comment: timestamp:1234567890\tfile:placeholder.exe\thashed\nABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZ==",
            url: downloadUrl
        };

        // 使用标准JSON响应，但确保签名格式正确
        res.json(updateResponse);

        console.log(`✅ 发现新版本: ${latestVersion.version}`);
        
    } catch (error) {
        console.error('❌ 检查更新失败:', error);
        res.status(500).json({
            success: false,
            message: '检查更新失败',
            error: error.message
        });
    }
});

// 获取更新信息 - 用于前端显示
router.get('/info', async (req, res) => {
    try {
        const currentVersion = await getCurrentVersion();
        const latestVersion = await getLatestVersion();
        
        if (!latestVersion) {
            return res.json({
                success: true,
                data: {
                    currentVersion: currentVersion.version,
                    latestVersion: currentVersion.version,
                    hasUpdate: false,
                    updateInfo: {
                        version: currentVersion.version,
                        description: '暂无更新信息',
                        features: [],
                        fileSize: '0 B',
                        releaseDate: new Date().toISOString().split('T')[0],
                        mandatory: false
                    }
                }
            });
        }
        
        let features = [];
        if (latestVersion.features) {
            if (typeof latestVersion.features === 'string') {
                try {
                    features = JSON.parse(latestVersion.features);
                } catch (error) {
                    console.error('❌ 解析features JSON字符串失败:', error);
                    features = [];
                }
            } else if (Array.isArray(latestVersion.features)) {
                features = latestVersion.features;
            } else {
                features = [];
            }
        }

        res.json({
            success: true,
            data: {
                currentVersion: currentVersion.version,
                latestVersion: latestVersion.version,
                hasUpdate: currentVersion.version !== latestVersion.version,
                updateInfo: {
                    version: latestVersion.version,
                    description: latestVersion.description,
                    features: features,
                    fileSize: formatFileSize(latestVersion.file_size || 0),
                    releaseDate: latestVersion.release_date.toISOString().split('T')[0],
                    mandatory: latestVersion.mandatory,
                    downloadUrl: latestVersion.download_url
                }
            }
        });
    } catch (error) {
        console.error('❌ 获取更新信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取更新信息失败',
            error: error.message
        });
    }
});

// 下载更新文件
router.get('/download/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join(__dirname, '../downloads', filename);
        
        // 检查文件是否存在
        try {
            await fs.access(filePath);
        } catch (error) {
            return res.status(404).json({
                success: false,
                message: '更新文件不存在'
            });
        }
        
        // 设置下载头
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'application/octet-stream');
        
        // 发送文件
        res.sendFile(filePath);
        
    } catch (error) {
        console.error('❌ 下载更新文件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载更新文件失败',
            error: error.message
        });
    }
});



// 发布新版本 - 需要管理员权限
router.post('/admin/publish', requireAuth, upload.fields([
    { name: 'updateFile', maxCount: 1 },
    { name: 'msiFile', maxCount: 1 }
]), async (req, res) => {
    try {
        const { version, description, features, mandatory } = req.body;
        const files = req.files;

        if (!files || (!files.updateFile && !files.msiFile)) {
            return res.status(400).json({
                success: false,
                message: '请至少上传一个更新文件（EXE或MSI）'
            });
        }

        let mainFile = null;
        let msiFile = null;

        // 处理主文件（EXE）
        if (files.updateFile && files.updateFile[0]) {
            mainFile = files.updateFile[0];
            // 重命名为固定的magic-box.exe，确保一致性
            const finalPath = path.join(__dirname, '../downloads', 'magic-box.exe');
            await fs.rename(mainFile.path, finalPath);
            // 更新文件对象的文件名
            mainFile.filename = 'magic-box.exe';
        }

        // 处理MSI文件
        if (files.msiFile && files.msiFile[0]) {
            msiFile = files.msiFile[0];
            // 重命名为固定的magic-box.msi，确保一致性
            const finalPath = path.join(__dirname, '../downloads', 'magic-box.msi');
            await fs.rename(msiFile.path, finalPath);
            // 更新文件对象的文件名
            msiFile.filename = 'magic-box.msi';
        }

        // 如果只有MSI文件，将其作为主文件
        if (!mainFile && msiFile) {
            mainFile = msiFile;
        }
        
        // 解析features（从textarea的换行分隔转换为数组）
        const featuresArray = features.split('\n').filter(f => f.trim()).map(f => f.trim());

        // 构建下载URL
        const downloadUrl = `${SERVER_URL}/downloads/${mainFile.filename}`;

        // 获取文件大小
        const finalPath = path.join(__dirname, '../downloads', mainFile.filename);
        const stats = await fs.stat(finalPath);
        const fileSize = stats.size;

        // 将旧版本设为非当前版本
        await safeQuery('UPDATE app_versions SET is_current = FALSE');

        // 插入新版本到数据库
        await safeQuery(
            `INSERT INTO app_versions
            (version, description, features, file_name, file_size, download_url, mandatory, is_current)
            VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)`,
            [
                version,
                description,
                JSON.stringify(featuresArray),
                mainFile.filename,
                fileSize,
                downloadUrl,
                mandatory === 'on' || mandatory === 'true'
            ]
        );

        console.log(`🚀 新版本发布成功: ${version}`);
        console.log(`📦 主文件: ${mainFile.filename}`);
        if (msiFile && msiFile !== mainFile) {
            console.log(`📦 MSI文件: ${msiFile.filename}`);
        }

        res.json({
            success: true,
            message: '新版本发布成功',
            data: {
                version,
                description,
                features: featuresArray,
                fileName: mainFile.filename,
                msiFileName: msiFile ? msiFile.filename : null,
                fileSize: formatFileSize(fileSize),
                downloadUrl,
                mandatory: mandatory === 'on' || mandatory === 'true'
            }
        });
        
    } catch (error) {
        console.error('❌ 发布新版本失败:', error);
        res.status(500).json({
            success: false,
            message: '发布新版本失败',
            error: error.message
        });
    }
});

module.exports = router;
