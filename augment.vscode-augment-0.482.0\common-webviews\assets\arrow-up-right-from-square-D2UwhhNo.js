var Xr=Object.defineProperty;var Kr=(t,e,n)=>e in t?Xr(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var W=(t,e,n)=>Kr(t,typeof e!="symbol"?e+"":e,n);import{S as ie,i as ae,s as ce,_ as Ae,T as Zr,R as Jt,au as Xt,a as P,V as Z,I as Ie,C as _r,W as Kt,X as O,e as X,f as pe,D as yr,a8 as B,$ as Re,a0 as je,a1 as Me,u as N,q as Zt,t as z,r as Qt,g as ve,h as T,F as vr,aa as br,Z as Qr,an as es,c as A,ab as C,n as F,L as en,M as tn,b as Fe,H as Be,w as ze,x as Ue,y as qe,d as Y,z as He,j as q,a2 as ts,a4 as ns,Y as nn,ag as rs}from"./SpinnerAugment-Cx9dt_ox.js";import{C as ss}from"./IconButtonAugment-BjDqXmYl.js";const b=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,fe="9.20.0",x=globalThis;function ct(){return ut(x),x}function ut(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||fe,e[fe]=e[fe]||{}}function nt(t,e,n=x){const r=n.__SENTRY__=n.__SENTRY__||{},o=r[fe]=r[fe]||{};return o[t]||(o[t]=e())}const wr=Object.prototype.toString;function Mt(t){switch(wr.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return ne(t,Error)}}function Se(t,e){return wr.call(t)===`[object ${e}]`}function xr(t){return Se(t,"ErrorEvent")}function rn(t){return Se(t,"DOMError")}function J(t){return Se(t,"String")}function Ft(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function Et(t){return t===null||Ft(t)||typeof t!="object"&&typeof t!="function"}function Oe(t){return Se(t,"Object")}function lt(t){return typeof Event<"u"&&ne(t,Event)}function dt(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function ne(t,e){try{return t instanceof e}catch{return!1}}function Er(t){return!(typeof t!="object"||t===null||!t.__isVue&&!t._isVue)}const Bt=x,os=80;function Sr(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,o=[];let s=0,i=0;const a=" > ",c=a.length;let u;const d=Array.isArray(e)?e:e.keyAttrs,f=!Array.isArray(e)&&e.maxStringLength||os;for(;n&&s++<r&&(u=is(n,d),!(u==="html"||s>1&&i+o.length*c+u.length>=f));)o.push(u),i+=u.length,n=n.parentNode;return o.reverse().join(a)}catch{return"<unknown>"}}function is(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(Bt.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const o=e!=null&&e.length?e.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(o!=null&&o.length)o.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&J(i)){const a=i.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const s=["aria-label","type","name","title","alt"];for(const i of s){const a=n.getAttribute(i);a&&r.push(`[${i}="${a}"]`)}return r.join("")}function zt(){try{return Bt.document.location.href}catch{return""}}const St=["debug","info","warn","error","log","assert","trace"],rt={};function $e(t){if(!("console"in x))return t();const e=x.console,n={},r=Object.keys(rt);r.forEach(o=>{const s=rt[o];n[o]=e[o],e[o]=s});try{return t()}finally{r.forEach(o=>{e[o]=n[o]})}}const y=nt("logger",function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return b?St.forEach(n=>{e[n]=(...r)=>{t&&$e(()=>{x.console[n](`Sentry Logger [${n}]:`,...r)})}}):St.forEach(n=>{e[n]=()=>{}}),e});function st(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function sn(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const o=t[r];try{Er(o)?n.push("[VueViewModel]"):n.push(String(o))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function as(t,e,n=!1){return!!J(t)&&(Se(e,"RegExp")?e.test(t):!!J(e)&&(n?t===e:t.includes(e)))}function Ve(t,e=[],n=!1){return e.some(r=>as(t,r,n))}function R(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const o=n(r);typeof o=="function"&&$r(o,r);try{t[e]=o}catch{b&&y.log(`Failed to replace method "${e}" in object`,t)}}function _e(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{b&&y.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function $r(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,_e(t,"__sentry_original__",e)}catch{}}function Ut(t){return t.__sentry_original__}function kr(t){if(Mt(t))return{message:t.message,name:t.name,stack:t.stack,...an(t)};if(lt(t)){const e={type:t.type,target:on(t.target),currentTarget:on(t.currentTarget),...an(t)};return typeof CustomEvent<"u"&&ne(t,CustomEvent)&&(e.detail=t.detail),e}return t}function on(t){try{return e=t,typeof Element<"u"&&ne(e,Element)?Sr(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}var e}function an(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function M(t=function(){const e=x;return e.crypto||e.msCrypto}()){let e=()=>16*Math.random();try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&e())>>n/4).toString(16))}function Tr(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function de(t){const{message:e,event_id:n}=t;if(e)return e;const r=Tr(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function $t(t,e,n){const r=t.exception=t.exception||{},o=r.values=r.values||[],s=o[0]=o[0]||{};s.value||(s.value=e||""),s.type||(s.type="Error")}function be(t,e){const n=Tr(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const o={...r==null?void 0:r.data,...e.data};n.mechanism.data=o}}function cn(t){if(function(e){try{return e.__sentry_captured__}catch{}}(t))return!0;try{_e(t,"__sentry_captured__",!0)}catch{}return!1}const Pr=1e3;function We(){return Date.now()/Pr}const K=function(){const{performance:t}=x;if(!(t!=null&&t.now))return We;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/Pr}();function cs(t){const e=K(),n={sid:M(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return t&&we(n,t),n}function we(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||K(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:M()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function Ye(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=Ye(r[o],e[o],n-1));return r}const kt="_sentrySpan";function un(t,e){e?_e(t,kt,e):delete t[kt]}function ln(t){return t[kt]}function dn(){return M()}function Cr(){return M().substring(16)}class Q{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:dn(),sampleRand:Math.random()}}clone(){const e=new Q;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,un(e,ln(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&we(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof Q?n.getScopeData():Oe(n)?e:void 0,{tags:o,extra:s,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:d}=r||{};return this._tags={...this._tags,...o},this._extra={...this._extra,...s},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),d&&(this._propagationContext=d),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,un(this,void 0),this._attachments=[],this.setPropagationContext({traceId:dn(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var s;const r=typeof n=="number"?n:100;if(r<=0)return this;const o={timestamp:We(),...e,message:e.message?st(e.message,2048):e.message};return this._breadcrumbs.push(o),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(s=this._client)==null||s.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:ln(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=Ye(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||M();if(!this._client)return y.warn("No client configured on scope - will not capture exception!"),r;const o=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:o,...n,event_id:r},this),r}captureMessage(e,n,r){const o=(r==null?void 0:r.event_id)||M();if(!this._client)return y.warn("No client configured on scope - will not capture message!"),o;const s=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:s,...r,event_id:o},this),o}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||M();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(y.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class us{constructor(e,n){let r,o;r=e||new Q,o=n||new Q,this._stack=[{scope:r}],this._isolationScope=o}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(o){throw this._popScope(),o}return dt(r)?r.then(o=>(this._popScope(),o),o=>{throw this._popScope(),o}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function xe(){const t=ut(ct());return t.stack=t.stack||new us(nt("defaultCurrentScope",()=>new Q),nt("defaultIsolationScope",()=>new Q))}function ls(t){return xe().withScope(t)}function ds(t,e){const n=xe();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function pn(t){return xe().withScope(()=>t(xe().getIsolationScope()))}function qt(t){const e=ut(t);return e.acs?e.acs:{withIsolationScope:pn,withScope:ls,withSetScope:ds,withSetIsolationScope:(n,r)=>pn(r),getCurrentScope:()=>xe().getScope(),getIsolationScope:()=>xe().getIsolationScope()}}function re(){return qt(ct()).getCurrentScope()}function ke(){return qt(ct()).getIsolationScope()}function Tt(...t){const e=qt(ct());if(t.length===2){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function D(){return re().getClient()}function ps(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:o}=e,s={trace_id:n,span_id:o||Cr()};return r&&(s.parent_span_id=r),s}const fs="sentry.source",hs="sentry.sample_rate",ms="sentry.previous_trace_sample_rate",gs="sentry.op",_s="sentry.origin",fn="sentry.profile_id",hn="sentry.exclusive_time",ys=0,vs=1,bs="_sentryScope",ws="_sentryIsolationScope";function Ir(t){return{scope:t[bs],isolationScope:t[ws]}}const xs="sentry-",Es=/^sentry-/;function Ss(t){const e=function(r){if(!(!r||!J(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((o,s)=>{const i=mn(s);return Object.entries(i).forEach(([a,c])=>{o[a]=c}),o},{}):mn(r)}(t);if(!e)return;const n=Object.entries(e).reduce((r,[o,s])=>(o.match(Es)&&(r[o.slice(xs.length)]=s),r),{});return Object.keys(n).length>0?n:void 0}function mn(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}const Or=1;let gn=!1;function $s(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),o=r?e:Ht(t).parent_span_id,s=Ir(t).scope;return{parent_span_id:o,span_id:r?(s==null?void 0:s.getPropagationContext().propagationSpanId)||Cr():e,trace_id:n}}function ks(t){return t&&t.length>0?t.map(({context:{spanId:e,traceId:n,traceFlags:r,...o},attributes:s})=>({span_id:e,trace_id:n,sampled:r===Or,attributes:s,...o})):void 0}function _n(t){return typeof t=="number"?yn(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?yn(t.getTime()):K()}function yn(t){return t>9999999999?t/1e3:t}function Ht(t){var r;if(function(o){return typeof o.getSpanJSON=="function"}(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(function(o){const s=o;return!!(s.attributes&&s.startTime&&s.name&&s.endTime&&s.status)}(t)){const{attributes:o,startTime:s,name:i,endTime:a,status:c,links:u}=t;return{span_id:e,trace_id:n,data:o,description:i,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:_n(s),timestamp:_n(a)||void 0,status:Ts(c),op:o[gs],origin:o[_s],links:ks(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function Ts(t){if(t&&t.code!==ys)return t.code===vs?"ok":t.message||"unknown_error"}const Ps="_sentryRootSpan";function Dr(t){return t[Ps]||t}function vn(){gn||($e(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),gn=!0)}const bn=50,he="?",wn=/\(error: (.*)\)/,xn=/captureMessage|captureException/;function Lr(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,o=0)=>{const s=[],i=n.split(`
`);for(let a=r;a<i.length;a++){const c=i[a];if(c.length>1024)continue;const u=wn.test(c)?c.replace(wn,"$1"):c;if(!u.match(/\S*Error: /)){for(const d of e){const f=d(u);if(f){s.push(f);break}}if(s.length>=bn+o)break}}return function(a){if(!a.length)return[];const c=Array.from(a);return/sentryWrapped/.test(Ge(c).function||"")&&c.pop(),c.reverse(),xn.test(Ge(c).function||"")&&(c.pop(),xn.test(Ge(c).function||"")&&c.pop()),c.slice(0,bn).map(u=>({...u,filename:u.filename||Ge(c).filename,function:u.function||he}))}(s.slice(o))}}function Ge(t){return t[t.length-1]||{}}const En="<anonymous>";function se(t){try{return t&&typeof t=="function"&&t.name||En}catch{return En}}function Sn(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const et={},$n={};function me(t,e){et[t]=et[t]||[],et[t].push(e)}function ge(t,e){if(!$n[t]){$n[t]=!0;try{e()}catch(n){b&&y.error(`Error while instrumenting ${t}`,n)}}}function U(t,e){const n=t&&et[t];if(n)for(const r of n)try{r(e)}catch(o){b&&y.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${se(r)}
Error:`,o)}}let pt=null;function Cs(){pt=x.onerror,x.onerror=function(t,e,n,r,o){return U("error",{column:r,error:o,line:n,msg:t,url:e}),!!pt&&pt.apply(this,arguments)},x.onerror.__SENTRY_INSTRUMENTED__=!0}let ft=null;function Is(){ft=x.onunhandledrejection,x.onunhandledrejection=function(t){return U("unhandledrejection",t),!ft||ft.apply(this,arguments)},x.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}const Wt="production",Os=/^o(\d+)\./,Ds=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function ot(t,e=!1){const{host:n,path:r,pass:o,port:s,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&o?`:${o}`:""}@${n}${s?`:${s}`:""}/${r&&`${r}/`}${i}`}function kn(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Ls(t){const e=typeof t=="string"?function(n){const r=Ds.exec(n);if(!r)return void $e(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[o,s,i="",a="",c="",u=""]=r.slice(1);let d="",f=u;const h=f.split("/");if(h.length>1&&(d=h.slice(0,-1).join("/"),f=h.pop()),f){const l=f.match(/^\d+/);l&&(f=l[0])}return kn({host:a,pass:i,path:d,projectId:f,port:c,protocol:o,publicKey:s})}(t):kn(t);if(e&&function(n){if(!b)return!0;const{port:r,projectId:o,protocol:s}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(y.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(o.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(s)?r&&isNaN(parseInt(r,10))&&(y.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(y.error(`Invalid Sentry Dsn: Invalid protocol ${s}`),1):(y.error(`Invalid Sentry Dsn: Invalid projectId ${o}`),1)))}(e))return e}const Ns="_frozenDsc";function Nr(t,e){const n=e.getOptions(),{publicKey:r,host:o}=e.getDsn()||{};let s;n.orgId?s=String(n.orgId):o&&(s=function(a){const c=a.match(Os);return c==null?void 0:c[1]}(o));const i={environment:n.environment||Wt,release:n.release,public_key:r,trace_id:t,org_id:s};return e.emit("createDsc",i),i}function As(t){var p;const e=D();if(!e)return{};const n=Dr(t),r=Ht(n),o=r.data,s=n.spanContext().traceState,i=(s==null?void 0:s.get("sentry.sample_rate"))??o[hs]??o[ms];function a(g){return typeof i!="number"&&typeof i!="string"||(g.sample_rate=`${i}`),g}const c=n[Ns];if(c)return a(c);const u=s==null?void 0:s.get("sentry.dsc"),d=u&&Ss(u);if(d)return a(d);const f=Nr(t.spanContext().traceId,e),h=o[fs],l=r.description;return h!=="url"&&l&&(f.transaction=l),function(g){var _;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const m=g||((_=D())==null?void 0:_.getOptions());return!(!m||m.tracesSampleRate==null&&!m.tracesSampler)}()&&(f.sampled=String(function(g){const{traceFlags:m}=g.spanContext();return m===Or}(n)),f.sample_rand=(s==null?void 0:s.get("sentry.sample_rand"))??((p=Ir(n).scope)==null?void 0:p.getPropagationContext().sampleRand.toString())),a(f),e.emit("createDsc",f,n),f}function V(t,e=100,n=1/0){try{return Pt("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Ar(t,e=3,n=102400){const r=V(t,e);return o=r,function(s){return~-encodeURI(s).split(/%..|./).length}(JSON.stringify(o))>n?Ar(t,e-1,n):r;var o}function Pt(t,e,n=1/0,r=1/0,o=function(){const s=new WeakSet;function i(c){return!!s.has(c)||(s.add(c),!1)}function a(c){s.delete(c)}return[i,a]}()){const[s,i]=o;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const a=function(l,p){try{if(l==="domain"&&p&&typeof p=="object"&&p._events)return"[Domain]";if(l==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&p===global)return"[Global]";if(typeof window<"u"&&p===window)return"[Window]";if(typeof document<"u"&&p===document)return"[Document]";if(Er(p))return"[VueViewModel]";if(Oe(g=p)&&"nativeEvent"in g&&"preventDefault"in g&&"stopPropagation"in g)return"[SyntheticEvent]";if(typeof p=="number"&&!Number.isFinite(p))return`[${p}]`;if(typeof p=="function")return`[Function: ${se(p)}]`;if(typeof p=="symbol")return`[${String(p)}]`;if(typeof p=="bigint")return`[BigInt: ${String(p)}]`;const m=function(_){const v=Object.getPrototypeOf(_);return v!=null&&v.constructor?v.constructor.name:"null prototype"}(p);return/^HTML(\w*)Element$/.test(m)?`[HTMLElement: ${m}]`:`[object ${m}]`}catch(m){return`**non-serializable** (${m})`}var g}(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const c=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(s(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{return Pt("",u.toJSON(),c-1,r,o)}catch{}const d=Array.isArray(e)?[]:{};let f=0;const h=kr(e);for(const l in h){if(!Object.prototype.hasOwnProperty.call(h,l))continue;if(f>=r){d[l]="[MaxProperties ~]";break}const p=h[l];d[l]=Pt(l,p,c-1,r,o),f++}return i(e),d}function De(t,e=[]){return[t,e]}function Rs(t,e){const[n,r]=t;return[n,[...r,e]]}function Tn(t,e){const n=t[1];for(const r of n)if(e(r,r[0].type))return!0;return!1}function Ct(t){const e=ut(x);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function js(t){const[e,n]=t;let r=JSON.stringify(e);function o(s){typeof r=="string"?r=typeof s=="string"?r+s:[Ct(r),s]:r.push(typeof s=="string"?Ct(s):s)}for(const s of n){const[i,a]=s;if(o(`
${JSON.stringify(i)}
`),typeof a=="string"||a instanceof Uint8Array)o(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(V(a))}o(c)}}return typeof r=="string"?r:function(s){const i=s.reduce((u,d)=>u+d.length,0),a=new Uint8Array(i);let c=0;for(const u of s)a.set(u,c),c+=u.length;return a}(r)}function Ms(t){const e=typeof t.data=="string"?Ct(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const Fs={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function Pn(t){return Fs[t]}function Rr(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function Bs(t,e,n,r){const o=Rr(n),s=t.type&&t.type!=="replay_event"?t.type:"event";(function(a,c){c&&(a.sdk=a.sdk||{},a.sdk.name=a.sdk.name||c.name,a.sdk.version=a.sdk.version||c.version,a.sdk.integrations=[...a.sdk.integrations||[],...c.integrations||[]],a.sdk.packages=[...a.sdk.packages||[],...c.packages||[]])})(t,n==null?void 0:n.sdk);const i=function(a,c,u,d){var h;const f=(h=a.sdkProcessingMetadata)==null?void 0:h.dynamicSamplingContext;return{event_id:a.event_id,sent_at:new Date().toISOString(),...c&&{sdk:c},...!!u&&d&&{dsn:ot(d)},...f&&{trace:f}}}(t,o,r,e);return delete t.sdkProcessingMetadata,De(i,[[{type:s},t]])}var G;function ye(t){return new oe(e=>{e(t)})}function it(t){return new oe((e,n)=>{n(t)})}(function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"})(G||(G={}));class oe{constructor(e){this._state=G.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new oe((r,o)=>{this._handlers.push([!1,s=>{if(e)try{r(e(s))}catch(i){o(i)}else r(s)},s=>{if(n)try{r(n(s))}catch(i){o(i)}else o(s)}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new oe((n,r)=>{let o,s;return this.then(i=>{s=!1,o=i,e&&e()},i=>{s=!0,o=i,e&&e()}).then(()=>{s?r(o):n(o)})})}_executeHandlers(){if(this._state===G.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===G.RESOLVED&&n[1](this._value),this._state===G.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(s,i)=>{this._state===G.PENDING&&(dt(i)?i.then(r,o):(this._state=s,this._value=i,this._executeHandlers()))},r=s=>{n(G.RESOLVED,s)},o=s=>{n(G.REJECTED,s)};try{e(r,o)}catch(s){o(s)}}}function It(t,e,n,r=0){return new oe((o,s)=>{const i=t[r];if(e===null||typeof i!="function")o(e);else{const a=i({...e},n);b&&i.id&&a===null&&y.log(`Event processor "${i.id}" dropped event`),dt(a)?a.then(c=>It(t,c,n,r+1).then(o)).then(null,s):It(t,a,n,r+1).then(o).then(null,s)}})}let Je,Cn,ht;function zs(t,e){const{fingerprint:n,span:r,breadcrumbs:o,sdkProcessingMetadata:s}=e;(function(i,a){const{extra:c,tags:u,user:d,contexts:f,level:h,transactionName:l}=a;Object.keys(c).length&&(i.extra={...c,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(d).length&&(i.user={...d,...i.user}),Object.keys(f).length&&(i.contexts={...f,...i.contexts}),h&&(i.level=h),l&&i.type!=="transaction"&&(i.transaction=l)})(t,e),r&&function(i,a){i.contexts={trace:$s(a),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:As(a),...i.sdkProcessingMetadata};const c=Dr(a),u=Ht(c).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(t,r),function(i,a){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],a&&(i.fingerprint=i.fingerprint.concat(a)),i.fingerprint.length||delete i.fingerprint}(t,n),function(i,a){const c=[...i.breadcrumbs||[],...a];i.breadcrumbs=c.length?c:void 0}(t,o),function(i,a){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...a}}(t,s)}function In(t,e){const{extra:n,tags:r,user:o,contexts:s,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:d,attachments:f,propagationContext:h,transactionName:l,span:p}=e;Xe(t,"extra",n),Xe(t,"tags",r),Xe(t,"user",o),Xe(t,"contexts",s),t.sdkProcessingMetadata=Ye(t.sdkProcessingMetadata,a,2),i&&(t.level=i),l&&(t.transactionName=l),p&&(t.span=p),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),f.length&&(t.attachments=[...t.attachments,...f]),t.propagationContext={...t.propagationContext,...h}}function Xe(t,e,n){t[e]=Ye(t[e],n,1)}function Us(t,e,n,r,o,s){const{normalizeDepth:i=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||M(),timestamp:e.timestamp||We()},u=n.integrations||t.integrations.map(p=>p.name);(function(p,g){const{environment:m,release:_,dist:v,maxValueLength:I=250}=g;p.environment=p.environment||m||Wt,!p.release&&_&&(p.release=_),!p.dist&&v&&(p.dist=v);const $=p.request;$!=null&&$.url&&($.url=st($.url,I))})(c,t),function(p,g){g.length>0&&(p.sdk=p.sdk||{},p.sdk.integrations=[...p.sdk.integrations||[],...g])}(c,u),o&&o.emit("applyFrameMetadata",e),e.type===void 0&&function(p,g){var _,v;const m=function(I){const $=x._sentryDebugIds;if(!$)return{};const E=Object.keys($);return ht&&E.length===Cn||(Cn=E.length,ht=E.reduce((S,w)=>{Je||(Je={});const H=Je[w];if(H)S[H[0]]=H[1];else{const j=I(w);for(let ee=j.length-1;ee>=0;ee--){const te=j[ee],Te=te==null?void 0:te.filename,Pe=$[w];if(Te&&Pe){S[Te]=Pe,Je[w]=[Te,Pe];break}}}return S},{})),ht}(g);(v=(_=p.exception)==null?void 0:_.values)==null||v.forEach(I=>{var $,E;(E=($=I.stacktrace)==null?void 0:$.frames)==null||E.forEach(S=>{S.filename&&(S.debug_id=m[S.filename])})})}(c,t.stackParser);const d=function(p,g){if(!g)return p;const m=p?p.clone():new Q;return m.update(g),m}(r,n.captureContext);n.mechanism&&be(c,n.mechanism);const f=o?o.getEventProcessors():[],h=nt("globalScope",()=>new Q).getScopeData();s&&In(h,s.getScopeData()),d&&In(h,d.getScopeData());const l=[...n.attachments||[],...h.attachments];return l.length&&(n.attachments=l),zs(c,h),It([...f,...h.eventProcessors],c,n).then(p=>(p&&function(g){var v,I;const m={};if((I=(v=g.exception)==null?void 0:v.values)==null||I.forEach($=>{var E,S;(S=(E=$.stacktrace)==null?void 0:E.frames)==null||S.forEach(w=>{w.debug_id&&(w.abs_path?m[w.abs_path]=w.debug_id:w.filename&&(m[w.filename]=w.debug_id),delete w.debug_id)})}),Object.keys(m).length===0)return;g.debug_meta=g.debug_meta||{},g.debug_meta.images=g.debug_meta.images||[];const _=g.debug_meta.images;Object.entries(m).forEach(([$,E])=>{_.push({type:"sourcemap",code_file:$,debug_id:E})})}(p),typeof i=="number"&&i>0?function(g,m,_){var I,$;if(!g)return null;const v={...g,...g.breadcrumbs&&{breadcrumbs:g.breadcrumbs.map(E=>({...E,...E.data&&{data:V(E.data,m,_)}}))},...g.user&&{user:V(g.user,m,_)},...g.contexts&&{contexts:V(g.contexts,m,_)},...g.extra&&{extra:V(g.extra,m,_)}};return(I=g.contexts)!=null&&I.trace&&v.contexts&&(v.contexts.trace=g.contexts.trace,g.contexts.trace.data&&(v.contexts.trace.data=V(g.contexts.trace.data,m,_))),g.spans&&(v.spans=g.spans.map(E=>({...E,...E.data&&{data:V(E.data,m,_)}}))),($=g.contexts)!=null&&$.flags&&v.contexts&&(v.contexts.flags=V(g.contexts.flags,3,_)),v}(p,i,a):p))}function Ot(t,e){return re().captureException(t,void 0)}function On(t,e){return re().captureEvent(t,e)}function Dn(t){const e=ke(),n=re(),{userAgent:r}=x.navigator||{},o=cs({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),s=e.getSession();return(s==null?void 0:s.status)==="ok"&&we(s,{status:"exited"}),jr(),e.setSession(o),o}function jr(){const t=ke(),e=re().getSession()||t.getSession();e&&function(n,r){let o={};n.status==="ok"&&(o={status:"exited"}),we(n,o)}(e),Mr(),t.setSession()}function Mr(){const t=ke(),e=D(),n=t.getSession();n&&e&&e.captureSession(n)}function Ln(t=!1){t?jr():Mr()}const qs="7";function Hs(t,e,n){return e||`${function(r){return`${function(o){const s=o.protocol?`${o.protocol}:`:"",i=o.port?`:${o.port}`:"";return`${s}//${o.host}${i}${o.path?`/${o.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(t)}?${function(r,o){const s={sentry_version:qs};return r.publicKey&&(s.sentry_key=r.publicKey),o&&(s.sentry_client=`${o.name}/${o.version}`),new URLSearchParams(s).toString()}(t,n)}`}const Nn=[];function Ws(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach(o=>{o.isDefaultInstance=!0}),Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const o=n(e);r=Array.isArray(o)?o:[o]}else r=e;return function(o){const s={};return o.forEach(i=>{const{name:a}=i,c=s[a];c&&!c.isDefaultInstance&&i.isDefaultInstance||(s[a]=i)}),Object.values(s)}(r)}function An(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function Rn(t,e,n){if(n[e.name])b&&y.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,Nn.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),Nn.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(o,s)=>r(o,s,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),o=Object.assign((s,i)=>r(s,i,t),{id:e.name});t.addEventProcessor(o)}b&&y.log(`Integration installed: ${e.name}`)}}function Fr(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}const jn="Not capturing exception because it's already been captured.",Mn="Discarded session because of missing or non-string release",Br=Symbol.for("SentryInternalError"),zr=Symbol.for("SentryDoNotSendEventError");function Ke(t){return{message:t,[Br]:!0}}function mt(t){return{message:t,[zr]:!0}}function Fn(t){return!!t&&typeof t=="object"&&Br in t}function Bn(t){return!!t&&typeof t=="object"&&zr in t}class Ys{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Ls(e.dsn):b&&y.warn("No DSN provided, client will not send events."),this._dsn){const n=Hs(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const o=M();if(cn(e))return b&&y.log(jn),o;const s={event_id:o,...n};return this._process(this.eventFromException(e,s).then(i=>this._captureEvent(i,s,r))),s.event_id}captureMessage(e,n,r,o){const s={event_id:M(),...r},i=Ft(e)?e:String(e),a=Et(e)?this.eventFromMessage(i,n,s):this.eventFromException(e,s);return this._process(a.then(c=>this._captureEvent(c,s,o))),s.event_id}captureEvent(e,n,r){const o=M();if(n!=null&&n.originalException&&cn(n.originalException))return b&&y.log(jn),o;const s={event_id:o,...n},i=e.sdkProcessingMetadata||{},a=i.capturedSpanScope,c=i.capturedSpanIsolationScope;return this._process(this._captureEvent(e,s,a||r,c)),s.event_id}captureSession(e){this.sendSession(e),we(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(o=>r&&o))):ye(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];Rn(this,e,this._integrations),n||An(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=Bs(e,this._dsn,this._options._metadata,this._options.tunnel);for(const s of n.attachments||[])r=Rs(r,Ms(s));const o=this.sendEnvelope(r);o&&o.then(s=>this.emit("afterSendEvent",e,s),null)}sendSession(e){const{release:n,environment:r=Wt}=this._options;if("aggregates"in e){const s=e.attrs||{};if(!s.release&&!n)return void(b&&y.warn(Mn));s.release=s.release||n,s.environment=s.environment||r,e.attrs=s}else{if(!e.release&&!n)return void(b&&y.warn(Mn));e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const o=function(s,i,a,c){const u=Rr(a);return De({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!c&&i&&{dsn:ot(i)}},["aggregates"in s?[{type:"sessions"},s]:[{type:"session"},s.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(o)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const o=`${e}:${n}`;b&&y.log(`Recording outcome: "${o}"${r>1?` (${r} times)`:""}`),this._outcomes[o]=(this._outcomes[o]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const o=r.indexOf(n);o>-1&&r.splice(o,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(o=>o(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>(b&&y.error("Error while sending envelope:",n),n)):(b&&y.error("Transport disabled"),ye({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(n,r){const o={};return r.forEach(s=>{s&&Rn(n,s,o)}),o}(this,e),An(this,e)}_updateSessionFromEvent(e,n){var a;let r=n.level==="fatal",o=!1;const s=(a=n.exception)==null?void 0:a.values;if(s){o=!0;for(const c of s){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=e.status==="ok";(i&&e.errors===0||i&&r)&&(we(e,{...r&&{status:"crashed"},errors:e.errors||Number(o||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new oe(n=>{let r=0;const o=setInterval(()=>{this._numProcessing==0?(clearInterval(o),n(!0)):(r+=1,e&&r>=e&&(clearInterval(o),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,o){const s=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",e,n),e.type||o.setLastEventId(e.event_id||n.event_id),Us(s,e,n,r,this,o).then(a=>{if(a===null)return a;this.emit("postprocessEvent",a,n),a.contexts={trace:ps(r),...a.contexts};const c=function(u,d){const f=d.getPropagationContext();return f.dsc||Nr(f.traceId,u)}(this,r);return a.sdkProcessingMetadata={dynamicSamplingContext:c,...a.sdkProcessingMetadata},a})}_captureEvent(e,n={},r=re(),o=ke()){return b&&gt(e)&&y.log(`Captured error event \`${Fr(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,o).then(s=>s.event_id,s=>{b&&(Bn(s)?y.log(s.message):Fn(s)?y.warn(s.message):y.warn(s))})}_processEvent(e,n,r,o){const s=this.getOptions(),{sampleRate:i}=s,a=zn(e),c=gt(e),u=e.type||"error",d=`before send for type \`${u}\``,f=i===void 0?void 0:function(l){if(typeof l=="boolean")return Number(l);const p=typeof l=="string"?parseFloat(l):l;return typeof p!="number"||isNaN(p)||p<0||p>1?void 0:p}(i);if(c&&typeof f=="number"&&Math.random()>f)return this.recordDroppedEvent("sample_rate","error"),it(mt(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const h=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,o).then(l=>{if(l===null)throw this.recordDroppedEvent("event_processor",h),mt("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return l;const p=function(g,m,_,v){const{beforeSend:I,beforeSendTransaction:$,beforeSendSpan:E}=m;let S=_;if(gt(S)&&I)return I(S,v);if(zn(S)){if(E){const H=E(function(j){var Gt;const{trace_id:ee,parent_span_id:te,span_id:Te,status:Pe,origin:Gr,data:ue,op:Jr}=((Gt=j.contexts)==null?void 0:Gt.trace)??{};return{data:ue??{},description:j.transaction,op:Jr,parent_span_id:te,span_id:Te??"",start_timestamp:j.start_timestamp??0,status:Pe,timestamp:j.timestamp,trace_id:ee??"",origin:Gr,profile_id:ue==null?void 0:ue[fn],exclusive_time:ue==null?void 0:ue[hn],measurements:j.measurements,is_segment:!0}}(S));if(H?S=Ye(_,{type:"transaction",timestamp:(w=H).timestamp,start_timestamp:w.start_timestamp,transaction:w.description,contexts:{trace:{trace_id:w.trace_id,span_id:w.span_id,parent_span_id:w.parent_span_id,op:w.op,status:w.status,origin:w.origin,data:{...w.data,...w.profile_id&&{[fn]:w.profile_id},...w.exclusive_time&&{[hn]:w.exclusive_time}}}},measurements:w.measurements}):vn(),S.spans){const j=[];for(const ee of S.spans){const te=E(ee);te?j.push(te):(vn(),j.push(ee))}S.spans=j}}if($){if(S.spans){const H=S.spans.length;S.sdkProcessingMetadata={..._.sdkProcessingMetadata,spanCountBeforeProcessing:H}}return $(S,v)}}var w;return S}(0,s,l,n);return function(g,m){const _=`${m} must return \`null\` or a valid event.`;if(dt(g))return g.then(v=>{if(!Oe(v)&&v!==null)throw Ke(_);return v},v=>{throw Ke(`${m} rejected with ${v}`)});if(!Oe(g)&&g!==null)throw Ke(_);return g}(p,d)}).then(l=>{var m;if(l===null){if(this.recordDroppedEvent("before_send",h),a){const _=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",_)}throw mt(`${d} returned \`null\`, will not send event.`)}const p=r.getSession()||o.getSession();if(c&&p&&this._updateSessionFromEvent(p,l),a){const _=(((m=l.sdkProcessingMetadata)==null?void 0:m.spanCountBeforeProcessing)||0)-(l.spans?l.spans.length:0);_>0&&this.recordDroppedEvent("before_send","span",_)}const g=l.transaction_info;if(a&&g&&l.transaction!==e.transaction){const _="custom";l.transaction_info={...g,source:_}}return this.sendEvent(l,n),l}).then(null,l=>{throw Bn(l)||Fn(l)?l:(this.captureException(l,{data:{__sentry__:!0},originalException:l}),Ke(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${l}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[o,s]=n.split(":");return{reason:o,category:s,quantity:r}})}_flushOutcomes(){b&&y.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void(b&&y.log("No outcomes to send"));if(!this._dsn)return void(b&&y.log("No dsn provided, will not send outcomes"));b&&y.log("Sending outcomes:",e);const n=(r=e,De((o=this._options.tunnel&&ot(this._dsn))?{dsn:o}:{},[[{type:"client_report"},{timestamp:We(),discarded_events:r}]]));var r,o;this.sendEnvelope(n)}}function gt(t){return t.type===void 0}function zn(t){return t.type==="transaction"}function _t(t,e){var s;const n=function(i){var a;return(a=x._sentryClientToLogBufferMap)==null?void 0:a.get(i)}(t)??[];if(n.length===0)return;const r=t.getOptions(),o=function(i,a,c,u){const d={};return a!=null&&a.sdk&&(d.sdk={name:a.sdk.name,version:a.sdk.version}),c&&u&&(d.dsn=ot(u)),De(d,[(f=i,[{type:"log",item_count:f.length,content_type:"application/vnd.sentry.items.log+json"},{items:f}])]);var f}(n,r._metadata,r.tunnel,t.getDsn());(s=x._sentryClientToLogBufferMap)==null||s.set(t,[]),t.emit("flushLogs"),t.sendEnvelope(o)}function Vs(t,e){e.debug===!0&&(b?y.enable():$e(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),re().update(e.initialScope);const n=new t(e);return function(r){re().setClient(r)}(n),n.init(),n}x._sentryClientToLogBufferMap=new WeakMap;const Ur=Symbol.for("SentryBufferFullError");function Gs(t){const e=[];function n(r){return e.splice(e.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(t===void 0||e.length<t))return it(Ur);const o=r();return e.indexOf(o)===-1&&e.push(o),o.then(()=>n(o)).then(null,()=>n(o).then(null,()=>{})),o},drain:function(r){return new oe((o,s)=>{let i=e.length;if(!i)return o(!0);const a=setTimeout(()=>{r&&r>0&&o(!1)},r);e.forEach(c=>{ye(c).then(()=>{--i||(clearTimeout(a),o(!0))},s)})})}}}const Js=6e4;function Xs(t,{statusCode:e,headers:n},r=Date.now()){const o={...t},s=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(s)for(const a of s.trim().split(",")){const[c,u,,,d]=a.split(":",5),f=parseInt(c,10),h=1e3*(isNaN(f)?60:f);if(u)for(const l of u.split(";"))l==="metric_bucket"&&d&&!d.split(";").includes("custom")||(o[l]=r+h);else o.all=r+h}else i?o.all=r+function(a,c=Date.now()){const u=parseInt(`${a}`,10);if(!isNaN(u))return 1e3*u;const d=Date.parse(`${a}`);return isNaN(d)?Js:d-c}(i,r):e===429&&(o.all=r+6e4);return o}const Ks=64;function Zs(t,e,n=Gs(t.bufferSize||Ks)){let r={};return{send:function(o){const s=[];if(Tn(o,(c,u)=>{const d=Pn(u);(function(f,h,l=Date.now()){return function(p,g){return p[g]||p.all||0}(f,h)>l})(r,d)?t.recordDroppedEvent("ratelimit_backoff",d):s.push(c)}),s.length===0)return ye({});const i=De(o[0],s),a=c=>{Tn(i,(u,d)=>{t.recordDroppedEvent(c,Pn(d))})};return n.add(()=>e({body:js(i)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&b&&y.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=Xs(r,c),c),c=>{throw a("network_error"),b&&y.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===Ur)return b&&y.error("Skipped sending event because buffer is full."),a("queue_overflow"),ye({});throw c})},flush:o=>n.drain(o)}}function Qs(t){var e;((e=t.user)==null?void 0:e.ip_address)===void 0&&(t.user={...t.user,ip_address:"{{auto}}"})}function eo(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function qr(t,e,n=[e],r="npm"){const o=t._metadata||{};o.sdk||(o.sdk={name:`sentry.javascript.${e}`,packages:n.map(s=>({name:`${r}:@sentry/${s}`,version:fe})),version:fe}),t._metadata=o}const to=100;function le(t,e){const n=D(),r=ke();if(!n)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:s=to}=n.getOptions();if(s<=0)return;const i={timestamp:We(),...t},a=o?$e(()=>o(i,e)):i;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,s))}let Un;const qn=new WeakMap,no=()=>({name:"FunctionToString",setupOnce(){Un=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=Ut(this),n=qn.has(D())&&e!==void 0?e:this;return Un.apply(n,t)}}catch{}},setup(t){qn.set(t,!0)}}),ro=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],so=(t={})=>{let e;return{name:"EventFilters",setup(n){const r=n.getOptions();e=Hn(t,r)},processEvent(n,r,o){if(!e){const s=o.getOptions();e=Hn(t,s)}return function(s,i){if(s.type){if(s.type==="transaction"&&function(a,c){if(!(c!=null&&c.length))return!1;const u=a.transaction;return!!u&&Ve(u,c)}(s,i.ignoreTransactions))return b&&y.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${de(s)}`),!0}else{if(function(a,c){return c!=null&&c.length?Fr(a).some(u=>Ve(u,c)):!1}(s,i.ignoreErrors))return b&&y.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${de(s)}`),!0;if(function(a){var c,u;return(u=(c=a.exception)==null?void 0:c.values)!=null&&u.length?!a.message&&!a.exception.values.some(d=>d.stacktrace||d.type&&d.type!=="Error"||d.value):!1}(s))return b&&y.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${de(s)}`),!0;if(function(a,c){if(!(c!=null&&c.length))return!1;const u=Ze(a);return!!u&&Ve(u,c)}(s,i.denyUrls))return b&&y.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${de(s)}.
Url: ${Ze(s)}`),!0;if(!function(a,c){if(!(c!=null&&c.length))return!0;const u=Ze(a);return!u||Ve(u,c)}(s,i.allowUrls))return b&&y.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${de(s)}.
Url: ${Ze(s)}`),!0}return!1}(n,e)?null:n}}},oo=(t={})=>({...so(t),name:"InboundFilters"});function Hn(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:ro],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function Ze(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(s=>{var i,a,c;return((i=s.mechanism)==null?void 0:i.parent_id)===void 0&&((c=(a=s.stacktrace)==null?void 0:a.frames)==null?void 0:c.length)}),o=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return o?function(s=[]){for(let i=s.length-1;i>=0;i--){const a=s[i];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(o):null}catch{return b&&y.error(`Cannot extract url for event ${de(t)}`),null}}function io(t,e,n,r,o,s){var a;if(!((a=o.exception)!=null&&a.values)||!s||!ne(s.originalException,Error))return;const i=o.exception.values.length>0?o.exception.values[o.exception.values.length-1]:void 0;i&&(o.exception.values=Dt(t,e,r,s.originalException,n,o.exception.values,i,0))}function Dt(t,e,n,r,o,s,i,a){if(s.length>=n+1)return s;let c=[...s];if(ne(r[o],Error)){Wn(i,a);const u=t(e,r[o]),d=c.length;Yn(u,o,d,a),c=Dt(t,e,n,r[o],o,[u,...c],u,d)}return Array.isArray(r.errors)&&r.errors.forEach((u,d)=>{if(ne(u,Error)){Wn(i,a);const f=t(e,u),h=c.length;Yn(f,`errors[${d}]`,h,a),c=Dt(t,e,n,u,o,[f,...c],f,h)}}),c}function Wn(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function Yn(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function ao(){"console"in x&&St.forEach(function(t){t in x.console&&R(x.console,t,function(e){return rt[t]=e,function(...n){U("console",{args:n,level:t});const r=rt[t];r==null||r.apply(x.console,n)}})})}function co(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const uo=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(n,r){return r?!!(function(o,s){const i=o.message,a=s.message;return!(!i&&!a||i&&!a||!i&&a||i!==a||!Gn(o,s)||!Vn(o,s))}(n,r)||function(o,s){const i=Jn(s),a=Jn(o);return!(!i||!a||i.type!==a.type||i.value!==a.value||!Gn(o,s)||!Vn(o,s))}(n,r)):!1}(e,t))return b&&y.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}};function Vn(t,e){let n=Sn(t),r=Sn(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let o=0;o<r.length;o++){const s=r[o],i=n[o];if(s.filename!==i.filename||s.lineno!==i.lineno||s.colno!==i.colno||s.function!==i.function)return!1}return!0}function Gn(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function Jn(t){var e;return((e=t.exception)==null?void 0:e.values)&&t.exception.values[0]}function yt(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function Xn(t){return t===void 0?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const Le=x;function Hr(){if(!("fetch"in Le))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Lt(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function lo(t,e){const n="fetch";me(n,t),ge(n,()=>function(r,o=!1){o&&!function(){var a;if(typeof EdgeRuntime=="string")return!0;if(!Hr())return!1;if(Lt(Le.fetch))return!0;let s=!1;const i=Le.document;if(i&&typeof i.createElement=="function")try{const c=i.createElement("iframe");c.hidden=!0,i.head.appendChild(c),(a=c.contentWindow)!=null&&a.fetch&&(s=Lt(c.contentWindow.fetch)),i.head.removeChild(c)}catch(c){b&&y.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",c)}return s}()||R(x,"fetch",function(s){return function(...i){const a=new Error,{method:c,url:u}=function(f){if(f.length===0)return{method:"GET",url:""};if(f.length===2){const[l,p]=f;return{url:Kn(l),method:Nt(p,"method")?String(p.method).toUpperCase():"GET"}}const h=f[0];return{url:Kn(h),method:Nt(h,"method")?String(h.method).toUpperCase():"GET"}}(i),d={args:i,fetchData:{method:c,url:u},startTimestamp:1e3*K(),virtualError:a,headers:po(i)};return U("fetch",{...d}),s.apply(x,i).then(async f=>(U("fetch",{...d,endTimestamp:1e3*K(),response:f}),f),f=>{if(U("fetch",{...d,endTimestamp:1e3*K(),error:f}),Mt(f)&&f.stack===void 0&&(f.stack=a.stack,_e(f,"framesToPop",1)),f instanceof TypeError&&(f.message==="Failed to fetch"||f.message==="Load failed"||f.message==="NetworkError when attempting to fetch resource."))try{const h=new URL(d.fetchData.url);f.message=`${f.message} (${h.host})`}catch{}throw f})}})}(0,e))}function Nt(t,e){return!!t&&typeof t=="object"&&!!t[e]}function Kn(t){return typeof t=="string"?t:t?Nt(t,"url")?t.url:t.toString?t.toString():"":""}function po(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(r=e,typeof Request<"u"&&ne(r,Request))return new Headers(e.headers)}catch{}var r}const k=x;let At=0;function Zn(){return At>0}function Ee(t,e={}){if(!function(r){return typeof r=="function"}(t))return t;try{const r=t.__sentry_wrapped__;if(r)return typeof r=="function"?r:t;if(Ut(t))return t}catch{return t}const n=function(...r){try{const o=r.map(s=>Ee(s,e));return t.apply(this,o)}catch(o){throw At++,setTimeout(()=>{At--}),Tt(s=>{s.addEventProcessor(i=>(e.mechanism&&($t(i,void 0),be(i,e.mechanism)),i.extra={...i.extra,arguments:r},i)),Ot(o)}),o}};try{for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}catch{}$r(n,t),_e(t,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>t.name})}catch{}return n}function Yt(t,e){const n=Vt(t,e),r={type:mo(e),value:go(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function fo(t,e,n,r){const o=D(),s=o==null?void 0:o.getOptions().normalizeDepth,i=function(u){for(const d in u)if(Object.prototype.hasOwnProperty.call(u,d)){const f=u[d];if(f instanceof Error)return f}}(e),a={__serialized__:Ar(e,s)};if(i)return{exception:{values:[Yt(t,i)]},extra:a};const c={exception:{values:[{type:lt(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:_o(e,{isUnhandledRejection:r})}]},extra:a};if(n){const u=Vt(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function vt(t,e){return{exception:{values:[Yt(t,e)]}}}function Vt(t,e){const n=e.stacktrace||e.stack||"",r=function(s){return s&&ho.test(s.message)?1:0}(e),o=function(s){return typeof s.framesToPop=="number"?s.framesToPop:0}(e);try{return t(n,r,o)}catch{}return[]}const ho=/Minified React error #\d+;/i;function Wr(t){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&t instanceof WebAssembly.Exception}function mo(t){const e=t==null?void 0:t.name;return!e&&Wr(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function go(t){const e=t==null?void 0:t.message;return Wr(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function Rt(t,e,n,r,o){let s;if(xr(e)&&e.error)return vt(t,e.error);if(rn(e)||Se(e,"DOMException")){const i=e;if("stack"in e)s=vt(t,e);else{const a=i.name||(rn(i)?"DOMError":"DOMException"),c=i.message?`${a}: ${i.message}`:a;s=jt(t,c,n,r),$t(s,c)}return"code"in i&&(s.tags={...s.tags,"DOMException.code":`${i.code}`}),s}return Mt(e)?vt(t,e):Oe(e)||lt(e)?(s=fo(t,e,n,o),be(s,{synthetic:!0}),s):(s=jt(t,e,n,r),$t(s,`${e}`),be(s,{synthetic:!0}),s)}function jt(t,e,n,r){const o={};if(r&&n){const s=Vt(t,n);s.length&&(o.exception={values:[{value:e,stacktrace:{frames:s}}]}),be(o,{synthetic:!0})}if(Ft(e)){const{__sentry_template_string__:s,__sentry_template_values__:i}=e;return o.logentry={message:s,params:i},o}return o.message=e,o}function _o(t,{isUnhandledRejection:e}){const n=function(o,s=40){const i=Object.keys(kr(o));i.sort();const a=i[0];if(!a)return"[object has no keys]";if(a.length>=s)return st(a,s);for(let c=i.length;c>0;c--){const u=i.slice(0,c).join(", ");if(!(u.length>s))return c===i.length?u:st(u,s)}return""}(t),r=e?"promise rejection":"exception";return xr(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:lt(t)?`Event \`${function(o){try{const s=Object.getPrototypeOf(o);return s?s.constructor.name:void 0}catch{}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class yo extends Ys{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e};qr(n,"browser",["browser"],k.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:o,_experiments:s}=r._options,i=s==null?void 0:s.enableLogs;n.sendClientReports&&k.document&&k.document.addEventListener("visibilitychange",()=>{k.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&_t(r))}),i&&(r.on("flush",()=>{_t(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{_t(r)},5e3)})),o&&(r.on("postprocessEvent",Qs),r.on("beforeSendSession",eo))}eventFromException(e,n){return function(r,o,s,i){const a=Rt(r,o,(s==null?void 0:s.syntheticException)||void 0,i);return be(a),a.level="error",s!=null&&s.event_id&&(a.event_id=s.event_id),ye(a)}(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return function(o,s,i="info",a,c){const u=jt(o,s,(a==null?void 0:a.syntheticException)||void 0,c);return u.level=i,a!=null&&a.event_id&&(u.event_id=a.event_id),ye(u)}(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,o){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,o)}}const vo=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,L=x,bo=1e3;let Qn,bt,wt,Qe;function wo(){if(!L.document)return;const t=U.bind(null,"dom"),e=er(t,!0);L.document.addEventListener("click",e,!1),L.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var s,i;const r=L,o=(s=r[n])==null?void 0:s.prototype;(i=o==null?void 0:o.hasOwnProperty)!=null&&i.call(o,"addEventListener")&&(R(o,"addEventListener",function(a){return function(c,u,d){if(c==="click"||c=="keypress")try{const f=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},h=f[c]=f[c]||{refCount:0};if(!h.handler){const l=er(t);h.handler=l,a.call(this,c,l,d)}h.refCount++}catch{}return a.call(this,c,u,d)}}),R(o,"removeEventListener",function(a){return function(c,u,d){if(c==="click"||c=="keypress")try{const f=this.__sentry_instrumentation_handlers__||{},h=f[c];h&&(h.refCount--,h.refCount<=0&&(a.call(this,c,h.handler,d),h.handler=void 0,delete f[c]),Object.keys(f).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return a.call(this,c,u,d)}}))})}function er(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(s){try{return s.target}catch{return null}}(n);if(function(s,i){return s==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;_e(n,"_sentryCaptured",!0),r&&!r._sentryId&&_e(r,"_sentryId",M());const o=n.type==="keypress"?"input":n.type;(function(s){if(s.type!==bt)return!1;try{if(!s.target||s.target._sentryId!==wt)return!1}catch{}return!0})(n)||(t({event:n,name:o,global:e}),bt=n.type,wt=r?r._sentryId:void 0),clearTimeout(Qn),Qn=L.setTimeout(()=>{wt=void 0,bt=void 0},bo)}}function Yr(t){const e="history";me(e,t),ge(e,xo)}function xo(){function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const o=Qe,s=function(i){try{return new URL(i,L.location.origin).toString()}catch{return i}}(String(r));if(Qe=s,o===s)return e.apply(this,n);U("history",{from:o,to:s})}return e.apply(this,n)}}L.addEventListener("popstate",()=>{const e=L.location.href,n=Qe;Qe=e,n!==e&&U("history",{from:n,to:e})}),"history"in Le&&Le.history&&(R(L.history,"pushState",t),R(L.history,"replaceState",t))}const tt={};function tr(t){tt[t]=void 0}const Ce="__sentry_xhr_v3__";function Eo(){if(!L.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const o=new Error,s=1e3*K(),i=J(r[0])?r[0].toUpperCase():void 0,a=function(u){if(J(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!a)return e.apply(n,r);n[Ce]={method:i,url:a,request_headers:{}},i==="POST"&&a.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[Ce];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}U("xhr",{endTimestamp:1e3*K(),startTimestamp:s,xhr:n,virtualError:o})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,d,f)=>(c(),u.apply(d,f))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,d,f){const[h,l]=f,p=d[Ce];return p&&J(h)&&J(l)&&(p.request_headers[h.toLowerCase()]=l),u.apply(d,f)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const o=n[Ce];return o?(r[0]!==void 0&&(o.body=r[0]),U("xhr",{startTimestamp:1e3*K(),xhr:n}),e.apply(n,r)):e.apply(n,r)}})}function So(t,e=function(n){const r=tt[n];if(r)return r;let o=L[n];if(Lt(o))return tt[n]=o.bind(L);const s=L.document;if(s&&typeof s.createElement=="function")try{const i=s.createElement("iframe");i.hidden=!0,s.head.appendChild(i);const a=i.contentWindow;a!=null&&a[n]&&(o=a[n]),s.head.removeChild(i)}catch(i){vo&&y.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return o&&(tt[n]=o.bind(L))}("fetch")){let n=0,r=0;return Zs(t,function(o){const s=o.body.length;n+=s,r++;const i={body:o.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return tr("fetch"),it("No fetch implementation available");try{return e(t.url,i).then(a=>(n-=s,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return tr("fetch"),n-=s,r--,it(a)}})}function xt(t,e,n,r){const o={filename:t,function:e==="<anonymous>"?he:e,in_app:!0};return n!==void 0&&(o.lineno=n),r!==void 0&&(o.colno=r),o}const $o=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,ko=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,To=/\((\S*)(?::(\d+))(?::(\d+))\)/,Po=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Co=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Io=Lr([30,t=>{const e=$o.exec(t);if(e){const[,r,o,s]=e;return xt(r,he,+o,+s)}const n=ko.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const s=To.exec(n[2]);s&&(n[2]=s[1],n[3]=s[2],n[4]=s[3])}const[r,o]=nr(n[1]||he,n[2]);return xt(o,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{const e=Po.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const o=Co.exec(e[3]);o&&(e[1]=e[1]||"eval",e[3]=o[1],e[4]=o[2],e[5]="")}let n=e[3],r=e[1]||he;return[r,n]=nr(r,n),xt(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),nr=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:he,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},Ne=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,rr=1024,Oo=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(n){var r;e.console&&function(o){const s="console";me(s,o),ge(s,ao)}(function(o){return function(s){if(D()!==o)return;const i={category:"console",data:{arguments:s.args,logger:"console"},level:co(s.level),message:sn(s.args," ")};if(s.level==="assert"){if(s.args[0]!==!1)return;i.message=`Assertion failed: ${sn(s.args.slice(1)," ")||"console.assert"}`,i.data.arguments=s.args.slice(1)}le(i,{input:s.args,level:s.level})}}(n)),e.dom&&(r=function(o,s){return function(i){if(D()!==o)return;let a,c,u=typeof s=="object"?s.serializeAttribute:void 0,d=typeof s=="object"&&typeof s.maxStringLength=="number"?s.maxStringLength:void 0;d&&d>rr&&(Ne&&y.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${d} was configured. Sentry will use 1024 instead.`),d=rr),typeof u=="string"&&(u=[u]);try{const h=i.event,l=function(p){return!!p&&!!p.target}(h)?h.target:h;a=Sr(l,{keyAttrs:u,maxStringLength:d}),c=function(p){if(!Bt.HTMLElement)return null;let g=p;for(let m=0;m<5;m++){if(!g)return null;if(g instanceof HTMLElement){if(g.dataset.sentryComponent)return g.dataset.sentryComponent;if(g.dataset.sentryElement)return g.dataset.sentryElement}g=g.parentNode}return null}(l)}catch{a="<unknown>"}if(a.length===0)return;const f={category:`ui.${i.name}`,message:a};c&&(f.data={"ui.component_name":c}),le(f,{event:i.event,name:i.name,global:i.global})}}(n,e.dom),me("dom",r),ge("dom",wo)),e.xhr&&function(o){me("xhr",o),ge("xhr",Eo)}(function(o){return function(s){if(D()!==o)return;const{startTimestamp:i,endTimestamp:a}=s,c=s.xhr[Ce];if(!i||!a||!c)return;const{method:u,url:d,status_code:f,body:h}=c,l={method:u,url:d,status_code:f},p={xhr:s.xhr,input:h,startTimestamp:i,endTimestamp:a},g={category:"xhr",data:l,type:"http",level:Xn(f)};o.emit("beforeOutgoingRequestBreadcrumb",g,p),le(g,p)}}(n)),e.fetch&&lo(function(o){return function(s){if(D()!==o)return;const{startTimestamp:i,endTimestamp:a}=s;if(a&&(!s.fetchData.url.match(/sentry_key/)||s.fetchData.method!=="POST"))if(s.fetchData.method,s.fetchData.url,s.error){const c=s.fetchData,u={data:s.error,input:s.args,startTimestamp:i,endTimestamp:a},d={category:"fetch",data:c,level:"error",type:"http"};o.emit("beforeOutgoingRequestBreadcrumb",d,u),le(d,u)}else{const c=s.response,u={...s.fetchData,status_code:c==null?void 0:c.status};s.fetchData.request_body_size,s.fetchData.response_body_size;const d={input:s.args,response:c,startTimestamp:i,endTimestamp:a},f={category:"fetch",data:u,type:"http",level:Xn(u.status_code)};o.emit("beforeOutgoingRequestBreadcrumb",f,d),le(f,d)}}}(n)),e.history&&Yr(function(o){return function(s){if(D()!==o)return;let i=s.from,a=s.to;const c=yt(k.location.href);let u=i?yt(i):void 0;const d=yt(a);u!=null&&u.path||(u=c),c.protocol===d.protocol&&c.host===d.host&&(a=d.relative),c.protocol===u.protocol&&c.host===u.host&&(i=u.relative),le({category:"navigation",data:{from:i,to:a}})}}(n)),e.sentry&&n.on("beforeSendEvent",function(o){return function(s){D()===o&&le({category:"sentry."+(s.type==="transaction"?"transaction":"event"),event_id:s.event_id,level:s.level,message:de(s)},{event:s})}}(n))}}},Do=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Lo=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&R(k,"setTimeout",sr),e.setInterval&&R(k,"setInterval",sr),e.requestAnimationFrame&&R(k,"requestAnimationFrame",No),e.XMLHttpRequest&&"XMLHttpRequest"in k&&R(XMLHttpRequest.prototype,"send",Ao);const n=e.eventTarget;n&&(Array.isArray(n)?n:Do).forEach(Ro)}}};function sr(t){return function(...e){const n=e[0];return e[0]=Ee(n,{mechanism:{data:{function:se(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function No(t){return function(e){return t.apply(this,[Ee(e,{mechanism:{data:{function:"requestAnimationFrame",handler:se(t)},handled:!1,type:"instrument"}})])}}function Ao(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&R(n,r,function(o){const s={mechanism:{data:{function:r,handler:se(o)},handled:!1,type:"instrument"}},i=Ut(o);return i&&(s.mechanism.data.handler=se(i)),Ee(o,s)})}),t.apply(this,e)}}function Ro(t){var r,o;const e=k,n=(r=e[t])==null?void 0:r.prototype;(o=n==null?void 0:n.hasOwnProperty)!=null&&o.call(n,"addEventListener")&&(R(n,"addEventListener",function(s){return function(i,a,c){try{typeof a.handleEvent=="function"&&(a.handleEvent=Ee(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:se(a),target:t},handled:!1,type:"instrument"}}))}catch{}return s.apply(this,[i,Ee(a,{mechanism:{data:{function:"addEventListener",handler:se(a),target:t},handled:!1,type:"instrument"}}),c])}}),R(n,"removeEventListener",function(s){return function(i,a,c){try{const u=a.__sentry_wrapped__;u&&s.call(this,i,u,c)}catch{}return s.call(this,i,a,c)}}))}const jo=()=>({name:"BrowserSession",setupOnce(){k.document!==void 0?(Dn({ignoreDuration:!0}),Ln(),Yr(({from:t,to:e})=>{t!==void 0&&t!==e&&(Dn({ignoreDuration:!0}),Ln())})):Ne&&y.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),Mo=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(function(r){(function(o){const s="error";me(s,o),ge(s,Cs)})(o=>{const{stackParser:s,attachStacktrace:i}=ir();if(D()!==r||Zn())return;const{msg:a,url:c,line:u,column:d,error:f}=o,h=function(l,p,g,m){const _=l.exception=l.exception||{},v=_.values=_.values||[],I=v[0]=v[0]||{},$=I.stacktrace=I.stacktrace||{},E=$.frames=$.frames||[],S=m,w=g,H=J(p)&&p.length>0?p:zt();return E.length===0&&E.push({colno:S,filename:H,function:he,in_app:!0,lineno:w}),l}(Rt(s,f||a,void 0,i,!1),c,u,d);h.level="error",On(h,{originalException:f,mechanism:{handled:!1,type:"onerror"}})})}(n),or("onerror")),e.onunhandledrejection&&(function(r){(function(o){const s="unhandledrejection";me(s,o),ge(s,Is)})(o=>{const{stackParser:s,attachStacktrace:i}=ir();if(D()!==r||Zn())return;const a=function(u){if(Et(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(o),c=Et(a)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(a)}`}]}}:Rt(s,a,void 0,i,!0);c.level="error",On(c,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),or("onunhandledrejection"))}}};function or(t){Ne&&y.log(`Global Handler attached: ${t}`)}function ir(){const t=D();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const Fo=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!k.navigator&&!k.location&&!k.document)return;const e=function(){const o=zt(),{referrer:s}=k.document||{},{userAgent:i}=k.navigator||{};return{url:o,headers:{...s&&{Referer:s},...i&&{"User-Agent":i}}}}(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),Bo=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,o,s){io(Yt,s.getOptions().stackParser,n,e,r,o)}}};function zo(t){const e={};for(const n of Object.getOwnPropertyNames(t)){const r=n;t[r]!==void 0&&(e[r]=t[r])}return e}function Uo(t={}){const e=function(o={}){var s;return{defaultIntegrations:[oo(),no(),Lo(),Oo(),Mo(),Bo(),uo(),Fo(),jo()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(s=k.SENTRY_RELEASE)==null?void 0:s.id,sendClientReports:!0,...zo(o)}}(t);if(!e.skipBrowserExtensionCheck&&function(){var d;const o=k.window!==void 0&&k;if(!o)return!1;const s=o[o.chrome?"chrome":"browser"],i=(d=s==null?void 0:s.runtime)==null?void 0:d.id,a=zt()||"",c=!!i&&k===k.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(f=>a.startsWith(`${f}//`)),u=o.nw!==void 0;return!!i&&!c&&!u}())return void(Ne&&$e(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));Ne&&!Hr()&&y.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...e,stackParser:(r=e.stackParser||Io,Array.isArray(r)?Lr(...r):r),integrations:Ws(e),transport:e.transport||So};var r;return Vs(yo,n)}class Vr extends Error{constructor(e){super(e),this.name="PerformanceException"}}class qo extends Vr{constructor(n,r,o,s="unknown"){super(`Slow framerate detected: ${n.toFixed(1)} fps`);W(this,"fps");W(this,"threshold");W(this,"avgFramerate");W(this,"webviewId");this.name="SlowFramerateException",this.fps=n,this.threshold=r,this.avgFramerate=o,this.webviewId=s}}class Ho extends Vr{constructor(n,r,o=null,s="unknown"){super(`Slow INP detected: ${n.toFixed(1)} ms${o?` on target: ${o}`:""}`);W(this,"inp");W(this,"threshold");W(this,"target");W(this,"webviewId");this.name="SlowINPException",this.inp=n,this.threshold=r,this.target=o,this.webviewId=s}}function Wo(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,n=performance.now(),r=60;const o=[];let s=0;const i=t.lowFramerateThreshold,a=t.slowInpThreshold;if(requestAnimationFrame(function c(u){const d=u-n;if(e++,d>1e3){r=1e3*e/d,e=0,n=u,o.push(r),o.length>10&&o.shift();const f=o.reduce((h,l)=>h+l,0)/o.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${f.toFixed(1)} fps`);const h=r<15,l=new qo(r,i,f,window.location.href);Tt(p=>{p.setTag("performance_issue","slow_framerate"),p.setTag("fps_value",r.toFixed(1)),p.setTag("avg_fps",f.toFixed(1)),p.setTag("webview_url",window.location.href),p.setExtra("performance_data",{fps:r,avgFps:f,threshold:i,isCritical:h,framerateHistory:[...o]}),p.setLevel("warning"),Ot(l)})}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const d=u.getEntries().filter(l=>"interactionId"in l&&"duration"in l&&l.startTime>0&&l.duration<1e6);if(d.length===0)return;d.sort((l,p)=>p.duration-l.duration);const f=Math.floor(.98*d.length),h=d[Math.min(f,d.length-1)].duration;if(h>a){console.error(`[Augment Performance] Slow INP detected: ${h.toFixed(1)} ms`);let l=null;const p=d[0];p&&"target"in p&&(l=p.target,console.error("[Augment Performance] Slow interaction target:",l,p));const g=new Ho(h,a,l?String(l):null,window.location.href);Tt(m=>{m.setTag("performance_issue","slow_inp"),m.setTag("inp_value",h.toFixed(1)),m.setTag("webview_url",window.location.href),l&&m.setTag("interaction_target",String(l)),m.setExtra("performance_data",{inp:h,threshold:a,target:l}),m.setLevel("warning"),Ot(g)}),h>s&&(s=h)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>s}const ar=16,cr=200;function ur(){var t;return((t=window.augmentFlags)==null?void 0:t.enablePerformanceMonitoring)??!1}let lr=!1;(function(){var n,r;const t=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var e;(e={enabled:ur(),lowFramerateThreshold:ar,slowInpThreshold:cr}).enabled&&Wo({lowFramerateThreshold:e.lowFramerateThreshold||ar,slowInpThreshold:e.slowInpThreshold||cr}),ur()&&!t&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var e,n;if(!((n=(e=window.augmentFlags)==null?void 0:e.sentry)!=null&&n.enabled))return;const t=window.augmentFlags.sentry;if(t)if(lr)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const o={...r};qr(o,"svelte"),Uo(o)})({dsn:t.dsn,release:t.release,environment:t.environment,tracesSampleRate:t.tracesSampleRate||0,replaysSessionSampleRate:t.replaysSessionSampleRate||0,replaysOnErrorSampleRate:t.replaysOnErrorSampleRate||0,sampleRate:t.errorSampleRate||0,sendDefaultPii:t.sendDefaultPii!==void 0&&t.sendDefaultPii,integrations:[],beforeSend:r=>r}),t.tags&&Object.entries(t.tags).forEach(([r,o])=>{(function(s,i){ke().setTag(s,i)})(r,String(o))}),lr=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();class at{constructor(e){this._opts=e}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}W(at,"CONTEXT_KEY","augment-badge");const Yo=t=>({}),dr=t=>({}),Vo=t=>({}),pr=t=>({}),Go=t=>({}),fr=t=>({});function hr(t){let e,n;const r=t[7].leftButtons,o=Ae(r,t,t[16],pr);return{c(){e=Z("div"),o&&o.c(),A(e,"class","c-badge__left-buttons svelte-p47xt3")},m(s,i){X(s,e,i),o&&o.m(e,null),n=!0},p(s,i){o&&o.p&&(!n||65536&i)&&Re(o,r,s,s[16],n?Me(r,s[16],i,Vo):je(s[16]),pr)},i(s){n||(N(o,s),n=!0)},o(s){z(o,s),n=!1},d(s){s&&T(e),o&&o.d(s)}}}function Jo(t){let e,n;const r=t[7].default,o=Ae(r,t,t[16],null);return{c(){e=Z("div"),o&&o.c(),A(e,"class","c-badge-body svelte-p47xt3")},m(s,i){X(s,e,i),o&&o.m(e,null),n=!0},p(s,i){o&&o.p&&(!n||65536&i)&&Re(o,r,s,s[16],n?Me(r,s[16],i,null):je(s[16]),null)},i(s){n||(N(o,s),n=!0)},o(s){z(o,s),n=!1},d(s){s&&T(e),o&&o.d(s)}}}function mr(t){let e,n;const r=t[7].rightButtons,o=Ae(r,t,t[16],dr);return{c(){e=Z("div"),o&&o.c(),A(e,"class","c-badge__right-buttons svelte-p47xt3")},m(s,i){X(s,e,i),o&&o.m(e,null),n=!0},p(s,i){o&&o.p&&(!n||65536&i)&&Re(o,r,s,s[16],n?Me(r,s[16],i,Yo):je(s[16]),dr)},i(s){n||(N(o,s),n=!0)},o(s){z(o,s),n=!1},d(s){s&&T(e),o&&o.d(s)}}}function Xo(t){let e,n,r,o,s,i,a,c,u;const d=t[7].chaser,f=Ae(d,t,t[16],fr);let h=t[6].leftButtons&&hr(t);o=new Zr({props:{size:t[5],weight:"medium",$$slots:{default:[Jo]},$$scope:{ctx:t}}});let l=t[6].rightButtons&&mr(t),p=[Jt(t[0]),Xt(t[3]),{class:i=`c-badge c-badge--${t[0]} c-badge--${t[1]} c-badge--size-${t[2]}`},{role:"button"},{tabindex:"0"}],g={};for(let m=0;m<p.length;m+=1)g=P(g,p[m]);return{c(){e=Z("div"),f&&f.c(),n=Ie(),h&&h.c(),r=Ie(),_r(o.$$.fragment),s=Ie(),l&&l.c(),Kt(e,g),O(e,"c-badge--highContrast",t[4]),O(e,"svelte-p47xt3",!0)},m(m,_){X(m,e,_),f&&f.m(e,null),pe(e,n),h&&h.m(e,null),pe(e,r),yr(o,e,null),pe(e,s),l&&l.m(e,null),a=!0,c||(u=[B(e,"click",t[8]),B(e,"keydown",t[9]),B(e,"keyup",t[10]),B(e,"mousedown",t[11]),B(e,"mouseover",t[12]),B(e,"focus",t[13]),B(e,"mouseleave",t[14]),B(e,"blur",t[15])],c=!0)},p(m,[_]){f&&f.p&&(!a||65536&_)&&Re(f,d,m,m[16],a?Me(d,m[16],_,Go):je(m[16]),fr),m[6].leftButtons?h?(h.p(m,_),64&_&&N(h,1)):(h=hr(m),h.c(),N(h,1),h.m(e,r)):h&&(Zt(),z(h,1,1,()=>{h=null}),Qt());const v={};65536&_&&(v.$$scope={dirty:_,ctx:m}),o.$set(v),m[6].rightButtons?l?(l.p(m,_),64&_&&N(l,1)):(l=mr(m),l.c(),N(l,1),l.m(e,null)):l&&(Zt(),z(l,1,1,()=>{l=null}),Qt()),Kt(e,g=ve(p,[1&_&&Jt(m[0]),8&_&&Xt(m[3]),(!a||7&_&&i!==(i=`c-badge c-badge--${m[0]} c-badge--${m[1]} c-badge--size-${m[2]}`))&&{class:i},{role:"button"},{tabindex:"0"}])),O(e,"c-badge--highContrast",m[4]),O(e,"svelte-p47xt3",!0)},i(m){a||(N(f,m),N(h),N(o.$$.fragment,m),N(l),a=!0)},o(m){z(f,m),z(h),z(o.$$.fragment,m),z(l),a=!1},d(m){m&&T(e),f&&f.d(m),h&&h.d(),vr(o),l&&l.d(),c=!1,br(u)}}}function Ko(t,e,n){let{$$slots:r={},$$scope:o}=e;const s=Qr(r);let{color:i="accent"}=e,{variant:a="soft"}=e,{size:c=1}=e,{radius:u="medium"}=e,{highContrast:d=!1}=e;const f=c===3?2:1,h=new at({color:i,size:c,variant:a});return es(at.CONTEXT_KEY,h),t.$$set=l=>{"color"in l&&n(0,i=l.color),"variant"in l&&n(1,a=l.variant),"size"in l&&n(2,c=l.size),"radius"in l&&n(3,u=l.radius),"highContrast"in l&&n(4,d=l.highContrast),"$$scope"in l&&n(16,o=l.$$scope)},[i,a,c,u,d,f,s,r,function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},function(l){C.call(this,t,l)},o]}class Zo extends ie{constructor(e){super(),ae(this,e,Ko,Xo,ce,{color:0,variant:1,size:2,radius:3,highContrast:4})}}function gr(t){let e,n,r,o,s,i=(t[5]||"")+"",a=(t[4]||"")+"";return{c(){e=Z("span"),n=en(i),r=Ie(),o=Z("span"),s=en(a),A(e,"class","c-toggle-text c-toggle-text--off svelte-waglmg"),O(e,"visible",!t[0]&&t[5]),A(o,"class","c-toggle-text c-toggle-text--on svelte-waglmg"),O(o,"visible",t[0]&&t[4])},m(c,u){X(c,e,u),pe(e,n),X(c,r,u),X(c,o,u),pe(o,s)},p(c,u){32&u&&i!==(i=(c[5]||"")+"")&&tn(n,i),33&u&&O(e,"visible",!c[0]&&c[5]),16&u&&a!==(a=(c[4]||"")+"")&&tn(s,a),17&u&&O(o,"visible",c[0]&&c[4])},d(c){c&&(T(e),T(r),T(o))}}}function Qo(t){let e,n,r,o,s,i,a=t[6]&&gr(t);return{c(){e=Z("label"),a&&a.c(),n=Ie(),r=Z("input"),A(r,"type","checkbox"),A(r,"class","c-toggle-input svelte-waglmg"),r.disabled=t[1],A(r,"aria-label",t[3]),A(r,"role","switch"),O(r,"disabled",t[1]),A(e,"class",o="c-toggle-track c-toggle-track-size--"+t[2]+" svelte-waglmg"),O(e,"checked",t[0]),O(e,"disabled",t[1]),O(e,"has-text",t[6])},m(c,u){X(c,e,u),a&&a.m(e,null),pe(e,n),pe(e,r),r.checked=t[0],s||(i=[B(r,"change",t[9]),B(r,"keydown",t[7]),B(e,"change",t[8])],s=!0)},p(c,[u]){c[6]?a?a.p(c,u):(a=gr(c),a.c(),a.m(e,n)):a&&(a.d(1),a=null),2&u&&(r.disabled=c[1]),8&u&&A(r,"aria-label",c[3]),1&u&&(r.checked=c[0]),2&u&&O(r,"disabled",c[1]),4&u&&o!==(o="c-toggle-track c-toggle-track-size--"+c[2]+" svelte-waglmg")&&A(e,"class",o),5&u&&O(e,"checked",c[0]),6&u&&O(e,"disabled",c[1]),68&u&&O(e,"has-text",c[6])},i:F,o:F,d(c){c&&T(e),a&&a.d(),s=!1,br(i)}}}function ei(t,e,n){let r,{checked:o=!1}=e,{disabled:s=!1}=e,{size:i=2}=e,{ariaLabel:a}=e,{onText:c}=e,{offText:u}=e;return t.$$set=d=>{"checked"in d&&n(0,o=d.checked),"disabled"in d&&n(1,s=d.disabled),"size"in d&&n(2,i=d.size),"ariaLabel"in d&&n(3,a=d.ariaLabel),"onText"in d&&n(4,c=d.onText),"offText"in d&&n(5,u=d.offText)},t.$$.update=()=>{48&t.$$.dirty&&n(6,r=c||u)},[o,s,i,a,c,u,r,function(d){s||d.key!=="Enter"&&d.key!==" "||(d.preventDefault(),n(0,o=!o))},function(d){C.call(this,t,d)},function(){o=this.checked,n(0,o)}]}class vi extends ie{constructor(e){super(),ae(this,e,ei,Qo,ce,{checked:0,disabled:1,size:2,ariaLabel:3,onText:4,offText:5})}}function ti(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],o={};for(let s=0;s<r.length;s+=1)o=P(o,r[s]);return{c(){e=Fe("svg"),n=new Be(!0),this.h()},l(s){e=ze(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ue(e);n=qe(i,!0),i.forEach(T),this.h()},h(){n.a=null,Y(e,o)},m(s,i){He(s,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',e)},p(s,[i]){Y(e,o=ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&s[0]]))},i:F,o:F,d(s){s&&T(e)}}}function ni(t,e,n){return t.$$set=r=>{n(0,e=P(P({},e),q(r)))},[e=q(e)]}class bi extends ie{constructor(e){super(),ae(this,e,ni,ti,ce,{})}}function ri(t){let e;const n=t[7].default,r=Ae(n,t,t[17],null);return{c(){r&&r.c()},m(o,s){r&&r.m(o,s),e=!0},p(o,s){r&&r.p&&(!e||131072&s)&&Re(r,n,o,o[17],e?Me(n,o[17],s,null):je(o[17]),null)},i(o){e||(N(r,o),e=!0)},o(o){z(r,o),e=!1},d(o){r&&r.d(o)}}}function si(t){let e,n,r;const o=[{size:t[6]},{variant:oi},{color:t[0]},{highContrast:t[1]},{disabled:t[2]},{class:`c-badge-icon-btn__base-btn ${t[4]}`},t[3]];let s={$$slots:{default:[ri]},$$scope:{ctx:t}};for(let i=0;i<o.length;i+=1)s=P(s,o[i]);return n=new ss({props:s}),n.$on("click",t[8]),n.$on("keyup",t[9]),n.$on("keydown",t[10]),n.$on("mousedown",t[11]),n.$on("mouseover",t[12]),n.$on("focus",t[13]),n.$on("mouseleave",t[14]),n.$on("blur",t[15]),n.$on("contextmenu",t[16]),{c(){e=Z("div"),_r(n.$$.fragment),A(e,"class",ts(`c-badge-icon-btn c-badge-icon-btn--${t[5].variant} c-badge-icon-btn--size-${t[6]}`)+" svelte-1im94um")},m(i,a){X(i,e,a),yr(n,e,null),r=!0},p(i,[a]){const c=95&a?ve(o,[64&a&&{size:i[6]},0,1&a&&{color:i[0]},2&a&&{highContrast:i[1]},4&a&&{disabled:i[2]},16&a&&{class:`c-badge-icon-btn__base-btn ${i[4]}`},8&a&&ns(i[3])]):{};131072&a&&(c.$$scope={dirty:a,ctx:i}),n.$set(c)},i(i){r||(N(n.$$.fragment,i),r=!0)},o(i){z(n.$$.fragment,i),r=!1},d(i){i&&T(e),vr(n)}}}let oi="ghost";function ii(t,e){return typeof t=="string"&&["accent","neutral","error","success","warning","info"].includes(t)?t:e}function ai(t,e,n){let r,o;const s=["color","highContrast","disabled"];let i=nn(e,s),{$$slots:a={},$$scope:c}=e;const u=rs(at.CONTEXT_KEY);let{color:d=ii(u.color,"neutral")}=e,{highContrast:f=!1}=e,{disabled:h=!1}=e,l=u.size;return t.$$set=p=>{e=P(P({},e),q(p)),n(18,i=nn(e,s)),"color"in p&&n(0,d=p.color),"highContrast"in p&&n(1,f=p.highContrast),"disabled"in p&&n(2,h=p.disabled),"$$scope"in p&&n(17,c=p.$$scope)},t.$$.update=()=>{n(4,{class:r,...o}=i,r,(n(3,o),n(18,i)))},[d,f,h,o,r,u,l,a,function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},function(p){C.call(this,t,p)},c]}const wi={Root:Zo,IconButton:class extends ie{constructor(t){super(),ae(this,t,ai,si,ce,{color:0,highContrast:1,disabled:2})}}};function ci(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},t[0]],o={};for(let s=0;s<r.length;s+=1)o=P(o,r[s]);return{c(){e=Fe("svg"),n=new Be(!0),this.h()},l(s){e=ze(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ue(e);n=qe(i,!0),i.forEach(T),this.h()},h(){n.a=null,Y(e,o)},m(s,i){He(s,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M320 464c8.8 0 16-7.2 16-16V160h-80c-17.7 0-32-14.3-32-32V48H64c-8.8 0-16 7.2-16 16v384c0 8.8 7.2 16 16 16zM0 64C0 28.7 28.7 0 64 0h165.5c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64z"/>',e)},p(s,[i]){Y(e,o=ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&i&&s[0]]))},i:F,o:F,d(s){s&&T(e)}}}function ui(t,e,n){return t.$$set=r=>{n(0,e=P(P({},e),q(r)))},[e=q(e)]}class xi extends ie{constructor(e){super(),ae(this,e,ui,ci,ce,{})}}function li(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},t[0]],o={};for(let s=0;s<r.length;s+=1)o=P(o,r[s]);return{c(){e=Fe("svg"),n=new Be(!0),this.h()},l(s){e=ze(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ue(e);n=qe(i,!0),i.forEach(T),this.h()},h(){n.a=null,Y(e,o)},m(s,i){He(s,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',e)},p(s,[i]){Y(e,o=ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&i&&s[0]]))},i:F,o:F,d(s){s&&T(e)}}}function di(t,e,n){return t.$$set=r=>{n(0,e=P(P({},e),q(r)))},[e=q(e)]}class Ei extends ie{constructor(e){super(),ae(this,e,di,li,ce,{})}}function pi(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],o={};for(let s=0;s<r.length;s+=1)o=P(o,r[s]);return{c(){e=Fe("svg"),n=new Be(!0),this.h()},l(s){e=ze(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ue(e);n=qe(i,!0),i.forEach(T),this.h()},h(){n.a=null,Y(e,o)},m(s,i){He(s,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M368 208a160 160 0 1 0-320 0 160 160 0 1 0 320 0m-30.9 163.1C301.7 399.2 256.8 416 208 416 93.1 416 0 322.9 0 208S93.1 0 208 0s208 93.1 208 208c0 48.8-16.8 93.7-44.9 129.1L505 471c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0z"/>',e)},p(s,[i]){Y(e,o=ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&s[0]]))},i:F,o:F,d(s){s&&T(e)}}}function fi(t,e,n){return t.$$set=r=>{n(0,e=P(P({},e),q(r)))},[e=q(e)]}class Si extends ie{constructor(e){super(),ae(this,e,fi,pi,ce,{})}}function hi(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],o={};for(let s=0;s<r.length;s+=1)o=P(o,r[s]);return{c(){e=Fe("svg"),n=new Be(!0),this.h()},l(s){e=ze(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ue(e);n=qe(i,!0),i.forEach(T),this.h()},h(){n.a=null,Y(e,o)},m(s,i){He(s,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 24c0 13.3 10.7 24 24 24h102.1L207 271c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l223-223L464 184c0 13.3 10.7 24 24 24s24-10.7 24-24V24c0-13.3-10.7-24-24-24H328c-13.3 0-24 10.7-24 24M72 32C32.2 32 0 64.2 0 104v336c0 39.8 32.2 72 72 72h336c39.8 0 72-32.2 72-72V312c0-13.3-10.7-24-24-24s-24 10.7-24 24v128c0 13.3-10.7 24-24 24H72c-13.3 0-24-10.7-24-24V104c0-13.3 10.7-24 24-24h128c13.3 0 24-10.7 24-24s-10.7-24-24-24z"/>',e)},p(s,[i]){Y(e,o=ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&s[0]]))},i:F,o:F,d(s){s&&T(e)}}}function mi(t,e,n){return t.$$set=r=>{n(0,e=P(P({},e),q(r)))},[e=q(e)]}class $i extends ie{constructor(e){super(),ae(this,e,mi,hi,ce,{})}}export{$i as A,wi as B,xi as F,Ei as L,Si as M,bi as P,vi as T,Zo as a};
