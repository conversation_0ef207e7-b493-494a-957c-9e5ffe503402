/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.openapi.options.Configurable
 *  com.intellij.openapi.options.ConfigurationException
 *  com.intellij.openapi.util.NlsContexts$ConfigurableName
 *  org.jetbrains.annotations.Nls
 *  org.jetbrains.annotations.Nls$Capitalization
 *  org.jetbrains.annotations.Nullable
 */
package com.wangzai.config;

import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.options.ConfigurationException;
import com.intellij.openapi.util.NlsContexts;
import com.wangzai.config.AugmentConfigPanel;
import javax.swing.JComponent;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nullable;

public class AugmentConfigurable
implements Configurable {
    private AugmentConfigPanel configPanel;

    @Nls(capitalization=Nls.Capitalization.Title)
    @NlsContexts.ConfigurableName
    public String getDisplayName() {
        return "Augment Helper";
    }

    @Nullable
    public JComponent createComponent() {
        if (this.configPanel == null) {
            this.configPanel = new AugmentConfigPanel();
        }
        return this.configPanel.getMainPanel();
    }

    public boolean isModified() {
        return this.configPanel != null && this.configPanel.isModified();
    }

    public void apply() throws ConfigurationException {
        if (this.configPanel != null) {
            this.configPanel.apply();
        }
    }

    public void reset() {
        if (this.configPanel != null) {
            this.configPanel.reset();
        }
    }

    public void disposeUIResources() {
        if (this.configPanel != null) {
            this.configPanel.dispose();
            this.configPanel = null;
        }
    }
}

