var Zt=Object.defineProperty;var te=(r,t,n)=>t in r?Zt(r,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[t]=n;var C=(r,t,n)=>te(r,typeof t!="symbol"?t+"":t,n);import{a3 as Y,aj as T,ak as q,al as At,S as ee,i as ne,s as se,V as W,C as w,I as F,c as H,X as Z,e as v,D as M,f as X,a8 as yt,u as h,q as V,t as f,r as P,h as z,F as y,aa as Gt,ag as Lt,ah as E,a5 as re,a6 as ie,a9 as mt,ad as ut,n as D,G as ct,A as oe,J as j}from"./SpinnerAugment-Cx9dt_ox.js";import{e as tt,u as Et,o as Ft}from"./BaseButton-BqzdgpkK.js";import{m as ae,G as le,g as ce,t as me,a as ue,M as de,n as ge,o as pe,A as he,b as fe,R as Ht,c as $e,S as _e,d as Se,e as Ie,P as xe,f as we,h as Me,C as ye,i as Le,U as be,j as Nt,E as Ce,k as Be,l as ve}from"./RemoteAgentRetry-ByFtlC0q.js";import"./Content-BiWRcmeV.js";import{g as dt,a as jt,h as Wt,b as Ut,c as Jt,d as Xt,e as Kt,f as Ot,j as Qt,k as gt,m as ze,S as pt,i as bt,E as qe}from"./lodash-Drc0SN5U.js";import"./folder-CEjIF7oG.js";import{aq as De,ar as Re}from"./AugmentMessage-kCRDis1x.js";import{S as ke}from"./main-panel-CD6EXLpt.js";import"./isObjectLike-BWVRxMGM.js";import{R as Te}from"./check-BrrMO4vE.js";import"./types-DDm27S8B.js";import"./MaterialIcon-8-Z76Y2_.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-BxQII05L.js";import"./Keybindings-C3J8hU1V.js";import"./CalloutAugment-BFrX0piu.js";import"./exclamation-triangle-BbVpV4C-.js";import"./CardAugment-RumqAz-v.js";import"./TextTooltipAugment-DTMpOwfF.js";import"./IconButtonAugment-BjDqXmYl.js";import"./index-CGnj6T3o.js";import"./pen-to-square-CZwCjcp0.js";import"./augment-logo-DdgjewTP.js";import"./ButtonAugment-DhtPLzGu.js";import"./folder-opened-CX_GXeEO.js";import"./expand-CURYX9ur.js";import"./diff-utils-C7XQLqYW.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-8X-F_Twk.js";import"./arrow-up-right-from-square-D2UwhhNo.js";import"./CollapseButtonAugment-D3vAw6HE.js";import"./open-in-new-window-C_TwPNdv.js";import"./github-7gPAsyj4.js";import"./index-DUiNNixO.js";import"./utils-DJhaageo.js";import"./chat-types-B-te1sXh.js";import"./globals-D0QH3NT1.js";import"./types-8LwCBeyq.js";import"./file-paths-BcSg4gks.js";import"./types-CGlLNakm.js";import"./TextAreaAugment-DEYj-_0J.js";import"./await_block-H61A9-v_.js";import"./CopyButton-CugjC8Pf.js";import"./magnifying-glass-Fv6Gz5Ea.js";import"./ellipsis-Cm0UKVWz.js";import"./IconFilePath-B4JAagx1.js";import"./LanguageIcon-FVMxq7uD.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-D-BqE8vd.js";import"./chevron-down-DYf4hfS2.js";import"./mcp-logo-DslCzNpc.js";import"./terminal-BjJSzToG.js";import"./design-system-init-BCZOObrS.js";import"./StatusIndicator-BAEKlH2H.js";import"./VSCodeCodicon-B3px2_jp.js";import"./chat-flags-model-GjgruWjX.js";class Ve{constructor(t=10){C(this,"samples",[]);C(this,"maxSamples");this.maxSamples=t}addSample(t,n=performance.now()){this.samples.push({position:t,timestamp:n}),this.samples.length>this.maxSamples&&this.samples.shift()}getVelocity(){if(this.samples.length<2)return 0;const t=this.samples.at(-1),n=this.samples.at(0),e=t.position-n.position,s=t.timestamp-n.timestamp;return s>0?e/s:0}getVelocityPPS(){return 1e3*this.getVelocity()}isFastScroll(t=500){return Math.abs(this.getVelocityPPS())>t}getDirection(){const t=this.getVelocity();return t>.05?"down":t<-.05?"up":"static"}predictPositionAfter(t){if(this.samples.length===0)return 0;const n=this.getVelocity();return this.samples[this.samples.length-1].position+n*t}reset(){this.samples=[]}getCurrentPosition(){return this.samples.length===0?0:this.samples[this.samples.length-1].position}}class Pe{captureScrollPosition(t){const{scrollHeight:n,scrollTop:e,clientHeight:s}=t;return{scrollHeight:n,scrollTop:e,clientHeight:s,distanceFromBottom:ae(t),timestamp:performance.now()}}restoreScrollPosition(t,n){const e=t.scrollHeight-n.distanceFromBottom-t.clientHeight;t.scrollTop=Math.max(0,e)}}class Ae{constructor(t,n={initialVisibleCount:20,batchSize:10}){C(this,"_config");C(this,"_disposables",[]);C(this,"_currentBatchSize");C(this,"_startIndex");C(this,"_endIndex");C(this,"_isLoading");C(this,"_loadingDirection");C(this,"_allItems");C(this,"_groupedAllItems");C(this,"_groupedVisibleItems");C(this,"hasMoreBefore");C(this,"hasMoreAfter");C(this,"isLoading");C(this,"loadingDirection");C(this,"totalItemCount");this._conversationModel=t,this._config={...n,minBatchSize:n.minBatchSize??n.batchSize,maxBatchSize:n.maxBatchSize??3*n.batchSize},this._currentBatchSize=this._config.batchSize,this._startIndex=Y(0),this._endIndex=Y(void 0),this._isLoading=Y(!1),this._loadingDirection=Y(null),this._allItems=T(this._conversationModel,s=>s.chatHistory.filter(i=>dt(i)||jt(i)||Wt(i)||Ut(i)||Jt(i)||Xt(i)||Kt(i)||Ot(i)||Qt(i)||gt(i))),this._groupedAllItems=T(this._allItems,s=>{const i=s.map((a,c)=>({turn:a,idx:c}));return this._groupItems(i)}),this._groupedVisibleItems=T([this._groupedAllItems,this._startIndex,this._endIndex],([s,i,a])=>{if(s.length===0)return[];let c=0;for(let l=0;l<s.length;l++){const m=s[l];if(m.turns[m.turns.length-1].idx>=i){c=l;break}if(l===s.length-1&&m.turns[m.turns.length-1].idx>=i){c=l;break}}let o=s.length-1;if(a!==void 0)for(let l=s.length-1;l>=0;l--){const m=s[l];if(m.turns[0].idx<=a){o=l;break}if(l===0&&m.turns[0].idx<=a){o=l;break}}return s.slice(c,o+1).map((l,m,g)=>({...l,isLastGroup:m===g.length-1}))}),this.hasMoreBefore=T([this._startIndex],([s])=>s>0),this.hasMoreAfter=T([this._endIndex,this._allItems],([s,i])=>s!==void 0&&s<i.length-1),this.isLoading=T(this._isLoading,s=>s),this.loadingDirection=T(this._loadingDirection,s=>s),this.totalItemCount=T(this._allItems,s=>s.length);const e=q(this._conversationModel);typeof e.onNewConversation=="function"&&this._disposables.push(e.onNewConversation(()=>{this.resetToBottom()})),this.resetToBottom()}subscribe(t){return this._groupedVisibleItems.subscribe(t)}loadMoreBefore(t){const n=q(this._startIndex),e=q(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const s=this._getValidBatchSize(t),i=Math.max(0,n-s);return this._startIndex.set(i),this._isLoading.set(!1),this._loadingDirection.set(null),!0}async loadToStart(t={}){const n=q(this._startIndex),e=q(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const{smooth:s,smoothInterval:i=500}=t;if(s)for(;this.loadMoreBefore();)await new Promise(a=>setTimeout(a,i)),await At();else this._startIndex.set(0);return this._isLoading.set(!1),this._loadingDirection.set(null),!0}loadMoreAfter(t){const n=q(this._endIndex),e=q(this._isLoading),s=q(this._allItems);if(n===void 0||n>=s.length-1||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("after");const i=this._getValidBatchSize(t),a=Math.min(s.length-1,n+i);return a>=s.length-1?this._endIndex.set(void 0):this._endIndex.set(a),this._isLoading.set(!1),this._loadingDirection.set(null),!0}resetToBottom(){const t=q(this._allItems);if(t.length===0)return;const n=Math.max(0,t.length-this._config.initialVisibleCount);this._startIndex.set(n),this._endIndex.set(void 0)}resetToTop(){const t=q(this._allItems);if(t.length===0)return;const n=Math.min(t.length-1,this._config.initialVisibleCount-1);this._startIndex.set(0),this._endIndex.set(n)}async jumpToMessage(t){const n=q(this._allItems),e=n.findIndex(c=>c.request_id===t);if(e===-1)return!1;const s=Math.floor(this._config.initialVisibleCount/2),i=Math.max(0,e-s),a=e+s>=n.length-5?void 0:Math.min(n.length-1,e+s);return this._startIndex.set(i),this._endIndex.set(a),!0}_groupItems(t){return t.reduce((n,{turn:e,idx:s})=>((e.isToolResult===!0||dt(e)&&ze(e))&&n.length>0||gt(e)&&n.length>0?n[n.length-1].turns.push({turn:e,idx:s}):n.push({turns:[{turn:e,idx:s}],firstRequestId:e.request_id,lastRequestId:e.request_id,isLastGroup:!1}),n),[]).map((n,e,s)=>{const i=n.turns.findLast(({turn:c})=>!!c.request_id),a=i==null?void 0:i.turn.request_id;return{...n,lastRequestId:a,isLastGroup:e===s.length-1}})}setDynamicBatchSize(t){this._currentBatchSize=this._getValidBatchSize(t)}getCurrentBatchSize(){return this._currentBatchSize}_getValidBatchSize(t){if(t===void 0)return this._currentBatchSize;const n=this._config.minBatchSize??this._config.batchSize,e=this._config.maxBatchSize??3*this._config.batchSize;return Math.max(n,Math.min(e,t))}dispose(){this._disposables.forEach(t=>t())}}function Ct(r,t,n){const e=r.slice();return e[50]=t[n],e[52]=n,e}function Bt(r,t,n){const e=r.slice();e[53]=t[n].turn,e[54]=t[n].idx;const s=e[54]+1===e[13].length;return e[55]=s,e}function vt(r){let t,n;return t=new he({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function zt(r){let t;return{c(){t=W("div"),t.textContent="Loading earlier messages...",H(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){v(n,t,e)},d(n){n&&z(t)}}}function Ge(r){let t,n,e,s;const i=[Xe,Je],a=[];function c(o,l){return o[19].enableRichCheckpointInfo?0:1}return t=c(r),n=a[t]=i[t](r),{c(){n.c(),e=j()},m(o,l){a[t].m(o,l),v(o,e,l),s=!0},p(o,l){let m=t;t=c(o),t===m?a[t].p(o,l):(V(),f(a[m],1,1,()=>{a[m]=null}),P(),n=a[t],n?n.p(o,l):(n=a[t]=i[t](o),n.c()),h(n,1),n.m(e.parentNode,e))},i(o){s||(h(n),s=!0)},o(o){f(n),s=!1},d(o){o&&z(e),a[t].d(o)}}}function Ee(r){let t,n;return t=new ye({props:{group:r[50].turns,chatModel:r[1],turn:r[53],turnIndex:r[54],isLastTurn:r[55],messageListContainer:r[0]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.group=e[50].turns),2&s[0]&&(i.chatModel=e[1]),2097152&s[0]&&(i.turn=e[53]),2097152&s[0]&&(i.turnIndex=e[54]),2105344&s[0]&&(i.isLastTurn=e[55]),1&s[0]&&(i.messageListContainer=e[0]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Fe(r){let t,n;return t=new Le({props:{stage:r[53].stage,iterationId:r[53].iterationId,stageCount:r[53].stageCount}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.stage=e[53].stage),2097152&s[0]&&(i.iterationId=e[53].iterationId),2097152&s[0]&&(i.stageCount=e[53].stageCount),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function He(r){let t,n;return t=new be({props:{chatModel:r[1],msg:r[53].response_text??""}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2&s[0]&&(i.chatModel=e[1]),2097152&s[0]&&(i.msg=e[53].response_text??""),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Ne(r){let t,n;return t=new De({props:{group:r[50].turns,markdown:r[53].response_text??"",messageListContainer:r[0]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.group=e[50].turns),2097152&s[0]&&(i.markdown=e[53].response_text??""),1&s[0]&&(i.messageListContainer=e[0]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function je(r){let t,n;function e(){return r[38](r[53])}return t=new Nt({props:{turn:r[53],preamble:ke,resendTurn:e,$$slots:{default:[Ke]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(s,i){M(t,s,i),n=!0},p(s,i){r=s;const a={};2097152&i[0]&&(a.turn=r[53]),2097156&i[0]&&(a.resendTurn=e),2162688&i[0]|134217728&i[1]&&(a.$$scope={dirty:i,ctx:r}),t.$set(a)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){f(t.$$.fragment,s),n=!1},d(s){y(t,s)}}}function We(r){let t,n;return t=new Ce({props:{flagsModel:r[14],turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};16384&s[0]&&(i.flagsModel=e[14]),2097152&s[0]&&(i.turn=e[53]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Ue(r){let t,n;return t=new Nt({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.turn=e[53]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Je(r){let t,n;return t=new Be({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.turn=e[53]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Xe(r){let t,n;return t=new ve({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};2097152&s[0]&&(i.turn=e[53]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Ke(r){let t,n;return t=new Re({props:{conversationModel:r[16],turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};65536&s[0]&&(i.conversationModel=e[16]),2097152&s[0]&&(i.turn=e[53]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function qt(r){let t,n,e,s;function i(){return r[39](r[53])}return{c(){t=W("div"),H(t,"class","c-msg-list__turn-seen")},m(a,c){v(a,t,c),e||(s=mt(n=$e.call(null,t,{onSeen:i,track:r[53].seen_state!==pt.seen})),e=!0)},p(a,c){r=a,n&&ut(n.update)&&2097152&c[0]&&n.update.call(null,{onSeen:i,track:r[53].seen_state!==pt.seen})},d(a){a&&z(t),e=!1,s()}}}function Dt(r,t){let n,e,s,i,a,c,o,l,m,g,L,S,I,B,R=bt(t[53]);const b=[Ue,We,je,Ne,He,Fe,Ee,Ge],_=[];function p($,x){return 2097152&x[0]&&(e=null),2097152&x[0]&&(s=null),2097152&x[0]&&(i=null),2097152&x[0]&&(a=null),2097152&x[0]&&(c=null),2097152&x[0]&&(o=null),2097152&x[0]&&(l=null),2097152&x[0]&&(m=null),e==null&&(e=!!jt($[53])),e?0:(s==null&&(s=!!Ut($[53])),s?1:(i==null&&(i=!!Jt($[53])),i?2:(a==null&&(a=!!Xt($[53])),a?3:(c==null&&(c=!!Kt($[53])),c?4:(o==null&&(o=!!Ot($[53])),o?5:(l==null&&(l=!!(dt($[53])||Wt($[53])||Qt($[53]))),l?6:(m==null&&(m=!(!gt($[53])||$[53].status!==qe.success)),m?7:-1)))))))}~(g=p(t,[-1,-1]))&&(L=_[g]=b[g](t));let d=R&&qt(t);return{key:r,first:null,c(){n=j(),L&&L.c(),S=F(),d&&d.c(),I=j(),this.first=n},m($,x){v($,n,x),~g&&_[g].m($,x),v($,S,x),d&&d.m($,x),v($,I,x),B=!0},p($,x){let A=g;g=p(t=$,x),g===A?~g&&_[g].p(t,x):(L&&(V(),f(_[A],1,1,()=>{_[A]=null}),P()),~g?(L=_[g],L?L.p(t,x):(L=_[g]=b[g](t),L.c()),h(L,1),L.m(S.parentNode,S)):L=null),2097152&x[0]&&(R=bt(t[53])),R?d?d.p(t,x):(d=qt(t),d.c(),d.m(I.parentNode,I)):d&&(d.d(1),d=null)},i($){B||(h(L),B=!0)},o($){f(L),B=!1},d($){$&&(z(n),z(S),z(I)),~g&&_[g].d($),d&&d.d($)}}}function Rt(r){let t,n,e,s;const i=[sn,nn,en,tn,Ze,Ye,Qe,Oe],a=[];function c(o,l){return o[8]?0:o[6].retryMessage?1:o[6].showResumingRemoteAgent?2:o[6].showPaused?3:o[6].showGeneratingResponse?4:o[6].showAwaitingUserInput?5:o[6].showRunningSpacer?6:o[6].showStopped?7:-1}return~(t=c(r))&&(n=a[t]=i[t](r)),{c(){n&&n.c(),e=j()},m(o,l){~t&&a[t].m(o,l),v(o,e,l),s=!0},p(o,l){let m=t;t=c(o),t===m?~t&&a[t].p(o,l):(n&&(V(),f(a[m],1,1,()=>{a[m]=null}),P()),~t?(n=a[t],n?n.p(o,l):(n=a[t]=i[t](o),n.c()),h(n,1),n.m(e.parentNode,e)):n=null)},i(o){s||(h(n),s=!0)},o(o){f(n),s=!1},d(o){o&&z(e),~t&&a[t].d(o)}}}function Oe(r){let t,n;return t=new _e({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p:D,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Qe(r){let t;return{c(){t=W("div"),H(t,"class","c-agent-running-spacer svelte-80qwt2")},m(n,e){v(n,t,e)},p:D,i:D,o:D,d(n){n&&z(t)}}}function Ye(r){let t,n;return t=new Se({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p:D,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Ze(r){let t,n;return t=new Ie({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p:D,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function tn(r){let t,n;return t=new xe({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p:D,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function en(r){let t,n;return t=new we({}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p:D,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function nn(r){let t,n;return t=new Me({props:{message:r[6].retryMessage}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};64&s[0]&&(i.message=e[6].retryMessage),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function sn(r){let t,n;return t=new Ht({props:{error:r[8].error,onRetry:r[8].onRetry,onDelete:r[8].onDelete}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};256&s[0]&&(i.error=e[8].error),256&s[0]&&(i.onRetry=e[8].onRetry),256&s[0]&&(i.onDelete=e[8].onDelete),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function rn(r){let t,n,e,s=[],i=new Map,a=tt(r[50].turns);const c=l=>l[53].request_id??`no-request-id-${l[54]}`;for(let l=0;l<a.length;l+=1){let m=Bt(r,a,l),g=c(m);i.set(g,s[l]=Dt(g,m))}let o=r[50].isLastGroup&&Rt(r);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();t=F(),o&&o.c(),n=j()},m(l,m){for(let g=0;g<s.length;g+=1)s[g]&&s[g].m(l,m);v(l,t,m),o&&o.m(l,m),v(l,n,m),e=!0},p(l,m){2711559&m[0]|2&m[1]&&(a=tt(l[50].turns),V(),s=Et(s,m,c,1,l,a,i,t.parentNode,Ft,Dt,t,Bt),P()),l[50].isLastGroup?o?(o.p(l,m),2097152&m[0]&&h(o,1)):(o=Rt(l),o.c(),h(o,1),o.m(n.parentNode,n)):o&&(V(),f(o,1,1,()=>{o=null}),P())},i(l){if(!e){for(let m=0;m<a.length;m+=1)h(s[m]);h(o),e=!0}},o(l){for(let m=0;m<s.length;m+=1)f(s[m]);f(o),e=!1},d(l){l&&(z(t),z(n));for(let m=0;m<s.length;m+=1)s[m].d(l);o&&o.d(l)}}}function kt(r,t){let n,e,s;return e=new fe({props:{class:"c-msg-list__item--grouped",chatModel:t[1],isLastItem:t[50].isLastGroup,userControlsScroll:t[7],requestId:t[50].firstRequestId,releaseScroll:t[40],messageListContainer:t[0],minHeight:t[50].isLastGroup?t[12]:0,dataRequestId:t[50].firstRequestId,$$slots:{default:[rn]},$$scope:{ctx:t}}}),{key:r,first:null,c(){n=j(),w(e.$$.fragment),this.first=n},m(i,a){v(i,n,a),M(e,i,a),s=!0},p(i,a){t=i;const c={};2&a[0]&&(c.chatModel=t[1]),2097152&a[0]&&(c.isLastItem=t[50].isLastGroup),128&a[0]&&(c.userControlsScroll=t[7]),2097152&a[0]&&(c.requestId=t[50].firstRequestId),128&a[0]&&(c.releaseScroll=t[40]),1&a[0]&&(c.messageListContainer=t[0]),2101248&a[0]&&(c.minHeight=t[50].isLastGroup?t[12]:0),2097152&a[0]&&(c.dataRequestId=t[50].firstRequestId),2711879&a[0]|134217728&a[1]&&(c.$$scope={dirty:a,ctx:t}),e.$set(c)},i(i){s||(h(e.$$.fragment,i),s=!0)},o(i){f(e.$$.fragment,i),s=!1},d(i){i&&z(n),y(e,i)}}}function Tt(r){let t;return{c(){t=W("div"),t.textContent="Loading more messages...",H(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){v(n,t,e)},d(n){n&&z(t)}}}function Vt(r){let t,n;return t=new Ht({props:{error:r[8].error,onRetry:r[8].onRetry,onDelete:r[8].onDelete}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};256&s[0]&&(i.error=e[8].error),256&s[0]&&(i.onRetry=e[8].onRetry),256&s[0]&&(i.onDelete=e[8].onDelete),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function on(r){let t,n,e,s,i,a,c,o,l,m,g=[],L=new Map,S=r[9]&&vt(),I=r[18]&&r[20]==="before"&&zt(),B=tt(r[21]);const R=p=>p[50].firstRequestId??`no-request-id-${p[52]}`;for(let p=0;p<B.length;p+=1){let d=Ct(r,B,p),$=R(d);L.set($,g[p]=kt($,d))}let b=r[18]&&r[20]==="after"&&Tt(),_=!r[13].length&&r[8]&&Vt(r);return{c(){t=W("div"),S&&S.c(),n=F(),I&&I.c(),e=F();for(let p=0;p<g.length;p+=1)g[p].c();s=F(),b&&b.c(),i=F(),_&&_.c(),H(t,"class","c-msg-list svelte-80qwt2"),Z(t,"c-msg-list--minimal",!r[19].fullFeatured)},m(p,d){v(p,t,d),S&&S.m(t,null),X(t,n),I&&I.m(t,null),X(t,e);for(let $=0;$<g.length;$+=1)g[$]&&g[$].m(t,null);X(t,s),b&&b.m(t,null),X(t,i),_&&_.m(t,null),r[41](t),o=!0,l||(m=[mt(a=me.call(null,t,{onScrollIntoBottom:r[42],onScrollAwayFromBottom:r[43],onScroll:r[44]})),mt(c=ue.call(null,t,{onHeightChange:r[45]}))],l=!0)},p(p,d){p[9]?S?512&d[0]&&h(S,1):(S=vt(),S.c(),h(S,1),S.m(t,n)):S&&(V(),f(S,1,1,()=>{S=null}),P()),p[18]&&p[20]==="before"?I||(I=zt(),I.c(),I.m(t,e)):I&&(I.d(1),I=null),2716103&d[0]|2&d[1]&&(B=tt(p[21]),V(),g=Et(g,d,R,1,p,B,L,t,Ft,kt,s,Ct),P()),p[18]&&p[20]==="after"?b||(b=Tt(),b.c(),b.m(t,i)):b&&(b.d(1),b=null),!p[13].length&&p[8]?_?(_.p(p,d),8448&d[0]&&h(_,1)):(_=Vt(p),_.c(),h(_,1),_.m(t,null)):_&&(V(),f(_,1,1,()=>{_=null}),P()),a&&ut(a.update)&&131233&d[0]&&a.update.call(null,{onScrollIntoBottom:p[42],onScrollAwayFromBottom:p[43],onScroll:p[44]}),c&&ut(c.update)&&16&d[0]&&c.update.call(null,{onHeightChange:p[45]}),(!o||524288&d[0])&&Z(t,"c-msg-list--minimal",!p[19].fullFeatured)},i(p){if(!o){h(S);for(let d=0;d<B.length;d+=1)h(g[d]);h(_),o=!0}},o(p){f(S);for(let d=0;d<g.length;d+=1)f(g[d]);f(_),o=!1},d(p){p&&z(t),S&&S.d(),I&&I.d();for(let d=0;d<g.length;d+=1)g[d].d();b&&b.d(),_&&_.d(),r[41](null),l=!1,Gt(m)}}}function Pt(r){let t,n;return t=new de({props:{messageListElement:r[0],showScrollDown:r[11]}}),{c(){w(t.$$.fragment)},m(e,s){M(t,e,s),n=!0},p(e,s){const i={};1&s[0]&&(i.messageListElement=e[0]),2048&s[0]&&(i.showScrollDown=e[11]),t.$set(i)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function an(r){let t,n,e,s,i,a;n=new le({props:{$$slots:{default:[on]},$$scope:{ctx:r}}});let c=r[10]&&Pt(r);return{c(){t=W("div"),w(n.$$.fragment),e=F(),c&&c.c(),H(t,"class","c-msg-list-container svelte-80qwt2"),H(t,"data-testid","chat-message-list"),Z(t,"c-msg-list--minimal",!r[19].fullFeatured)},m(o,l){v(o,t,l),M(n,t,null),X(t,e),c&&c.m(t,null),s=!0,i||(a=[yt(t,"mouseenter",r[46]),yt(t,"mouseleave",r[47])],i=!0)},p(o,l){const m={};4158455&l[0]|134217728&l[1]&&(m.$$scope={dirty:l,ctx:o}),n.$set(m),o[10]?c?(c.p(o,l),1024&l[0]&&h(c,1)):(c=Pt(o),c.c(),h(c,1),c.m(t,null)):c&&(V(),f(c,1,1,()=>{c=null}),P()),(!s||524288&l[0])&&Z(t,"c-msg-list--minimal",!o[19].fullFeatured)},i(o){s||(h(n.$$.fragment,o),h(c),s=!0)},o(o){f(n.$$.fragment,o),f(c),s=!1},d(o){o&&z(t),y(n),c&&c.d(),i=!1,Gt(a)}}}function ln(r,t,n){let e,s,i,a,c,o,l,m,g,L,S,I,B,R,b,_,p,d,$,x,A=D,et=D,ht=()=>(et(),et=ct(U,u=>n(37,p=u)),U),nt=D;r.$$.on_destroy.push(()=>A()),r.$$.on_destroy.push(()=>et()),r.$$.on_destroy.push(()=>nt());let{chatModel:U}=t;ht();let{onboardingWorkspaceModel:st}=t,{msgListElement:k}=t;const Yt=Lt("agentConversationModel"),{agentExchangeStatus:ft,isCurrConversationAgentic:$t}=Yt;E(r,ft,u=>n(36,_=u)),E(r,$t,u=>n(35,b=u));const _t=Lt(Te.key);E(r,_t,u=>n(34,R=u));const G=new Ae(T(U,u=>u.currentConversationModel),{initialVisibleCount:20,batchSize:10,minBatchSize:5,maxBatchSize:20});E(r,G,u=>n(21,x=u));const{hasMoreBefore:St,isLoading:It,loadingDirection:xt}=G;E(r,St,u=>n(17,I=u)),E(r,It,u=>n(18,B=u)),E(r,xt,u=>n(20,$=u));const J=new Ve(3),wt=new Pe;re(()=>{G.dispose()});let K=!1,O=!1;function N(){n(7,K=!0)}async function rt(){if(!k||!I||B)return;N();const u=wt.captureScrollPosition(k),ot=J.getVelocityPPS(),at=ge(ot,G.getCurrentBatchSize()),lt=G.loadMoreBefore(at);u&&(N(),wt.restoreScrollPosition(k,u),await At()),lt&&k&&k.scrollTop<=1&&I&&rt()}ie(()=>{var u;((u=S.lastExchange)==null?void 0:u.seen_state)===pt.unseen&&N()});let it=0,Q=!0;const Mt=u=>S.markSeen(u);return r.$$set=u=>{"chatModel"in u&&ht(n(1,U=u.chatModel)),"onboardingWorkspaceModel"in u&&n(2,st=u.onboardingWorkspaceModel),"msgListElement"in u&&n(0,k=u.msgListElement)},r.$$.update=()=>{64&r.$$.dirty[1]&&(n(15,e=p.currentConversationModel),A(),A=ct(e,u=>n(16,S=u))),64&r.$$.dirty[1]&&(n(14,s=p.flags),nt(),nt=ct(s,u=>n(19,d=u))),120&r.$$.dirty[1]&&n(33,i=ce(p,_,b,R)),4&r.$$.dirty[1]&&n(13,a=i.chatHistory),16&r.$$.dirty[0]&&n(12,c=it),40&r.$$.dirty[0]&&n(11,o=!Q&&O),4&r.$$.dirty[1]&&n(6,l=i.lastGroupConfig),4&r.$$.dirty[1]&&n(10,m=i.doShowFloatingButtons),4&r.$$.dirty[1]&&n(9,g=i.doShowAgentSetupLogs),64&r.$$.dirty[0]&&n(8,L=l.remoteAgentErrorConfig)},[k,U,st,O,it,Q,l,K,L,g,m,o,c,a,s,e,S,I,B,d,$,x,ft,$t,_t,G,St,It,xt,J,N,rt,Mt,i,R,b,_,p,u=>st.retryProjectSummary(u),u=>Mt(u),()=>n(7,K=!0),function(u){oe[u?"unshift":"push"](()=>{k=u,n(0,k)})},()=>{n(7,K=!1),n(5,Q=!0),G.resetToBottom()},()=>{N(),n(5,Q=!1)},u=>{if(u<=1&&N(),k){J.addSample(u);const ot=J.getVelocityPPS(),at=J.getDirection(),lt=pe(ot,{baseThreshold:200,predictTime:300});at==="up"&&u<lt&&I&&rt()}},u=>n(4,it=u),()=>n(3,O=!0),()=>n(3,O=!1)]}class ps extends ee{constructor(t){super(),ne(this,t,ln,an,se,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{ps as default};
