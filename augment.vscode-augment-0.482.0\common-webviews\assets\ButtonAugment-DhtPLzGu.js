import{S as H,i as J,s as K,a as x,C as A,D as E,g as M,a4 as N,u as d,t as g,F as G,Y as D,Z as O,j as P,ab as m,V as h,I as F,c as f,a2 as I,e as p,f as S,q as b,r as z,h as v,_ as C,$ as w,a0 as y,a1 as k,T as Q}from"./SpinnerAugment-Cx9dt_ox.js";import{B as U}from"./BaseButton-BqzdgpkK.js";const W=a=>({}),T=a=>({}),X=a=>({}),V=a=>({});function Y(a){let t,l;const c=a[10].iconLeft,o=C(c,a,a[20],V);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,s){p(i,t,s),o&&o.m(t,null),l=!0},p(i,s){o&&o.p&&(!l||1048576&s)&&w(o,c,i,i[20],l?k(c,i[20],s,X):y(i[20]),V)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function Z(a){let t,l,c;return l=new Q({props:{size:a[0]===.5?1:a[0],weight:a[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:a}}}),{c(){t=h("div"),A(l.$$.fragment),f(t,"class","c-button--text svelte-psg43r")},m(o,i){p(o,t,i),E(l,t,null),c=!0},p(o,i){const s={};1&i&&(s.size=o[0]===.5?1:o[0]),2&i&&(s.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(s.$$scope={dirty:i,ctx:o}),l.$set(s)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){g(l.$$.fragment,o),c=!1},d(o){o&&v(t),G(l)}}}function tt(a){let t;const l=a[10].default,c=C(l,a,a[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&w(c,l,o,o[20],t?k(l,o[20],i,null):y(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){g(c,o),t=!1},d(o){c&&c.d(o)}}}function _(a){let t,l;const c=a[10].iconRight,o=C(c,a,a[20],T);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,s){p(i,t,s),o&&o.m(t,null),l=!0},p(i,s){o&&o.p&&(!l||1048576&s)&&w(o,c,i,i[20],l?k(c,i[20],s,W):y(i[20]),T)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function it(a){let t,l,c,o,i,s=a[9].iconLeft&&Y(a),r=a[9].default&&Z(a),u=a[9].iconRight&&_(a);return{c(){t=h("div"),s&&s.c(),l=F(),r&&r.c(),c=F(),u&&u.c(),f(t,"class",o=I(`c-button--content c-button--size-${a[0]}`)+" svelte-psg43r")},m(n,$){p(n,t,$),s&&s.m(t,null),S(t,l),r&&r.m(t,null),S(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?s?(s.p(n,$),512&$&&d(s,1)):(s=Y(n),s.c(),d(s,1),s.m(t,l)):s&&(b(),g(s,1,1,()=>{s=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=Z(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),g(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=_(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),g(u,1,1,()=>{u=null}),z()),(!i||1&$&&o!==(o=I(`c-button--content c-button--size-${n[0]}`)+" svelte-psg43r"))&&f(t,"class",o)},i(n){i||(d(s),d(r),d(u),i=!0)},o(n){g(s),g(r),g(u),i=!1},d(n){n&&v(t),s&&s.d(),r&&r.d(),u&&u.d()}}}function ot(a){let t,l;const c=[{size:a[0]},{variant:a[1]},{color:a[2]},{highContrast:a[3]},{disabled:a[4]},{loading:a[6]},{alignment:a[7]},{radius:a[5]},a[8]];let o={$$slots:{default:[it]},$$scope:{ctx:a}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new U({props:o}),t.$on("click",a[11]),t.$on("keyup",a[12]),t.$on("keydown",a[13]),t.$on("mousedown",a[14]),t.$on("mouseover",a[15]),t.$on("focus",a[16]),t.$on("mouseleave",a[17]),t.$on("blur",a[18]),t.$on("contextmenu",a[19]),{c(){A(t.$$.fragment)},m(i,s){E(t,i,s),l=!0},p(i,[s]){const r=511&s?M(c,[1&s&&{size:i[0]},2&s&&{variant:i[1]},4&s&&{color:i[2]},8&s&&{highContrast:i[3]},16&s&&{disabled:i[4]},64&s&&{loading:i[6]},128&s&&{alignment:i[7]},32&s&&{radius:i[5]},256&s&&N(i[8])]):{};1049091&s&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){g(t.$$.fragment,i),l=!1},d(i){G(t,i)}}}function at(a,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=D(t,c),{$$slots:i={},$$scope:s}=t;const r=O(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="accent"}=t,{highContrast:L=!1}=t,{disabled:R=!1}=t,{radius:B="medium"}=t,{loading:j=!1}=t,{alignment:q="center"}=t;return a.$$set=e=>{t=x(x({},t),P(e)),l(8,o=D(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,L=e.highContrast),"disabled"in e&&l(4,R=e.disabled),"radius"in e&&l(5,B=e.radius),"loading"in e&&l(6,j=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,s=e.$$scope)},[u,n,$,L,R,B,j,q,o,r,i,function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},function(e){m.call(this,a,e)},s]}class et extends H{constructor(t){super(),J(this,t,at,ot,K,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
