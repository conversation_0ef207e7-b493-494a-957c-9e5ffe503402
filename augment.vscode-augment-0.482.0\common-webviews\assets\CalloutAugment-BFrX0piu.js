import{S as T,i as A,s as V,T as W,R as y,a as g,V as v,C as X,W as b,X as h,e as d,D as Y,g as Z,u as $,t as u,h as f,F as _,Y as j,Z as k,j as B,_ as D,I as E,c as F,q as G,r as H,$ as I,a0 as N,a1 as R}from"./SpinnerAugment-Cx9dt_ox.js";const J=o=>({}),q=o=>({});function w(o){let l,c;const i=o[8].icon,a=D(i,o,o[9],q);return{c(){l=v("div"),a&&a.c(),F(l,"class","c-callout-icon svelte-8as1i4")},m(e,n){d(e,l,n),a&&a.m(l,null),c=!0},p(e,n){a&&a.p&&(!c||512&n)&&I(a,i,e,e[9],c?R(i,e[9],n,J):N(e[9]),q)},i(e){c||($(a,e),c=!0)},o(e){u(a,e),c=!1},d(e){e&&f(l),a&&a.d(e)}}}function K(o){let l,c,i,a=o[7].icon&&w(o);const e=o[8].default,n=D(e,o,o[9],null);return{c(){a&&a.c(),l=E(),c=v("div"),n&&n.c(),F(c,"class","c-callout-body")},m(t,s){a&&a.m(t,s),d(t,l,s),d(t,c,s),n&&n.m(c,null),i=!0},p(t,s){t[7].icon?a?(a.p(t,s),128&s&&$(a,1)):(a=w(t),a.c(),$(a,1),a.m(l.parentNode,l)):a&&(G(),u(a,1,1,()=>{a=null}),H()),n&&n.p&&(!i||512&s)&&I(n,e,t,t[9],i?R(e,t[9],s,null):N(t[9]),null)},i(t){i||($(a),$(n,t),i=!0)},o(t){u(a),u(n,t),i=!1},d(t){t&&(f(l),f(c)),a&&a.d(t),n&&n.d(t)}}}function L(o){let l,c,i,a;c=new W({props:{size:o[6],$$slots:{default:[K]},$$scope:{ctx:o}}});let e=[y(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=g(n,e[t]);return{c(){l=v("div"),X(c.$$.fragment),b(l,n),h(l,"c-callout--highContrast",o[3]),h(l,"svelte-8as1i4",!0)},m(t,s){d(t,l,s),Y(c,l,null),a=!0},p(t,[s]){const p={};640&s&&(p.$$scope={dirty:s,ctx:t}),c.$set(p),b(l,n=Z(e,[1&s&&y(t[0]),(!a||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),h(l,"c-callout--highContrast",t[3]),h(l,"svelte-8as1i4",!0)},i(t){a||($(c.$$.fragment,t),a=!0)},o(t){u(c.$$.fragment,t),a=!1},d(t){t&&f(l),_(c)}}}function M(o,l,c){let i,a;const e=["color","variant","size","highContrast"];let n=j(l,e),{$$slots:t={},$$scope:s}=l;const p=k(t);let{color:C="info"}=l,{variant:z="soft"}=l,{size:m=2}=l,{highContrast:x=!1}=l;const S=m;return o.$$set=r=>{l=g(g({},l),B(r)),c(10,n=j(l,e)),"color"in r&&c(0,C=r.color),"variant"in r&&c(1,z=r.variant),"size"in r&&c(2,m=r.size),"highContrast"in r&&c(3,x=r.highContrast),"$$scope"in r&&c(9,s=r.$$scope)},o.$$.update=()=>{c(5,{class:i,...a}=n,i,(c(4,a),c(10,n)))},[C,z,m,x,a,i,S,p,t,s]}class P extends T{constructor(l){super(),A(this,l,M,L,V,{color:0,variant:1,size:2,highContrast:3})}}export{P as C};
