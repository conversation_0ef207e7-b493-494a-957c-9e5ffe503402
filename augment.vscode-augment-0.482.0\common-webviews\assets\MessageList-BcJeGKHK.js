import{S as Me,i as Se,s as xe,V as P,C as M,I as F,c as H,X as z,e as _,D as S,f as O,a8 as re,u,q as v,t as p,r as q,h as R,F as x,aa as de,ag as oe,ah as V,a6 as ke,a9 as Q,ad as he,K as Le,n as D,G as X,A as _e,J as j}from"./SpinnerAugment-Cx9dt_ox.js";import{e as W,u as Re,o as Ie}from"./BaseButton-BqzdgpkK.js";import{G as ve,g as qe,t as De,a as Ee,M as Ae,A as be,b as Fe,R as we,c as Ne,S as He,d as je,e as Be,P as Ge,f as Te,h as ze,C as We,i as Pe,U as Ue,j as ye,E as Je,k as Ke,l as Ve}from"./RemoteAgentRetry-ByFtlC0q.js";import"./Content-BiWRcmeV.js";import{S as Y,i as se,a as Xe,b as Oe,c as Qe,d as Ye,e as Ze,f as et,g as tt,h as nt,j as rt,k as ot,E as st}from"./lodash-Drc0SN5U.js";import"./folder-CEjIF7oG.js";import{R as at}from"./check-BrrMO4vE.js";import"./isObjectLike-BWVRxMGM.js";import{S as lt}from"./main-panel-CD6EXLpt.js";import{aq as it,ar as mt}from"./AugmentMessage-kCRDis1x.js";import"./types-DDm27S8B.js";import"./MaterialIcon-8-Z76Y2_.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-BxQII05L.js";import"./Keybindings-C3J8hU1V.js";import"./CalloutAugment-BFrX0piu.js";import"./exclamation-triangle-BbVpV4C-.js";import"./CardAugment-RumqAz-v.js";import"./TextTooltipAugment-DTMpOwfF.js";import"./IconButtonAugment-BjDqXmYl.js";import"./index-CGnj6T3o.js";import"./pen-to-square-CZwCjcp0.js";import"./augment-logo-DdgjewTP.js";import"./ButtonAugment-DhtPLzGu.js";import"./folder-opened-CX_GXeEO.js";import"./expand-CURYX9ur.js";import"./diff-utils-C7XQLqYW.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-8X-F_Twk.js";import"./arrow-up-right-from-square-D2UwhhNo.js";import"./CollapseButtonAugment-D3vAw6HE.js";import"./open-in-new-window-C_TwPNdv.js";import"./github-7gPAsyj4.js";import"./index-DUiNNixO.js";import"./utils-DJhaageo.js";import"./chat-types-B-te1sXh.js";import"./globals-D0QH3NT1.js";import"./types-8LwCBeyq.js";import"./file-paths-BcSg4gks.js";import"./types-CGlLNakm.js";import"./TextAreaAugment-DEYj-_0J.js";import"./design-system-init-BCZOObrS.js";import"./StatusIndicator-BAEKlH2H.js";import"./await_block-H61A9-v_.js";import"./Filespan-D-BqE8vd.js";import"./ellipsis-Cm0UKVWz.js";import"./terminal-BjJSzToG.js";import"./VSCodeCodicon-B3px2_jp.js";import"./chat-flags-model-GjgruWjX.js";import"./CopyButton-CugjC8Pf.js";import"./magnifying-glass-Fv6Gz5Ea.js";import"./IconFilePath-B4JAagx1.js";import"./LanguageIcon-FVMxq7uD.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-DYf4hfS2.js";import"./mcp-logo-DslCzNpc.js";function ae(o,t,n){const e=o.slice();e[39]=t[n],e[42]=n;const r=e[42]+1===e[12].length;return e[40]=r,e}function le(o,t,n){const e=o.slice();e[43]=t[n].turn,e[44]=t[n].idx;const r=e[44]+1===e[13].length;return e[45]=r,e}function ie(o){let t,n;return t=new be({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ut(o){let t,n,e,r;const s=[yt,wt],c=[];function m(l,i){return l[17].enableRichCheckpointInfo?0:1}return t=m(o),n=c[t]=s[t](o),{c(){n.c(),e=j()},m(l,i){c[t].m(l,i),_(l,e,i),r=!0},p(l,i){let a=t;t=m(l),t===a?c[t].p(l,i):(v(),p(c[a],1,1,()=>{c[a]=null}),q(),n=c[t],n?n.p(l,i):(n=c[t]=s[t](l),n.c()),u(n,1),n.m(e.parentNode,e))},i(l){r||(u(n),r=!0)},o(l){p(n),r=!1},d(l){l&&R(e),c[t].d(l)}}}function ct(o){let t,n;return t=new We({props:{group:o[39],chatModel:o[1],turn:o[43],turnIndex:o[44],isLastTurn:o[45],messageListContainer:o[0]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.turn=e[43]),4096&r[0]&&(s.turnIndex=e[44]),12288&r[0]&&(s.isLastTurn=e[45]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function $t(o){let t,n;return t=new Pe({props:{stage:o[43].stage,iterationId:o[43].iterationId,stageCount:o[43].stageCount}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.stage=e[43].stage),4096&r[0]&&(s.iterationId=e[43].iterationId),4096&r[0]&&(s.stageCount=e[43].stageCount),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function pt(o){let t,n;return t=new Ue({props:{chatModel:o[1],msg:o[43].response_text??""}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.msg=e[43].response_text??""),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function gt(o){let t,n;return t=new it({props:{group:o[39],markdown:o[43].response_text??"",messageListContainer:o[0]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),4096&r[0]&&(s.markdown=e[43].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ft(o){let t,n;function e(){return o[30](o[43])}return t=new ye({props:{turn:o[43],preamble:lt,resendTurn:e,$$slots:{default:[Ct]},$$scope:{ctx:o}}}),{c(){M(t.$$.fragment)},m(r,s){S(t,r,s),n=!0},p(r,s){o=r;const c={};4096&s[0]&&(c.turn=o[43]),4100&s[0]&&(c.resendTurn=e),69632&s[0]|131072&s[1]&&(c.$$scope={dirty:s,ctx:o}),t.$set(c)},i(r){n||(u(t.$$.fragment,r),n=!0)},o(r){p(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function dt(o){let t,n;return t=new Je({props:{flagsModel:o[14],turn:o[43]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};16384&r[0]&&(s.flagsModel=e[14]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ht(o){let t,n;return t=new ye({props:{turn:o[43]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function wt(o){let t,n;return t=new Ke({props:{turn:o[43]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function yt(o){let t,n;return t=new Ve({props:{turn:o[43]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Ct(o){let t,n;return t=new mt({props:{conversationModel:o[16],turn:o[43]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};65536&r[0]&&(s.conversationModel=e[16]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function me(o){let t,n,e,r;function s(){return o[31](o[43])}return{c(){t=P("div"),H(t,"class","c-msg-list__turn-seen")},m(c,m){_(c,t,m),e||(r=Q(n=Ne.call(null,t,{onSeen:s,track:o[43].seen_state!==Y.seen})),e=!0)},p(c,m){o=c,n&&he(n.update)&&4096&m[0]&&n.update.call(null,{onSeen:s,track:o[43].seen_state!==Y.seen})},d(c){c&&R(t),e=!1,r()}}}function ue(o,t){let n,e,r,s,c,m,l,i,a,g,$,f,y,h,I=se(t[43]);const A=[ht,dt,ft,gt,pt,$t,ct,ut],L=[];function N(w,C){return 4096&C[0]&&(e=null),4096&C[0]&&(r=null),4096&C[0]&&(s=null),4096&C[0]&&(c=null),4096&C[0]&&(m=null),4096&C[0]&&(l=null),4096&C[0]&&(i=null),4096&C[0]&&(a=null),e==null&&(e=!!Xe(w[43])),e?0:(r==null&&(r=!!Oe(w[43])),r?1:(s==null&&(s=!!Qe(w[43])),s?2:(c==null&&(c=!!Ye(w[43])),c?3:(m==null&&(m=!!Ze(w[43])),m?4:(l==null&&(l=!!et(w[43])),l?5:(i==null&&(i=!!(tt(w[43])||nt(w[43])||rt(w[43]))),i?6:(a==null&&(a=!(!ot(w[43])||w[43].status!==st.success)),a?7:-1)))))))}~(g=N(t,[-1,-1]))&&($=L[g]=A[g](t));let k=I&&me(t);return{key:o,first:null,c(){n=j(),$&&$.c(),f=F(),k&&k.c(),y=j(),this.first=n},m(w,C){_(w,n,C),~g&&L[g].m(w,C),_(w,f,C),k&&k.m(w,C),_(w,y,C),h=!0},p(w,C){let E=g;g=N(t=w,C),g===E?~g&&L[g].p(t,C):($&&(v(),p(L[E],1,1,()=>{L[E]=null}),q()),~g?($=L[g],$?$.p(t,C):($=L[g]=A[g](t),$.c()),u($,1),$.m(f.parentNode,f)):$=null),4096&C[0]&&(I=se(t[43])),I?k?k.p(t,C):(k=me(t),k.c(),k.m(y.parentNode,y)):k&&(k.d(1),k=null)},i(w){h||(u($),h=!0)},o(w){p($),h=!1},d(w){w&&(R(n),R(f),R(y)),~g&&L[g].d(w),k&&k.d(w)}}}function ce(o){let t,n,e,r,s;const c=[Rt,_t,Lt,kt,xt,St,Mt],m=[];function l(a,g){return a[9]?0:a[6].retryMessage?1:a[6].showResumingRemoteAgent?2:a[6].showPaused?3:a[6].showGeneratingResponse?4:a[6].showAwaitingUserInput?5:a[6].showStopped?6:-1}~(t=l(o))&&(n=m[t]=c[t](o));let i=o[6].showRunningSpacer&&$e();return{c(){n&&n.c(),e=F(),i&&i.c(),r=j()},m(a,g){~t&&m[t].m(a,g),_(a,e,g),i&&i.m(a,g),_(a,r,g),s=!0},p(a,g){let $=t;t=l(a),t===$?~t&&m[t].p(a,g):(n&&(v(),p(m[$],1,1,()=>{m[$]=null}),q()),~t?(n=m[t],n?n.p(a,g):(n=m[t]=c[t](a),n.c()),u(n,1),n.m(e.parentNode,e)):n=null),a[6].showRunningSpacer?i||(i=$e(),i.c(),i.m(r.parentNode,r)):i&&(i.d(1),i=null)},i(a){s||(u(n),s=!0)},o(a){p(n),s=!1},d(a){a&&(R(e),R(r)),~t&&m[t].d(a),i&&i.d(a)}}}function Mt(o){let t,n;return t=new He({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function St(o){let t,n;return t=new je({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function xt(o){let t,n;return t=new Be({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function kt(o){let t,n;return t=new Ge({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Lt(o){let t,n;return t=new Te({}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function _t(o){let t,n;return t=new ze({props:{message:o[6].retryMessage}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};64&r[0]&&(s.message=e[6].retryMessage),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Rt(o){let t,n;return t=new we({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function $e(o){let t;return{c(){t=P("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){_(n,t,e)},d(n){n&&R(t)}}}function It(o){let t,n,e,r=[],s=new Map,c=W(o[39]);const m=i=>i[43].request_id??`no-request-id-${i[44]}`;for(let i=0;i<c.length;i+=1){let a=le(o,c,i),g=m(a);s.set(g,r[i]=ue(g,a))}let l=o[40]&&ce(o);return{c(){for(let i=0;i<r.length;i+=1)r[i].c();t=F(),l&&l.c(),n=j()},m(i,a){for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(i,a);_(i,t,a),l&&l.m(i,a),_(i,n,a),e=!0},p(i,a){17002503&a[0]&&(c=W(i[39]),v(),r=Re(r,a,m,1,i,c,s,t.parentNode,Ie,ue,t,le),q()),i[40]?l?(l.p(i,a),4096&a[0]&&u(l,1)):(l=ce(i),l.c(),u(l,1),l.m(n.parentNode,n)):l&&(v(),p(l,1,1,()=>{l=null}),q())},i(i){if(!e){for(let a=0;a<c.length;a+=1)u(r[a]);u(l),e=!0}},o(i){for(let a=0;a<r.length;a+=1)p(r[a]);p(l),e=!1},d(i){i&&(R(t),R(n));for(let a=0;a<r.length;a+=1)r[a].d(i);l&&l.d(i)}}}function pe(o){let t,n;return t=new Fe({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[40],userControlsScroll:o[3],requestId:o[39][0].turn.request_id,releaseScroll:o[32],messageListContainer:o[0],minHeight:o[40]?o[8]:0,$$slots:{default:[It]},$$scope:{ctx:o}}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.isLastItem=e[40]),8&r[0]&&(s.userControlsScroll=e[3]),4096&r[0]&&(s.requestId=e[39][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[32]),1&r[0]&&(s.messageListContainer=e[0]),4352&r[0]&&(s.minHeight=e[40]?e[8]:0),225863&r[0]|131072&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ge(o){let t,n;return t=new we({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function vt(o){let t,n,e,r,s,c,m,l=o[10]&&ie(),i=W(o[12]),a=[];for(let f=0;f<i.length;f+=1)a[f]=pe(ae(o,i,f));const g=f=>p(a[f],1,1,()=>{a[f]=null});let $=!o[13].length&&o[9]&&ge(o);return{c(){t=P("div"),l&&l.c(),n=F();for(let f=0;f<a.length;f+=1)a[f].c();e=F(),$&&$.c(),H(t,"class","c-msg-list svelte-t9khzq"),z(t,"c-msg-list--minimal",!o[17].fullFeatured)},m(f,y){_(f,t,y),l&&l.m(t,null),O(t,n);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(t,null);O(t,e),$&&$.m(t,null),o[33](t),s=!0,c||(m=[Q(De.call(null,t,{onScrollIntoBottom:o[21],onScrollAwayFromBottom:o[22],onScroll:o[34]})),Q(r=Ee.call(null,t,{onHeightChange:o[35]}))],c=!0)},p(f,y){if(f[10]?l?1024&y[0]&&u(l,1):(l=ie(),l.c(),u(l,1),l.m(t,n)):l&&(v(),p(l,1,1,()=>{l=null}),q()),17003343&y[0]){let h;for(i=W(f[12]),h=0;h<i.length;h+=1){const I=ae(f,i,h);a[h]?(a[h].p(I,y),u(a[h],1)):(a[h]=pe(I),a[h].c(),u(a[h],1),a[h].m(t,e))}for(v(),h=i.length;h<a.length;h+=1)g(h);q()}!f[13].length&&f[9]?$?($.p(f,y),8704&y[0]&&u($,1)):($=ge(f),$.c(),u($,1),$.m(t,null)):$&&(v(),p($,1,1,()=>{$=null}),q()),r&&he(r.update)&&32&y[0]&&r.update.call(null,{onHeightChange:f[35]}),(!s||131072&y[0])&&z(t,"c-msg-list--minimal",!f[17].fullFeatured)},i(f){if(!s){u(l);for(let y=0;y<i.length;y+=1)u(a[y]);u($),s=!0}},o(f){p(l),a=a.filter(Boolean);for(let y=0;y<a.length;y+=1)p(a[y]);p($),s=!1},d(f){f&&R(t),l&&l.d(),Le(a,f),$&&$.d(),o[33](null),c=!1,de(m)}}}function fe(o){let t,n;return t=new Ae({props:{messageListElement:o[0],showScrollDown:o[7]}}),{c(){M(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),128&r[0]&&(s.showScrollDown=e[7]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function qt(o){let t,n,e,r,s,c;n=new ve({props:{$$slots:{default:[vt]},$$scope:{ctx:o}}});let m=o[11]&&fe(o);return{c(){t=P("div"),M(n.$$.fragment),e=F(),m&&m.c(),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),z(t,"c-msg-list--minimal",!o[17].fullFeatured)},m(l,i){_(l,t,i),S(n,t,null),O(t,e),m&&m.m(t,null),r=!0,s||(c=[re(t,"mouseenter",o[36]),re(t,"mouseleave",o[37])],s=!0)},p(l,i){const a={};227183&i[0]|131072&i[1]&&(a.$$scope={dirty:i,ctx:l}),n.$set(a),l[11]?m?(m.p(l,i),2048&i[0]&&u(m,1)):(m=fe(l),m.c(),u(m,1),m.m(t,null)):m&&(v(),p(m,1,1,()=>{m=null}),q()),(!r||131072&i[0])&&z(t,"c-msg-list--minimal",!l[17].fullFeatured)},i(l){r||(u(n.$$.fragment,l),u(m),r=!0)},o(l){p(n.$$.fragment,l),p(m),r=!1},d(l){l&&R(t),x(n),m&&m.d(),s=!1,de(c)}}}function Dt(o,t,n){let e,r,s,c,m,l,i,a,g,$,f,y,h,I,A,L,N,k=D,w=D,C=()=>(w(),w=X(B,d=>n(29,L=d)),B),E=D;o.$$.on_destroy.push(()=>k()),o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>E());let{chatModel:B}=t;C();let{onboardingWorkspaceModel:U}=t,{msgListElement:G}=t;const Ce=oe("agentConversationModel"),{agentExchangeStatus:Z,isCurrConversationAgentic:ee}=Ce;V(o,Z,d=>n(28,A=d)),V(o,ee,d=>n(27,I=d));const te=oe(at.key);V(o,te,d=>n(26,h=d));let b=!1,T=!1;function J(){n(3,b=!0)}ke(()=>{var d;((d=y.lastExchange)==null?void 0:d.seen_state)===Y.unseen&&J()});let K=0;const ne=d=>y.markSeen(d);return o.$$set=d=>{"chatModel"in d&&C(n(1,B=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,U=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,G=d.msgListElement)},o.$$.update=()=>{536870912&o.$$.dirty[0]&&(n(15,e=L.currentConversationModel),k(),k=X(e,d=>n(16,y=d))),536870912&o.$$.dirty[0]&&(n(14,r=L.flags),E(),E=X(r,d=>n(17,N=d))),1006632960&o.$$.dirty[0]&&n(25,s=qe(L,A,I,h)),33554432&o.$$.dirty[0]&&n(13,c=s.chatHistory),33554432&o.$$.dirty[0]&&n(12,m=s.groupedChatHistory),33554432&o.$$.dirty[0]&&n(6,l=s.lastGroupConfig),33554432&o.$$.dirty[0]&&n(11,i=s.doShowFloatingButtons),33554432&o.$$.dirty[0]&&n(10,a=s.doShowAgentSetupLogs),64&o.$$.dirty[0]&&n(9,g=l.remoteAgentErrorConfig),32&o.$$.dirty[0]&&n(8,$=K),24&o.$$.dirty[0]&&n(7,f=b&&T)},[G,B,U,b,T,K,l,f,$,g,a,i,m,c,r,e,y,N,Z,ee,te,function(){n(3,b=!1)},function(){n(3,b=!0)},J,ne,s,h,I,A,L,d=>U.retryProjectSummary(d),d=>ne(d),()=>n(3,b=!0),function(d){_e[d?"unshift":"push"](()=>{G=d,n(0,G)})},d=>{d<=1&&J()},d=>n(5,K=d),()=>n(4,T=!0),()=>n(4,T=!1)]}class Hn extends Me{constructor(t){super(),Se(this,t,Dt,qt,xe,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Hn as default};
