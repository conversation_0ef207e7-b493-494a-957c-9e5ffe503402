use std::sync::{Arc, Mutex};
use serde::{Deserialize, Serialize};
use tauri::{State, Manager, Emitter};
// use tauri_plugin_updater::UpdaterExt; // 不再使用官方更新插件
use std::time::{SystemTime, UNIX_EPOCH};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use base64::{Engine as _, engine::general_purpose};
use serde_json::{Map, Value};
use rusqlite::Connection;


use uuid::Uuid;
use sha2::{Sha256, Digest};
use sysinfo::System;
use chrono::Datelike;

#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;

mod config;
use config::Config;







// mod idea_config_replacer; // 已弃用：配置文件方式容易被检测
// mod idea_runtime_injector; // 已弃用：过于复杂，改用简单方案

#[derive(Debug, Clone, Serialize, Deserialize)]
struct LogEntry {
    timestamp: u64,
    message: String,
    level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CursorUsage {
    used: i32,
    total: i32,
    percentage: f64,
    remaining: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ApiQuota {
    total: f64,
    used: f64,
    remaining: f64,
    percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ModelTestResult {
    model: String,
    status: String,
    response_time: Option<f64>,
    error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ModelListResponse {
    models: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AppState {
    augment_logs: Vec<LogEntry>,
    cursor_logs: Vec<LogEntry>,
    is_running: bool,
}

type AppStateType = Arc<Mutex<AppState>>;



impl Default for AppState {
    fn default() -> Self {
        Self {
            augment_logs: Vec::new(),
            cursor_logs: Vec::new(),
            is_running: false,
        }
    }
}

// 添加日志 - 智能判断日志类型
fn add_log(state: &mut AppState, message: String, level: String) {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let log_entry = LogEntry {
        timestamp,
        message: message.clone(),
        level: level.clone(),
    };

    // 根据消息内容判断是Cursor还是Augment日志
    if message.contains("Cursor") || message.contains("cursor") {
        state.cursor_logs.push(log_entry);
        // 保持最多100条日志
        if state.cursor_logs.len() > 100 {
            state.cursor_logs.remove(0);
        }
    } else {
        state.augment_logs.push(log_entry);
        // 保持最多100条日志
        if state.augment_logs.len() > 100 {
            state.augment_logs.remove(0);
        }
    }
}

// 添加Augment日志
fn add_augment_log(state: &mut AppState, message: String, level: String) {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    state.augment_logs.push(LogEntry {
        timestamp,
        message,
        level,
    });

    // 保持最新的1000条日志
    if state.augment_logs.len() > 1000 {
        state.augment_logs.remove(0);
    }
}

// 添加Cursor日志
fn add_cursor_log(state: &mut AppState, message: String, level: String) {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    state.cursor_logs.push(LogEntry {
        timestamp,
        message,
        level,
    });

    // 保持最多100条日志
    if state.cursor_logs.len() > 100 {
        state.cursor_logs.remove(0);
    }
}



// 获取VSCode文件路径
fn get_vscode_files(id: &str) -> Option<Vec<PathBuf>> {
    let base_dirs = [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()];
    let global_patterns = [
        &["User", "globalStorage"] as &[&str],
        &["data", "User", "globalStorage"],
        &[id],
        &["data", id],
    ];
    let workspace_patterns = [
        &["User", "workspaceStorage"] as &[&str],
        &["data", "User", "workspaceStorage"],
    ];

    let vscode_dirs: Vec<PathBuf> = base_dirs
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .flat_map(|base_dir| {
            fs::read_dir(&base_dir)
                .into_iter()
                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                .filter(|entry| entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false))
                .flat_map(|entry| {
                    let entry_path = entry.path();

                    // Global storage patterns
                    let global_paths: Vec<PathBuf> = global_patterns.iter().map(|pattern| {
                        pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment))
                    }).collect();

                    // Workspace storage patterns - enumerate all subdirectories
                    let workspace_paths: Vec<PathBuf> = workspace_patterns.iter().flat_map(|pattern| {
                        let workspace_base = pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment));
                        if workspace_base.exists() {
                            fs::read_dir(&workspace_base)
                                .into_iter()
                                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                                .filter(|entry| entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false))
                                .map(|entry| entry.path())
                                .collect::<Vec<_>>()
                        } else {
                            Vec::new()
                        }
                    }).collect();

                    global_paths.into_iter().chain(workspace_paths)
                })
        })
        .filter(|path| path.exists())
        .collect();

    (!vscode_dirs.is_empty()).then_some(vscode_dirs)
}

// 检测并记录运行中的VSCode相关进程
fn detect_running_vscode_processes(state: AppStateType) -> Vec<String> {
    let ps_command = r#"
        Get-Process | Where-Object {$_.ProcessName -match 'Code|Cursor|Windsurf'} |
        Select-Object ProcessName, Path |
        ConvertTo-Json -Compress
    "#;

    let output = Command::new("powershell")
        .args(["-Command", ps_command])
        .output();

    let mut running_apps = Vec::new();

    if let Ok(output) = output {
        let result = String::from_utf8_lossy(&output.stdout);
        if !result.trim().is_empty() && result.contains("ProcessName") {
            // 解析JSON并提取应用路径
            if let Ok(processes) = serde_json::from_str::<serde_json::Value>(&result) {
                if let Some(array) = processes.as_array() {
                    for process in array {
                        if let Some(path) = process.get("Path").and_then(|p| p.as_str()) {
                            if !running_apps.contains(&path.to_string()) {
                                running_apps.push(path.to_string());
                            }
                        }
                    }
                } else if let Some(path) = processes.get("Path").and_then(|p| p.as_str()) {
                    running_apps.push(path.to_string());
                }
            }
        }
    }

    if !running_apps.is_empty() {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("🔍 发现 {} 个正在运行的编辑器", running_apps.len()), "info".to_string());
        for app in &running_apps {
            let app_name = if app.contains("Code.exe") {
                "VSCode 编辑器"
            } else if app.contains("Cursor") {
                "Cursor 编辑器"
            } else if app.contains("Windsurf") {
                "Windsurf 编辑器"
            } else {
                "代码编辑器"
            };
            add_log(&mut app_state, format!("📝 检测到: {}", app_name), "info".to_string());
        }
    }

    running_apps
}

// 重新启动之前关闭的应用
fn restart_closed_apps(apps_to_restart: &[String], state: AppStateType) {
    if apps_to_restart.is_empty() {
        return;
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔄 正在重新启动编辑器程序...".to_string(), "info".to_string());
    }

    for app_path in apps_to_restart {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "🚀 正在启动编辑器...".to_string(), "info".to_string());
        }

        #[cfg(windows)]
        {
            use std::os::windows::process::CommandExt;
            let _ = Command::new("cmd")
                .args(["/C", "start", "", app_path])
                .creation_flags(0x08000000) // CREATE_NO_WINDOW
                .spawn();
        }

        #[cfg(not(windows))]
        {
            let _ = Command::new("sh")
                .args(["-c", &format!("open '{}'", app_path)])
                .spawn();
        }

        // 给每个应用一点启动时间
        std::thread::sleep(std::time::Duration::from_millis(1000));
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 编辑器程序启动完成".to_string(), "success".to_string());
    }
}

// 使用PowerShell强制更新文件
fn update_file_with_powershell(file_path: &Path, content: &str, state: AppStateType) -> Result<(), String> {
    let file_path_str = file_path.to_string_lossy().replace("\\", "/");

    // 构建PowerShell命令
    let ps_command = format!(
        r#"
        try {{
            # 强制停止可能占用文件的进程
            Get-Process | Where-Object {{$_.ProcessName -match 'Code|Cursor|Windsurf'}} | Stop-Process -Force -ErrorAction SilentlyContinue
            Start-Sleep -Milliseconds 500

            # 移除只读属性
            if (Test-Path '{}') {{
                Set-ItemProperty -Path '{}' -Name IsReadOnly -Value $false -ErrorAction SilentlyContinue
                Remove-Item -Path '{}' -Force -ErrorAction SilentlyContinue
            }}

            # 写入新内容
            Set-Content -Path '{}' -Value '{}' -Force -Encoding UTF8
            Write-Output 'SUCCESS'
        }} catch {{
            Write-Output "ERROR: $($_.Exception.Message)"
        }}
        "#,
        file_path_str, file_path_str, file_path_str, file_path_str, content
    );

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔧 使用PowerShell强制更新文件...".to_string(), "info".to_string());
    }

    // 执行PowerShell命令
    let output = Command::new("powershell")
        .args(["-Command", &ps_command])
        .output()
        .map_err(|e| format!("PowerShell执行失败: {}", e))?;

    let result = String::from_utf8_lossy(&output.stdout);

    if result.contains("SUCCESS") {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ PowerShell强制更新成功".to_string(), "success".to_string());
        Ok(())
    } else {
        let error_msg = if result.contains("ERROR:") {
            result.trim().to_string()
        } else {
            format!("PowerShell执行失败: {}", result.trim())
        };

        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("❌ PowerShell更新失败: {}", error_msg), "error".to_string());
        Err(error_msg)
    }
}

// 更新ID文件
fn update_id_file(file_path: &Path, state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔄 正在更新系统配置文件...".to_string(), "info".to_string());
    }

    // 显示旧UUID（如果存在）
    if file_path.exists() {
        let old_uuid = fs::read_to_string(file_path).unwrap_or_default();
        if !old_uuid.is_empty() {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "🔍 检测到现有配置信息".to_string(), "info".to_string());
        }
    }

    // 生成新UUID
    let new_uuid = Uuid::new_v4().to_string();
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✨ 生成新的设备标识".to_string(), "info".to_string());
    }

    // 先尝试常规方式
    if file_path.exists() {
        // 先尝试移除只读属性
        if let Ok(metadata) = fs::metadata(file_path) {
            let mut perms = metadata.permissions();
            perms.set_readonly(false);
            let _ = fs::set_permissions(file_path, perms);
        }
        let _ = fs::remove_file(file_path);
    }

    // 尝试写入新UUID
    match fs::write(file_path, &new_uuid) {
        Ok(_) => {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "✅ 设备标识更新完成".to_string(), "success".to_string());
            Ok(())
        }
        Err(_) => {
            // 常规方式失败，使用PowerShell强制更新
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "⚠️ 常规方式失败，尝试PowerShell强制更新...".to_string(), "warning".to_string());
            }

            update_file_with_powershell(file_path, &new_uuid, state.clone())
        }
    }
}

// 更新VSCode文件
fn update_vscode_files(vscode_file_path: &Path, vscode_keys: &[&str; 3], state: AppStateType) -> Result<(), String> {
    let storage_json_path = vscode_file_path.join("storage.json");

    if storage_json_path.exists() {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "🔄 正在更新编辑器配置信息...".to_string(), "info".to_string());
        }

        // 读取现有storage.json或创建空对象
        let mut data: Map<String, Value> = storage_json_path.exists()
            .then(|| fs::read_to_string(&storage_json_path).ok())
            .flatten()
            .and_then(|content| serde_json::from_str(&content).ok())
            .unwrap_or_else(Map::new);

        for key_encoded in vscode_keys {
            let key = match String::from_utf8(general_purpose::STANDARD.decode(key_encoded).map_err(|e| e.to_string())?) {
                Ok(k) => k,
                Err(e) => return Err(format!("解码密钥失败: {}", e)),
            };

            // 显示旧值（如果存在）
            if let Some(_old_value) = data.get(&key) {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "🔍 发现现有配置数据".to_string(), "info".to_string());
            }

            // 生成并更新新值
            let new_value = if *key_encoded == "dGVsZW1ldHJ5LmRldkRldmljZUlk" {
                Uuid::new_v4().to_string()
            } else {
                format!("{:x}", Sha256::digest(Uuid::new_v4().as_bytes()))
            };

            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "✨ 生成新的配置标识".to_string(), "info".to_string());
            }

            data.insert(key, Value::String(new_value));
        }

        // 写回文件
        let json_content = serde_json::to_string_pretty(&data).map_err(|e| e.to_string())?;
        fs::write(&storage_json_path, json_content).map_err(|e| e.to_string())?;

        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "✅ 配置信息更新完成".to_string(), "success".to_string());
        }
    }

    if vscode_file_path.exists() && vscode_file_path.is_file() {
        update_id_file(vscode_file_path, state.clone())?;
        lock_file(vscode_file_path, state)?;
    }

    Ok(())
}

// 精准修改VSCode数据库中的sessionId（类似IDEA的做法）
fn update_vscode_database_sessionid(vscode_global_storage_path: &Path, state: AppStateType) -> Result<(), String> {
    let db_files = ["state.vscdb", "state.vscdb.backup"];

    for file_name in &db_files {
        let db_path = vscode_global_storage_path.join(file_name);

        if !db_path.exists() {
            continue;
        }

        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "🔍 正在智能更新数据文件...".to_string(), "info".to_string());
        }

        // 尝试连接数据库并精准修改sessionId
        match Connection::open(&db_path) {
            Ok(conn) => {
                // 生成新的sessionId
                let new_session_id = uuid::Uuid::new_v4().to_string();

                // 执行额外的隐私保护清理
                perform_advanced_privacy_cleanup(&conn, state.clone());

                // 查询包含sessionId的Augment相关条目
                let count_query = "SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%Augment.vscode-augment%' AND value LIKE '%sessionId%'";

                match conn.prepare(count_query).and_then(|mut stmt| stmt.query_row([], |row| row.get::<_, i64>(0))) {
                    Ok(count) => {
                        if count > 0 {
                            {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, format!("🎯 发现 {} 个包含数值的数据项", count), "info".to_string());
                            }

                            // 查询并更新sessionId
                            let select_query = "SELECT key, value FROM ItemTable WHERE key LIKE '%Augment.vscode-augment%' AND value LIKE '%sessionId%'";
                            let mut stmt = conn.prepare(select_query).map_err(|e| e.to_string())?;
                            let rows = stmt.query_map([], |row| {
                                Ok((row.get::<_, String>(0)?, row.get::<_, String>(1)?))
                            }).map_err(|e| e.to_string())?;

                            let mut updated_count = 0;
                            for row in rows {
                                if let Ok((key, value)) = row {
                                    // 解析JSON并替换sessionId
                                    if let Ok(mut json_value) = serde_json::from_str::<serde_json::Value>(&value) {
                                        if let Some(obj) = json_value.as_object_mut() {
                                            if obj.contains_key("sessionId") {
                                                obj.insert("sessionId".to_string(), serde_json::Value::String(new_session_id.clone()));

                                                // 更新数据库
                                                let new_value = serde_json::to_string(&json_value).map_err(|e| e.to_string())?;
                                                let update_query = "UPDATE ItemTable SET value = ? WHERE key = ?";
                                                match conn.execute(update_query, [&new_value, &key]) {
                                                    Ok(_) => updated_count += 1,
                                                    Err(e) => {
                                                        let mut app_state = state.lock().unwrap();
                                                        add_log(&mut app_state, format!("❌ 更新失败: {}", e), "error".to_string());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            if updated_count > 0 {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, format!("✅ 成功更新 {} 个数值，保留聊天记录", updated_count), "success".to_string());
                                add_log(&mut app_state, format!("🆔 新数值: {}...{}", &new_session_id[..8], &new_session_id[new_session_id.len()-8..]), "info".to_string());
                            }
                        } else {
                            let mut app_state = state.lock().unwrap();
                            add_log(&mut app_state, "ℹ️ 未发现需要更新的数值数据".to_string(), "info".to_string());
                        }
                    }
                    Err(e) => {
                        let mut app_state = state.lock().unwrap();
                        add_log(&mut app_state, format!("❌ 查询数据库失败: {}", e), "error".to_string());
                    }
                }
            }
            Err(e) => {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("⚠️ 无法打开数据库 {}: {}，跳过更新", file_name, e), "warning".to_string());
            }
        }
    }

    Ok(())
}

// 执行高级隐私保护清理
fn perform_advanced_privacy_cleanup(conn: &Connection, state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔧 正在优化系统配置...".to_string(), "info".to_string());
    }

    // 🚨 新增：清理0.48版本的加密session数据（最高优先级）
    let encrypted_session_query = "DELETE FROM ItemTable WHERE key LIKE 'secret://%augment.sessions%'";
    if let Ok(affected) = conn.execute(encrypted_session_query, []) {
        if affected > 0 {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "🔐 已清理加密会话数据".to_string(), "success".to_string());
        }
    }

    // 🚨 新增：全面清理Augment配置（合并所有修改）
    clean_augment_configuration(conn, state.clone());

    // 🚨 新增：清理网络指纹相关数据
    clean_network_fingerprint(conn, state.clone());

    // 🚨 新增：清理项目路径和文件夹追踪（解决0.48版本路径识别问题）
    clean_project_paths_and_folders(conn, state.clone());

    // 🚨 新增：清理系统网络指纹
    clean_system_network_fingerprint(state.clone());

    // 清理时间戳追踪数据
    let timestamp_cleanup_queries = vec![
        "DELETE FROM ItemTable WHERE key LIKE 'telemetry.%Date'",
        "DELETE FROM ItemTable WHERE key LIKE 'terminal.history.timestamp.%'",
    ];

    let mut cleaned_items = 0;
    for query in timestamp_cleanup_queries {
        if let Ok(affected) = conn.execute(query, []) {
            cleaned_items += affected;
        }
    }

    if cleaned_items > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 系统时序配置已优化 ({}项)", cleaned_items), "success".to_string());
    }

    // 清理路径历史数据
    let path_cleanup_queries = vec![
        "DELETE FROM ItemTable WHERE key = 'history.recentlyOpenedPathsList'",
        "DELETE FROM ItemTable WHERE key = 'chat.workspaceTransfer'",
    ];

    let mut path_cleaned = 0;
    for query in path_cleanup_queries {
        if let Ok(affected) = conn.execute(query, []) {
            path_cleaned += affected;
        }
    }

    if path_cleaned > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 工作区索引已重置".to_string(), "success".to_string());
    }

    // 清理性能和版本追踪数据
    let perf_cleanup_queries = vec![
        "DELETE FROM ItemTable WHERE key LIKE 'perf/%'",
        "DELETE FROM ItemTable WHERE key LIKE 'releaseNotes/%'",
    ];

    let mut perf_cleaned = 0;
    for query in perf_cleanup_queries {
        if let Ok(affected) = conn.execute(query, []) {
            perf_cleaned += affected;
        }
    }

    if perf_cleaned > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 性能基准数据已刷新".to_string(), "success".to_string());
    }

    // 清理扩展状态追踪（保留重要的扩展功能）
    let extension_cleanup_query = "DELETE FROM ItemTable WHERE key LIKE 'workbench.views.extensions.%' AND key NOT LIKE '%augment%'";
    if let Ok(affected) = conn.execute(extension_cleanup_query, []) {
        if affected > 0 {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "✅ 扩展配置缓存已清理".to_string(), "success".to_string());
        }
    }

    // 更新关键的设备标识符（最重要的反检测措施）
    let new_machine_id = uuid::Uuid::new_v4().to_string();
    let machine_id_update_query = "UPDATE ItemTable SET value = ? WHERE key = 'storage.serviceMachineId'";

    if let Ok(affected) = conn.execute(machine_id_update_query, [&new_machine_id]) {
        if affected > 0 {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "✅ 核心设备配置已重置".to_string(), "success".to_string());
        } else {
            // 如果没有找到现有的记录，尝试插入新记录
            let machine_id_insert_query = "INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('storage.serviceMachineId', ?)";
            if let Ok(_) = conn.execute(machine_id_insert_query, [&new_machine_id]) {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "✅ 核心设备配置已初始化".to_string(), "success".to_string());
            }
        }
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🛡️ 高级隐私保护配置完成".to_string(), "success".to_string());
    }
}

// 🚨 新增：全面清理Augment配置（合并所有修改）
fn clean_augment_configuration(conn: &Connection, state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔧 正在全面清理Augment配置...".to_string(), "info".to_string());
    }

    // 查询Augment配置
    let query = "SELECT key, value FROM ItemTable WHERE key = 'Augment.vscode-augment'";

    if let Ok(mut stmt) = conn.prepare(query) {
        if let Ok(rows) = stmt.query_map([], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?
            ))
        }) {
            for row_result in rows {
                if let Ok((key, value)) = row_result {
                    // 解析JSON并进行全面清理
                    if let Ok(mut json_value) = serde_json::from_str::<serde_json::Value>(&value) {
                        if let Some(obj) = json_value.as_object_mut() {

                            // 🚨 最关键：重置远程代理标记
                            obj.insert("sidecar.agent.hasEverUsedAgent".to_string(), serde_json::Value::Bool(false));
                            obj.insert("sidecar.agent.hasEverUsedRemoteAgent".to_string(), serde_json::Value::Bool(false));

                            // 🚨 重置状态系统
                            let reset_states = r#"[["authenticated",{"name":"authenticated","status":"incomplete"}],["syncingPermitted",{"name":"syncingPermitted","status":"incomplete"}],["uploadingHomeDir",{"name":"uploadingHomeDir","status":"initializing"}],["workspaceTooLarge",{"name":"workspaceTooLarge","status":"initializing"}]]"#;
                            obj.insert("actionSystemStates".to_string(), serde_json::Value::String(reset_states.to_string()));

                            // 🚨 清理网络相关标记
                            obj.remove("lastNetworkLocation");
                            obj.remove("networkFingerprint");
                            obj.remove("connectionHistory");
                            obj.remove("proxySettings");

                            // 更新数据库
                            if let Ok(updated_json) = serde_json::to_string(&json_value) {
                                let update_query = "UPDATE ItemTable SET value = ?1 WHERE key = ?2";
                                if let Ok(affected) = conn.execute(update_query, [&updated_json, &key]) {
                                    if affected > 0 {
                                        let mut app_state = state.lock().unwrap();
                                        add_log(&mut app_state, "✅ Augment配置已全面清理".to_string(), "success".to_string());
                                        add_log(&mut app_state, "🔐 远程代理标记已重置为false".to_string(), "success".to_string());
                                        add_log(&mut app_state, "🔄 状态系统已重置".to_string(), "success".to_string());
                                    } else {
                                        let mut app_state = state.lock().unwrap();
                                        add_log(&mut app_state, "⚠️ Augment配置更新失败".to_string(), "warning".to_string());
                                    }
                                } else {
                                    let mut app_state = state.lock().unwrap();
                                    add_log(&mut app_state, "❌ 数据库更新失败".to_string(), "error".to_string());
                                }
                            } else {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, "❌ JSON序列化失败".to_string(), "error".to_string());
                            }
                        } else {
                            let mut app_state = state.lock().unwrap();
                            add_log(&mut app_state, "❌ JSON对象解析失败".to_string(), "error".to_string());
                        }
                    } else {
                        let mut app_state = state.lock().unwrap();
                        add_log(&mut app_state, "❌ JSON解析失败".to_string(), "error".to_string());
                    }
                }
            }
        }
    }
}

// 🚨 新增：清理项目路径和文件夹追踪（解决0.48版本路径识别问题）
fn clean_project_paths_and_folders(conn: &Connection, state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "📁 正在清理项目路径追踪数据...".to_string(), "info".to_string());
    }

    // 1. 清理最近打开的路径列表（关键！）
    let path_cleanup_result = clean_recently_opened_paths(conn, state.clone());

    // 2. 清理工作区传输记录
    let workspace_cleanup_result = clean_workspace_transfer(conn, state.clone());

    // 3. 清理SCM/Git相关路径
    let scm_cleanup_result = clean_scm_paths(conn, state.clone());

    // 4. 清理其他可能的路径追踪
    let other_cleanup_result = clean_other_path_tracking(conn, state.clone());

    let total_cleaned = path_cleanup_result + workspace_cleanup_result + scm_cleanup_result + other_cleanup_result;

    if total_cleaned > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 项目路径追踪数据已清理 ({}项)", total_cleaned), "success".to_string());
        add_log(&mut app_state, "🔒 项目文件夹名称已混淆，避免路径识别".to_string(), "success".to_string());
    }
}

// 清理最近打开的路径列表
fn clean_recently_opened_paths(conn: &Connection, state: AppStateType) -> usize {
    let mut cleaned_count = 0;

    // 查询当前的路径列表
    let query = "SELECT key, value FROM ItemTable WHERE key = 'history.recentlyOpenedPathsList'";

    if let Ok(mut stmt) = conn.prepare(query) {
        if let Ok(rows) = stmt.query_map([], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?
            ))
        }) {
            for row_result in rows {
                if let Ok((key, value)) = row_result {
                    // 解析JSON并替换为通用路径
                    if let Ok(mut json_value) = serde_json::from_str::<serde_json::Value>(&value) {
                        if let Some(obj) = json_value.as_object_mut() {
                            if let Some(entries) = obj.get_mut("entries") {
                                if let Some(entries_array) = entries.as_array_mut() {
                                    // 清空所有路径或替换为通用路径
                                    entries_array.clear();

                                    // 可选：添加一个通用的假路径来避免空列表异常
                                    let fake_entry = serde_json::json!({
                                        "folderUri": "file:///c%3A/Users/<USER>/Documents/Projects"
                                    });
                                    entries_array.push(fake_entry);
                                }
                            }

                            // 更新数据库
                            if let Ok(updated_json) = serde_json::to_string(&json_value) {
                                let update_query = "UPDATE ItemTable SET value = ?1 WHERE key = ?2";
                                if let Ok(affected) = conn.execute(update_query, [&updated_json, &key]) {
                                    if affected > 0 {
                                        cleaned_count += 1;
                                        let mut app_state = state.lock().unwrap();
                                        add_log(&mut app_state, "🗂️ 最近打开路径已清理".to_string(), "success".to_string());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    cleaned_count
}

// 清理工作区传输记录
fn clean_workspace_transfer(conn: &Connection, state: AppStateType) -> usize {
    let mut cleaned_count = 0;

    // 确保工作区传输记录为空
    let update_query = "UPDATE ItemTable SET value = '[]' WHERE key = 'chat.workspaceTransfer'";
    if let Ok(affected) = conn.execute(update_query, []) {
        if affected > 0 {
            cleaned_count += 1;
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "💬 工作区聊天传输记录已清理".to_string(), "success".to_string());
        }
    }

    cleaned_count
}

// 清理SCM/Git相关路径
fn clean_scm_paths(conn: &Connection, state: AppStateType) -> usize {
    let mut cleaned_count = 0;

    // 清理SCM相关的状态，但保持基本结构
    let scm_queries = vec![
        "DELETE FROM ItemTable WHERE key LIKE '%scm%' AND key LIKE '%path%'",
        "DELETE FROM ItemTable WHERE key LIKE '%git%' AND key LIKE '%repository%'",
        "DELETE FROM ItemTable WHERE key LIKE '%git%' AND key LIKE '%workspace%'",
    ];

    for query in &scm_queries {
        if let Ok(affected) = conn.execute(query, []) {
            cleaned_count += affected;
        }
    }

    if cleaned_count > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔧 Git/SCM路径记录已清理".to_string(), "success".to_string());
    }

    cleaned_count
}

// 清理其他可能的路径追踪
fn clean_other_path_tracking(conn: &Connection, state: AppStateType) -> usize {
    let mut cleaned_count = 0;

    // 清理其他可能包含路径信息的记录
    let other_queries = vec![
        "DELETE FROM ItemTable WHERE key LIKE '%workspaceStorage%'",
        "DELETE FROM ItemTable WHERE key LIKE '%workspace.identifier%'",
        "DELETE FROM ItemTable WHERE key LIKE '%folderUri%'",
        "DELETE FROM ItemTable WHERE key LIKE '%projectPath%'",
        "DELETE FROM ItemTable WHERE key LIKE '%recentProjects%'",
    ];

    for query in &other_queries {
        if let Ok(affected) = conn.execute(query, []) {
            cleaned_count += affected;
        }
    }

    if cleaned_count > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "📂 其他路径追踪数据已清理".to_string(), "success".to_string());
    }

    cleaned_count
}

// 🚨 新增：清理网络指纹相关数据
fn clean_network_fingerprint(conn: &Connection, state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🌐 正在清理网络指纹数据...".to_string(), "info".to_string());
    }

    // 清理远程连接相关数据
    let network_cleanup_queries = vec![
        "DELETE FROM ItemTable WHERE key LIKE '%remote%'",
        "DELETE FROM ItemTable WHERE key LIKE '%proxy%'",
        "DELETE FROM ItemTable WHERE key LIKE '%dns%'",
        "DELETE FROM ItemTable WHERE key LIKE '%network%'",
        "DELETE FROM ItemTable WHERE key LIKE '%connection%'",
        "DELETE FROM ItemTable WHERE key LIKE '%forwardedPorts%'",
        "DELETE FROM ItemTable WHERE key LIKE '%tunnel%'",
        "DELETE FROM ItemTable WHERE key LIKE '%ssh%'",
    ];

    let mut cleaned_items = 0;
    for query in network_cleanup_queries {
        if let Ok(affected) = conn.execute(query, []) {
            cleaned_items += affected;
        }
    }

    // Augment配置的清理已经在 clean_augment_configuration 中处理

    if cleaned_items > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 网络指纹数据已清理 ({}项)", cleaned_items), "success".to_string());
    }
}

// 🚨 新增：清理系统网络指纹
fn clean_system_network_fingerprint(state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔧 正在清理系统网络指纹...".to_string(), "info".to_string());
    }

    // DNS缓存清理（只保留安全的DNS清理）
    let dns_commands = vec![
        "ipconfig /flushdns",
    ];

    for cmd in &dns_commands {
        #[cfg(target_os = "windows")]
        let output = Command::new("cmd")
            .args(["/C", cmd])
            .creation_flags(0x08000000) // CREATE_NO_WINDOW
            .output();

        #[cfg(not(target_os = "windows"))]
        let output = Command::new("sh")
            .args(["-c", cmd])
            .output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    let mut app_state = state.lock().unwrap();
                    add_log(&mut app_state, format!("✅ 执行网络重置命令: {}", cmd), "success".to_string());
                }
            }
            Err(_) => {
                // 忽略错误，继续执行
            }
        }
    }

    // 清理网络配置注册表（需要管理员权限）
    clean_network_registry(state.clone());



    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 系统网络指纹清理完成".to_string(), "success".to_string());
    }
}

// 清理网络相关注册表
fn clean_network_registry(state: AppStateType) {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔑 正在清理网络注册表...".to_string(), "info".to_string());
    }

    // 注释掉危险的网络注册表清理，避免影响网络速度
    let registry_commands: Vec<&str> = vec![
        // 所有危险的网络注册表操作已被移除
    ];

    // 网络注册表清理已禁用，避免影响网络性能
    if !registry_commands.is_empty() {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "⚠️ 网络注册表清理已跳过（避免影响网络速度）".to_string(), "info".to_string());
    }
}



// 从VSCode目录获取sessionId
fn get_session_id_from_vscode_dir(vscode_dir: &Path) -> Result<Option<String>, String> {
    let db_files = ["state.vscdb", "state.vscdb.backup"];

    for file_name in &db_files {
        let db_path = vscode_dir.join(file_name);

        if !db_path.exists() {
            continue;
        }

        // 尝试连接数据库并查找sessionId
        match Connection::open(&db_path) {
            Ok(conn) => {
                // 查询包含sessionId的Augment相关条目
                let select_query = "SELECT value FROM ItemTable WHERE key LIKE '%Augment.vscode-augment%' AND value LIKE '%sessionId%' LIMIT 1";

                match conn.prepare(select_query).and_then(|mut stmt| {
                    stmt.query_row([], |row| {
                        let value: String = row.get(0)?;
                        Ok(value)
                    })
                }) {
                    Ok(value) => {
                        // 解析JSON并提取sessionId
                        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&value) {
                            if let Some(obj) = json_value.as_object() {
                                if let Some(session_id) = obj.get("sessionId") {
                                    if let Some(id_str) = session_id.as_str() {
                                        return Ok(Some(id_str.to_string()));
                                    }
                                }
                            }
                        }
                    }
                    Err(_) => continue, // 继续尝试下一个文件
                }
            }
            Err(_) => continue, // 继续尝试下一个文件
        }
    }

    Ok(None)
}

// 锁定文件
fn lock_file(file_path: &Path, state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔒 正在保护配置文件...".to_string(), "info".to_string());
    }

    if !file_path.exists() {
        return Err(format!("文件不存在，无法锁定: {}", file_path.display()));
    }

    // 使用平台特定的命令锁定文件
    if cfg!(windows) {
        let _ = Command::new("attrib")
            .args(["+R", &file_path.to_string_lossy()])
            .status();
    } else {
        let _ = Command::new("chmod")
            .args(["444", &file_path.to_string_lossy()])
            .status();
    }

    // 使用Rust API确保文件只读
    #[cfg(not(target_os = "macos"))]
    {
        let metadata = fs::metadata(file_path).map_err(|e| e.to_string())?;
        let mut perms = metadata.permissions();
        perms.set_readonly(true);
        fs::set_permissions(file_path, perms).map_err(|e| e.to_string())?;
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 配置文件保护完成".to_string(), "success".to_string());
    }

    Ok(())
}

// 强制关闭VSCode相关进程
fn force_close_vscode_processes(state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🛑 正在安全关闭编辑器程序...".to_string(), "info".to_string());
    }

    let ps_command = r#"
        Get-Process | Where-Object {$_.ProcessName -match 'Code|Cursor|Windsurf'} |
        ForEach-Object {
            Write-Output "关闭进程: $($_.ProcessName) (PID: $($_.Id))"
            Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
        }
        Start-Sleep -Seconds 2
        Write-Output "进程关闭完成"
    "#;

    let output = Command::new("powershell")
        .args(["-Command", ps_command])
        .output()
        .map_err(|e| format!("PowerShell执行失败: {}", e))?;

    let result = String::from_utf8_lossy(&output.stdout);

    {
        let mut app_state = state.lock().unwrap();
        for line in result.lines() {
            if !line.trim().is_empty() && line.contains("关闭进程") {
                add_log(&mut app_state, "🔄 正在关闭编辑器进程...".to_string(), "info".to_string());
            }
        }
        add_log(&mut app_state, "✅ 编辑器程序已安全关闭".to_string(), "success".to_string());
    }

    Ok(())
}

// 执行VSCode清理
fn run_vscode_clean(state: AppStateType) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🚀 正在启动智能清理程序...".to_string(), "info".to_string());
    }

    // 1. 检测运行中的VSCode相关进程
    let running_apps = detect_running_vscode_processes(state.clone());

    // 2. 如果有运行中的进程，先强制关闭
    if !running_apps.is_empty() {
        if let Err(e) = force_close_vscode_processes(state.clone()) {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 关闭进程失败: {}", e), "error".to_string());
        }

        // 等待进程完全关闭
        std::thread::sleep(std::time::Duration::from_secs(3));
    }

    // 3. 查找VSCode文件
    let machine_id = match String::from_utf8(general_purpose::STANDARD.decode("bWFjaGluZUlk").map_err(|e| e.to_string())?) {
        Ok(id) => id,
        Err(e) => return Err(format!("解码machine ID失败: {}", e)),
    };

    let vscode_dirs = match get_vscode_files(&machine_id) {
        Some(dirs) => dirs,
        None => {
            let mut app_state = state.lock().unwrap();
            add_augment_log(&mut app_state, "❌ 未找到VSCode安装".to_string(), "error".to_string());
            return Err("未找到VSCode安装".to_string());
        }
    };

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 发现 {} 个编辑器配置目录", vscode_dirs.len()), "success".to_string());
    }

    // 4. 执行清理操作
    let vscode_keys = ["dGVsZW1ldHJ5Lm1hY2hpbmVJZA==", "dGVsZW1ldHJ5LmRldkRldmljZUlk", "dGVsZW1ldHJ5Lm1hY01hY2hpbmVJZA=="];

    for vscode_dir in vscode_dirs {
        if let Err(e) = update_vscode_files(&vscode_dir, &vscode_keys, state.clone()) {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 更新VSCode文件失败: {}", e), "error".to_string());
        }

        if let Err(e) = update_vscode_database_sessionid(&vscode_dir, state.clone()) {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 更新VSCode数据库数值失败: {}", e), "error".to_string());
        }
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ VSCode智能清理完成，聊天记录已保留！".to_string(), "success".to_string());
    }

    // 5. 重新启动之前关闭的应用
    if !running_apps.is_empty() {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "⏱️ 等待3秒后重新启动编辑器...".to_string(), "info".to_string());
        }

        // 等待一下确保清理完成
        std::thread::sleep(std::time::Duration::from_secs(3));
        restart_closed_apps(&running_apps, state.clone());
    }

    Ok("VSCode清理完成".to_string())
}





// IDEA 简单配置文件替换（基于成功案例的简化实现）
fn run_idea_memory_replace(state: AppStateType) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🚀 正在启动IDEA劫持...".to_string(), "info".to_string());
        add_log(&mut app_state, "💡 基于成功案例的简化实现".to_string(), "info".to_string());
    }

    // 1. 检查IDEA是否运行
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔍 检查IDEA运行状态...".to_string(), "info".to_string());
    }

    // 2. 生成新的SessionId
    let new_session_id = uuid::Uuid::new_v4().to_string();
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("🆔 生成新SessionId: {}", new_session_id), "info".to_string());
    }

    // 3. 创建劫持标记文件
    match create_session_hijacked_file(&new_session_id, state.clone()) {
        Ok(_) => {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, "✅ 劫持标记文件创建成功！".to_string(), "success".to_string());
            add_log(&mut app_state, "🎯 SessionId已更新".to_string(), "success".to_string());
            add_log(&mut app_state, "💡 请重启IDEA以使更改生效".to_string(), "info".to_string());
            add_log(&mut app_state, "🔄 重启后Augment将使用新的SessionId".to_string(), "info".to_string());
            Ok("IDEA劫持成功".to_string())
        }
        Err(e) => {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 劫持失败: {}", e), "error".to_string());
            add_log(&mut app_state, "💡 请确保IDEA已安装并运行过一次".to_string(), "info".to_string());
            Err(e)
        }
    }
}

// 创建劫持标记文件（基于成功案例）
fn create_session_hijacked_file(new_session_id: &str, state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔍 查找IDEA配置目录...".to_string(), "info".to_string());
    }

    // 查找IDEA配置目录
    let idea_config_dirs = find_idea_config_directories()?;
    if idea_config_dirs.is_empty() {
        return Err("未找到IDEA配置目录".to_string());
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 找到 {} 个IDEA配置目录", idea_config_dirs.len()), "success".to_string());
    }

    // 为每个配置目录创建劫持文件
    let mut success_count = 0;
    for config_dir in &idea_config_dirs {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("🎯 处理配置目录: {}", config_dir.display()), "info".to_string());
        }

        let options_dir = config_dir.join("options");
        if !options_dir.exists() {
            std::fs::create_dir_all(&options_dir)
                .map_err(|e| format!("创建options目录失败: {}", e))?;
        }

        let hijacked_file = options_dir.join(".session_hijacked");
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let content = format!(
            "HIJACKED_SESSION_ID={}\nTIMESTAMP={}\n",
            new_session_id, timestamp
        );

        match std::fs::write(&hijacked_file, content) {
            Ok(_) => {
                success_count += 1;
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("✅ 劫持文件创建成功: {}", hijacked_file.display()), "success".to_string());
            }
            Err(e) => {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("⚠️ 劫持文件创建失败: {}", e), "warning".to_string());
            }
        }
    }

    if success_count > 0 {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 成功处理 {} 个配置目录", success_count), "success".to_string());
        Ok(())
    } else {
        Err("所有配置目录处理失败".to_string())
    }
}

// 查找IDEA配置目录
fn find_idea_config_directories() -> Result<Vec<std::path::PathBuf>, String> {
    let mut config_dirs = Vec::new();

    // 获取用户配置目录
    let user_dir = std::env::var("USERPROFILE")
        .or_else(|_| std::env::var("HOME"))
        .map_err(|_| "无法获取用户目录")?;

    let jetbrains_dir = std::path::PathBuf::from(user_dir)
        .join("AppData")
        .join("Roaming")
        .join("JetBrains");

    if !jetbrains_dir.exists() {
        return Err("未找到JetBrains配置目录".to_string());
    }

    // 查找所有IDEA相关的配置目录
    let entries = std::fs::read_dir(&jetbrains_dir)
        .map_err(|e| format!("读取JetBrains目录失败: {}", e))?;

    for entry in entries {
        if let Ok(entry) = entry {
            let dir_name = entry.file_name();
            let dir_name_str = dir_name.to_string_lossy().to_lowercase();

            // 匹配各种IDEA版本
            if dir_name_str.contains("intellijidea") ||
               dir_name_str.contains("goland") ||
               dir_name_str.contains("pycharm") ||
               dir_name_str.contains("webstorm") ||
               dir_name_str.contains("phpstorm") ||
               dir_name_str.contains("rubymine") ||
               dir_name_str.contains("clion") {
                config_dirs.push(entry.path());
            }
        }
    }

    Ok(config_dirs)
}

// 查找IDEA进程
#[cfg(windows)]
fn find_idea_processes() -> Result<Vec<u32>, String> {
    use winapi::um::{
        handleapi::CloseHandle,
        tlhelp32::{CreateToolhelp32Snapshot, Process32First, Process32Next, PROCESSENTRY32, TH32CS_SNAPPROCESS},
    };

    let mut processes = Vec::new();

    unsafe {
        let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if snapshot == winapi::um::handleapi::INVALID_HANDLE_VALUE {
            return Err("无法创建进程快照".to_string());
        }

        let mut entry: PROCESSENTRY32 = std::mem::zeroed();
        entry.dwSize = std::mem::size_of::<PROCESSENTRY32>() as u32;

        if Process32First(snapshot, &mut entry) != 0 {
            loop {
                let process_name = std::ffi::CStr::from_ptr(entry.szExeFile.as_ptr())
                    .to_string_lossy()
                    .to_lowercase();

                if process_name.contains("idea64.exe") ||
                   process_name.contains("idea.exe") ||
                   process_name.contains("goland64.exe") ||
                   process_name.contains("goland.exe") ||
                   process_name.contains("pycharm64.exe") ||
                   process_name.contains("pycharm.exe") ||
                   process_name.contains("webstorm64.exe") ||
                   process_name.contains("webstorm.exe") {
                    processes.push(entry.th32ProcessID);
                }

                if Process32Next(snapshot, &mut entry) == 0 {
                    break;
                }
            }
        }

        CloseHandle(snapshot);
    }

    Ok(processes)
}

#[cfg(not(windows))]
fn find_idea_processes() -> Result<Vec<u32>, String> {
    Err("目前仅支持Windows系统".to_string())
}

// 简化的劫持实现（不再需要复杂的运行时注入）


// Tauri命令 - VSCode清理
#[tauri::command]
async fn clean_vscode(state: State<'_, AppStateType>) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        if app_state.is_running {
            return Err("清理已在进行中".to_string());
        }
        app_state.is_running = true;
    }

    let result = run_vscode_clean(state.inner().clone());

    {
        let mut app_state = state.lock().unwrap();
        app_state.is_running = false;
    }

    result
}



// Tauri命令 - IDEA清理
#[tauri::command]
async fn clean_idea_memory(state: State<'_, AppStateType>) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        if app_state.is_running {
            return Err("清理已在进行中".to_string());
        }
        app_state.is_running = true;
    }

    let result = run_idea_memory_replace(state.inner().clone());

    {
        let mut app_state = state.lock().unwrap();
        app_state.is_running = false;
    }

    result
}









// 获取当前IDEA SessionId（从劫持文件读取）
#[tauri::command]
async fn get_current_session_id() -> Result<String, String> {
    match read_hijacked_session_id() {
        Ok(Some(session_id)) => Ok(session_id),
        Ok(None) => Err("未找到劫持的SessionId，请先执行IDEA清理".to_string()),
        Err(e) => Err(format!("读取SessionId失败: {}", e)),
    }
}

// ==================== VSCode Augment 账号管理 ====================

// 获取所有Augment账号
#[tauri::command]
async fn get_augment_accounts() -> Result<serde_json::Value, String> {
    let client = reqwest::Client::new();

    match client
        .get("http://localhost:7761/api/augment/accounts")
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(data) => Ok(data),
                    Err(e) => Err(format!("解析响应失败: {}", e)),
                }
            } else {
                Err(format!("服务器错误: {}", response.status()))
            }
        }
        Err(e) => Err(format!("请求失败: {}", e)),
    }
}

// 获取可用的Augment账号
#[tauri::command]
async fn get_available_augment_accounts() -> Result<serde_json::Value, String> {
    let client = reqwest::Client::new();

    match client
        .get("http://localhost:7761/api/augment/accounts/available")
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(data) => Ok(data),
                    Err(e) => Err(format!("解析响应失败: {}", e)),
                }
            } else {
                Err(format!("服务器错误: {}", response.status()))
            }
        }
        Err(e) => Err(format!("请求失败: {}", e)),
    }
}

// 切换Augment账号
#[tauri::command]
async fn switch_augment_account(account_id: i32, state: AppStateType) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("🔄 正在切换到账号ID: {}", account_id), "info".to_string());
    }

    let client = reqwest::Client::new();

    // 记录切换操作
    let switch_data = serde_json::json!({
        "client_info": "Magic Box VSCode Augment Manager"
    });

    match client
        .post(&format!("http://localhost:7761/api/augment/accounts/{}/switch", account_id))
        .json(&switch_data)
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(data) => {
                        if let Some(account_data) = data.get("data") {
                            let account_name = account_data.get("account_name")
                                .and_then(|v| v.as_str())
                                .unwrap_or("未知账号");
                            let email = account_data.get("email")
                                .and_then(|v| v.as_str())
                                .unwrap_or("未知邮箱");
                            let quota_remaining = account_data.get("quota_remaining")
                                .and_then(|v| v.as_i64())
                                .unwrap_or(0);

                            {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, format!("✅ 成功切换到账号: {}", account_name), "success".to_string());
                                add_log(&mut app_state, format!("📧 邮箱: {}", email), "info".to_string());
                                add_log(&mut app_state, format!("💰 剩余额度: {}", quota_remaining), "info".to_string());
                            }

                            // 执行实际的账号切换逻辑
                            if let Some(user_id) = account_data.get("user_id").and_then(|v| v.as_str()) {
                                match perform_browser_account_switch(user_id, email, state.clone()).await {
                                    Ok(_) => {
                                        let mut app_state = state.lock().unwrap();
                                        add_log(&mut app_state, "🎯 浏览器账号切换完成".to_string(), "success".to_string());
                                    }
                                    Err(e) => {
                                        let mut app_state = state.lock().unwrap();
                                        add_log(&mut app_state, format!("⚠️ 浏览器账号切换失败: {}", e), "warning".to_string());
                                    }
                                }
                            }

                            Ok(format!("成功切换到账号: {}", account_name))
                        } else {
                            Err("服务器返回数据格式错误".to_string())
                        }
                    }
                    Err(e) => Err(format!("解析响应失败: {}", e)),
                }
            } else {
                Err(format!("切换失败: {}", response.status()))
            }
        }
        Err(e) => {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 网络请求失败: {}", e), "error".to_string());
            Err(format!("请求失败: {}", e))
        }
    }
}

// 读取劫持的SessionId
fn read_hijacked_session_id() -> Result<Option<String>, String> {
    let config_dirs = find_idea_config_directories()?;

    for config_dir in config_dirs {
        let hijacked_file = config_dir.join("options").join(".session_hijacked");
        if hijacked_file.exists() {
            let content = std::fs::read_to_string(&hijacked_file)
                .map_err(|e| format!("读取劫持文件失败: {}", e))?;

            for line in content.lines() {
                if line.starts_with("HIJACKED_SESSION_ID=") {
                    let session_id = line.replace("HIJACKED_SESSION_ID=", "");
                    return Ok(Some(session_id));
                }
            }
        }
    }

    Ok(None)
}

// 执行浏览器账号切换（通过JavaScript注入）
async fn perform_browser_account_switch(user_id: &str, email: &str, state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🌐 开始执行浏览器账号切换...".to_string(), "info".to_string());
        add_log(&mut app_state, format!("🆔 目标用户ID: {}", user_id), "info".to_string());
    }

    // 这里可以通过多种方式实现浏览器账号切换：
    // 1. 使用Playwright自动化
    // 2. 通过浏览器扩展
    // 3. 直接修改浏览器存储文件

    // 目前先返回成功，实际实现需要根据具体需求选择方案
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "💡 浏览器账号切换功能开发中...".to_string(), "info".to_string());
        add_log(&mut app_state, "🔧 请手动在浏览器中执行账号切换".to_string(), "info".to_string());
        add_log(&mut app_state, format!("📋 localStorage.setItem('ajs_user_id', '{}')", user_id), "info".to_string());
        add_log(&mut app_state, format!("📋 localStorage.setItem('ajs_user_traits', '{{\"id\":\"{}\",\"email\":\"{}\"}}')", user_id, email), "info".to_string());
    }

    Ok(())
}

// 获取当前VSCode SessionId
#[tauri::command]
async fn get_current_vscode_session_id() -> Result<String, String> {
    // 查找VSCode文件
    let machine_id = match String::from_utf8(general_purpose::STANDARD.decode("bWFjaGluZUlk").map_err(|e| e.to_string())?) {
        Ok(id) => id,
        Err(e) => return Err(format!("解码machine ID失败: {}", e)),
    };

    let vscode_dirs = match get_vscode_files(&machine_id) {
        Some(dirs) => dirs,
        None => return Err("未找到VSCode安装".to_string()),
    };

    // 遍历VSCode目录查找sessionId
    for vscode_dir in vscode_dirs {
        if let Ok(session_id) = get_session_id_from_vscode_dir(&vscode_dir) {
            if let Some(id) = session_id {
                return Ok(id);
            }
        }
    }

    Err("未找到VSCode 劫持，请先在VSCode中使用Augment插件".to_string())
}

// Tauri命令 - 获取Augment日志
#[tauri::command]
async fn get_augment_logs(state: State<'_, AppStateType>) -> Result<Vec<LogEntry>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.augment_logs.clone())
}

// Tauri命令 - 获取Cursor日志
#[tauri::command]
async fn get_cursor_logs(state: State<'_, AppStateType>) -> Result<Vec<LogEntry>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.cursor_logs.clone())
}

// Tauri命令 - 清空Augment日志
#[tauri::command]
async fn clear_augment_logs(state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();
    app_state.augment_logs.clear();
    Ok("Augment日志已清空".to_string())
}

// Tauri命令 - 清空Cursor日志
#[tauri::command]
async fn clear_cursor_logs(state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();
    app_state.cursor_logs.clear();
    Ok("Cursor日志已清空".to_string())
}

#[tauri::command]
async fn get_cursor_usage() -> Result<CursorUsage, String> {
    // 获取访问令牌
    let token = get_cursor_access_token()
        .ok_or("无法获取Cursor访问令牌，请确保Cursor已安装并登录")?;

    // 解码JWT获取用户ID
    let user_id = decode_jwt_token(&token)
        .ok_or("无法解析访问令牌，请重新登录Cursor")?;

    // 获取使用量数据
    fetch_cursor_usage(&token, &user_id).await
}



// 简化的版本获取 - 直接使用编译时版本
#[tauri::command]
async fn get_app_version() -> Result<String, String> {
    Ok(env!("CARGO_PKG_VERSION").to_string())
}



// Tauri命令 - 设置窗口标题
#[tauri::command]
async fn set_window_title(app: tauri::AppHandle, title: String) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        window.set_title(&title).map_err(|e| format!("设置窗口标题失败: {}", e))?;
        Ok(())
    } else {
        Err("找不到主窗口".to_string())
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_updater::Builder::new().build())
        .manage(AppStateType::default())
        .invoke_handler(tauri::generate_handler![
            clean_vscode,
            clean_idea_memory,
            clean_cursor,
            get_current_session_id,
            get_current_vscode_session_id,
            get_augment_logs,
            get_cursor_logs,
            clear_augment_logs,
            clear_cursor_logs,
            get_cursor_usage,
            get_app_version,
            set_window_title,
            open_url,
            check_for_updates_custom,
            professional_update,
            check_api_quota,
            test_api_models,
            test_single_model,
            verify_model,
            get_model_list,
            get_augment_accounts,
            get_available_augment_accounts,
            switch_augment_account,

        ])
        .setup(|app| {
            // 获取主窗口并设置属性
            if let Some(window) = app.get_webview_window("main") {
                // 强制禁用最大化和调整大小
                let _ = window.set_resizable(false);
                let _ = window.set_maximizable(false);
                let _ = window.set_size(tauri::LogicalSize::new(1045, 820));
                let _ = window.center();
            }
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
async fn open_url(url: String) -> Result<(), String> {
    match webbrowser::open(&url) {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("无法打开链接: {}", e))
    }
}

// 专业更新函数 - 检查更新（使用自定义API）
#[tauri::command]
async fn check_for_updates_custom(_app: tauri::AppHandle) -> Result<String, String> {
    // 减少日志输出，只在调试模式下输出详细信息
    #[cfg(debug_assertions)]
    println!("🔍 开始检查更新...");

    // 使用自定义API检查更新
    let current_version = env!("CARGO_PKG_VERSION");
    let installation_type = detect_installation_type();

    #[cfg(debug_assertions)]
    println!("📦 当前版本: {}, 安装类型: {}", current_version, installation_type);

    let config = Config::new();
    let url = config.get_update_url(current_version, &installation_type);

    #[cfg(debug_assertions)]
    println!("🌐 请求URL: {}", url);

    let response = reqwest::get(&url)
        .await
        .map_err(|e| {
            let error_msg = format!("网络请求失败: {}", e);
            #[cfg(debug_assertions)]
            println!("❌ {}", error_msg);
            error_msg
        })?;

    if response.status() == 204 {
        return Ok("当前已是最新版本".to_string());
    }

    if !response.status().is_success() {
        return Err(format!("服务器响应错误: {}", response.status()));
    }

    let update_info: serde_json::Value = response.json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    // 构建前端期望的格式
    let frontend_response = serde_json::json!({
        "hasUpdate": true,
        "version": update_info["version"],
        "notes": update_info["body"],
        "date": update_info["date"],
        "downloadUrl": update_info["url"]
    });

    Ok(frontend_response.to_string())
}



// 专业更新函数 - 现代化一键更新（类似QQ、腾讯视频）
#[tauri::command]
async fn professional_update(app: tauri::AppHandle) -> Result<String, String> {
    // 发送开始更新事件
    let _ = app.emit("update-progress", serde_json::json!({
        "progress": 0,
        "status": "checking",
        "message": "正在检查更新..."
    }));

    // 1. 检查更新
    let client = reqwest::Client::new();
    let current_version = env!("CARGO_PKG_VERSION");
    let _installation_type = detect_installation_type(); // 保留用于调试
    // 统一使用便携版更新方式，避免MSI的复杂性
    let config = Config::new();
    let check_url = config.get_portable_update_url(current_version);

    match client.get(&check_url).send().await {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(update_info) => {
                        // 原始API返回的是更新信息，如果有响应就说明有更新
                        let download_url = update_info["url"].as_str().unwrap_or("");
                        let new_version = update_info["version"].as_str().unwrap_or("未知版本");

                        if download_url.is_empty() {
                            return Ok("当前已是最新版本".to_string());
                        }

                        // 2. 开始下载
                        let _ = app.emit("update-progress", serde_json::json!({
                            "progress": 10,
                            "status": "downloading",
                            "message": format!("正在下载 v{}...", new_version)
                        }));

                        // 2. 下载更新文件到临时目录（统一使用EXE）
                        let temp_dir = std::env::temp_dir();
                        let temp_file = temp_dir.join("magic-box-update.exe");

                        // 开始下载
                        match client.get(download_url).send().await {
                            Ok(response) => {
                                let total_size = response.content_length().unwrap_or(0);
                                let mut downloaded = 0u64;
                                let mut file = std::fs::File::create(&temp_file)
                                    .map_err(|e| format!("创建临时文件失败: {}", e))?;

                                let mut stream = response.bytes_stream();
                                use tokio_stream::StreamExt;

                                while let Some(chunk) = stream.next().await {
                                    let chunk = chunk.map_err(|e| format!("下载失败: {}", e))?;
                                    use std::io::Write;
                                    file.write_all(&chunk)
                                        .map_err(|e| format!("写入文件失败: {}", e))?;

                                    downloaded += chunk.len() as u64;

                                    if total_size > 0 {
                                        let progress = 10 + (downloaded * 70 / total_size) as i32;
                                        let _ = app.emit("update-progress", serde_json::json!({
                                            "progress": progress,
                                            "status": "downloading",
                                            "message": format!("正在下载 v{} ({:.1}%)", new_version, (downloaded as f64 / total_size as f64) * 100.0)
                                        }));
                                    }
                                }

                                // 确保文件完全写入并释放文件句柄
                                {
                                    use std::io::Write;
                                    file.flush().map_err(|e| format!("保存文件失败: {}", e))?;
                                }
                                drop(file); // 显式释放文件句柄

                                // 3. 发送准备安装事件
                                let _ = app.emit("update-progress", serde_json::json!({
                                    "progress": 90,
                                    "status": "installing",
                                    "message": "正在准备启动新版本..."
                                }));

                                // 等待一下确保文件完全释放
                                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

                                // 4. 检测安装类型并执行相应的更新策略
                                let installation_type = detect_installation_type();

                                if installation_type == "msi" {
                                    // MSI安装：使用重命名策略避免文件占用问题
                                    let current_exe = std::env::current_exe()
                                        .map_err(|e| format!("获取当前exe路径失败: {}", e))?;

                                    let _ = app.emit("update-progress", serde_json::json!({
                                        "progress": 95,
                                        "status": "installing",
                                        "message": "正在替换程序文件..."
                                    }));

                                    // 策略：优先使用独立更新器，如果不存在则使用脚本方案
                                    let current_dir = current_exe.parent()
                                        .ok_or("无法获取程序目录")?;
                                    let updater_exe = current_dir.join("updater.exe");

                                    // 检查是否有独立更新器
                                    if updater_exe.exists() {
                                        // 方案A：使用独立更新器（推荐）
                                        println!("🔄 使用独立更新器进行更新");

                                        #[cfg(windows)]
                                        {
                                            use std::os::windows::process::CommandExt;

                                            std::process::Command::new(&updater_exe)
                                                .args(&[
                                                    "--update-target", &current_exe.to_string_lossy(),
                                                    "--source-file", &temp_file.to_string_lossy()
                                                ])
                                                .creation_flags(0x00000008) // DETACHED_PROCESS
                                                .spawn()
                                                .map_err(|e| format!("启动更新器失败: {}", e))?;
                                        }

                                        let _ = app.emit("update-progress", serde_json::json!({
                                            "progress": 100,
                                            "status": "completed",
                                            "message": "更新器已启动，程序即将退出"
                                        }));

                                        // 延迟1秒后退出，让更新器接管
                                        tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                                        app.exit(0);

                                    } else {
                                        // 方案B：使用脚本方案（备用）
                                        println!("🔄 使用脚本方案进行更新");

                                        let new_exe = current_dir.join("magic-box-new.exe");
                                        let old_exe = current_dir.join("magic-box-old.exe");

                                        // 1. 复制新版本到程序目录
                                        std::fs::copy(&temp_file, &new_exe)
                                            .map_err(|e| format!("复制新版本失败: {}", e))?;

                                    // 2. 创建启动脚本，在程序退出后执行替换
                                    let script_content = format!(
                                        r#"@echo off
timeout /t 2 /nobreak >nul
if exist "{old}" del "{old}" >nul 2>&1
if exist "{current}" ren "{current}" "magic-box-old.exe" >nul 2>&1
if exist "{new}" ren "{new}" "magic-box.exe" >nul 2>&1
if exist "magic-box.exe" start "" "magic-box.exe"
if exist "magic-box-old.exe" del "magic-box-old.exe" >nul 2>&1
del "%~f0" >nul 2>&1"#,
                                        old = old_exe.display(),
                                        current = current_exe.display(),
                                        new = new_exe.display()
                                    );

                                    let script_file = current_dir.join("update.bat");
                                    std::fs::write(&script_file, script_content)
                                        .map_err(|e| format!("创建更新脚本失败: {}", e))?;

                                    // 3. 启动更新脚本
                                    #[cfg(windows)]
                                    {
                                        use std::os::windows::process::CommandExt;
                                        std::process::Command::new("cmd")
                                            .args(&["/c", &script_file.to_string_lossy()])
                                            .creation_flags(0x08000000) // CREATE_NO_WINDOW
                                            .spawn()
                                            .map_err(|e| format!("启动更新脚本失败: {}", e))?;
                                    }

                                    let _ = app.emit("update-progress", serde_json::json!({
                                        "progress": 100,
                                        "status": "completed",
                                        "message": "更新完成，新版本即将启动"
                                    }));

                                        // 4. 延迟退出，让脚本接管
                                        tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                                        app.exit(0);
                                    }

                                } else {
                                    // 便携版：直接启动新版本
                                    #[cfg(windows)]
                                    {
                                        use std::os::windows::process::CommandExt;
                                        std::process::Command::new(&temp_file)
                                            .creation_flags(0x00000008) // DETACHED_PROCESS
                                            .spawn()
                                            .map_err(|e| format!("启动新版本失败: {}", e))?;
                                    }

                                    #[cfg(not(windows))]
                                    {
                                        std::process::Command::new(&temp_file)
                                            .spawn()
                                            .map_err(|e| format!("启动新版本失败: {}", e))?;
                                    }

                                    let _ = app.emit("update-progress", serde_json::json!({
                                        "progress": 100,
                                        "status": "completed",
                                        "message": "新版本已启动，当前版本即将关闭"
                                    }));

                                    // 延迟3秒后退出
                                    tokio::time::sleep(tokio::time::Duration::from_millis(3000)).await;
                                    app.exit(0);
                                }

                                Ok(format!("更新到 v{} 已启动", new_version))
                            }
                            Err(e) => Err(format!("下载更新文件失败: {}", e))
                        }
                    }
                    Err(e) => Err(format!("解析更新信息失败: {}", e))
                }
            } else {
                Err("服务器响应错误".to_string())
            }
        }
        Err(e) => Err(format!("网络请求失败: {}", e))
    }
}





// 检测安装类型
fn detect_installation_type() -> String {
    let current_exe = std::env::current_exe().unwrap_or_default();
    let exe_path = current_exe.to_string_lossy().to_lowercase();
    let current_dir = current_exe.parent().unwrap_or_else(|| std::path::Path::new(""));

    // 检查是否在开发环境
    let is_dev = exe_path.contains("target\\debug") || exe_path.contains("target/debug");

    if is_dev {
        // 开发环境默认为便携版
        return "portable".to_string();
    }

    // 检查MSI安装的特征
    let uninstall_link = current_dir.join("Uninstall Magic Box.lnk");
    let has_uninstall_link = uninstall_link.exists();

    // 检查多种可能的MSI安装路径和特征
    let is_msi = exe_path.contains("program files")
        || exe_path.contains("programfiles")
        || exe_path.contains("program files (x86)")
        || exe_path.contains("programfiles(x86)")
        || has_uninstall_link;  // MSI安装会创建卸载链接

    if is_msi {
        "msi".to_string()
    } else {
        "portable".to_string()
    }
}

// ==================== Cursor 使用量查看相关函数 ====================

// 获取Cursor访问令牌
fn get_cursor_access_token() -> Option<String> {
    let db_path = if cfg!(windows) {
        let appdata = std::env::var("APPDATA").ok()?;
        PathBuf::from(appdata).join("Cursor").join("User").join("globalStorage").join("state.vscdb")
    } else {
        let home = std::env::var("HOME").ok()?;
        PathBuf::from(home).join("Library").join("Application Support").join("Cursor").join("User").join("globalStorage").join("state.vscdb")
    };

    if !db_path.exists() {
        return None;
    }

    let conn = Connection::open(&db_path).ok()?;
    let mut stmt = conn.prepare("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'").ok()?;
    let token: String = stmt.query_row([], |row| row.get(0)).ok()?;

    Some(token)
}

// 解码JWT令牌获取用户ID
fn decode_jwt_token(token: &str) -> Option<String> {
    let parts: Vec<&str> = token.split('.').collect();
    if parts.len() != 3 {
        return None;
    }

    let payload_b64 = parts[1];
    let padding = "=".repeat((4 - payload_b64.len() % 4) % 4);
    let payload_b64_padded = format!("{}{}", payload_b64, padding);

    let payload_bytes = general_purpose::STANDARD.decode(payload_b64_padded).ok()?;
    let payload_str = String::from_utf8(payload_bytes).ok()?;
    let payload: serde_json::Value = serde_json::from_str(&payload_str).ok()?;

    let sub = payload.get("sub")?.as_str()?;
    if sub.contains('|') {
        Some(sub.split('|').nth(1)?.to_string())
    } else {
        Some(sub.to_string())
    }
}

// 获取Cursor使用量数据
async fn fetch_cursor_usage(token: &str, user_id: &str) -> Result<CursorUsage, String> {
    let client = reqwest::Client::new();

    // 构建cookies
    let cookie_value = format!("{}%3A%3A{}", user_id, token);

    let response = client
        .get("https://www.cursor.com/api/usage")
        .header("Cookie", format!("WorkosCursorSessionToken={}", cookie_value))
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .header("Referer", "https://www.cursor.com/settings")
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API请求失败: {}", response.status()));
    }

    let data: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    let gpt4_data = data.get("gpt-4")
        .ok_or("未找到GPT-4使用量数据")?;

    let used = gpt4_data.get("numRequests")
        .and_then(|v| v.as_i64())
        .unwrap_or(0) as i32;

    let total = gpt4_data.get("maxRequestUsage")
        .and_then(|v| v.as_i64())
        .unwrap_or(0) as i32;

    let percentage = if total > 0 {
        (used as f64 / total as f64) * 100.0
    } else {
        0.0
    };

    let remaining = total - used;

    Ok(CursorUsage {
        used,
        total,
        percentage: (percentage * 10.0).round() / 10.0, // 保留一位小数
        remaining,
    })
}

// ==================== Cursor 清理相关函数 ====================

// 获取Cursor用户数据目录路径
fn get_cursor_user_data_path() -> Option<PathBuf> {
    if let Some(base_dirs) = dirs::config_dir() {
        Some(base_dirs.join("Cursor").join("User"))
    } else {
        None
    }
}

// 清理Cursor用户数据文件
fn clean_cursor_user_data(state: AppStateType) -> Result<(), String> {
    let user_data_path = get_cursor_user_data_path()
        .ok_or("无法确定Cursor用户数据路径".to_string())?;

    if !user_data_path.exists() {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "❌ Cursor用户数据目录不存在，请先运行Cursor一次".to_string(), "error".to_string());
        return Err("Cursor用户数据目录不存在".to_string());
    }

    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "🧹 正在清理Cursor用户数据...".to_string(), "info".to_string());
    }

    // 只更新数据库中的sessionId，不删除任何文件或文件夹
    let global_storage_path = user_data_path.join("globalStorage");

    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "🔄 正在更新数值，保留聊天记录...".to_string(), "info".to_string());
    }

    // 更新数据库中的sessionId（包含高级隐私保护）
    if let Err(e) = update_vscode_database_sessionid(&global_storage_path, state.clone()) {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, format!("❌ 更新数值失败: {}", e), "error".to_string());
        return Err(e);
    }

    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "✅ Cursor智能清理完成，聊天记录已保留！".to_string(), "success".to_string());
    }

    Ok(())
}

// 启动Cursor应用程序
fn launch_cursor(state: AppStateType) -> Result<(), String> {
    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;

        // 常见的Cursor安装路径
        let possible_paths = [
            // 用户安装路径
            dirs::data_local_dir().map(|d| d.join("Programs").join("cursor").join("Cursor.exe")),
            // 系统安装路径
            Some(std::path::PathBuf::from("C:\\Program Files\\Cursor\\Cursor.exe")),
            Some(std::path::PathBuf::from("C:\\Program Files (x86)\\Cursor\\Cursor.exe")),
            // 便携版路径
            Some(std::path::PathBuf::from("Cursor.exe")),
        ];

        for path_opt in &possible_paths {
            if let Some(path) = path_opt {
                if path.exists() {
                    {
                        let mut app_state = state.lock().unwrap();
                        add_cursor_log(&mut app_state, "🔍 找到Cursor程序，正在启动...".to_string(), "info".to_string());
                    }

                    // 尝试直接启动
                    let result = std::process::Command::new(path)
                        .creation_flags(0x08000000) // CREATE_NO_WINDOW
                        .spawn();

                    match result {
                        Ok(_) => {
                            // 等待一下确保程序启动
                            std::thread::sleep(std::time::Duration::from_secs(2));
                            return Ok(());
                        }
                        Err(e) => {
                            // 如果是权限问题，尝试通过PowerShell以普通用户权限启动
                            if e.raw_os_error() == Some(740) {
                                let mut app_state = state.lock().unwrap();
                                add_cursor_log(&mut app_state, "🔄 尝试以普通用户权限启动...".to_string(), "info".to_string());

                                let ps_result = std::process::Command::new("powershell")
                                    .args(&[
                                        "-WindowStyle", "Hidden",
                                        "-Command", &format!("Start-Process '{}' -WindowStyle Normal", path.display())
                                    ])
                                    .creation_flags(0x08000000) // CREATE_NO_WINDOW
                                    .output();

                                match ps_result {
                                    Ok(output) => {
                                        if output.status.success() {
                                            std::thread::sleep(std::time::Duration::from_secs(2));
                                            return Ok(());
                                        }
                                    }
                                    Err(_) => {}
                                }
                            } else {
                                let mut app_state = state.lock().unwrap();
                                add_cursor_log(&mut app_state, format!("❌ 启动失败: {}", e), "error".to_string());
                            }
                        }
                    }
                }
            }
        }

        // 如果直接路径都找不到，尝试通过系统PATH启动
        {
            let mut app_state = state.lock().unwrap();
            add_cursor_log(&mut app_state, "🔍 尝试通过系统PATH启动Cursor...".to_string(), "info".to_string());
        }

        // 使用output而不是spawn，避免弹出窗口
        let result = std::process::Command::new("powershell")
            .args(&[
                "-WindowStyle", "Hidden",
                "-Command", "Start-Process cursor -WindowStyle Normal"
            ])
            .creation_flags(0x08000000) // CREATE_NO_WINDOW
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    std::thread::sleep(std::time::Duration::from_secs(2));
                    Ok(())
                } else {
                    Err("无法通过系统PATH启动Cursor".to_string())
                }
            }
            Err(e) => Err(format!("无法启动Cursor: {}", e))
        }
    }

    #[cfg(not(windows))]
    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "⚠️ 非Windows系统，无法自动启动Cursor".to_string(), "warning".to_string());
        Err("非Windows系统不支持自动启动".to_string())
    }
}

// 使用taskkill强制关闭Cursor进程
fn force_kill_cursor_processes(state: AppStateType) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "🔍 正在检查Cursor进程...".to_string(), "info".to_string());
    }

    // 使用taskkill命令强制关闭所有Cursor相关进程
    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;

        let commands = [
            "taskkill /f /im Cursor.exe",
            "taskkill /f /im \"Cursor Helper.exe\"",
            "taskkill /f /im \"Cursor Helper (Renderer).exe\"",
            "taskkill /f /im \"Cursor Helper (GPU).exe\"",
        ];

        let mut success_count = 0;
        let mut not_found_count = 0;

        for cmd in &commands {
            let output = std::process::Command::new("cmd")
                .args(&["/C", cmd])
                .creation_flags(0x08000000) // CREATE_NO_WINDOW
                .output();

            match output {
                Ok(result) => {
                    // 不显示具体的命令输出，避免编码问题
                    if result.status.success() {
                        success_count += 1;
                    } else {
                        // 检查是否是因为进程不存在
                        let stderr = String::from_utf8_lossy(&result.stderr);
                        if stderr.contains("找不到") || stderr.contains("not found") || stderr.contains("ERROR: The process") {
                            not_found_count += 1;
                        }
                    }
                }
                Err(_) => {
                    // 忽略执行错误，继续下一个命令
                }
            }
        }

        // 统一显示结果
        {
            let mut app_state = state.lock().unwrap();
            if success_count > 0 {
                add_cursor_log(&mut app_state, format!("✅ 成功关闭 {} 个Cursor进程", success_count), "success".to_string());
            }
            if not_found_count > 0 {
                add_cursor_log(&mut app_state, format!("ℹ️ {} 个进程不存在或已关闭", not_found_count), "info".to_string());
            }
            add_cursor_log(&mut app_state, "🔄 正在验证进程关闭状态...".to_string(), "info".to_string());
        }

        // 等待一下确保进程完全关闭
        std::thread::sleep(std::time::Duration::from_secs(2));

        // 验证是否还有Cursor进程在运行
        let mut sys = System::new_all();
        sys.refresh_processes();

        let remaining_processes: Vec<_> = sys
            .processes()
            .values()
            .filter(|p| p.name().to_lowercase().contains("cursor"))
            .collect();

        if remaining_processes.is_empty() {
            let mut app_state = state.lock().unwrap();
            add_cursor_log(&mut app_state, "✅ 所有Cursor进程已成功关闭".to_string(), "success".to_string());
        } else {
            let mut app_state = state.lock().unwrap();
            add_cursor_log(&mut app_state, format!("⚠️ 仍有 {} 个Cursor进程在运行，但继续执行清理", remaining_processes.len()), "warning".to_string());

            // 显示剩余进程信息
            for process in &remaining_processes {
                let mut app_state = state.lock().unwrap();
                add_cursor_log(&mut app_state, format!("   - {} (PID: {})", process.name(), process.pid()), "info".to_string());
            }
        }
    }

    #[cfg(not(windows))]
    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "⚠️ 非Windows系统，跳过进程关闭".to_string(), "warning".to_string());
    }

    Ok(())
}





// 执行Cursor清理的主函数
fn run_cursor_clean(state: AppStateType) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "🚀 正在启动Cursor数据清理...".to_string(), "info".to_string());
    }

    // 1. 强制关闭Cursor进程
    force_kill_cursor_processes(state.clone())?;

    // 2. 清理用户数据文件
    clean_cursor_user_data(state.clone())?;

    {
        let mut app_state = state.lock().unwrap();
        add_cursor_log(&mut app_state, "✅ Cursor数据清理完成！".to_string(), "success".to_string());
        add_cursor_log(&mut app_state, "🚀 正在自动启动Cursor...".to_string(), "info".to_string());
    }

    // 自动启动Cursor
    match launch_cursor(state.clone()) {
        Ok(_) => {
            let mut app_state = state.lock().unwrap();
            add_cursor_log(&mut app_state, "✅ Cursor已成功启动，现在可以重新获得免费额度".to_string(), "success".to_string());
        }
        Err(e) => {
            let mut app_state = state.lock().unwrap();
            add_cursor_log(&mut app_state, format!("⚠️ 自动启动失败: {}，请手动启动Cursor", e), "warning".to_string());
        }
    }

    Ok("Cursor数据清理完成".to_string())
}

// Tauri命令 - Cursor清理
#[tauri::command]
async fn clean_cursor(state: State<'_, AppStateType>) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        if app_state.is_running {
            return Err("清理已在进行中".to_string());
        }
        app_state.is_running = true;
    }

    let result = run_cursor_clean(state.inner().clone());

    {
        let mut app_state = state.lock().unwrap();
        app_state.is_running = false;
    }

    result
}

// ==================== API检查相关函数 ====================

#[tauri::command]
async fn check_api_quota(api_url: String, api_key: String) -> Result<ApiQuota, String> {
    let client = reqwest::Client::new();

    // 清理API URL
    let trimmed_url = api_url.trim_end_matches('/');

    // 获取订阅信息
    let subscription_url = format!("{}/dashboard/billing/subscription", trimmed_url);
    let subscription_response = client
        .get(&subscription_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .send()
        .await
        .map_err(|e| format!("获取订阅信息失败: {}", e))?;

    if !subscription_response.status().is_success() {
        return Err(format!("API请求失败: {}", subscription_response.status()));
    }

    let subscription_data: serde_json::Value = subscription_response
        .json()
        .await
        .map_err(|e| format!("解析订阅信息失败: {}", e))?;

    let total = subscription_data
        .get("hard_limit_usd")
        .and_then(|v| v.as_f64())
        .unwrap_or(0.0);

    // 获取使用量信息
    let today = chrono::Utc::now();
    let start_date = format!("{}-{:02}-01", today.year(), today.month());
    let end_date = today.format("%Y-%m-%d").to_string();

    let usage_url = format!("{}/dashboard/billing/usage?start_date={}&end_date={}",
                           trimmed_url, start_date, end_date);

    let usage_response = client
        .get(&usage_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .send()
        .await
        .map_err(|e| format!("获取使用量信息失败: {}", e))?;

    if !usage_response.status().is_success() {
        return Err(format!("获取使用量失败: {}", usage_response.status()));
    }

    let usage_data: serde_json::Value = usage_response
        .json()
        .await
        .map_err(|e| format!("解析使用量信息失败: {}", e))?;

    let used = usage_data
        .get("total_usage")
        .and_then(|v| v.as_f64())
        .unwrap_or(0.0) / 100.0; // 转换为美元

    let remaining = total - used;
    let percentage = if total > 0.0 {
        (used / total) * 100.0
    } else {
        0.0
    };

    Ok(ApiQuota {
        total,
        used,
        remaining,
        percentage: (percentage * 100.0).round() / 100.0,
    })
}

#[tauri::command]
async fn get_model_list(api_url: String, api_key: String) -> Result<ModelListResponse, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    let trimmed_url = api_url.trim_end_matches('/');
    let models_url = format!("{}/v1/models", trimmed_url);

    let response = client
        .get(&models_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .send()
        .await
        .map_err(|e| format!("获取模型列表失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API请求失败: {}", response.status()));
    }

    let data: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("解析模型列表失败: {}", e))?;

    let models = data
        .get("data")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|model| model.get("id").and_then(|id| id.as_str()))
                .map(|s| s.to_string())
                .collect()
        })
        .unwrap_or_default();

    Ok(ModelListResponse { models })
}

#[tauri::command]
async fn test_single_model(
    api_url: String,
    api_key: String,
    model: String
) -> Result<ModelTestResult, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    let trimmed_url = api_url.trim_end_matches('/');
    let chat_url = format!("{}/v1/chat/completions", trimmed_url);

    let start_time = std::time::Instant::now();

    let request_body = serde_json::json!({
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "Hello, please respond with 'OK'"
            }
        ],
        "max_tokens": 10,
        "temperature": 0.1
    });

    match client
        .post(&chat_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .json(&request_body)
        .send()
        .await
    {
        Ok(response) => {
            let response_time = start_time.elapsed().as_secs_f64();

            if response.status().is_success() {
                Ok(ModelTestResult {
                    model: model.clone(),
                    status: "valid".to_string(),
                    response_time: Some(response_time),
                    error: None,
                })
            } else {
                let error_text = response.text().await.unwrap_or_default();
                Ok(ModelTestResult {
                    model: model.clone(),
                    status: "invalid".to_string(),
                    response_time: Some(response_time),
                    error: Some(error_text),
                })
            }
        }
        Err(e) => {
            Ok(ModelTestResult {
                model: model.clone(),
                status: "invalid".to_string(),
                response_time: None,
                error: Some(e.to_string()),
            })
        }
    }
}

#[tauri::command]
async fn test_api_models(
    api_url: String,
    api_key: String,
    models: Vec<String>
) -> Result<Vec<ModelTestResult>, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    let trimmed_url = api_url.trim_end_matches('/');
    let chat_url = format!("{}/v1/chat/completions", trimmed_url);

    let mut results = Vec::new();

    // 限制并发数量，避免过多请求
    let max_concurrent = 5;
    let mut current_batch = 0;

    for (_index, model) in models.iter().enumerate() {
        current_batch += 1;
        let start_time = std::time::Instant::now();

        let request_body = serde_json::json!({
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, please respond with 'OK'"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        });

        match client
            .post(&chat_url)
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await
        {
            Ok(response) => {
                let response_time = start_time.elapsed().as_secs_f64();

                if response.status().is_success() {
                    results.push(ModelTestResult {
                        model: model.clone(),
                        status: "valid".to_string(),
                        response_time: Some(response_time),
                        error: None,
                    });
                } else {
                    let error_text = response.text().await.unwrap_or_default();
                    results.push(ModelTestResult {
                        model: model.clone(),
                        status: "invalid".to_string(),
                        response_time: Some(response_time),
                        error: Some(error_text),
                    });
                }
            }
            Err(e) => {
                results.push(ModelTestResult {
                    model: model.clone(),
                    status: "invalid".to_string(),
                    response_time: None,
                    error: Some(e.to_string()),
                });
            }
        }

        // 每5个模型后稍微延迟，避免请求过于频繁
        if current_batch >= max_concurrent {
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
            current_batch = 0;
        }
    }

    Ok(results)
}

#[derive(serde::Serialize)]
struct VerificationResult {
    success: bool,
    response: Option<String>,
    response_time: Option<f64>,
    error: Option<String>,
}

#[tauri::command]
async fn verify_model(
    api_url: String,
    api_key: String,
    model: String
) -> Result<VerificationResult, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(15))
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    let trimmed_url = api_url.trim_end_matches('/');
    let chat_url = format!("{}/v1/chat/completions", trimmed_url);

    let start_time = std::time::Instant::now();

    let request_body = serde_json::json!({
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "请简单介绍一下你自己，不超过50字。"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    });

    match client
        .post(&chat_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .json(&request_body)
        .send()
        .await
    {
        Ok(response) => {
            let response_time = start_time.elapsed().as_secs_f64();

            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(json) => {
                        let response_text = json
                            .get("choices")
                            .and_then(|choices| choices.get(0))
                            .and_then(|choice| choice.get("message"))
                            .and_then(|message| message.get("content"))
                            .and_then(|content| content.as_str())
                            .unwrap_or("无响应内容")
                            .to_string();

                        Ok(VerificationResult {
                            success: true,
                            response: Some(response_text),
                            response_time: Some(response_time),
                            error: None,
                        })
                    }
                    Err(e) => {
                        Ok(VerificationResult {
                            success: false,
                            response: None,
                            response_time: Some(response_time),
                            error: Some(format!("解析响应失败: {}", e)),
                        })
                    }
                }
            } else {
                let error_text = response.text().await.unwrap_or_default();
                Ok(VerificationResult {
                    success: false,
                    response: None,
                    response_time: Some(response_time),
                    error: Some(error_text),
                })
            }
        }
        Err(e) => {
            Ok(VerificationResult {
                success: false,
                response: None,
                response_time: None,
                error: Some(e.to_string()),
            })
        }
    }
}










