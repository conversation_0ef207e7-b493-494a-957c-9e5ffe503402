<?xml version="1.0" encoding="windows-1252"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
        Id="*"
        Name="Magic Box"
        UpgradeCode="{{61DAA78B-AC5B-4B62-9CDA-36C4D2D4F2E5}}"
        Manufacturer="Magic Box Team"
        Language="2052"
        Codepage="936"
        Version="{{version}}"
    >
        <Package
            Id="*"
            Keywords="Installer"
            Description="Magic Box 安装程序"
            Manufacturer="Magic Box Team"
            InstallerVersion="450"
            Languages="2052"
            Compressed="yes"
            SummaryCodepage="936"
        />

        <!-- 自定义安装界面 -->
        <WixVariable Id="WixUILicenseRtf" Value="license.rtf" />
        <WixVariable Id="WixUIBannerBmp" Value="banner.bmp" />
        <WixVariable Id="WixUIDialogBmp" Value="dialog.bmp" />
        
        <!-- 使用自定义UI -->
        <UIRef Id="WixUI_InstallDir" />
        <Property Id="WIXUI_INSTALLDIR" Value="APPLICATIONFOLDER" />

        <!-- 自定义属性 -->
        <Property Id="ARPPRODUCTICON" Value="icon.ico" />
        <Property Id="ARPHELPLINK" Value="https://magic-box.com" />
        <Property Id="ARPURLINFOABOUT" Value="https://magic-box.com" />
        <Property Id="ARPNOMODIFY" Value="yes" Secure="yes" />
        
        <MajorUpgrade
            Schedule="afterInstallInitialize"
            DowngradeErrorMessage="已安装更新版本的 Magic Box。安装程序将退出。"
        />

        <MediaTemplate EmbedCab="yes" />

        <Feature
            Id="MainApplication"
            Title="Magic Box"
            Description="Magic Box 主程序"
            Level="1"
            ConfigurableDirectory="APPLICATIONFOLDER"
            AllowAdvertise="no"
            Display="expand"
            Absent="disallow"
        >
            {{#each binaries as |binary|}}
            <ComponentRef Id="{{binary.id}}" />
            {{/each}}

            {{#each resources as |resource|}}
            <ComponentRef Id="{{resource.id}}" />
            {{/each}}

            <Feature
                Id="Environment"
                Title="PATH 环境变量"
                Description="将 Magic Box 添加到 PATH 环境变量"
                Level="1"
                Absent="allow"
            >
                {{#each binaries as |binary|}}
                {{#if binary.main}}
                <ComponentRef Id="Path" />
                {{/if}}
                {{/each}}
            </Feature>

            <Feature
                Id="Associations"
                Title="文件关联"
                Description="注册文件类型关联"
                Level="1"
                Absent="allow"
            >
                {{#each binaries as |binary|}}
                {{#if binary.main}}
                <ComponentRef Id="Registry" />
                {{/if}}
                {{/each}}
            </Feature>
        </Feature>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[APPLICATIONFOLDER]" After="CostFinalize" />
    </Product>

    <Fragment>
        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="{{program_files_folder}}" Name="PFiles">
                <Directory Id="APPLICATIONFOLDER" Name="{{product_name}}">
                    {{#each binaries as |binary|}}
                    <Component Id="{{binary.id}}" Guid="*">
                        <File
                            Id="{{binary.id}}"
                            Source="{{binary.path}}"
                            KeyPath="yes"
                            Checksum="yes"
                        />
                    </Component>
                    {{/each}}

                    {{#each resources as |resource|}}
                    <Component Id="{{resource.id}}" Guid="*">
                        <File
                            Id="{{resource.id}}"
                            Source="{{resource.path}}"
                            KeyPath="yes"
                            Checksum="yes"
                        />
                    </Component>
                    {{/each}}
                </Directory>
            </Directory>

            <Directory Id="ProgramMenuFolder" Name="Programs">
                <Directory Id="ApplicationProgramsFolder" Name="{{product_name}}" />
            </Directory>

            <Directory Id="DesktopFolder" Name="Desktop" />
        </Directory>
    </Fragment>

    <Fragment>
        <ComponentGroup Id="Binaries">
            {{#each binaries as |binary|}}
            <ComponentRef Id="{{binary.id}}" />
            {{/each}}
        </ComponentGroup>
    </Fragment>
</Wix>
