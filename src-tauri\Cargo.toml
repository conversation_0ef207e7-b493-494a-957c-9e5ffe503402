[package]
name = "magic-box"
version = "1.5.0"
description = "Magic Box"
authors = ["you"]
edition = "2021"
default-run = "magic-box"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "magic_box_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-updater = "2.0.0"
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
base64 = "0.21"
uuid = { version = "1.0", features = ["v4"] }
sha2 = "0.10"
dirs = "5.0"
rusqlite = { version = "0.29", features = ["bundled"] }
webbrowser = "0.8"
self_update = { version = "0.42", features = ["archive-zip", "compression-zip-deflate"] }

tokio-stream = "0.1"
tokio = { version = "1.0", features = ["full"] }

# 自动化相关依赖
regex = "1.0"


# Cursor清理相关依赖
sysinfo = "0.30"

# 环境变量支持
dotenv = "0.15"

# Java集成支持 (暂时保留，但使用新的文件系统方案)
jni = { version = "0.21", features = ["invocation"] }
java-locator = "0.1"
chrono = { version = "0.4", features = ["serde"] }

# Windows API for memory manipulation
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winnt", "processthreadsapi", "memoryapi", "handleapi", "tlhelp32", "psapi"] }

# 定义多个二进制文件
[[bin]]
name = "magic-box"
path = "src/main.rs"

[[bin]]
name = "updater"
path = "src/bin/updater.rs"


