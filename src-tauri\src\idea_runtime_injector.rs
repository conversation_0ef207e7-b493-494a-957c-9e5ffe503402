use std::path::PathBuf;
use std::process::Command;
use std::fs;
use uuid::Uuid;

/// IDEA运行时内存注入器（基于成功的Java实现）
#[derive(Debug, Clone)]
pub struct IdeaRuntimeInjector {
}

impl IdeaRuntimeInjector {
    pub fn new() -> Self {
        Self {}
    }

    /// 执行运行时内存注入
    pub fn inject_runtime_memory(&self, process_ids: &[u32], new_session_id: Option<String>) -> Result<String, String> {
        let session_id = new_session_id.unwrap_or_else(|| Uuid::new_v4().to_string());
        
        println!("🎯 开始IDEA运行时内存注入...");
        println!("📝 新SessionId: {}", session_id);

        // 1. 查找Java可执行文件
        let java_exe = self.find_java_executable()?;
        println!("☕ 找到Java: {}", java_exe.display());

        // 2. 创建注入器JAR文件
        let injector_jar = self.create_injector_jar(&session_id)?;
        println!("📦 创建注入器: {}", injector_jar.display());

        // 3. 对每个IDEA进程执行注入
        let mut success_count = 0;
        for &process_id in process_ids {
            println!("🎯 正在注入进程 PID: {}", process_id);
            
            match self.inject_process(&java_exe, &injector_jar, process_id, &session_id) {
                Ok(_) => {
                    success_count += 1;
                    println!("✅ 进程 {} 注入成功", process_id);
                }
                Err(e) => {
                    println!("⚠️ 进程 {} 注入失败: {}", process_id, e);
                }
            }
        }

        if success_count > 0 {
            Ok(format!("成功注入 {} 个进程", success_count))
        } else {
            Err("所有进程注入失败".to_string())
        }
    }

    /// 查找Java可执行文件
    fn find_java_executable(&self) -> Result<PathBuf, String> {
        // 1. 尝试从JAVA_HOME环境变量
        if let Ok(java_home) = std::env::var("JAVA_HOME") {
            let java_exe = PathBuf::from(java_home).join("bin").join("java.exe");
            if java_exe.exists() {
                return Ok(java_exe);
            }
        }

        // 2. 尝试从PATH环境变量
        if let Ok(output) = Command::new("where").arg("java").output() {
            if output.status.success() {
                let java_path = String::from_utf8_lossy(&output.stdout);
                let java_exe = PathBuf::from(java_path.trim());
                if java_exe.exists() {
                    return Ok(java_exe);
                }
            }
        }

        // 3. 尝试常见的Java安装路径
        let common_paths = vec![
            r"C:\Program Files\Java\jdk-11\bin\java.exe",
            r"C:\Program Files\Java\jdk-17\bin\java.exe",
            r"C:\Program Files\Java\jdk-21\bin\java.exe",
            r"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe",
            r"C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe",
            r"C:\Program Files\OpenJDK\openjdk-11.0.2\bin\java.exe",
        ];

        for path in common_paths {
            let java_exe = PathBuf::from(path);
            if java_exe.exists() {
                return Ok(java_exe);
            }
        }

        Err("未找到Java可执行文件，请确保已安装Java并配置环境变量".to_string())
    }

    /// 创建注入器JAR文件（完整的Java反射实现）
    fn create_injector_jar(&self, session_id: &str) -> Result<PathBuf, String> {
        let temp_dir = std::env::temp_dir();
        let injector_jar = temp_dir.join("idea_runtime_injector.jar");

        // 创建完整的Java注入器代码（基于成功的实现）
        let java_code = format!(r#"
import java.lang.reflect.*;
import java.util.*;
import java.io.*;
import java.net.*;

public class IdeaRuntimeInjector {{
    private static final String TARGET_SESSION_ID = "{}";

    public static void main(String[] args) {{
        if (args.length < 1) {{
            System.err.println("Usage: java IdeaRuntimeInjector <sessionId>");
            System.exit(1);
        }}

        String sessionId = args[0];

        try {{
            System.out.println("🔍 开始运行时内存注入...");
            System.out.println("🆔 新SessionId: " + sessionId);

            // 基于成功Java代码的完整实现
            boolean success = reinitializeHttpClient(sessionId);

            if (success) {{
                System.out.println("✅ 运行时内存注入成功");
                System.exit(0);
            }} else {{
                System.out.println("❌ 运行时内存注入失败");
                System.exit(1);
            }}
        }} catch (Exception e) {{
            System.err.println("❌ 注入过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }}
    }}

    private static boolean reinitializeHttpClient(String sessionId) {{
        try {{
            System.out.println("🔍 搜索目标插件类加载器...");

            // 1. 获取所有类加载器
            ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
            ClassLoader targetClassLoader = null;

            // 2. 尝试查找Augment插件的类加载器
            try {{
                // 模拟PluginManager.getInstance().getPlugins()的查找过程
                System.out.println("🔍 查找Augment插件...");

                // 尝试通过类名查找目标插件
                String[] possibleClasses = {{
                    "com.augmentcode.intellij.api.AugmentAPI",
                    "com.augmentcode.intellij.AugmentPlugin",
                    "com.augmentcode.intellij.core.AugmentService"
                }};

                for (String className : possibleClasses) {{
                    try {{
                        Class<?> clazz = Class.forName(className, false, currentClassLoader);
                        targetClassLoader = clazz.getClassLoader();
                        System.out.println("✅ 找到目标类: " + className);
                        break;
                    }} catch (ClassNotFoundException e) {{
                        // 继续尝试下一个类
                    }}
                }}

                if (targetClassLoader == null) {{
                    System.out.println("⚠️ 未找到目标插件类加载器，使用当前类加载器");
                    targetClassLoader = currentClassLoader;
                }}

            }} catch (Exception e) {{
                System.out.println("⚠️ 无法获取目标插件类加载器: " + e.getMessage());
                targetClassLoader = currentClassLoader;
            }}

            // 3. 设置上下文类加载器
            Thread.currentThread().setContextClassLoader(targetClassLoader);

            try {{
                System.out.println("🔍 查找AugmentAPI服务实例...");

                // 4. 尝试获取AugmentAPI服务实例
                Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);

                // 5. 模拟ApplicationManager.getApplication().getService()
                Object serviceInstance = getServiceInstance(apiImplClass);

                if (serviceInstance == null) {{
                    System.out.println("❌ 无法获取AugmentAPI服务实例");
                    return false;
                }}

                System.out.println("✅ 成功获取AugmentAPI服务实例");

                // 6. 获取httpClient字段
                Field httpClientField = findHttpClientField(serviceInstance.getClass());

                if (httpClientField == null) {{
                    System.out.println("❌ 未找到httpClient字段");
                    return false;
                }}

                httpClientField.setAccessible(true);

                System.out.println("🔧 正在替换HttpClient实例...");

                // 7. 创建新的HttpClient实例
                Object newHttpClient = createNewHttpClient(sessionId, targetClassLoader);

                if (newHttpClient == null) {{
                    System.out.println("❌ 无法创建新的HttpClient实例");
                    return false;
                }}

                // 8. 替换httpClient字段
                httpClientField.set(serviceInstance, newHttpClient);

                System.out.println("✅ HttpClient实例已成功替换");
                System.out.println("🆔 新SessionId: " + sessionId);

                return true;

            }} catch (ClassNotFoundException e) {{
                System.out.println("❌ 未找到AugmentAPI类: " + e.getMessage());
                return false;
            }} catch (Exception e) {{
                System.out.println("❌ 反射操作失败: " + e.getMessage());
                e.printStackTrace();
                return false;
            }} finally {{
                // 恢复原始类加载器
                Thread.currentThread().setContextClassLoader(currentClassLoader);
            }}

        }} catch (Exception e) {{
            System.out.println("❌ 重新初始化HttpClient失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }}
    }}

    private static Object getServiceInstance(Class<?> serviceClass) {{
        try {{
            // 尝试多种方式获取服务实例

            // 方式1: 通过ApplicationManager
            try {{
                Class<?> appManagerClass = Class.forName("com.intellij.openapi.application.ApplicationManager");
                Method getApplicationMethod = appManagerClass.getMethod("getApplication");
                Object application = getApplicationMethod.invoke(null);

                Method getServiceMethod = application.getClass().getMethod("getService", Class.class);
                return getServiceMethod.invoke(application, serviceClass);
            }} catch (Exception e) {{
                System.out.println("⚠️ ApplicationManager方式失败: " + e.getMessage());
            }}

            // 方式2: 尝试单例模式
            try {{
                Method getInstanceMethod = serviceClass.getMethod("getInstance");
                return getInstanceMethod.invoke(null);
            }} catch (Exception e) {{
                System.out.println("⚠️ getInstance方式失败: " + e.getMessage());
            }}

            // 方式3: 尝试构造函数
            try {{
                Constructor<?> constructor = serviceClass.getDeclaredConstructor();
                constructor.setAccessible(true);
                return constructor.newInstance();
            }} catch (Exception e) {{
                System.out.println("⚠️ 构造函数方式失败: " + e.getMessage());
            }}

            return null;
        }} catch (Exception e) {{
            System.out.println("❌ 获取服务实例失败: " + e.getMessage());
            return null;
        }}
    }}

    private static Field findHttpClientField(Class<?> clazz) {{
        try {{
            // 查找httpClient字段
            String[] possibleFieldNames = {{"httpClient", "client", "augmentHttpClient", "apiClient"}};

            for (String fieldName : possibleFieldNames) {{
                try {{
                    Field field = clazz.getDeclaredField(fieldName);
                    System.out.println("✅ 找到字段: " + fieldName);
                    return field;
                }} catch (NoSuchFieldException e) {{
                    // 继续查找
                }}
            }}

            // 如果直接查找失败，遍历所有字段
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {{
                String fieldType = field.getType().getSimpleName().toLowerCase();
                if (fieldType.contains("httpclient") || fieldType.contains("client")) {{
                    System.out.println("✅ 通过类型找到字段: " + field.getName());
                    return field;
                }}
            }}

            return null;
        }} catch (Exception e) {{
            System.out.println("❌ 查找httpClient字段失败: " + e.getMessage());
            return null;
        }}
    }}

    private static Object createNewHttpClient(String sessionId, ClassLoader classLoader) {{
        try {{
            // 尝试创建新的HttpClient实例
            String[] possibleClasses = {{
                "com.augmentcode.intellij.api.AugmentHttpClient",
                "com.augmentcode.intellij.http.HttpClient",
                "com.augmentcode.intellij.core.AugmentClient"
            }};

            for (String className : possibleClasses) {{
                try {{
                    Class<?> httpClientClass = Class.forName(className, true, classLoader);

                    // 尝试使用sessionId参数的构造函数
                    try {{
                        Constructor<?> constructor = httpClientClass.getConstructor(String.class);
                        Object instance = constructor.newInstance(sessionId);
                        System.out.println("✅ 使用构造函数创建HttpClient: " + className);
                        return instance;
                    }} catch (NoSuchMethodException e) {{
                        // 尝试无参构造函数
                        try {{
                            Constructor<?> constructor = httpClientClass.getDeclaredConstructor();
                            constructor.setAccessible(true);
                            Object instance = constructor.newInstance();

                            // 尝试设置sessionId
                            setSessionId(instance, sessionId);

                            System.out.println("✅ 使用无参构造函数创建HttpClient: " + className);
                            return instance;
                        }} catch (Exception e2) {{
                            System.out.println("⚠️ 无法使用无参构造函数: " + e2.getMessage());
                        }}
                    }}
                }} catch (ClassNotFoundException e) {{
                    // 继续尝试下一个类
                }}
            }}

            return null;
        }} catch (Exception e) {{
            System.out.println("❌ 创建HttpClient实例失败: " + e.getMessage());
            return null;
        }}
    }}

    private static void setSessionId(Object httpClient, String sessionId) {{
        try {{
            // 尝试设置sessionId字段
            String[] possibleFieldNames = {{"sessionId", "session", "id", "apiKey"}};

            for (String fieldName : possibleFieldNames) {{
                try {{
                    Field field = httpClient.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(httpClient, sessionId);
                    System.out.println("✅ 设置字段: " + fieldName);
                    return;
                }} catch (NoSuchFieldException e) {{
                    // 继续尝试
                }}
            }}

            // 尝试setter方法
            String[] possibleMethodNames = {{"setSessionId", "setSession", "setId", "setApiKey"}};

            for (String methodName : possibleMethodNames) {{
                try {{
                    Method method = httpClient.getClass().getDeclaredMethod(methodName, String.class);
                    method.setAccessible(true);
                    method.invoke(httpClient, sessionId);
                    System.out.println("✅ 调用方法: " + methodName);
                    return;
                }} catch (NoSuchMethodException e) {{
                    // 继续尝试
                }}
            }}

        }} catch (Exception e) {{
            System.out.println("⚠️ 设置SessionId失败: " + e.getMessage());
        }}
    }}
}}
"#, session_id);

        // 创建临时Java文件
        let java_file = temp_dir.join("IdeaRuntimeInjector.java");
        fs::write(&java_file, java_code)
            .map_err(|e| format!("创建Java文件失败: {}", e))?;

        // 尝试编译Java文件
        self.compile_java_file(&java_file, &injector_jar)
    }

    /// 编译Java文件并创建JAR
    fn compile_java_file(&self, java_file: &PathBuf, jar_file: &PathBuf) -> Result<PathBuf, String> {
        let java_exe = self.find_java_executable()?;
        let java_dir = java_exe.parent().unwrap();

        // 1. 尝试使用javac编译
        let javac_exe = java_dir.join("javac.exe");
        if javac_exe.exists() {
            println!("☕ 使用javac编译Java文件...");

            let compile_output = Command::new(&javac_exe)
                .arg(java_file)
                .output()
                .map_err(|e| format!("编译Java文件失败: {}", e))?;

            if !compile_output.status.success() {
                let error = String::from_utf8_lossy(&compile_output.stderr);
                return Err(format!("Java编译失败: {}", error));
            }

            // 2. 创建JAR文件
            let jar_exe = java_dir.join("jar.exe");
            if jar_exe.exists() {
                let class_file = java_file.with_extension("class");
                let temp_dir = java_file.parent().unwrap();

                let jar_output = Command::new(&jar_exe)
                    .args(&["cfe", &jar_file.to_string_lossy(), "IdeaRuntimeInjector"])
                    .arg(&class_file)
                    .current_dir(temp_dir)
                    .output()
                    .map_err(|e| format!("创建JAR文件失败: {}", e))?;

                if !jar_output.status.success() {
                    let error = String::from_utf8_lossy(&jar_output.stderr);
                    return Err(format!("JAR创建失败: {}", error));
                }

                println!("✅ JAR文件创建成功");
                return Ok(jar_file.clone());
            } else {
                return Err("未找到jar工具，无法创建JAR文件".to_string());
            }
        }

        // 3. 如果没有javac，尝试直接运行Java文件（某些JDK版本支持）
        println!("⚠️ 未找到javac，尝试直接运行Java源文件...");

        // 创建一个简单的脚本来运行Java源文件
        let script_file = java_file.with_extension("bat");
        let script_content = format!(
            r#"@echo off
cd /d "{}"
"{}" IdeaRuntimeInjector.java %*
"#,
            java_file.parent().unwrap().display(),
            java_exe.display()
        );

        fs::write(&script_file, script_content)
            .map_err(|e| format!("创建脚本文件失败: {}", e))?;

        // 返回脚本文件路径作为"JAR"文件
        Ok(script_file)
    }

    /// 向指定进程注入
    fn inject_process(&self, java_exe: &PathBuf, injector_file: &PathBuf, _process_id: u32, session_id: &str) -> Result<(), String> {
        let output = if injector_file.extension().and_then(|s| s.to_str()) == Some("jar") {
            // 如果是JAR文件，使用-jar参数
            Command::new(java_exe)
                .arg("-jar")
                .arg(injector_file)
                .arg(session_id)
                .output()
                .map_err(|e| format!("执行JAR注入器失败: {}", e))?
        } else if injector_file.extension().and_then(|s| s.to_str()) == Some("bat") {
            // 如果是批处理文件，直接执行
            Command::new(injector_file)
                .arg(session_id)
                .output()
                .map_err(|e| format!("执行脚本注入器失败: {}", e))?
        } else {
            // 尝试直接运行Java源文件（JDK 11+支持）
            Command::new(java_exe)
                .arg(injector_file)
                .arg(session_id)
                .output()
                .map_err(|e| format!("执行源文件注入器失败: {}", e))?
        };

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            println!("✅ 注入器输出: {}", stdout);
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            println!("❌ 注入器错误输出: {}", stderr);
            println!("📝 注入器标准输出: {}", stdout);
            Err(format!("注入器执行失败: {}", stderr))
        }
    }
}
