var ps=Object.defineProperty;var Qt=r=>{throw TypeError(r)};var fs=(r,e,t)=>e in r?ps(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var fe=(r,e,t)=>fs(r,typeof e!="symbol"?e+"":e,t),gs=(r,e,t)=>e.has(r)||Qt("Cannot "+t);var Gt=(r,e,t)=>e.has(r)?Qt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var gt=(r,e,t)=>(gs(r,e,"access private method"),t);import{a3 as He,S as ee,i as te,s as Y,V as A,c as x,X as ye,e as g,f as q,n as X,h as $,A as qe,L as T,M as le,C as L,D as M,u as f,t as h,F as R,I as j,ac as Ae,q as Z,r as H,ao as zi,ah as Le,a5 as Li,a6 as et,K as Me,J as xe,ap as Dt,an as zt,ag as Ge,B as Je,E as Ye,ai as Mi,T as ge,aC as Ne,a8 as ot,aa as Ri,ae as $s,af as Jt,b as qi,_ as be,a as Ni,az as Ft,$ as Ee,a0 as _e,a1 as Be,g as Ti,aA as Yt,aB as hs,ab as ms,ad as Ds,am as Tt}from"./SpinnerAugment-Cx9dt_ox.js";import"./design-system-init-BCZOObrS.js";import{g as $t,p as Fs,a as xs}from"./index-DhtTPDph.js";import"./design-system-init-y6tm-B4G.js";import{W as Ze,e as pe,u as Oi,o as Pi,h as Si}from"./BaseButton-BqzdgpkK.js";import{T as Ue,M as Cs}from"./TextTooltipAugment-DTMpOwfF.js";import{s as Xt}from"./index-DUiNNixO.js";import{c as xt,p as ks,g as Ie,M as ws,P as ut,b as vs,i as ys,d as As}from"./diff-utils-C7XQLqYW.js";import{a as Ii,C as ji,b as bs}from"./CollapseButtonAugment-D3vAw6HE.js";import{a as Ot,g as Pt,b as Vi,S as Es,M as _s}from"./index-8X-F_Twk.js";import{I as yt,A as Bs}from"./IconButtonAugment-BjDqXmYl.js";import{V as ct}from"./VSCodeCodicon-B3px2_jp.js";import{B as Ve}from"./ButtonAugment-DhtPLzGu.js";import{M as At}from"./MaterialIcon-8-Z76Y2_.js";import{n as Ui,a as We,g as re}from"./file-paths-BcSg4gks.js";import{T as Xe}from"./Content-BiWRcmeV.js";import{O as Zi,R as zs}from"./open-in-new-window-C_TwPNdv.js";import{F as Ls}from"./types-DDm27S8B.js";import{L as Ms}from"./LanguageIcon-FVMxq7uD.js";import{g as Rs}from"./globals-D0QH3NT1.js";import{E as qs}from"./expand-CURYX9ur.js";import{E as Hi}from"./exclamation-triangle-BbVpV4C-.js";import"./toggleHighContrast-Th-X2FgN.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BxQII05L.js";class Ct{constructor(e){fe(this,"_opts",null);fe(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:Ze.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===Ze.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}fe(Ct,"key","remoteAgentDiffModel");class nt{constructor(e){fe(this,"_applyingFilePaths",He([]));fe(this,"_appliedFilePaths",He([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:Ze.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(i){return console.error("Failed to get diff explanation:",i),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:Ze.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(i){return console.error("Failed to group changes:",i),[]}}async getDescriptions(e,t){try{const n=await this._asyncMsgSender.send({type:Ze.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}},1e5);return{explanation:n.data.explanation,error:n.data.error}}catch(n){return console.error("Failed to get descriptions:",n),{explanation:[],error:`Failed to get descriptions: ${n instanceof Error?n.message:String(n)}`}}}async applyChanges(e,t,n){this._applyingFilePaths.update(i=>[...i.filter(s=>s!==e),e]);try{(await this._asyncMsgSender.send({type:Ze.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4)).data.success&&this._appliedFilePaths.update(i=>[...i.filter(s=>s!==e),e])}catch(i){console.error("applyChanges error",i)}finally{this._applyingFilePaths.update(i=>i.filter(s=>s!==e))}}async openFile(e){try{const t=await this._asyncMsgSender.send({type:Ze.openFileRequest,data:{path:e}},1e4);return t.data.success||console.error("Failed to open file:",t.data.error),t.data.success}catch(t){console.error("openFile error",t)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:Ze.reportAgentChangesApplied})}}fe(nt,"key","remoteAgentsDiffOpsModel");function Kt(r,e,t,n,i={}){const{context:s=3,generateId:o=!0}=i,l=xt(r,e,t,n,"","",{context:s}),a=e||r;let u;return o?u=`${Ie(a)}-${Ie(t+n)}`:u=Math.random().toString(36).substring(2,15),{id:u,path:a,diff:l,originalCode:t,modifiedCode:n}}function Lt(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function Wi(r){return!r.originalCode||r.originalCode.trim()===""}function Qi(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class Ns{static generateDiff(e,t,n,i){return Kt(e,t,n,i)}static generateDiffs(e){return function(t,n={}){return t.map(i=>Kt(i.oldPath,i.newPath,i.oldContent,i.newContent,n))}(e)}static getDiffStats(e){return Lt(e)}static getDiffObjectStats(e){return Lt(e.diff)}static isNewFile(e){return Wi(e)}static isDeletedFile(e){return Qi(e)}}function Ts(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function Os(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function Ps(r){let e,t,n;function i(l,a){return l[2]?Os:Ts}let s=i(r),o=s(r);return{c(){e=A("span"),t=A("code"),o.c(),x(t,"class","markdown-codespan svelte-11ta4gi"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),ye(t,"markdown-string",r[4])},m(l,a){g(l,e,a),q(e,t),o.m(t,null),r[6](e)},p(l,[a]){s===(s=i(l))&&o?o.p(l,a):(o.d(1),o=s(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&ye(t,"markdown-string",l[4])},i:X,o:X,d(l){l&&$(e),o.d(),r[6](null)}}}function Ss(r,e,t){let n,i,s,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,i=n.startsWith('"')),2&r.$$.dirty&&t(2,s=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=s&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,s,o,i,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}let Is=class extends ee{constructor(r){super(),te(this,r,Ss,Ps,Y,{token:5,element:0})}};function js(r){let e,t;return e=new ws({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.markdown=n[1](n[0])),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Vs(r,e,t){let{markdown:n}=e;const i={codespan:Is};return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown)},[n,s=>s.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),i]}let Gi=class extends ee{constructor(r){super(),te(this,r,Vs,js,Y,{markdown:0})}};function en(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function tn(r){let e,t,n,i,s;t=new yt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Hs]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=nn(en(r,o,u));const a=u=>h(l[u],1,1,()=>{l[u]=null});return{c(){e=A("div"),L(t.$$.fragment),n=j(),i=A("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(i,"class","descriptions svelte-14s1ghg"),Ae(i,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,i,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(i,null);s=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=pe(u[1]),p=0;p<o.length;p+=1){const F=en(u,o,p);l[p]?(l[p].p(F,c),f(l[p],1)):(l[p]=nn(F),l[p].c(),f(l[p],1),l[p].m(i,null))}for(Z(),p=o.length;p<l.length;p+=1)a(p);H()}(!s||16&c[0])&&Ae(i,"transform","translateY("+-u[4]+"px)")},i(u){if(!s){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);s=!0}},o(u){h(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)h(l[c]);s=!1},d(u){u&&($(e),$(n),$(i)),R(t),Me(l,u)}}}function Us(r){let e,t;return e=new ct({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Zs(r){let e,t;return e=new ct({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Hs(r){let e,t,n,i;const s=[Zs,Us],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function nn(r){let e,t,n,i;return t=new Gi({props:{markdown:r[47].text}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),x(e,"class","description svelte-14s1ghg"),Ae(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ae(e,"--user-theme-sidebar-background","transparent")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};2&o[0]&&(l.markdown=s[47].text),t.$set(l),(!i||34&o[0])&&Ae(e,"top",(s[5][s[49]]||s[9](s[47]))+"px")},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function Ws(r){let e,t,n,i,s=r[1].length>0&&tn(r);return{c(){e=A("div"),t=A("div"),n=j(),s&&s.c(),x(t,"class","editor-container svelte-14s1ghg"),Ae(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),ye(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),q(e,t),r[23](t),q(e,n),s&&s.m(e,null),i=!0},p(o,l){(!i||8&l[0])&&Ae(t,"height",o[3]+"px"),o[1].length>0?s?(s.p(o,l),2&l[0]&&f(s,1)):(s=tn(o),s.c(),f(s,1),s.m(e,null)):s&&(Z(),h(s,1,1,()=>{s=null}),H()),(!i||3&l[0])&&ye(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){i||(f(s),i=!0)},o(o){h(s),i=!1},d(o){o&&$(e),r[23](null),s&&s.d()}}}function Qs(r,e,t){let n,i,s;const o=zi();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:F=[]}=e,{theme:v}=e,{areDescriptionsVisible:w=!0}=e,{isNewFile:E=!1}=e,{isDeletedFile:b=!1}=e;const z=Ot.getContext().monaco;let m,D,C,y;Le(r,z,k=>t(22,n=k));let _,P=[];const U=Pt();let Q,G=He(0);Le(r,G,k=>t(4,i=k));let oe=E?20*a.split(`
`).length+40:100;const ae=n?n.languages.getLanguages().map(k=>k.id):[];function ce(k,N){var S,V;if(N){const I=(S=N.split(".").pop())==null?void 0:S.toLowerCase();if(I){const W=(V=n==null?void 0:n.languages.getLanguages().find(K=>{var ne;return(ne=K.extensions)==null?void 0:ne.includes("."+I)}))==null?void 0:V.id;if(W&&ae.includes(W))return W}}return"plaintext"}const de=He({});Le(r,de,k=>t(5,s=k));let J=null;function Fe(){if(!m)return;P=P.filter(S=>(S.dispose(),!1));const k=m.getOriginalEditor(),N=m.getModifiedEditor();P.push(k.onDidScrollChange(()=>{Dt(G,i=k.getScrollTop(),i)}),N.onDidScrollChange(()=>{Dt(G,i=N.getScrollTop(),i)}))}function $e(){if(!m||!_)return;const k=m.getOriginalEditor(),N=m.getModifiedEditor();P.push(N.onDidContentSizeChange(()=>U.requestLayout()),k.onDidContentSizeChange(()=>U.requestLayout()),m.onDidUpdateDiff(()=>U.requestLayout()),N.onDidChangeHiddenAreas(()=>U.requestLayout()),k.onDidChangeHiddenAreas(()=>U.requestLayout()),N.onDidLayoutChange(()=>U.requestLayout()),k.onDidLayoutChange(()=>U.requestLayout()),N.onDidFocusEditorWidget(()=>{O(!0)}),k.onDidFocusEditorWidget(()=>{O(!0)}),N.onDidBlurEditorWidget(()=>{O(!1)}),k.onDidBlurEditorWidget(()=>{O(!1)}),N.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const S=(y==null?void 0:y.getValue())||"";if(S===a)return;const V=S.replace(p.join(""),"").replace(F.join(""),"");o("codeChange",{modifiedCode:V});const I=setTimeout(()=>{Ce=!1},500);P.push({dispose:()=>clearTimeout(I)})})),function(){!_||!m||(J&&clearTimeout(J),J=setTimeout(()=>{if(!_.__hasClickListener){const S=V=>{const I=V.target;I&&(I.closest('[title="Show Unchanged Region"]')||I.closest('[title="Hide Unchanged Region"]'))&&we()};_.addEventListener("click",S),t(2,_.__hasClickListener=!0,_),P.push({dispose:()=>{_.removeEventListener("click",S)}})}m&&P.push(m.onDidUpdateDiff(()=>{we()}))},300))}()}Li(()=>{m==null||m.dispose(),D==null||D.dispose(),C==null||C.dispose(),y==null||y.dispose(),P.forEach(k=>k.dispose()),J&&clearTimeout(J),Q==null||Q()});let ie=null;function we(){ie&&clearTimeout(ie),ie=setTimeout(()=>{U.requestLayout(),ie=null},100),ie&&P.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ve(k,N,S,V=[],I=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");C==null||C.dispose(),y==null||y.dispose(),N=N||"",S=S||"";const W=V.join(""),K=I.join("");if(N=E?S.split(`
`).map(()=>" ").join(`
`):W+N+K,S=W+S+K,C=n.editor.createModel(N,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0),b&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,y=n.editor.createModel(S,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:C,modified:y});const ne=m.getOriginalEditor();ne&&ne.updateOptions({lineNumbers:"off"}),Fe(),J&&clearTimeout(J),J=setTimeout(()=>{$e(),J=null},300)}}et(()=>{if(n)if(E){t(20,D=n.editor.create(_,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:v,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-p.length+V}`}));const k=ce(0,u);t(21,y=n.editor.createModel(a,k,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(y),P.push(D.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const V=(y==null?void 0:y.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const I=setTimeout(()=>{Ce=!1},500);P.push({dispose:()=>clearTimeout(I)})})),P.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const N=D.getContentHeight();t(3,oe=Math.max(N,60));const S=setTimeout(()=>{D==null||D.layout()},0);P.push({dispose:()=>clearTimeout(S)})}else t(19,m=n.editor.createDiffEditor(_,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:v,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:k=>`${d-p.length+k}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Q&&Q(),Q=U.registerEditor({editor:m,updateHeight:ke,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ve(u,l,a,p,F),Fe(),$e(),J&&clearTimeout(J),J=setTimeout(()=>{U.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,Re=0;function ue(k,N=!0){return m?(N?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(k):18*k}function ke(){if(!m)return;const k=m.getModel(),N=k==null?void 0:k.original,S=k==null?void 0:k.modified;if(!N||!S)return;const V=m.getOriginalEditor(),I=m.getModifiedEditor(),W=m.getLineChanges()||[];let K;if(W.length===0){const ne=V.getContentHeight(),De=I.getContentHeight();K=Math.max(100,ne,De)}else{let ne=0,De=0;for(const Te of W)Te.originalEndLineNumber>0&&(ne=Math.max(ne,Te.originalEndLineNumber)),Te.modifiedEndLineNumber>0&&(De=Math.max(De,Te.modifiedEndLineNumber));ne=Math.min(ne+3,N.getLineCount()),De=Math.min(De+3,S.getLineCount());const ze=V.getTopForLineNumber(ne),Pe=I.getTopForLineNumber(De);K=Math.max(ze,Pe)+60}t(3,oe=Math.min(K,2e4)),m.layout(),se()}function O(k){if(!m)return;const N=m.getOriginalEditor(),S=m.getModifiedEditor();N.updateOptions({scrollbar:{handleMouseWheel:k}}),S.updateOptions({scrollbar:{handleMouseWheel:k}})}function B(k){if(!m)return 0;const N=m.getModel(),S=N==null?void 0:N.original,V=N==null?void 0:N.modified;if(!S||!V)return 0;const I=ue(k.range.start+1,!1),W=ue(k.range.start+1,!0);return I&&!W?I:!I&&W?W:Math.min(I,W)}function se(){if(!m||c.length===0)return;const k={};c.forEach((N,S)=>{k[S]=B(N)}),de.set(k)}return r.$$set=k=>{"originalCode"in k&&t(10,l=k.originalCode),"modifiedCode"in k&&t(11,a=k.modifiedCode),"path"in k&&t(12,u=k.path),"descriptions"in k&&t(1,c=k.descriptions),"lineOffset"in k&&t(13,d=k.lineOffset),"extraPrefixLines"in k&&t(14,p=k.extraPrefixLines),"extraSuffixLines"in k&&t(15,F=k.extraSuffixLines),"theme"in k&&t(16,v=k.theme),"areDescriptionsVisible"in k&&t(0,w=k.areDescriptionsVisible),"isNewFile"in k&&t(17,E=k.isNewFile),"isDeletedFile"in k&&t(18,b=k.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(k=a,!(Ce||Date.now()-Re<1e3||y&&y.getValue()===p.join("")+k+F.join(""))))if(E&&D){if(y)y.setValue(a);else{const N=ce(0,u);n&&t(21,y=n.editor.createModel(a,N,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),y&&D.setModel(y)}t(3,oe=20*a.split(`
`).length+40),D.layout()}else!E&&m&&(ve(u,l,a,p,F),U.requestLayout());var k;if(524290&r.$$.dirty[0]&&m&&c.length>0&&se(),1181696&r.$$.dirty[0]&&E&&a&&D){const N=D.getContentHeight();t(3,oe=Math.max(N,60)),D.layout()}},[w,c,_,oe,i,s,z,G,de,B,l,a,u,d,p,F,v,E,b,m,D,y,n,function(k){qe[k?"unshift":"push"](()=>{_=k,t(2,_)})},()=>t(0,w=!w)]}let Gs=class extends ee{constructor(r){super(),te(this,r,Qs,Ws,Y,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}};const Js=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],Ys=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Ji=1048576;function St(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function lt(r){switch(St(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function sn(r){const e=St(r);return Js.includes(e)}function rn(r){return r>Ji}const Yi=Symbol("focusedPath");function Xi(){return Ge(Yi)}function Mt(r){return`file-diff-${Ie(r)}`}function Xs(r){let e,t,n;function i(o){r[41](o)}let s={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(s.areDescriptionsVisible=r[1]),e=new Gs({props:s}),qe.push(()=>Je(e,"areDescriptionsVisible",i)),e.$on("codeChange",r[26]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Ye(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){h(e.$$.fragment,o),n=!1},d(o){R(e,o)}}}function Ks(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[nr]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};5888&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function er(r){let e,t,n;return t=new ge({props:{$$slots:{default:[or]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};2101632&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function tr(r){let e,t,n,i;const s=[ar,lr],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=s[t](r)),{c(){e=A("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),~t&&o[t].d()}}}function nr(r){let e,t,n,i,s,o,l,a=re(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=T('File "'),t=T(a),n=T('" is too large to display a diff (size: '),i=T(u),s=T(" bytes, max: "),o=T(Ji),l=T(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,i,d),g(c,s,d),g(c,o,d),g(c,l,d)},p(c,d){4096&d[0]&&a!==(a=re(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(i,u)},d(c){c&&($(e),$(t),$(n),$(i),$(s),$(o),$(l))}}}function ir(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file modified: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function sr(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file deleted: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function rr(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file added: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function or(r){let e;function t(s,o){return s[21]||s[7]?rr:s[8]?sr:ir}let n=t(r),i=n(r);return{c(){i.c(),e=T(`
            No text preview available.`)},m(s,o){i.m(s,o),g(s,e,o)},p(s,o){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},d(s){s&&$(e),i.d(s)}}}function lr(r){let e,t,n,i,s,o,l,a;e=new ge({props:{class:"image-info-text",$$slots:{default:[dr]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&on(r);return{c(){L(e.$$.fragment),t=j(),n=A("img"),o=j(),u&&u.c(),l=xe(),Ne(n.src,i="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",i),x(n,"alt",s="Current "+re(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Ne(n.src,i="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",i),(!a||4096&d[0]&&s!==(s="Current "+re(c[12])))&&x(n,"alt",s),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=on(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(Z(),h(u,1,1,()=>{u=null}),H())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){h(e.$$.fragment,c),h(u),a=!1},d(c){c&&($(t),$(n),$(o),$(l)),R(e,c),u&&u.d(c)}}}function ar(r){let e,t,n,i;e=new ge({props:{class:"image-info-text",$$slots:{default:[fr]},$$scope:{ctx:r}}});let s=r[0].originalCode&&ln(r);return{c(){L(e.$$.fragment),t=j(),s&&s.c(),n=xe()},m(o,l){M(e,o,l),g(o,t,l),s&&s.m(o,l),g(o,n,l),i=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?s?(s.p(o,l),1&l[0]&&f(s,1)):(s=ln(o),s.c(),f(s,1),s.m(n.parentNode,n)):s&&(Z(),h(s,1,1,()=>{s=null}),H())},i(o){i||(f(e.$$.fragment,o),f(s),i=!0)},o(o){h(e.$$.fragment,o),h(s),i=!1},d(o){o&&($(t),$(n)),R(e,o),s&&s.d(o)}}}function ur(r){let e;return{c(){e=T("Image modified")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function cr(r){let e;return{c(){e=T("New image added")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function dr(r){let e,t,n=re(r[12])+"";function i(l,a){return l[21]||l[7]?cr:ur}let s=i(r),o=s(r);return{c(){o.c(),e=T(": "),t=T(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){s!==(s=i(l))&&(o.d(1),o=s(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=re(l[12])+"")&&le(t,n)},d(l){l&&($(e),$(t)),o.d(l)}}}function on(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[pr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),n=A("img"),Ne(n.src,i="data:"+lt(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+re(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+lt(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+re(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function pr(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function fr(r){let e,t,n=re(r[12])+"";return{c(){e=T("Image deleted: "),t=T(n)},m(i,s){g(i,e,s),g(i,t,s)},p(i,s){4096&s[0]&&n!==(n=re(i[12])+"")&&le(t,n)},d(i){i&&($(e),$(t))}}}function ln(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[gr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),n=A("img"),Ne(n.src,i="data:"+lt(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+re(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+lt(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+re(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function gr(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function $r(r){let e,t,n,i;const s=[tr,er,Ks,Xs],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=s[t](r),{c(){e=A("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function hr(r){let e,t=re(r[12])+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){4096&i[0]&&t!==(t=re(n[12])+"")&&le(e,t)},d(n){n&&$(e)}}}function mr(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[hr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};4096&i[0]|16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function an(r){let e,t,n=We(r[12])+"";return{c(){e=A("span"),t=T(n),x(e,"class","c-directory svelte-1536g7w")},m(i,s){g(i,e,s),q(e,t)},p(i,s){4096&s[0]&&n!==(n=We(i[12])+"")&&le(t,n)},d(i){i&&$(e)}}}function Dr(r){let e,t,n,i=r[23]>0&&un(r),s=r[22]>0&&cn(r);return{c(){e=A("div"),i&&i.c(),t=j(),s&&s.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),i&&i.m(e,null),q(e,t),s&&s.m(e,null),n=!0},p(o,l){o[23]>0?i?(i.p(o,l),8388608&l[0]&&f(i,1)):(i=un(o),i.c(),f(i,1),i.m(e,t)):i&&(Z(),h(i,1,1,()=>{i=null}),H()),o[22]>0?s?(s.p(o,l),4194304&l[0]&&f(s,1)):(s=cn(o),s.c(),f(s,1),s.m(e,null)):s&&(Z(),h(s,1,1,()=>{s=null}),H())},i(o){n||(f(i),f(s),n=!0)},o(o){h(i),h(s),n=!1},d(o){o&&$(e),i&&i.d(),s&&s.d()}}}function Fr(r){let e;return{c(){e=A("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&$(e)}}}function un(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[xr]},$$scope:{ctx:r}}}),{c(){e=A("span"),L(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};8388608&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function xr(r){let e,t;return{c(){e=T("+"),t=T(r[23])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){8388608&i[0]&&le(t,n[23])},d(n){n&&($(e),$(t))}}}function cn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Cr]},$$scope:{ctx:r}}}),{c(){e=A("span"),L(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Cr(r){let e,t;return{c(){e=T("-"),t=T(r[22])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){4194304&i[0]&&le(t,n[22])},d(n){n&&($(e),$(t))}}}function kr(r){let e;return{c(){e=T("Apply")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function wr(r){let e;return{c(){e=T("Applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function vr(r){let e,t,n;return t=new ut({}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function yr(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Ar(r){let e,t,n,i,s;function o(p,F){return p[5]?wr:kr}let l=o(r),a=l(r);const u=[yr,vr],c=[];function d(p,F){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=j(),n.c(),i=xe()},m(p,F){a.m(p,F),g(p,e,F),c[t].m(p,F),g(p,i,F),s=!0},p(p,F){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let v=t;t=d(p),t!==v&&(Z(),h(c[v],1,1,()=>{c[v]=null}),H(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(i.parentNode,i))},i(p){s||(f(n),s=!0)},o(p){h(n),s=!1},d(p){p&&($(e),$(i)),a.d(p),c[t].d(p)}}}function br(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Ar]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[0]&&(s.disabled=n[14]),32&i[0]|16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function dn(r){let e,t;return e=new Ue({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[_r]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2048&i[0]&&(s.content=n[11]),16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Er(r){let e,t;return e=new Zi({}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function _r(r){let e,t;return e=new yt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Er]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Br(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,v=We(r[12]);t=new ji({}),s=new Ue({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[mr]},$$scope:{ctx:r}}});let w=v&&an(r);const E=[Fr,Dr],b=[];function z(D,C){return D[21]?0:1}a=z(r),u=b[a]=E[a](r),d=new Ue({props:{content:r[13],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[br]},$$scope:{ctx:r}}});let m=r[5]&&dn(r);return{c(){e=A("div"),L(t.$$.fragment),n=j(),i=A("div"),L(s.$$.fragment),o=j(),w&&w.c(),l=j(),u.c(),c=j(),L(d.$$.fragment),p=j(),m&&m.c(),x(i,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,C){g(D,e,C),M(t,e,null),q(e,n),q(e,i),M(s,i,null),q(i,o),w&&w.m(i,null),q(e,l),b[a].m(e,null),q(e,c),M(d,e,null),q(e,p),m&&m.m(e,null),F=!0},p(D,C){const y={};2048&C[0]&&(y.content=D[11]),4096&C[0]|16384&C[1]&&(y.$$scope={dirty:C,ctx:D}),s.$set(y),4096&C[0]&&(v=We(D[12])),v?w?w.p(D,C):(w=an(D),w.c(),w.m(i,null)):w&&(w.d(1),w=null);let _=a;a=z(D),a===_?b[a].p(D,C):(Z(),h(b[_],1,1,()=>{b[_]=null}),H(),u=b[a],u?u.p(D,C):(u=b[a]=E[a](D),u.c()),f(u,1),u.m(e,c));const P={};8192&C[0]&&(P.content=D[13]),16416&C[0]|16384&C[1]&&(P.$$scope={dirty:C,ctx:D}),d.$set(P),D[5]?m?(m.p(D,C),32&C[0]&&f(m,1)):(m=dn(D),m.c(),f(m,1),m.m(e,null)):m&&(Z(),h(m,1,1,()=>{m=null}),H())},i(D){F||(f(t.$$.fragment,D),f(s.$$.fragment,D),f(u),f(d.$$.fragment,D),f(m),F=!0)},o(D){h(t.$$.fragment,D),h(s.$$.fragment,D),h(u),h(d.$$.fragment,D),h(m),F=!1},d(D){D&&$(e),R(t),R(s),w&&w.d(),b[a].d(),R(d),m&&m.d()}}}function zr(r){let e,t,n,i,s;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Br],default:[$r]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ii({props:l}),qe.push(()=>Je(t,"collapsed",o)),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",i=Mt(r[3])),ye(e,"focused",r[24]===r[3])},m(a,u){g(a,e,u),M(t,e,null),s=!0},p(a,u){const c={};16777211&u[0]|16384&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Ye(()=>n=!1)),t.$set(c),(!s||8&u[0]&&i!==(i=Mt(a[3])))&&x(e,"id",i),(!s||16777224&u[0])&&ye(e,"focused",a[24]===a[3])},i(a){s||(f(t.$$.fragment,a),s=!0)},o(a){h(t.$$.fragment,a),s=!1},d(a){a&&$(e),R(t)}}}function Lr(r,e,t){let n,i,s,o,l,a,u,c,d,p,F,v,w,E,b,z,m,D,C,y,_,P,U;Le(r,Mi,B=>t(40,P=B));let{path:Q}=e,{change:G}=e,{descriptions:oe=[]}=e,{areDescriptionsVisible:ae=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:de=!ce}=e,{isApplying:J}=e,{hasApplied:Fe}=e,{onApplyChanges:$e}=e,{onCodeChange:ie}=e,{onOpenFile:we}=e,{isAgentFromDifferentRepo:ve=!1}=e;const Ce=Xi();Le(r,Ce,B=>t(24,U=B));const Re=Ge(nt.key);let ue=G.modifiedCode,ke=C;function O(){t(11,ke=`Open ${C??"file"}`)}return et(()=>{O()}),r.$$set=B=>{"path"in B&&t(3,Q=B.path),"change"in B&&t(0,G=B.change),"descriptions"in B&&t(4,oe=B.descriptions),"areDescriptionsVisible"in B&&t(1,ae=B.areDescriptionsVisible),"isExpandedDefault"in B&&t(29,ce=B.isExpandedDefault),"isCollapsed"in B&&t(2,de=B.isCollapsed),"isApplying"in B&&t(30,J=B.isApplying),"hasApplied"in B&&t(5,Fe=B.hasApplied),"onApplyChanges"in B&&t(31,$e=B.onApplyChanges),"onCodeChange"in B&&t(32,ie=B.onCodeChange),"onOpenFile"in B&&t(33,we=B.onOpenFile),"isAgentFromDifferentRepo"in B&&t(34,ve=B.isAgentFromDifferentRepo)},r.$$.update=()=>{var B;1&r.$$.dirty[0]&&t(6,ue=G.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Lt(G.diff)),256&r.$$.dirty[1]&&t(23,i=n.additions),256&r.$$.dirty[1]&&t(22,s=n.deletions),1&r.$$.dirty[0]&&t(21,o=Wi(G)),1&r.$$.dirty[0]&&t(20,l=Qi(G)),8&r.$$.dirty[0]&&t(38,a=sn(Q)),8&r.$$.dirty[0]&&t(19,u=lt(Q)),8&r.$$.dirty[0]&&t(37,c=function(se){if(sn(se))return!1;const k=St(se);return Ys.includes(k)}(Q)),1&r.$$.dirty[0]&&t(10,d=((B=G.originalCode)==null?void 0:B.length)||0),64&r.$$.dirty[0]&&t(9,p=(ue==null?void 0:ue.length)||0),1024&r.$$.dirty[0]&&t(36,F=rn(d)),512&r.$$.dirty[0]&&t(35,v=rn(p)),65&r.$$.dirty[0]&&t(8,w=!ue&&!!G.originalCode),65&r.$$.dirty[0]&&t(7,E=!!ue&&!G.originalCode),128&r.$$.dirty[1]&&t(18,b=a),192&r.$$.dirty[1]&&t(17,z=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!c&&(v||w&&F||E&&v)),512&r.$$.dirty[1]&&t(15,D=Vi(P==null?void 0:P.category,P==null?void 0:P.intensity)),8&r.$$.dirty[0]&&t(12,C=Ui(Q)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,y=J||ve),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,_=J?"Applying changes...":Fe?"Reapply changes to local file":ve?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[G,ae,de,Q,oe,Fe,ue,E,w,p,d,ke,C,_,y,D,m,z,b,u,l,o,s,i,U,Ce,function(B){t(6,ue=B.detail.modifiedCode),ie==null||ie(ue)},function(){Re.reportApplyChangesEvent(),t(0,G.modifiedCode=ue,G),ie==null||ie(ue),$e==null||$e()},async function(){we&&(t(11,ke="Opening file..."),await we()?O():(t(11,ke="Failed to open file. Does the file exist?"),setTimeout(()=>{O()},2e3)))},ce,J,$e,ie,we,ve,v,F,c,a,n,P,function(B){ae=B,t(1,ae)},function(B){de=B,t(2,de)}]}let Mr=class extends ee{constructor(r){super(),te(this,r,Lr,zr,Y,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}};function pn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Rr(r){let e,t;return e=new Ms({props:{filename:r[0].name}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.filename=n[0].name),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function qr(r){let e,t;return e=new ct({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Nr(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=A("span"),t=T(n),x(e,"class","full-path-text svelte-qnxoj")},m(i,s){g(i,e,s),q(e,t)},p(i,s){1&s&&n!==(n=(i[0].displayName||i[0].name)+"")&&le(t,n)},d(i){i&&$(e)}}}function fn(r){let e,t,n=pe(Array.from(r[0].children.values()).sort($n)),i=[];for(let o=0;o<n.length;o+=1)i[o]=gn(pn(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){e=A("div");for(let o=0;o<i.length;o+=1)i[o].c();x(e,"class","tree-node__children svelte-qnxoj"),x(e,"role","group")},m(o,l){g(o,e,l);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);t=!0},p(o,l){if(3&l){let a;for(n=pe(Array.from(o[0].children.values()).sort($n)),a=0;a<n.length;a+=1){const u=pn(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=gn(u),i[a].c(),f(i[a],1),i[a].m(e,null))}for(Z(),a=n.length;a<i.length;a+=1)s(a);H()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Me(i,o)}}}function gn(r){let e,t;return e=new Ki({props:{node:r[6],indentLevel:r[1]+1}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.node=n[6]),2&i&&(s.indentLevel=n[1]+1),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Tr(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,v,w,E,b;const z=[qr,Rr],m=[];function D(y,_){return y[0].isFile?1:0}o=D(r),l=m[o]=z[o](r),c=new ge({props:{size:1,$$slots:{default:[Nr]},$$scope:{ctx:r}}});let C=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&fn(r);return{c(){e=A("div"),t=A("div"),n=A("div"),i=j(),s=A("div"),l.c(),a=j(),u=A("span"),L(c.$$.fragment),v=j(),C&&C.c(),x(n,"class","tree-node__indent svelte-qnxoj"),Ae(n,"width",6*r[1]+"px"),x(s,"class","tree-node__icon-container svelte-qnxoj"),x(u,"class","tree-node__label svelte-qnxoj"),x(u,"title",d=r[0].displayName||r[0].name),ye(u,"full-path",r[0].displayName),x(t,"class","tree-node__content svelte-qnxoj"),x(t,"role","treeitem"),x(t,"tabindex","0"),x(t,"aria-selected",p=r[0].path===r[2]),x(t,"aria-expanded",F=r[0].isFile?void 0:r[0].isExpanded),ye(t,"selected",r[0].path===r[2]),ye(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),x(e,"class","tree-node svelte-qnxoj")},m(y,_){g(y,e,_),q(e,t),q(t,n),q(t,i),q(t,s),m[o].m(s,null),q(t,a),q(t,u),M(c,u,null),q(e,v),C&&C.m(e,null),w=!0,E||(b=[ot(t,"click",r[4]),ot(t,"keydown",r[5])],E=!0)},p(y,[_]){(!w||2&_)&&Ae(n,"width",6*y[1]+"px");let P=o;o=D(y),o===P?m[o].p(y,_):(Z(),h(m[P],1,1,()=>{m[P]=null}),H(),l=m[o],l?l.p(y,_):(l=m[o]=z[o](y),l.c()),f(l,1),l.m(s,null));const U={};513&_&&(U.$$scope={dirty:_,ctx:y}),c.$set(U),(!w||1&_&&d!==(d=y[0].displayName||y[0].name))&&x(u,"title",d),(!w||1&_)&&ye(u,"full-path",y[0].displayName),(!w||5&_&&p!==(p=y[0].path===y[2]))&&x(t,"aria-selected",p),(!w||1&_&&F!==(F=y[0].isFile?void 0:y[0].isExpanded))&&x(t,"aria-expanded",F),(!w||5&_)&&ye(t,"selected",y[0].path===y[2]),(!w||1&_)&&ye(t,"collapsed-folder",y[0].displayName&&!y[0].isFile),!y[0].isFile&&y[0].isExpanded&&y[0].children.size>0?C?(C.p(y,_),1&_&&f(C,1)):(C=fn(y),C.c(),f(C,1),C.m(e,null)):C&&(Z(),h(C,1,1,()=>{C=null}),H())},i(y){w||(f(l),f(c.$$.fragment,y),f(C),w=!0)},o(y){h(l),h(c.$$.fragment,y),h(C),w=!1},d(y){y&&$(e),m[o].d(),R(c),C&&C.d(),E=!1,Ri(b)}}}const $n=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Or(r,e,t){let n,{node:i}=e,{indentLevel:s=0}=e;const o=Xi();function l(){i.isFile?o.set(i.path):t(0,i.isExpanded=!i.isExpanded,i)}return Le(r,o,a=>t(2,n=a)),r.$$set=a=>{"node"in a&&t(0,i=a.node),"indentLevel"in a&&t(1,s=a.indentLevel)},[i,s,n,o,l,a=>a.key==="Enter"&&l()]}class Ki extends ee{constructor(e){super(),te(this,e,Or,Tr,Y,{node:0,indentLevel:1})}}function hn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Pr(r){let e,t,n=pe(Array.from(r[1].children.values()).sort(Dn)),i=[];for(let o=0;o<n.length;o+=1)i[o]=mn(hn(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(2&l){let a;for(n=pe(Array.from(o[1].children.values()).sort(Dn)),a=0;a<n.length;a+=1){const u=hn(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=mn(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(Z(),a=n.length;a<i.length;a+=1)s(a);H()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Me(i,o)}}}function Sr(r){let e,t,n;return t=new ge({props:{size:1,color:"neutral",$$slots:{default:[jr]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","tree-view__empty svelte-1tnd9l7")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};128&s&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Ir(r){let e;return{c(){e=A("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',x(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&$(e)}}}function mn(r){let e,t;return e=new Ki({props:{node:r[4],indentLevel:0}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2&i&&(s.node=n[4]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function jr(r){let e;return{c(){e=T("No changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Vr(r){let e,t,n,i,s;const o=[Ir,Sr,Pr],l=[];function a(u,c){return u[0]?0:u[1].children.size===0?1:2}return n=a(r),i=l[n]=o[n](r),{c(){e=A("div"),t=A("div"),i.c(),x(t,"class","tree-view__content svelte-1tnd9l7"),x(t,"role","tree"),x(t,"aria-label","Changed Files"),x(e,"class","tree-view svelte-1tnd9l7")},m(u,c){g(u,e,c),q(e,t),l[n].m(t,null),s=!0},p(u,[c]){let d=n;n=a(u),n===d?l[n].p(u,c):(Z(),h(l[d],1,1,()=>{l[d]=null}),H(),i=l[n],i?i.p(u,c):(i=l[n]=o[n](u),i.c()),f(i,1),i.m(t,null))},i(u){s||(f(i),s=!0)},o(u){h(i),s=!1},d(u){u&&$(e),l[n].d()}}}function Rt(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),a=o;for(;;){const u=Array.from(a.children.values()).filter(d=>!d.isFile),c=Array.from(a.children.values()).filter(d=>d.isFile);if(u.length!==1||c.length!==0)break;a=u[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Rt(o);const i=Array.from(r.children.values()).filter(o=>!o.isFile),s=Array.from(r.children.values()).filter(o=>o.isFile);if(i.length===1&&s.length===0){const o=i[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${a}`;r.children.set(c,u)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${l}/${a}`;r.children.set(c,u)}r.children.delete(l)}}}const Dn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Ur(r,e,t){let n,{changedFiles:i=[]}=e,{isLoading:s=!1}=e;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(u=>{const c=u.change_type===Ls.deleted?u.old_path:u.new_path;c&&function(d,p){const F=p.split("/");let v=d;for(let w=0;w<F.length;w++){const E=F[w],b=w===F.length-1,z=F.slice(0,w+1).join("/");v.children.has(E)||v.children.set(E,{name:E,path:z,isFile:b,children:new Map,isExpanded:!0}),v=v.children.get(E)}}(a,c)}),function(u){if(!u.isFile)if(u.path!=="")Rt(u);else{const c=Array.from(u.children.values()).filter(d=>!d.isFile);for(const d of c)Rt(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&t(2,i=l.changedFiles),"isLoading"in l&&t(0,s=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(i))},[s,n,i]}class es extends ee{constructor(e){super(),te(this,e,Ur,Vr,Y,{changedFiles:2,isLoading:0})}}function Fn(r,e,t){const n=r.slice();return n[19]=e[t],n}function Zr(r){let e;return{c(){e=T("Changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Hr(r){let e,t,n;return t=new ge({props:{size:1,color:"neutral",$$slots:{default:[Qr]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Wr(r){let e,t,n,i,s,o,l=[],a=new Map,u=r[9].length>0&&xn(r),c=pe(r[9]);const d=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<c.length;p+=1){let F=Fn(r,c,p),v=d(F);a.set(v,l[p]=Cn(v,F))}return{c(){e=A("div"),t=A("div"),u&&u.c(),n=j(),i=A("div"),s=A("div");for(let p=0;p<l.length;p+=1)l[p].c();x(t,"class","c-edits-list-controls svelte-6iqvaj"),x(e,"class","c-edits-list-header svelte-6iqvaj"),x(s,"class","c-edits-section svelte-6iqvaj"),x(i,"class","c-edits-list svelte-6iqvaj")},m(p,F){g(p,e,F),q(e,t),u&&u.m(t,null),g(p,n,F),g(p,i,F),q(i,s);for(let v=0;v<l.length;v+=1)l[v]&&l[v].m(s,null);o=!0},p(p,F){p[9].length>0?u?(u.p(p,F),512&F&&f(u,1)):(u=xn(p),u.c(),f(u,1),u.m(t,null)):u&&(Z(),h(u,1,1,()=>{u=null}),H()),2654&F&&(c=pe(p[9]),Z(),l=Oi(l,F,d,1,p,c,a,s,Pi,Cn,null,Fn),H())},i(p){if(!o){f(u);for(let F=0;F<c.length;F+=1)f(l[F]);o=!0}},o(p){h(u);for(let F=0;F<l.length;F+=1)h(l[F]);o=!1},d(p){p&&($(e),$(n),$(i)),u&&u.d();for(let F=0;F<l.length;F+=1)l[F].d()}}}function Qr(r){let e;return{c(){e=T("No changes to show")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function xn(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[7]||r[8]||r[3].length>0||!r[10],$$slots:{default:[Xr]},$$scope:{ctx:r}}}),e.$on("click",r[12]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1416&i&&(s.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&i&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Gr(r){let e;return{c(){e=T("Apply all")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Jr(r){let e;return{c(){e=T("All applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Yr(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Xr(r){let e,t,n,i;function s(a,u){return a[7]?Yr:a[8]?Jr:Gr}let o=s(r),l=o(r);return n=new ut({}),{c(){l.c(),e=j(),t=A("div"),L(n.$$.fragment),x(t,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,u){l.m(a,u),g(a,e,u),g(a,t,u),M(n,t,null),i=!0},p(a,u){o!==(o=s(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(e.parentNode,e)))},i(a){i||(f(n.$$.fragment,a),i=!0)},o(a){h(n.$$.fragment,a),i=!1},d(a){a&&($(e),$(t)),l.d(a),R(n)}}}function Cn(r,e){let t,n,i,s,o;function l(...c){return e[16](e[19],...c)}function a(){return e[17](e[19])}function u(){return e[18](e[19])}return n=new Mr({props:{path:e[19].qualifiedPathName.relPath,change:e[19].diff,isApplying:e[3].includes(e[19].qualifiedPathName.relPath),hasApplied:e[4].includes(e[19].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,onOpenFile:e[2]?u:void 0,isExpandedDefault:!0}}),{key:r,first:null,c(){t=A("div"),L(n.$$.fragment),i=j(),x(t,"class",""),this.first=t},m(c,d){g(c,t,d),M(n,t,null),q(t,i),o=!0},p(c,d){e=c;const p={};512&d&&(p.path=e[19].qualifiedPathName.relPath),512&d&&(p.change=e[19].diff),520&d&&(p.isApplying=e[3].includes(e[19].qualifiedPathName.relPath)),528&d&&(p.hasApplied=e[4].includes(e[19].qualifiedPathName.relPath)),512&d&&(p.onCodeChange=l),578&d&&(p.onApplyChanges=a),516&d&&(p.onOpenFile=e[2]?u:void 0),n.$set(p)},i(c){o||(f(n.$$.fragment,c),c&&$s(()=>{o&&(s||(s=Jt(t,Xt,{},!0)),s.run(1))}),o=!0)},o(c){h(n.$$.fragment,c),c&&(s||(s=Jt(t,Xt,{},!1)),s.run(0)),o=!1},d(c){c&&$(t),R(n),c&&s&&s.end()}}}function Kr(r){let e,t,n,i,s,o,l,a,u,c,d,p;s=new ge({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Zr]},$$scope:{ctx:r}}}),l=new es({props:{changedFiles:r[0],isLoading:r[5]}});const F=[Wr,Hr],v=[];function w(E,b){return E[9].length>0?0:1}return c=w(r),d=v[c]=F[c](r),{c(){e=A("div"),t=A("div"),n=A("div"),i=A("div"),L(s.$$.fragment),o=j(),L(l.$$.fragment),a=j(),u=A("div"),d.c(),x(i,"class","c-file-explorer__tree__header svelte-6iqvaj"),x(n,"class","c-file-explorer__tree svelte-6iqvaj"),x(u,"class","c-file-explorer__details svelte-6iqvaj"),x(t,"class","c-file-explorer__layout svelte-6iqvaj"),x(e,"class","c-edits-list-container svelte-6iqvaj")},m(E,b){g(E,e,b),q(e,t),q(t,n),q(n,i),M(s,i,null),q(i,o),M(l,i,null),q(t,a),q(t,u),v[c].m(u,null),p=!0},p(E,[b]){const z={};4194304&b&&(z.$$scope={dirty:b,ctx:E}),s.$set(z);const m={};1&b&&(m.changedFiles=E[0]),32&b&&(m.isLoading=E[5]),l.$set(m);let D=c;c=w(E),c===D?v[c].p(E,b):(Z(),h(v[D],1,1,()=>{v[D]=null}),H(),d=v[c],d?d.p(E,b):(d=v[c]=F[c](E),d.c()),f(d,1),d.m(u,null))},i(E){p||(f(s.$$.fragment,E),f(l.$$.fragment,E),f(d),p=!0)},o(E){h(s.$$.fragment,E),h(l.$$.fragment,E),h(d),p=!1},d(E){E&&$(e),R(s),R(l),v[c].d()}}}function eo(r,e,t){let n,i,s,o,l,{changedFiles:a}=e,{onApplyChanges:u}=e,{onOpenFile:c}=e,{pendingFiles:d=[]}=e,{appliedFiles:p=[]}=e,{isLoadingTreeView:F=!1}=e,v={},w=!1,E=!1;function b(z,m){t(6,v[z]=m,v)}return r.$$set=z=>{"changedFiles"in z&&t(0,a=z.changedFiles),"onApplyChanges"in z&&t(1,u=z.onApplyChanges),"onOpenFile"in z&&t(2,c=z.onOpenFile),"pendingFiles"in z&&t(3,d=z.pendingFiles),"appliedFiles"in z&&t(4,p=z.appliedFiles),"isLoadingTreeView"in z&&t(5,F=z.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&t(15,n=JSON.stringify(a)),16&r.$$.dirty&&t(13,i=JSON.stringify(p)),8&r.$$.dirty&&t(14,s=JSON.stringify(d)),32768&r.$$.dirty&&n&&(t(6,v={}),t(7,w=!1),t(8,E=!1)),65&r.$$.dirty&&t(9,l=a.map(z=>{const m=z.new_path||z.old_path,D=z.old_contents||"",C=z.new_contents||"",y=Ns.generateDiff(z.old_path,z.new_path,D,C),_=function(P,U){const Q=xt("oldFile","newFile",P,U,"","",{context:3}),G=ks(Q);let oe=0,ae=0,ce=[];for(const de of G)for(const J of de.hunks)for(const Fe of J.lines){const $e=Fe.startsWith("+"),ie=Fe.startsWith("-");$e&&oe++,ie&&ae++,ce.push({value:Fe,added:$e,removed:ie})}return{totalAddedLines:oe,totalRemovedLines:ae,changes:ce,diff:Q}}(D,C);return v[m]||t(6,v[m]=C,v),{qualifiedPathName:{rootPath:"",relPath:m},lineChanges:_,oldContents:D,newContents:C,diff:y}})),57880&r.$$.dirty&&t(10,o=(()=>{if(n&&i&&s){const z=l.map(m=>m.qualifiedPathName.relPath);return z.length!==0&&z.some(m=>!p.includes(m)&&!d.includes(m))}return!1})()),664&r.$$.dirty&&w){const z=l.map(m=>m.qualifiedPathName.relPath);z.filter(m=>!p.includes(m)&&!d.includes(m)).length===0&&z.every(m=>p.includes(m)||d.includes(m))&&d.length===0&&p.length>0&&(t(7,w=!1),t(8,E=!0))}if(9104&r.$$.dirty&&l.length>0&&!w&&i){const z=l.map(m=>m.qualifiedPathName.relPath);if(z.length>0){const m=z.every(D=>p.includes(D));m&&p.length>0?t(8,E=!0):!m&&E&&t(8,E=!1)}}},[a,u,c,d,p,F,v,w,E,l,o,b,function(){if(!u)return;const z=l.map(D=>D.qualifiedPathName.relPath);if(z.every(D=>p.includes(D)))return void t(8,E=!0);const m=z.filter(D=>!p.includes(D)&&!d.includes(D));m.length!==0&&(t(7,w=!0),t(8,E=!1),m.forEach(D=>{const C=l.find(y=>y.qualifiedPathName.relPath===D);if(C){const y=v[D]||C.newContents;u(D,C.oldContents,y)}}))},i,s,n,(z,m)=>{b(z.qualifiedPathName.relPath,m)},z=>{const m=v[z.qualifiedPathName.relPath]||z.newContents;u(z.qualifiedPathName.relPath,z.oldContents,m)},z=>c(z.qualifiedPathName.relPath)]}class to extends ee{constructor(e){super(),te(this,e,eo,Kr,Y,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function kn(r,e,t){const n=r.slice();return n[3]=e[t],n}function wn(r){let e,t=pe(r[1].paths),n=[];for(let i=0;i<t.length;i+=1)n[i]=vn(kn(r,t,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=xe()},m(i,s){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(i,s);g(i,e,s)},p(i,s){if(2&s){let o;for(t=pe(i[1].paths),o=0;o<t.length;o+=1){const l=kn(i,t,o);n[o]?n[o].p(l,s):(n[o]=vn(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(i){i&&$(e),Me(n,i)}}}function vn(r){let e,t;return{c(){e=qi("path"),x(e,"d",t=r[3]),x(e,"fill-rule","evenodd"),x(e,"clip-rule","evenodd")},m(n,i){g(n,e,i)},p(n,i){2&i&&t!==(t=n[3])&&x(e,"d",t)},d(n){n&&$(e)}}}function no(r){let e,t=r[1]&&wn(r);return{c(){e=qi("svg"),t&&t.c(),x(e,"width","14"),x(e,"viewBox","0 0 20 20"),x(e,"fill","currentColor"),x(e,"class","svelte-10h4f31")},m(n,i){g(n,e,i),t&&t.m(e,null)},p(n,i){n[1]?t?t.p(n,i):(t=wn(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&$(e),t&&t.d()}}}function io(r){let e,t;return e=new Ue({props:{content:`This is a ${r[0]} change`,triggerOn:[Xe.Hover],$$slots:{default:[no]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.content=`This is a ${n[0]} change`),66&i&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function so(r,e,t){let n,{type:i}=e;const s={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,i=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=s[i]??s.other)},[i,n]}class ro extends ee{constructor(e){super(),te(this,e,so,io,Y,{type:0})}}function yn(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function An(r){let e,t,n,i,s;t=new yt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[ao]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=bn(yn(r,o,u));const a=u=>h(l[u],1,1,()=>{l[u]=null});return{c(){e=A("div"),L(t.$$.fragment),n=j(),i=A("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(i,"class","descriptions svelte-14s1ghg"),Ae(i,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,i,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(i,null);s=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=pe(u[1]),p=0;p<o.length;p+=1){const F=yn(u,o,p);l[p]?(l[p].p(F,c),f(l[p],1)):(l[p]=bn(F),l[p].c(),f(l[p],1),l[p].m(i,null))}for(Z(),p=o.length;p<l.length;p+=1)a(p);H()}(!s||16&c[0])&&Ae(i,"transform","translateY("+-u[4]+"px)")},i(u){if(!s){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);s=!0}},o(u){h(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)h(l[c]);s=!1},d(u){u&&($(e),$(n),$(i)),R(t),Me(l,u)}}}function oo(r){let e,t;return e=new ct({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function lo(r){let e,t;return e=new ct({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function ao(r){let e,t,n,i;const s=[lo,oo],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function bn(r){let e,t,n,i;return t=new Gi({props:{markdown:r[47].text}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),x(e,"class","description svelte-14s1ghg"),Ae(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ae(e,"--user-theme-sidebar-background","transparent")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};2&o[0]&&(l.markdown=s[47].text),t.$set(l),(!i||34&o[0])&&Ae(e,"top",(s[5][s[49]]||s[9](s[47]))+"px")},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function uo(r){let e,t,n,i,s=r[1].length>0&&An(r);return{c(){e=A("div"),t=A("div"),n=j(),s&&s.c(),x(t,"class","editor-container svelte-14s1ghg"),Ae(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),ye(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),q(e,t),r[23](t),q(e,n),s&&s.m(e,null),i=!0},p(o,l){(!i||8&l[0])&&Ae(t,"height",o[3]+"px"),o[1].length>0?s?(s.p(o,l),2&l[0]&&f(s,1)):(s=An(o),s.c(),f(s,1),s.m(e,null)):s&&(Z(),h(s,1,1,()=>{s=null}),H()),(!i||3&l[0])&&ye(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){i||(f(s),i=!0)},o(o){h(s),i=!1},d(o){o&&$(e),r[23](null),s&&s.d()}}}function co(r,e,t){let n,i,s;const o=zi();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:F=[]}=e,{theme:v}=e,{areDescriptionsVisible:w=!0}=e,{isNewFile:E=!1}=e,{isDeletedFile:b=!1}=e;const z=Ot.getContext().monaco;let m,D,C,y;Le(r,z,k=>t(22,n=k));let _,P=[];const U=Pt();let Q,G=He(0);Le(r,G,k=>t(4,i=k));let oe=E?20*a.split(`
`).length+40:100;const ae=n?n.languages.getLanguages().map(k=>k.id):[];function ce(k,N){var S,V;if(N){const I=(S=N.split(".").pop())==null?void 0:S.toLowerCase();if(I){const W=(V=n==null?void 0:n.languages.getLanguages().find(K=>{var ne;return(ne=K.extensions)==null?void 0:ne.includes("."+I)}))==null?void 0:V.id;if(W&&ae.includes(W))return W}}return"plaintext"}const de=He({});Le(r,de,k=>t(5,s=k));let J=null;function Fe(){if(!m)return;P=P.filter(S=>(S.dispose(),!1));const k=m.getOriginalEditor(),N=m.getModifiedEditor();P.push(k.onDidScrollChange(()=>{Dt(G,i=k.getScrollTop(),i)}),N.onDidScrollChange(()=>{Dt(G,i=N.getScrollTop(),i)}))}function $e(){if(!m||!_)return;const k=m.getOriginalEditor(),N=m.getModifiedEditor();P.push(N.onDidContentSizeChange(()=>U.requestLayout()),k.onDidContentSizeChange(()=>U.requestLayout()),m.onDidUpdateDiff(()=>U.requestLayout()),N.onDidChangeHiddenAreas(()=>U.requestLayout()),k.onDidChangeHiddenAreas(()=>U.requestLayout()),N.onDidLayoutChange(()=>U.requestLayout()),k.onDidLayoutChange(()=>U.requestLayout()),N.onDidFocusEditorWidget(()=>{O(!0)}),k.onDidFocusEditorWidget(()=>{O(!0)}),N.onDidBlurEditorWidget(()=>{O(!1)}),k.onDidBlurEditorWidget(()=>{O(!1)}),N.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const S=(y==null?void 0:y.getValue())||"";if(S===a)return;const V=S.replace(p.join(""),"").replace(F.join(""),"");o("codeChange",{modifiedCode:V});const I=setTimeout(()=>{Ce=!1},500);P.push({dispose:()=>clearTimeout(I)})})),function(){!_||!m||(J&&clearTimeout(J),J=setTimeout(()=>{if(!_.__hasClickListener){const S=V=>{const I=V.target;I&&(I.closest('[title="Show Unchanged Region"]')||I.closest('[title="Hide Unchanged Region"]'))&&we()};_.addEventListener("click",S),t(2,_.__hasClickListener=!0,_),P.push({dispose:()=>{_.removeEventListener("click",S)}})}m&&P.push(m.onDidUpdateDiff(()=>{we()}))},300))}()}Li(()=>{m==null||m.dispose(),D==null||D.dispose(),C==null||C.dispose(),y==null||y.dispose(),P.forEach(k=>k.dispose()),J&&clearTimeout(J),Q==null||Q()});let ie=null;function we(){ie&&clearTimeout(ie),ie=setTimeout(()=>{U.requestLayout(),ie=null},100),ie&&P.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ve(k,N,S,V=[],I=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");C==null||C.dispose(),y==null||y.dispose(),N=N||"",S=S||"";const W=V.join(""),K=I.join("");if(N=E?S.split(`
`).map(()=>" ").join(`
`):W+N+K,S=W+S+K,C=n.editor.createModel(N,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0),b&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,y=n.editor.createModel(S,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:C,modified:y});const ne=m.getOriginalEditor();ne&&ne.updateOptions({lineNumbers:"off"}),Fe(),J&&clearTimeout(J),J=setTimeout(()=>{$e(),J=null},300)}}et(()=>{if(n)if(E){t(20,D=n.editor.create(_,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:v,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-p.length+V}`}));const k=ce(0,u);t(21,y=n.editor.createModel(a,k,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(y),P.push(D.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const V=(y==null?void 0:y.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const I=setTimeout(()=>{Ce=!1},500);P.push({dispose:()=>clearTimeout(I)})})),P.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const N=D.getContentHeight();t(3,oe=Math.max(N,60));const S=setTimeout(()=>{D==null||D.layout()},0);P.push({dispose:()=>clearTimeout(S)})}else t(19,m=n.editor.createDiffEditor(_,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:v,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:k=>`${d-p.length+k}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Q&&Q(),Q=U.registerEditor({editor:m,updateHeight:ke,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ve(u,l,a,p,F),Fe(),$e(),J&&clearTimeout(J),J=setTimeout(()=>{U.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,Re=0;function ue(k,N=!0){return m?(N?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(k):18*k}function ke(){if(!m)return;const k=m.getModel(),N=k==null?void 0:k.original,S=k==null?void 0:k.modified;if(!N||!S)return;const V=m.getOriginalEditor(),I=m.getModifiedEditor(),W=m.getLineChanges()||[];let K;if(W.length===0){const ne=V.getContentHeight(),De=I.getContentHeight();K=Math.max(100,ne,De)}else{let ne=0,De=0;for(const Te of W)Te.originalEndLineNumber>0&&(ne=Math.max(ne,Te.originalEndLineNumber)),Te.modifiedEndLineNumber>0&&(De=Math.max(De,Te.modifiedEndLineNumber));ne=Math.min(ne+3,N.getLineCount()),De=Math.min(De+3,S.getLineCount());const ze=V.getTopForLineNumber(ne),Pe=I.getTopForLineNumber(De);K=Math.max(ze,Pe)+60}t(3,oe=Math.min(K,2e4)),m.layout(),se()}function O(k){if(!m)return;const N=m.getOriginalEditor(),S=m.getModifiedEditor();N.updateOptions({scrollbar:{handleMouseWheel:k}}),S.updateOptions({scrollbar:{handleMouseWheel:k}})}function B(k){if(!m)return 0;const N=m.getModel(),S=N==null?void 0:N.original,V=N==null?void 0:N.modified;if(!S||!V)return 0;const I=ue(k.range.start+1,!1),W=ue(k.range.start+1,!0);return I&&!W?I:!I&&W?W:Math.min(I,W)}function se(){if(!m||c.length===0)return;const k={};c.forEach((N,S)=>{k[S]=B(N)}),de.set(k)}return r.$$set=k=>{"originalCode"in k&&t(10,l=k.originalCode),"modifiedCode"in k&&t(11,a=k.modifiedCode),"path"in k&&t(12,u=k.path),"descriptions"in k&&t(1,c=k.descriptions),"lineOffset"in k&&t(13,d=k.lineOffset),"extraPrefixLines"in k&&t(14,p=k.extraPrefixLines),"extraSuffixLines"in k&&t(15,F=k.extraSuffixLines),"theme"in k&&t(16,v=k.theme),"areDescriptionsVisible"in k&&t(0,w=k.areDescriptionsVisible),"isNewFile"in k&&t(17,E=k.isNewFile),"isDeletedFile"in k&&t(18,b=k.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(k=a,!(Ce||Date.now()-Re<1e3||y&&y.getValue()===p.join("")+k+F.join(""))))if(E&&D){if(y)y.setValue(a);else{const N=ce(0,u);n&&t(21,y=n.editor.createModel(a,N,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),y&&D.setModel(y)}t(3,oe=20*a.split(`
`).length+40),D.layout()}else!E&&m&&(ve(u,l,a,p,F),U.requestLayout());var k;if(524290&r.$$.dirty[0]&&m&&c.length>0&&se(),1181696&r.$$.dirty[0]&&E&&a&&D){const N=D.getContentHeight();t(3,oe=Math.max(N,60)),D.layout()}},[w,c,_,oe,i,s,z,G,de,B,l,a,u,d,p,F,v,E,b,m,D,y,n,function(k){qe[k?"unshift":"push"](()=>{_=k,t(2,_)})},()=>t(0,w=!w)]}class po extends ee{constructor(e){super(),te(this,e,co,uo,Y,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}}const fo=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],go=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],ts=1048576;function It(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function at(r){switch(It(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function En(r){const e=It(r);return fo.includes(e)}function _n(r){return r>ts}const $o=Symbol("focusedPath");function Bn(r){return`file-diff-${Ie(r)}`}function ho(r){let e,t,n;function i(o){r[41](o)}let s={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(s.areDescriptionsVisible=r[1]),e=new po({props:s}),qe.push(()=>Je(e,"areDescriptionsVisible",i)),e.$on("codeChange",r[26]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Ye(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){h(e.$$.fragment,o),n=!1},d(o){R(e,o)}}}function mo(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[xo]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};5888&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Do(r){let e,t,n;return t=new ge({props:{$$slots:{default:[vo]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};2101632&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Fo(r){let e,t,n,i;const s=[Ao,yo],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=s[t](r)),{c(){e=A("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),~t&&o[t].d()}}}function xo(r){let e,t,n,i,s,o,l,a=re(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=T('File "'),t=T(a),n=T('" is too large to display a diff (size: '),i=T(u),s=T(" bytes, max: "),o=T(ts),l=T(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,i,d),g(c,s,d),g(c,o,d),g(c,l,d)},p(c,d){4096&d[0]&&a!==(a=re(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(i,u)},d(c){c&&($(e),$(t),$(n),$(i),$(s),$(o),$(l))}}}function Co(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file modified: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function ko(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file deleted: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function wo(r){let e,t,n,i=re(r[12])+"";return{c(){e=T("Binary file added: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=re(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function vo(r){let e;function t(s,o){return s[21]||s[7]?wo:s[8]?ko:Co}let n=t(r),i=n(r);return{c(){i.c(),e=T(`
            No text preview available.`)},m(s,o){i.m(s,o),g(s,e,o)},p(s,o){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},d(s){s&&$(e),i.d(s)}}}function yo(r){let e,t,n,i,s,o,l,a;e=new ge({props:{class:"image-info-text",$$slots:{default:[_o]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&zn(r);return{c(){L(e.$$.fragment),t=j(),n=A("img"),o=j(),u&&u.c(),l=xe(),Ne(n.src,i="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",i),x(n,"alt",s="Current "+re(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Ne(n.src,i="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",i),(!a||4096&d[0]&&s!==(s="Current "+re(c[12])))&&x(n,"alt",s),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=zn(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(Z(),h(u,1,1,()=>{u=null}),H())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){h(e.$$.fragment,c),h(u),a=!1},d(c){c&&($(t),$(n),$(o),$(l)),R(e,c),u&&u.d(c)}}}function Ao(r){let e,t,n,i;e=new ge({props:{class:"image-info-text",$$slots:{default:[zo]},$$scope:{ctx:r}}});let s=r[0].originalCode&&Ln(r);return{c(){L(e.$$.fragment),t=j(),s&&s.c(),n=xe()},m(o,l){M(e,o,l),g(o,t,l),s&&s.m(o,l),g(o,n,l),i=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?s?(s.p(o,l),1&l[0]&&f(s,1)):(s=Ln(o),s.c(),f(s,1),s.m(n.parentNode,n)):s&&(Z(),h(s,1,1,()=>{s=null}),H())},i(o){i||(f(e.$$.fragment,o),f(s),i=!0)},o(o){h(e.$$.fragment,o),h(s),i=!1},d(o){o&&($(t),$(n)),R(e,o),s&&s.d(o)}}}function bo(r){let e;return{c(){e=T("Image modified")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Eo(r){let e;return{c(){e=T("New image added")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function _o(r){let e,t,n=re(r[12])+"";function i(l,a){return l[21]||l[7]?Eo:bo}let s=i(r),o=s(r);return{c(){o.c(),e=T(": "),t=T(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){s!==(s=i(l))&&(o.d(1),o=s(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=re(l[12])+"")&&le(t,n)},d(l){l&&($(e),$(t)),o.d(l)}}}function zn(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[Bo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),n=A("img"),Ne(n.src,i="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+re(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+re(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function Bo(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function zo(r){let e,t,n=re(r[12])+"";return{c(){e=T("Image deleted: "),t=T(n)},m(i,s){g(i,e,s),g(i,t,s)},p(i,s){4096&s[0]&&n!==(n=re(i[12])+"")&&le(t,n)},d(i){i&&($(e),$(t))}}}function Ln(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[Lo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),n=A("img"),Ne(n.src,i="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+re(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+re(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function Lo(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Mo(r){let e,t,n,i;const s=[Fo,Do,mo,ho],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=s[t](r),{c(){e=A("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function Ro(r){let e,t=re(r[12])+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){4096&i[0]&&t!==(t=re(n[12])+"")&&le(e,t)},d(n){n&&$(e)}}}function qo(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[Ro]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};4096&i[0]|16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Mn(r){let e,t,n=We(r[12])+"";return{c(){e=A("span"),t=T(n),x(e,"class","c-directory svelte-1536g7w")},m(i,s){g(i,e,s),q(e,t)},p(i,s){4096&s[0]&&n!==(n=We(i[12])+"")&&le(t,n)},d(i){i&&$(e)}}}function No(r){let e,t,n,i=r[23]>0&&Rn(r),s=r[22]>0&&qn(r);return{c(){e=A("div"),i&&i.c(),t=j(),s&&s.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),i&&i.m(e,null),q(e,t),s&&s.m(e,null),n=!0},p(o,l){o[23]>0?i?(i.p(o,l),8388608&l[0]&&f(i,1)):(i=Rn(o),i.c(),f(i,1),i.m(e,t)):i&&(Z(),h(i,1,1,()=>{i=null}),H()),o[22]>0?s?(s.p(o,l),4194304&l[0]&&f(s,1)):(s=qn(o),s.c(),f(s,1),s.m(e,null)):s&&(Z(),h(s,1,1,()=>{s=null}),H())},i(o){n||(f(i),f(s),n=!0)},o(o){h(i),h(s),n=!1},d(o){o&&$(e),i&&i.d(),s&&s.d()}}}function To(r){let e;return{c(){e=A("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&$(e)}}}function Rn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Oo]},$$scope:{ctx:r}}}),{c(){e=A("span"),L(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};8388608&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Oo(r){let e,t;return{c(){e=T("+"),t=T(r[23])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){8388608&i[0]&&le(t,n[23])},d(n){n&&($(e),$(t))}}}function qn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Po]},$$scope:{ctx:r}}}),{c(){e=A("span"),L(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s[0]|16384&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Po(r){let e,t;return{c(){e=T("-"),t=T(r[22])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){4194304&i[0]&&le(t,n[22])},d(n){n&&($(e),$(t))}}}function So(r){let e;return{c(){e=T("Apply")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Io(r){let e;return{c(){e=T("Applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function jo(r){let e,t,n;return t=new ut({}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Vo(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Uo(r){let e,t,n,i,s;function o(p,F){return p[5]?Io:So}let l=o(r),a=l(r);const u=[Vo,jo],c=[];function d(p,F){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=j(),n.c(),i=xe()},m(p,F){a.m(p,F),g(p,e,F),c[t].m(p,F),g(p,i,F),s=!0},p(p,F){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let v=t;t=d(p),t!==v&&(Z(),h(c[v],1,1,()=>{c[v]=null}),H(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(i.parentNode,i))},i(p){s||(f(n),s=!0)},o(p){h(n),s=!1},d(p){p&&($(e),$(i)),a.d(p),c[t].d(p)}}}function Zo(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Uo]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[0]&&(s.disabled=n[14]),32&i[0]|16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Nn(r){let e,t;return e=new Ue({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Wo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2048&i[0]&&(s.content=n[11]),16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Ho(r){let e,t;return e=new Zi({}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Wo(r){let e,t;return e=new yt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Ho]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Qo(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,v=We(r[12]);t=new ji({}),s=new Ue({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[qo]},$$scope:{ctx:r}}});let w=v&&Mn(r);const E=[To,No],b=[];function z(D,C){return D[21]?0:1}a=z(r),u=b[a]=E[a](r),d=new Ue({props:{content:r[13],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Zo]},$$scope:{ctx:r}}});let m=r[5]&&Nn(r);return{c(){e=A("div"),L(t.$$.fragment),n=j(),i=A("div"),L(s.$$.fragment),o=j(),w&&w.c(),l=j(),u.c(),c=j(),L(d.$$.fragment),p=j(),m&&m.c(),x(i,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,C){g(D,e,C),M(t,e,null),q(e,n),q(e,i),M(s,i,null),q(i,o),w&&w.m(i,null),q(e,l),b[a].m(e,null),q(e,c),M(d,e,null),q(e,p),m&&m.m(e,null),F=!0},p(D,C){const y={};2048&C[0]&&(y.content=D[11]),4096&C[0]|16384&C[1]&&(y.$$scope={dirty:C,ctx:D}),s.$set(y),4096&C[0]&&(v=We(D[12])),v?w?w.p(D,C):(w=Mn(D),w.c(),w.m(i,null)):w&&(w.d(1),w=null);let _=a;a=z(D),a===_?b[a].p(D,C):(Z(),h(b[_],1,1,()=>{b[_]=null}),H(),u=b[a],u?u.p(D,C):(u=b[a]=E[a](D),u.c()),f(u,1),u.m(e,c));const P={};8192&C[0]&&(P.content=D[13]),16416&C[0]|16384&C[1]&&(P.$$scope={dirty:C,ctx:D}),d.$set(P),D[5]?m?(m.p(D,C),32&C[0]&&f(m,1)):(m=Nn(D),m.c(),f(m,1),m.m(e,null)):m&&(Z(),h(m,1,1,()=>{m=null}),H())},i(D){F||(f(t.$$.fragment,D),f(s.$$.fragment,D),f(u),f(d.$$.fragment,D),f(m),F=!0)},o(D){h(t.$$.fragment,D),h(s.$$.fragment,D),h(u),h(d.$$.fragment,D),h(m),F=!1},d(D){D&&$(e),R(t),R(s),w&&w.d(),b[a].d(),R(d),m&&m.d()}}}function Go(r){let e,t,n,i,s;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Qo],default:[Mo]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ii({props:l}),qe.push(()=>Je(t,"collapsed",o)),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",i=Bn(r[3])),ye(e,"focused",r[24]===r[3])},m(a,u){g(a,e,u),M(t,e,null),s=!0},p(a,u){const c={};16777211&u[0]|16384&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Ye(()=>n=!1)),t.$set(c),(!s||8&u[0]&&i!==(i=Bn(a[3])))&&x(e,"id",i),(!s||16777224&u[0])&&ye(e,"focused",a[24]===a[3])},i(a){s||(f(t.$$.fragment,a),s=!0)},o(a){h(t.$$.fragment,a),s=!1},d(a){a&&$(e),R(t)}}}function Jo(r,e,t){let n,i,s,o,l,a,u,c,d,p,F,v,w,E,b,z,m,D,C,y,_,P,U;Le(r,Mi,B=>t(40,P=B));let{path:Q}=e,{change:G}=e,{descriptions:oe=[]}=e,{areDescriptionsVisible:ae=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:de=!ce}=e,{isApplying:J}=e,{hasApplied:Fe}=e,{onApplyChanges:$e}=e,{onCodeChange:ie}=e,{onOpenFile:we}=e,{isAgentFromDifferentRepo:ve=!1}=e;const Ce=Ge($o);Le(r,Ce,B=>t(24,U=B));const Re=Ge(zs.key);let ue=G.modifiedCode,ke=C;function O(){t(11,ke=`Open ${C??"file"}`)}return et(()=>{O()}),r.$$set=B=>{"path"in B&&t(3,Q=B.path),"change"in B&&t(0,G=B.change),"descriptions"in B&&t(4,oe=B.descriptions),"areDescriptionsVisible"in B&&t(1,ae=B.areDescriptionsVisible),"isExpandedDefault"in B&&t(29,ce=B.isExpandedDefault),"isCollapsed"in B&&t(2,de=B.isCollapsed),"isApplying"in B&&t(30,J=B.isApplying),"hasApplied"in B&&t(5,Fe=B.hasApplied),"onApplyChanges"in B&&t(31,$e=B.onApplyChanges),"onCodeChange"in B&&t(32,ie=B.onCodeChange),"onOpenFile"in B&&t(33,we=B.onOpenFile),"isAgentFromDifferentRepo"in B&&t(34,ve=B.isAgentFromDifferentRepo)},r.$$.update=()=>{var B;1&r.$$.dirty[0]&&t(6,ue=G.modifiedCode),1&r.$$.dirty[0]&&t(39,n=vs(G.diff)),256&r.$$.dirty[1]&&t(23,i=n.additions),256&r.$$.dirty[1]&&t(22,s=n.deletions),1&r.$$.dirty[0]&&t(21,o=ys(G)),1&r.$$.dirty[0]&&t(20,l=As(G)),8&r.$$.dirty[0]&&t(38,a=En(Q)),8&r.$$.dirty[0]&&t(19,u=at(Q)),8&r.$$.dirty[0]&&t(37,c=function(se){if(En(se))return!1;const k=It(se);return go.includes(k)}(Q)),1&r.$$.dirty[0]&&t(10,d=((B=G.originalCode)==null?void 0:B.length)||0),64&r.$$.dirty[0]&&t(9,p=(ue==null?void 0:ue.length)||0),1024&r.$$.dirty[0]&&t(36,F=_n(d)),512&r.$$.dirty[0]&&t(35,v=_n(p)),65&r.$$.dirty[0]&&t(8,w=!ue&&!!G.originalCode),65&r.$$.dirty[0]&&t(7,E=!!ue&&!G.originalCode),128&r.$$.dirty[1]&&t(18,b=a),192&r.$$.dirty[1]&&t(17,z=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!c&&(v||w&&F||E&&v)),512&r.$$.dirty[1]&&t(15,D=Vi(P==null?void 0:P.category,P==null?void 0:P.intensity)),8&r.$$.dirty[0]&&t(12,C=Ui(Q)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,y=J||ve),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,_=J?"Applying changes...":Fe?"Reapply changes to local file":ve?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[G,ae,de,Q,oe,Fe,ue,E,w,p,d,ke,C,_,y,D,m,z,b,u,l,o,s,i,U,Ce,function(B){t(6,ue=B.detail.modifiedCode),ie==null||ie(ue)},function(){Re.reportApplyChangesEvent(),t(0,G.modifiedCode=ue,G),ie==null||ie(ue),$e==null||$e()},async function(){we&&(t(11,ke="Opening file..."),await we()?O():(t(11,ke="Failed to open file. Does the file exist?"),setTimeout(()=>{O()},2e3)))},ce,J,$e,ie,we,ve,v,F,c,a,n,P,function(B){ae=B,t(1,ae)},function(B){de=B,t(2,de)}]}class Yo extends ee{constructor(e){super(),te(this,e,Jo,Go,Y,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function Tn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function Xo(r,e,t){const n=r.slice();return n[1]=e[t],n}function Ko(r,e,t){const n=r.slice();return n[1]=e[t],n}function el(r){let e;return{c(){e=A("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',x(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){g(t,e,n)},p:X,d(t){t&&$(e)}}}function tl(r){let e,t,n,i,s=pe(Array(2)),o=[];for(let l=0;l<s.length;l+=1)o[l]=el(Ko(r,s,l));return{c(){e=A("div"),t=A("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=j(),i=A("div");for(let l=0;l<o.length;l+=1)o[l].c();x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(i,"class","c-skeleton-diff__changes svelte-1eiztmz"),x(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){g(l,e,a),q(e,t),q(e,n),q(e,i);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(i,null)},p:X,d(l){l&&$(e),Me(o,l)}}}function On(r){let e,t,n,i,s,o,l=r[3]===0&&function(c){let d;return{c(){d=A("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',x(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,F){g(p,d,F)},d(p){p&&$(d)}}}(),a=pe(Array(2)),u=[];for(let c=0;c<a.length;c+=1)u[c]=tl(Xo(r,a,c));return{c(){e=A("div"),t=A("div"),n=A("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',i=j(),l&&l.c(),s=j();for(let c=0;c<u.length;c+=1)u[c].c();o=j(),x(n,"class","c-skeleton-diff__content svelte-1eiztmz"),x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){g(c,e,d),q(e,t),q(t,n),q(t,i),l&&l.m(t,null),q(e,s);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(e,null);q(e,o)},p(c,d){},d(c){c&&$(e),l&&l.d(),Me(u,c)}}}function nl(r){let e,t=pe(Array(r[0])),n=[];for(let i=0;i<t.length;i+=1)n[i]=On(Tn(r,t,i));return{c(){e=A("div");for(let i=0;i<n.length;i+=1)n[i].c();x(e,"class","c-skeleton-diff svelte-1eiztmz")},m(i,s){g(i,e,s);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(i,[s]){if(1&s){let o;for(t=pe(Array(i[0])),o=0;o<t.length;o+=1){const l=Tn(i,t,o);n[o]?n[o].p(l,s):(n[o]=On(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:X,o:X,d(i){i&&$(e),Me(n,i)}}}function il(r,e,t){let{count:n=2}=e;return r.$$set=i=>{"count"in i&&t(0,n=i.count)},[n]}class sl extends ee{constructor(e){super(),te(this,e,il,nl,Y,{count:0})}}function Pn(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Sn(r){return r.startsWith("/")||r.startsWith("#")}function _t(r){let e,t;const n=r[5].default,i=be(n,r,r[4],null);let s=[{id:r[1]}],o={};for(let l=0;l<s.length;l+=1)o=Ni(o,s[l]);return{c(){e=A(`h${r[0].depth}`),i&&i.c(),Ft(`h${r[0].depth}`)(e,o)},m(l,a){g(l,e,a),i&&i.m(e,null),t=!0},p(l,a){i&&i.p&&(!t||16&a)&&Ee(i,n,l,l[4],t?Be(n,l[4],a,null):_e(l[4]),null),Ft(`h${l[0].depth}`)(e,o=Ti(s,[(!t||2&a)&&{id:l[1]}]))},i(l){t||(f(i,l),t=!0)},o(l){h(i,l),t=!1},d(l){l&&$(e),i&&i.d(l)}}}function rl(r){let e,t,n=`h${r[0].depth}`,i=`h${r[0].depth}`&&_t(r);return{c(){i&&i.c(),e=xe()},m(s,o){i&&i.m(s,o),g(s,e,o),t=!0},p(s,[o]){`h${s[0].depth}`?n?Y(n,`h${s[0].depth}`)?(i.d(1),i=_t(s),n=`h${s[0].depth}`,i.c(),i.m(e.parentNode,e)):i.p(s,o):(i=_t(s),n=`h${s[0].depth}`,i.c(),i.m(e.parentNode,e)):n&&(i.d(1),i=null,n=`h${s[0].depth}`)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function ol(r,e,t){let{$$slots:n={},$$scope:i}=e,{token:s}=e,{options:o}=e,l;return r.$$set=a=>{"token"in a&&t(0,s=a.token),"options"in a&&t(2,o=a.options),"$$scope"in a&&t(4,i=a.$$scope)},r.$$.update=()=>{var a,u;5&r.$$.dirty&&t(1,(a=s.text,u=o.slugger,l=u.slug(a).replace(/--+/g,"-")))},[s,l,o,void 0,i,n]}class ll extends ee{constructor(e){super(),te(this,e,ol,rl,Y,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function al(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("blockquote"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function ul(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class cl extends ee{constructor(e){super(),te(this,e,ul,al,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function In(r,e,t){const n=r.slice();return n[3]=e[t],n}function jn(r){let e,t,n=pe(r[0]),i=[];for(let o=0;o<n.length;o+=1)i[o]=Vn(In(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(7&l){let a;for(n=pe(o[0]),a=0;a<n.length;a+=1){const u=In(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=Vn(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(Z(),a=n.length;a<i.length;a+=1)s(a);H()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Me(i,o)}}}function Vn(r){let e,t;return e=new ns({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.token=n[3]),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function dl(r){let e,t,n=r[0]&&jn(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s),t=!0},p(i,[s]){i[0]?n?(n.p(i,s),1&s&&f(n,1)):(n=jn(i),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(Z(),h(n,1,1,()=>{n=null}),H())},i(i){t||(f(n),t=!0)},o(i){h(n),t=!1},d(i){i&&$(e),n&&n.d(i)}}}function pl(r,e,t){let{tokens:n}=e,{renderers:i}=e,{options:s}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,i=o.renderers),"options"in o&&t(2,s=o.options)},[n,i,s]}class bt extends ee{constructor(e){super(),te(this,e,pl,dl,Y,{tokens:0,renderers:1,options:2})}}function Un(r){let e,t,n;var i=r[1][r[0].type];function s(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[$l]},$$scope:{ctx:o}}}}return i&&(e=Yt(i,s(r))),{c(){e&&L(e.$$.fragment),t=xe()},m(o,l){e&&M(e,o,l),g(o,t,l),n=!0},p(o,l){if(3&l&&i!==(i=o[1][o[0].type])){if(e){Z();const a=e;h(a.$$.fragment,1,0,()=>{R(a,1)}),H()}i?(e=Yt(i,s(o)),L(e.$$.fragment),f(e.$$.fragment,1),M(e,t.parentNode,t)):e=null}else if(i){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)}},i(o){n||(e&&f(e.$$.fragment,o),n=!0)},o(o){e&&h(e.$$.fragment,o),n=!1},d(o){o&&$(t),e&&R(e,o)}}}function fl(r){let e,t=r[0].raw+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){1&i&&t!==(t=n[0].raw+"")&&le(e,t)},i:X,o:X,d(n){n&&$(e)}}}function gl(r){let e,t;return e=new bt({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.tokens=n[0].tokens),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function $l(r){let e,t,n,i;const s=[gl,fl],o=[];function l(a,u){return"tokens"in a[0]&&a[0].tokens?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t?t.p(a,u):(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function hl(r){let e,t,n=r[1][r[0].type]&&Un(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s),t=!0},p(i,[s]){i[1][i[0].type]?n?(n.p(i,s),3&s&&f(n,1)):(n=Un(i),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(Z(),h(n,1,1,()=>{n=null}),H())},i(i){t||(f(n),t=!0)},o(i){h(n),t=!1},d(i){i&&$(e),n&&n.d(i)}}}function ml(r,e,t){let{token:n}=e,{renderers:i}=e,{options:s}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,i=o.renderers),"options"in o&&t(2,s=o.options)},[n,i,s]}class ns extends ee{constructor(e){super(),te(this,e,ml,hl,Y,{token:0,renderers:1,options:2})}}function Zn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Hn(r){let e,t;return e=new ns({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.token={...n[4]}),2&i&&(s.options=n[1]),4&i&&(s.renderers=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Bt(r){let e,t,n,i=pe(r[0].items),s=[];for(let u=0;u<i.length;u+=1)s[u]=Hn(Zn(r,i,u));const o=u=>h(s[u],1,1,()=>{s[u]=null});let l=[{start:t=r[0].start||1}],a={};for(let u=0;u<l.length;u+=1)a=Ni(a,l[u]);return{c(){e=A(r[3]);for(let u=0;u<s.length;u+=1)s[u].c();Ft(r[3])(e,a)},m(u,c){g(u,e,c);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(e,null);n=!0},p(u,c){if(7&c){let d;for(i=pe(u[0].items),d=0;d<i.length;d+=1){const p=Zn(u,i,d);s[d]?(s[d].p(p,c),f(s[d],1)):(s[d]=Hn(p),s[d].c(),f(s[d],1),s[d].m(e,null))}for(Z(),d=i.length;d<s.length;d+=1)o(d);H()}Ft(u[3])(e,a=Ti(l,[(!n||1&c&&t!==(t=u[0].start||1))&&{start:t}]))},i(u){if(!n){for(let c=0;c<i.length;c+=1)f(s[c]);n=!0}},o(u){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)h(s[c]);n=!1},d(u){u&&$(e),Me(s,u)}}}function Dl(r){let e,t=r[3],n=r[3]&&Bt(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s)},p(i,[s]){i[3]?t?Y(t,i[3])?(n.d(1),n=Bt(i),t=i[3],n.c(),n.m(e.parentNode,e)):n.p(i,s):(n=Bt(i),t=i[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=i[3])},i:X,o(i){h(n,i)},d(i){i&&$(e),n&&n.d(i)}}}function Fl(r,e,t){let n,{token:i}=e,{options:s}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,s=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=i.ordered?"ol":"ul")},[i,s,o,n]}class xl extends ee{constructor(e){super(),te(this,e,Fl,Dl,Y,{token:0,options:1,renderers:2})}}function Cl(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("li"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function kl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class wl extends ee{constructor(e){super(),te(this,e,kl,Cl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function vl(r){let e;return{c(){e=A("br")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&$(e)}}}function yl(r,e,t){return[void 0,void 0,void 0]}class Al extends ee{constructor(e){super(),te(this,e,yl,vl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function bl(r){let e,t,n,i,s=r[0].text+"";return{c(){e=A("pre"),t=A("code"),n=T(s),x(t,"class",i=`lang-${r[0].lang}`)},m(o,l){g(o,e,l),q(e,t),q(t,n)},p(o,[l]){1&l&&s!==(s=o[0].text+"")&&le(n,s),1&l&&i!==(i=`lang-${o[0].lang}`)&&x(t,"class",i)},i:X,o:X,d(o){o&&$(e)}}}function El(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class _l extends ee{constructor(e){super(),te(this,e,El,bl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Bl(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=A("code"),t=T(n)},m(i,s){g(i,e,s),q(e,t)},p(i,[s]){1&s&&n!==(n=i[0].raw.slice(1,i[0].raw.length-1)+"")&&le(t,n)},i:X,o:X,d(i){i&&$(e)}}}function zl(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class Ll extends ee{constructor(e){super(),te(this,e,zl,Bl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Qn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Gn(r,e,t){const n=r.slice();return n[9]=e[t],n}function Jn(r){let e,t,n,i;return t=new bt({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=A("th"),L(t.$$.fragment),n=j(),x(e,"scope","col")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};1&o&&(l.tokens=s[9].tokens),2&o&&(l.options=s[1]),4&o&&(l.renderers=s[2]),t.$set(l)},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function Yn(r){let e,t,n;return t=new bt({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=A("td"),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};1&s&&(o.tokens=i[6].tokens),2&s&&(o.options=i[1]),4&s&&(o.renderers=i[2]),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Xn(r){let e,t,n,i=pe(r[3]),s=[];for(let l=0;l<i.length;l+=1)s[l]=Yn(Qn(r,i,l));const o=l=>h(s[l],1,1,()=>{s[l]=null});return{c(){e=A("tr");for(let l=0;l<s.length;l+=1)s[l].c();t=j()},m(l,a){g(l,e,a);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);q(e,t),n=!0},p(l,a){if(7&a){let u;for(i=pe(l[3]),u=0;u<i.length;u+=1){const c=Qn(l,i,u);s[u]?(s[u].p(c,a),f(s[u],1)):(s[u]=Yn(c),s[u].c(),f(s[u],1),s[u].m(e,t))}for(Z(),u=i.length;u<s.length;u+=1)o(u);H()}},i(l){if(!n){for(let a=0;a<i.length;a+=1)f(s[a]);n=!0}},o(l){s=s.filter(Boolean);for(let a=0;a<s.length;a+=1)h(s[a]);n=!1},d(l){l&&$(e),Me(s,l)}}}function Ml(r){let e,t,n,i,s,o,l=pe(r[0].header),a=[];for(let F=0;F<l.length;F+=1)a[F]=Jn(Gn(r,l,F));const u=F=>h(a[F],1,1,()=>{a[F]=null});let c=pe(r[0].rows),d=[];for(let F=0;F<c.length;F+=1)d[F]=Xn(Wn(r,c,F));const p=F=>h(d[F],1,1,()=>{d[F]=null});return{c(){e=A("table"),t=A("thead"),n=A("tr");for(let F=0;F<a.length;F+=1)a[F].c();i=j(),s=A("tbody");for(let F=0;F<d.length;F+=1)d[F].c()},m(F,v){g(F,e,v),q(e,t),q(t,n);for(let w=0;w<a.length;w+=1)a[w]&&a[w].m(n,null);q(e,i),q(e,s);for(let w=0;w<d.length;w+=1)d[w]&&d[w].m(s,null);o=!0},p(F,[v]){if(7&v){let w;for(l=pe(F[0].header),w=0;w<l.length;w+=1){const E=Gn(F,l,w);a[w]?(a[w].p(E,v),f(a[w],1)):(a[w]=Jn(E),a[w].c(),f(a[w],1),a[w].m(n,null))}for(Z(),w=l.length;w<a.length;w+=1)u(w);H()}if(7&v){let w;for(c=pe(F[0].rows),w=0;w<c.length;w+=1){const E=Wn(F,c,w);d[w]?(d[w].p(E,v),f(d[w],1)):(d[w]=Xn(E),d[w].c(),f(d[w],1),d[w].m(s,null))}for(Z(),w=c.length;w<d.length;w+=1)p(w);H()}},i(F){if(!o){for(let v=0;v<l.length;v+=1)f(a[v]);for(let v=0;v<c.length;v+=1)f(d[v]);o=!0}},o(F){a=a.filter(Boolean);for(let v=0;v<a.length;v+=1)h(a[v]);d=d.filter(Boolean);for(let v=0;v<d.length;v+=1)h(d[v]);o=!1},d(F){F&&$(e),Me(a,F),Me(d,F)}}}function Rl(r,e,t){let{token:n}=e,{options:i}=e,{renderers:s}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,i=o.options),"renderers"in o&&t(2,s=o.renderers)},[n,i,s]}class ql extends ee{constructor(e){super(),te(this,e,Rl,Ml,Y,{token:0,options:1,renderers:2})}}function Nl(r){let e,t,n=r[0].text+"";return{c(){e=new hs(!1),t=xe(),e.a=t},m(i,s){e.m(n,i,s),g(i,t,s)},p(i,[s]){1&s&&n!==(n=i[0].text+"")&&e.p(n)},i:X,o:X,d(i){i&&($(t),e.d())}}}function Tl(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class Ol extends ee{constructor(e){super(),te(this,e,Tl,Nl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Pl(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("p"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Sl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}let Il=class extends ee{constructor(r){super(),te(this,r,Sl,Pl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function jl(r){let e,t,n,i;const s=r[4].default,o=be(s,r,r[3],null);return{c(){e=A("a"),o&&o.c(),x(e,"href",t=Sn(r[0].href)?Pn(r[1].baseUrl,r[0].href):r[0].href),x(e,"title",n=r[0].title)},m(l,a){g(l,e,a),o&&o.m(e,null),i=!0},p(l,[a]){o&&o.p&&(!i||8&a)&&Ee(o,s,l,l[3],i?Be(s,l[3],a,null):_e(l[3]),null),(!i||3&a&&t!==(t=Sn(l[0].href)?Pn(l[1].baseUrl,l[0].href):l[0].href))&&x(e,"href",t),(!i||1&a&&n!==(n=l[0].title))&&x(e,"title",n)},i(l){i||(f(o,l),i=!0)},o(l){h(o,l),i=!1},d(l){l&&$(e),o&&o.d(l)}}}function Vl(r,e,t){let{$$slots:n={},$$scope:i}=e,{token:s}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,i=l.$$scope)},[s,o,void 0,i,n]}class Ul extends ee{constructor(e){super(),te(this,e,Vl,jl,Y,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Zl(r){let e;const t=r[4].default,n=be(t,r,r[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||8&s)&&Ee(n,t,i,i[3],e?Be(t,i[3],s,null):_e(i[3]),null)},i(i){e||(f(n,i),e=!0)},o(i){h(n,i),e=!1},d(i){n&&n.d(i)}}}function Hl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Wl extends ee{constructor(e){super(),te(this,e,Hl,Zl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ql(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("dfn"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Gl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Jl extends ee{constructor(e){super(),te(this,e,Gl,Ql,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Yl(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("del"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Xl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Kl extends ee{constructor(e){super(),te(this,e,Xl,Yl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ea(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("em"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function ta(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class na extends ee{constructor(e){super(),te(this,e,ta,ea,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ia(r){let e;return{c(){e=A("hr")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&$(e)}}}function sa(r,e,t){return[void 0,void 0,void 0]}class ra extends ee{constructor(e){super(),te(this,e,sa,ia,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function oa(r){let e,t;const n=r[4].default,i=be(n,r,r[3],null);return{c(){e=A("strong"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&Ee(i,n,s,s[3],t?Be(n,s[3],o,null):_e(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function la(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class aa extends ee{constructor(e){super(),te(this,e,la,oa,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ua(r){let e,t,n,i;return{c(){e=A("img"),Ne(e.src,t=r[0].href)||x(e,"src",t),x(e,"title",n=r[0].title),x(e,"alt",i=r[0].text),x(e,"class","markdown-image svelte-z38cge")},m(s,o){g(s,e,o)},p(s,[o]){1&o&&!Ne(e.src,t=s[0].href)&&x(e,"src",t),1&o&&n!==(n=s[0].title)&&x(e,"title",n),1&o&&i!==(i=s[0].text)&&x(e,"alt",i)},i:X,o:X,d(s){s&&$(e)}}}function ca(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class da extends ee{constructor(e){super(),te(this,e,ca,ua,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function pa(r){let e;const t=r[4].default,n=be(t,r,r[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||8&s)&&Ee(n,t,i,i[3],e?Be(t,i[3],s,null):_e(i[3]),null)},i(i){e||(f(n,i),e=!0)},o(i){h(n,i),e=!1},d(i){n&&n.d(i)}}}function fa(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Kn extends ee{constructor(e){super(),te(this,e,fa,pa,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ga(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let tt={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function ei(r){tt=r}const is=/[&<>"']/,$a=new RegExp(is.source,"g"),ss=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ha=new RegExp(ss.source,"g"),ma={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ti=r=>ma[r];function Oe(r,e){if(e){if(is.test(r))return r.replace($a,ti)}else if(ss.test(r))return r.replace(ha,ti);return r}const Da=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Fa(r){return r.replace(Da,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const xa=/(^|[^\[])\^/g;function me(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(i,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(xa,"$1"),t=t.replace(i,o),n},getRegex:()=>new RegExp(t,e)};return n}function ni(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const st={exec:()=>null};function ii(r,e){const t=r.replace(/\|/g,(i,s,o)=>{let l=!1,a=s;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function ht(r,e,t){const n=r.length;if(n===0)return"";let i=0;for(;i<n;){const s=r.charAt(n-i-1);if(s!==e||t){if(s===e||!t)break;i++}else i++}return r.slice(0,n-i)}function si(r,e,t,n){const i=e.href,s=e.title?Oe(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:i,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:i,title:s,text:Oe(o)}}class kt{constructor(e){fe(this,"options");fe(this,"rules");fe(this,"lexer");this.options=e||tt}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:ht(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],i=function(s,o){const l=s.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(u=>{const c=u.match(/^\s+/);if(c===null)return u;const[d]=c;return d.length>=a.length?u.slice(a.length):u}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const i=ht(n,"#");this.options.pedantic?n=i.trim():i&&!/ $/.test(i)||(n=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=ht(t[0].replace(/^ *>[ \t]?/gm,""),`
`),i=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=i,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const i=n.length>1,s={type:"list",raw:"",ordered:i,start:i?+n.slice(0,-1):"",loose:!1,items:[]};n=i?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=i?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",u=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,b=>" ".repeat(3*b.length)),p=e.split(`
`,1)[0],F=0;this.options.pedantic?(F=2,a=d.trimStart()):(F=t[2].search(/[^ ]/),F=F>4?1:F,a=d.slice(F),F+=t[1].length);let v=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,e=e.substring(p.length+1),c=!0),!c){const b=new RegExp(`^ {0,${Math.min(3,F-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),z=new RegExp(`^ {0,${Math.min(3,F-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),m=new RegExp(`^ {0,${Math.min(3,F-1)}}(?:\`\`\`|~~~)`),D=new RegExp(`^ {0,${Math.min(3,F-1)}}#`);for(;e;){const C=e.split(`
`,1)[0];if(p=C,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),m.test(p)||D.test(p)||b.test(p)||z.test(e))break;if(p.search(/[^ ]/)>=F||!p.trim())a+=`
`+p.slice(F);else{if(v||d.search(/[^ ]/)>=4||m.test(d)||D.test(d)||z.test(d))break;a+=`
`+p}v||p.trim()||(v=!0),l+=C+`
`,e=e.substring(C.length+1),d=p.slice(F)}}s.loose||(u?s.loose=!0:/\n *\n *$/.test(l)&&(u=!0));let w,E=null;this.options.gfm&&(E=/^\[[ xX]\] /.exec(a),E&&(w=E[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:l,task:!!E,checked:w,loose:!1,text:a,tokens:[]}),s.raw+=l}s.items[s.items.length-1].raw=l.trimEnd(),s.items[s.items.length-1].text=a.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const d=s.items[c].tokens.filter(F=>F.type==="space"),p=d.length>0&&d.some(F=>/\n.*\n/.test(F.raw));s.loose=p}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),i=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:i,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=ii(t[1]),i=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===i.length){for(const l of i)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of s)o.rows.push(ii(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Oe(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=ht(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let u=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===a[0])u++;else if(l[c]===a[1]&&(u--,u<0))return c;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let i=t[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);o&&(i=o[1],s=o[3])}else s=t[3]?t[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(i=this.options.pedantic&&!/>$/.test(n)?i.slice(1):i.slice(1,-1)),si(t,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const i=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!i){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return si(n,i,n[0],this.lexer)}}emStrong(e,t,n=""){let i=this.rules.inline.emStrongLDelim.exec(e);if(i&&!(i[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(i[1]||i[2])||!n||this.rules.inline.punctuation.exec(n))){const s=[...i[0]].length-1;let o,l,a=s,u=0;const c=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(i=c.exec(t))!=null;){if(o=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!o)continue;if(l=[...o].length,i[3]||i[4]){a+=l;continue}if((i[5]||i[6])&&s%3&&!((s+l)%3)){u+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+u);const d=[...i[0]][0].length,p=e.slice(0,s+i.index+d+l);if(Math.min(s,l)%2){const v=p.slice(1,-1);return{type:"em",raw:p,text:v,tokens:this.lexer.inlineTokens(v)}}const F=p.slice(2,-2);return{type:"strong",raw:p,text:F,tokens:this.lexer.inlineTokens(F)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const i=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return i&&s&&(n=n.substring(1,n.length-1)),n=Oe(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,i;return t[2]==="@"?(n=Oe(t[1]),i="mailto:"+n):(n=Oe(t[1]),i=n),{type:"link",raw:t[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let i,s;if(t[2]==="@")i=Oe(t[0]),s="mailto:"+i;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);i=Oe(t[0]),s=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:i,href:s,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:Oe(t[0]),{type:"text",raw:t[0],text:n}}}}const dt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,rs=/(?:[*+-]|\d{1,9}[.)])/,os=me(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,rs).getRegex(),jt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Vt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ca=me(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Vt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ka=me(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,rs).getRegex(),Et="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ut=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,wa=me("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ut).replace("tag",Et).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ri=me(jt).replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),Zt={blockquote:me(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ri).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Ca,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:dt,html:wa,lheading:os,list:ka,newline:/^(?: *(?:\n|$))+/,paragraph:ri,table:st,text:/^[^\n]+/},oi=me("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),va={...Zt,table:oi,paragraph:me(jt).replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",oi).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex()},ya={...Zt,html:me(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ut).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:st,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:me(jt).replace("hr",dt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",os).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ls=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,as=/^( {2,}|\\)\n(?!\s*$)/,pt="\\p{P}$+<=>`^|~",Aa=me(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,pt).getRegex(),ba=me(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,pt).getRegex(),Ea=me("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,pt).getRegex(),_a=me("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,pt).getRegex(),Ba=me(/\\([punct])/,"gu").replace(/punct/g,pt).getRegex(),za=me(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),La=me(Ut).replace("(?:-->|$)","-->").getRegex(),Ma=me("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",La).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),wt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ra=me(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",wt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),li=me(/^!?\[(label)\]\[(ref)\]/).replace("label",wt).replace("ref",Vt).getRegex(),ai=me(/^!?\[(ref)\](?:\[\])?/).replace("ref",Vt).getRegex(),Ht={_backpedal:st,anyPunctuation:Ba,autolink:za,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:as,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:st,emStrongLDelim:ba,emStrongRDelimAst:Ea,emStrongRDelimUnd:_a,escape:ls,link:Ra,nolink:ai,punctuation:Aa,reflink:li,reflinkSearch:me("reflink|nolink(?!\\()","g").replace("reflink",li).replace("nolink",ai).getRegex(),tag:Ma,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:st},qa={...Ht,link:me(/^!?\[(label)\]\((.*?)\)/).replace("label",wt).getRegex(),reflink:me(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",wt).getRegex()},qt={...Ht,escape:me(ls).replace("])","~|])").getRegex(),url:me(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Na={...qt,br:me(as).replace("{2,}","*").getRegex(),text:me(qt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},mt={normal:Zt,gfm:va,pedantic:ya},it={normal:Ht,gfm:qt,breaks:Na,pedantic:qa};class Se{constructor(e){fe(this,"tokens");fe(this,"options");fe(this,"state");fe(this,"tokenizer");fe(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||tt,this.options.tokenizer=this.options.tokenizer||new kt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:mt.normal,inline:it.normal};this.options.pedantic?(t.block=mt.pedantic,t.inline=it.pedantic):this.options.gfm&&(t.block=mt.gfm,this.options.breaks?t.inline=it.breaks:t.inline=it.gfm),this.tokenizer.rules=t}static get rules(){return{block:mt,inline:it}}static lex(e,t){return new Se(t).lex(e)}static lexInline(e,t){return new Se(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,i,s,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,a,u)=>a+"    ".repeat(u.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?t.push(n):(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(i.raw+=`
`+n.raw,i.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(s=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=e.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},a),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(s=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s)))i=t[t.length-1],o&&i.type==="paragraph"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n),o=s.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&i.type==="text"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,i,s,o,l,a,u=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(u))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(u))!=null;)u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(u))!=null;)u=u.slice(0,o.index)+"++"+u.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,u,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(F=>{p=F.call({lexer:this},d),typeof p=="number"&&p>=0&&(c=Math.min(c,p))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,i=t[t.length-1],i&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class vt{constructor(e){fe(this,"options");this.options=e||tt}code(e,t,n){var s;const i=(s=(t||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,i?'<pre><code class="language-'+Oe(i)+'">'+(n?e:Oe(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:Oe(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const i=t?"ol":"ul";return"<"+i+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+i+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const i=ni(e);if(i===null)return n;let s='<a href="'+(e=i)+'"';return t&&(s+=' title="'+t+'"'),s+=">"+n+"</a>",s}image(e,t,n){const i=ni(e);if(i===null)return n;let s=`<img src="${e=i}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return e}}class Wt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class je{constructor(e){fe(this,"options");fe(this,"renderer");fe(this,"textRenderer");this.options=e||tt,this.options.renderer=this.options.renderer||new vt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Wt}static parse(e,t){return new je(t).parse(e)}static parseInline(e,t){return new je(t).parseInline(e)}parse(e,t=!0){let n="";for(let i=0;i<e.length;i++){const s=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(s.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=s;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Fa(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let l="",a="";for(let c=0;c<o.header.length;c++)a+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(a);let u="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];a="";for(let p=0;p<d.length;p++)a+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});u+=this.renderer.tablerow(a)}n+=this.renderer.table(l,u);continue}case"blockquote":{const o=s,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=s,l=o.ordered,a=o.start,u=o.loose;let c="";for(let d=0;d<o.items.length;d++){const p=o.items[d],F=p.checked,v=p.task;let w="";if(p.task){const E=this.renderer.checkbox(!!F);u?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=E+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=E+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:E+" "}):w+=E+" "}w+=this.parse(p.tokens,u),c+=this.renderer.listitem(w,v,!!F)}n+=this.renderer.list(c,l,a);continue}case"html":{const o=s;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,l=o.tokens?this.parseInline(o.tokens):o.text;for(;i+1<e.length&&e[i+1].type==="text";)o=e[++i],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let i=0;i<e.length;i++){const s=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=o||"";continue}}switch(s.type){case"escape":{const o=s;n+=t.text(o.text);break}case"html":{const o=s;n+=t.html(o.text);break}case"link":{const o=s;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=s;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=s;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=s;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=s;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=s;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=s;n+=t.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class rt{constructor(e){fe(this,"options");this.options=e||tt}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}fe(rt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Ke,Nt,us,Bi;const Qe=new(Bi=class{constructor(...r){Gt(this,Ke);fe(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});fe(this,"options",this.setOptions);fe(this,"parse",gt(this,Ke,Nt).call(this,Se.lex,je.parse));fe(this,"parseInline",gt(this,Ke,Nt).call(this,Se.lexInline,je.parseInline));fe(this,"Parser",je);fe(this,"Renderer",vt);fe(this,"TextRenderer",Wt);fe(this,"Lexer",Se);fe(this,"Tokenizer",kt);fe(this,"Hooks",rt);this.use(...r)}walkTokens(r,e){var n,i;let t=[];for(const s of r)switch(t=t.concat(e.call(this,s)),s.type){case"table":{const o=s;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const a of l)t=t.concat(this.walkTokens(a.tokens,e));break}case"list":{const o=s;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=s;(i=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&i[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);t=t.concat(this.walkTokens(a,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const s=e.renderers[i.name];e.renderers[i.name]=s?function(...o){let l=i.renderer.apply(this,o);return l===!1&&(l=s.apply(this,o)),l}:i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=e[i.level];s?s.unshift(i.tokenizer):e[i.level]=[i.tokenizer],i.start&&(i.level==="block"?e.startBlock?e.startBlock.push(i.start):e.startBlock=[i.start]:i.level==="inline"&&(e.startInline?e.startInline.push(i.start):e.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(e.childTokens[i.name]=i.childTokens)}),n.extensions=e),t.renderer){const i=this.defaults.renderer||new vt(this.defaults);for(const s in t.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,l=t.renderer[o],a=i[o];i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c||""}}n.renderer=i}if(t.tokenizer){const i=this.defaults.tokenizer||new kt(this.defaults);for(const s in t.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,l=t.tokenizer[o],a=i[o];i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c}}n.tokenizer=i}if(t.hooks){const i=this.defaults.hooks||new rt;for(const s in t.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,l=t.hooks[o],a=i[o];rt.passThroughHooks.has(s)?i[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(i,u)).then(d=>a.call(i,d));const c=l.call(i,u);return a.call(i,c)}:i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c}}n.hooks=i}if(t.walkTokens){const i=this.defaults.walkTokens,s=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(s.call(this,o)),i&&(l=l.concat(i.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Se.lex(r,e??this.defaults)}parser(r,e){return je.parse(r,e??this.defaults)}},Ke=new WeakSet,Nt=function(r,e){return(t,n)=>{const i={...n},s={...this.defaults,...i};this.defaults.async===!0&&i.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=gt(this,Ke,us).call(this,!!s.silent,!!s.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(l=>r(l,s)).then(l=>s.hooks?s.hooks.processAllTokens(l):l).then(l=>s.walkTokens?Promise.all(this.walkTokens(l,s.walkTokens)).then(()=>l):l).then(l=>e(l,s)).then(l=>s.hooks?s.hooks.postprocess(l):l).catch(o);try{s.hooks&&(t=s.hooks.preprocess(t));let l=r(t,s);s.hooks&&(l=s.hooks.processAllTokens(l)),s.walkTokens&&this.walkTokens(l,s.walkTokens);let a=e(l,s);return s.hooks&&(a=s.hooks.postprocess(a)),a}catch(l){return o(l)}}},us=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Oe(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Bi);function he(r,e){return Qe.parse(r,e)}he.options=he.setOptions=function(r){return Qe.setOptions(r),he.defaults=Qe.defaults,ei(he.defaults),he},he.getDefaults=ga,he.defaults=tt,he.use=function(...r){return Qe.use(...r),he.defaults=Qe.defaults,ei(he.defaults),he},he.walkTokens=function(r,e){return Qe.walkTokens(r,e)},he.parseInline=Qe.parseInline,he.Parser=je,he.parser=je.parse,he.Renderer=vt,he.TextRenderer=Wt,he.Lexer=Se,he.lexer=Se.lex,he.Tokenizer=kt,he.Hooks=rt,he.parse=he,he.options,he.setOptions,he.use,he.walkTokens,he.parseInline,je.parse,Se.lex;const Ta=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Oa=Object.hasOwnProperty;class Pa{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let i=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(Ta,"").replace(/ /g,"-"))}(e,t===!0);const s=i;for(;Oa.call(n.occurrences,i);)n.occurrences[s]++,i=s+"-"+n.occurrences[s];return n.occurrences[i]=0,i}reset(){this.occurrences=Object.create(null)}}function Sa(r){let e,t;return e=new bt({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.tokens=n[0]),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Ia(r,e,t){(function(){const u=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||u(c)},et(()=>{console.warn=u})})();let n,i,s,{source:o}=e,{options:l={}}=e,{renderers:a={}}=e;return r.$$set=u=>{"source"in u&&t(3,o=u.source),"options"in u&&t(4,l=u.options),"renderers"in u&&t(5,a=u.renderers)},r.$$.update=()=>{var u;56&r.$$.dirty&&(t(0,(u=o,n=new Se().lex(u))),t(1,i={heading:ll,blockquote:cl,list:xl,list_item:wl,br:Al,code:_l,codespan:Ll,table:ql,html:Ol,paragraph:Il,link:Ul,text:Wl,def:Jl,del:Kl,em:na,hr:ra,strong:aa,image:da,space:Kn,escape:Kn,...a}),t(2,s={baseUrl:"/",slugger:new Pa,...l}))},[n,i,s,o,l,a]}class ja extends ee{constructor(e){super(),te(this,e,Ia,Sa,Y,{source:3,options:4,renderers:5})}}const Va=r=>({}),ui=r=>({}),Ua=r=>({}),ci=r=>({}),Za=r=>({}),di=r=>({});function Ha(r){let e,t,n,i,s,o,l,a,u,c,d,p;const F=r[13].topBarLeft,v=be(F,r,r[12],di),w=r[13].topBarRight,E=be(w,r,r[12],ci);function b(C){r[16](C)}let z={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(z.editorInstance=r[0]),o=new Es({props:z}),qe.push(()=>Je(o,"editorInstance",b));const m=r[13].actionsBar,D=be(m,r,r[12],ui);return{c(){e=A("div"),t=A("div"),n=A("div"),v&&v.c(),i=j(),E&&E.c(),s=j(),L(o.$$.fragment),a=j(),u=A("div"),D&&D.c(),x(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),x(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),x(u,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),x(e,"class","c-codeblock svelte-mexfz1"),x(e,"role","button"),x(e,"tabindex","0")},m(C,y){g(C,e,y),q(e,t),q(t,n),v&&v.m(n,null),q(t,i),E&&E.m(t,null),q(e,s),M(o,e,null),q(e,a),q(e,u),D&&D.m(u,null),r[17](e),c=!0,d||(p=[ot(window,"focus",r[15]),ot(e,"mouseenter",r[14])],d=!0)},p(C,[y]){v&&v.p&&(!c||4096&y)&&Ee(v,F,C,C[12],c?Be(F,C[12],y,Za):_e(C[12]),di),E&&E.p&&(!c||4096&y)&&Ee(E,w,C,C[12],c?Be(w,C[12],y,Ua):_e(C[12]),ci);const _={};36&y&&(_.options={lineNumbers:"off",wrappingIndent:"same",padding:C[5],wordWrap:C[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&y&&(_.text=C[3].text),24&y&&(_.lang=C[4]||C[3].lang),64&y&&(_.height=C[6]),!l&&1&y&&(l=!0,_.editorInstance=C[0],Ye(()=>l=!1)),o.$set(_),D&&D.p&&(!c||4096&y)&&Ee(D,m,C,C[12],c?Be(m,C[12],y,Va):_e(C[12]),ui)},i(C){c||(f(v,C),f(E,C),f(o.$$.fragment,C),f(D,C),c=!0)},o(C){h(v,C),h(E,C),h(o.$$.fragment,C),h(D,C),c=!1},d(C){C&&$(e),v&&v.d(C),E&&E.d(C),R(o),D&&D.d(C),r[17](null),d=!1,Ri(p)}}}function Wa(r,e,t){let n,{$$slots:i={},$$scope:s}=e,{scroll:o=!1}=e,{token:l}=e,{language:a}=e,{padding:u={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:d}=e,{height:p}=e;const F=Ot.getContext().monaco;Le(r,F,b=>t(18,n=b));const v=Pt(),w=()=>{if(!c)return;const b=c.getSelections();if(!(b!=null&&b.length))return;const z=c.getModel();if(b.map(m=>(z==null?void 0:z.getValueLengthInRange(m))||0).reduce((m,D)=>m+D,0)!==0)return b.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(m=>(z==null?void 0:z.getValueInRange(m))||"").join(`
`)},E=()=>{if(c)return c.getValue()||""};return r.$$set=b=>{"scroll"in b&&t(2,o=b.scroll),"token"in b&&t(3,l=b.token),"language"in b&&t(4,a=b.language),"padding"in b&&t(5,u=b.padding),"editorInstance"in b&&t(0,c=b.editorInstance),"element"in b&&t(1,d=b.element),"height"in b&&t(6,p=b.height),"$$scope"in b&&t(12,s=b.$$scope)},r.$$.update=()=>{var b;32&r.$$.dirty&&(b=u,c==null||c.updateOptions({padding:b})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[c,d,o,l,a,u,p,F,v,()=>c&&(w()||E())||"",w,E,s,i,function(b){ms.call(this,r,b)},()=>v.requestLayout(),function(b){c=b,t(0,c)},function(b){qe[b?"unshift":"push"](()=>{d=b,t(1,d)})}]}class pi extends ee{constructor(e){super(),te(this,e,Wa,Ha,Y,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const Qa=r=>({codespanContents:2&r}),fi=r=>({codespanContents:r[1]});function Ga(r){let e,t,n;const i=r[4].default,s=be(i,r,r[3],fi),o=s||function(l){let a;return{c(){a=T(l[1])},m(u,c){g(u,a,c)},p(u,c){2&c&&le(a,u[1])},d(u){u&&$(a)}}}(r);return{c(){e=A("span"),t=A("code"),o&&o.c(),x(t,"class","markdown-codespan svelte-1dofrdh")},m(l,a){g(l,e,a),q(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[a]){s?s.p&&(!n||10&a)&&Ee(s,i,l,l[3],n?Be(i,l[3],a,Qa):_e(l[3]),fi):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(f(o,l),n=!0)},o(l){h(o,l),n=!1},d(l){l&&$(e),o&&o.d(l),r[5](null)}}}function Ja(r,e,t){let n,{$$slots:i={},$$scope:s}=e,{token:o}=e,{element:l}=e;return r.$$set=a=>{"token"in a&&t(2,o=a.token),"element"in a&&t(0,l=a.element),"$$scope"in a&&t(3,s=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,s,i,function(a){qe[a?"unshift":"push"](()=>{l=a,t(0,l)})}]}class gi extends ee{constructor(e){super(),te(this,e,Ja,Ga,Y,{token:2,element:0})}}function Ya(r){let e,t,n,i,s=r[0].text+"";return{c(){e=A("span"),t=T("~"),n=T(s),i=T("~")},m(o,l){g(o,e,l),q(e,t),q(e,n),q(e,i)},p(o,[l]){1&l&&s!==(s=o[0].text+"")&&le(n,s)},i:X,o:X,d(o){o&&$(e)}}}function Xa(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n]}class $i extends ee{constructor(e){super(),te(this,e,Xa,Ya,Y,{token:0})}}function Ka(r){let e,t;const n=r[1].default,i=be(n,r,r[0],null);return{c(){e=A("p"),i&&i.c(),x(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||1&o)&&Ee(i,n,s,s[0],t?Be(n,s[0],o,null):_e(s[0]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function eu(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(0,i=s.$$scope)},[i,n]}class hi extends ee{constructor(e){super(),te(this,e,eu,Ka,Y,{})}}function tu(r){let e,t,n;return t=new ja({props:{source:r[0],renderers:{codespan:gi,code:pi,paragraph:hi,del:$i,...r[1]}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c-markdown svelte-n6ddeo")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,[s]){const o={};1&s&&(o.source=i[0]),2&s&&(o.renderers={codespan:gi,code:pi,paragraph:hi,del:$i,...i[1]}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function nu(r,e,t){let{markdown:n}=e,{renderers:i={}}=e;return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown),"renderers"in s&&t(1,i=s.renderers)},[n,i]}class iu extends ee{constructor(e){super(),te(this,e,nu,tu,Y,{markdown:0,renderers:1})}}function su(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function ru(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function ou(r){let e,t,n;function i(l,a){return l[2]?ru:su}let s=i(r),o=s(r);return{c(){e=A("span"),t=A("code"),o.c(),x(t,"class","markdown-codespan svelte-11ta4gi"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),ye(t,"markdown-string",r[4])},m(l,a){g(l,e,a),q(e,t),o.m(t,null),r[6](e)},p(l,[a]){s===(s=i(l))&&o?o.p(l,a):(o.d(1),o=s(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&ye(t,"markdown-string",l[4])},i:X,o:X,d(l){l&&$(e),o.d(),r[6](null)}}}function lu(r,e,t){let n,i,s,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,i=n.startsWith('"')),2&r.$$.dirty&&t(2,s=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=s&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,s,o,i,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}class au extends ee{constructor(e){super(),te(this,e,lu,ou,Y,{token:5,element:0})}}function uu(r){let e,t;return e=new iu({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.markdown=n[1](n[0])),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function cu(r,e,t){let{markdown:n}=e;const i={codespan:au};return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown)},[n,s=>s.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),i]}class du extends ee{constructor(e){super(),te(this,e,cu,uu,Y,{markdown:0})}}const{Boolean:cs,Map:pu}=Rs;function mi(r,e,t){const n=r.slice();return n[48]=e[t],n[49]=e,n[50]=t,n}function Di(r,e,t){const n=r.slice();return n[51]=e[t],n[52]=e,n[53]=t,n}function Fi(r,e,t){const n=r.slice();return n[54]=e[t],n[55]=e,n[56]=t,n}function xi(r){let e,t,n,i,s,o,l,a;t=new Hi({}),o=new Ve({props:{variant:"ghost",size:1,$$slots:{default:[fu]},$$scope:{ctx:r}}}),o.$on("click",r[29]);let u=r[4]&&Ci(r);return{c(){e=A("div"),L(t.$$.fragment),n=j(),i=T(r[18]),s=j(),L(o.$$.fragment),l=j(),u&&u.c(),x(e,"class","c-diff-view__error svelte-ibi4q5")},m(c,d){g(c,e,d),M(t,e,null),q(e,n),q(e,i),q(e,s),M(o,e,null),q(e,l),u&&u.m(e,null),a=!0},p(c,d){(!a||262144&d[0])&&le(i,c[18]);const p={};67108864&d[1]&&(p.$$scope={dirty:d,ctx:c}),o.$set(p),c[4]?u?(u.p(c,d),16&d[0]&&f(u,1)):(u=Ci(c),u.c(),f(u,1),u.m(e,null)):u&&(Z(),h(u,1,1,()=>{u=null}),H())},i(c){a||(f(t.$$.fragment,c),f(o.$$.fragment,c),f(u),a=!0)},o(c){h(t.$$.fragment,c),h(o.$$.fragment,c),h(u),a=!1},d(c){c&&$(e),R(t),R(o),u&&u.d()}}}function fu(r){let e;return{c(){e=T("Retry")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Ci(r){let e,t;return e=new Ve({props:{variant:"ghost",size:1,$$slots:{default:[gu]},$$scope:{ctx:r}}}),e.$on("click",function(){Ds(r[4])&&r[4].apply(this,arguments)}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){r=n;const s={};67108864&i[1]&&(s.$$scope={dirty:i,ctx:r}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function gu(r){let e;return{c(){e=T("Render as list")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function $u(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,v=r[1]&&r[2]!==r[1]&&ki(r),w=r[2]&&wi(r);o=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Cu]},$$scope:{ctx:r}}}),a=new es({props:{changedFiles:r[0]}});const E=[wu,ku],b=[];function z(m,D){return m[16]&&m[15].length===0?0:m[6]&&m[6].length>0?1:-1}return~(d=z(r))&&(p=b[d]=E[d](r)),{c(){e=A("div"),t=A("div"),n=A("div"),v&&v.c(),i=j(),w&&w.c(),s=j(),L(o.$$.fragment),l=j(),L(a.$$.fragment),u=j(),c=A("div"),p&&p.c(),x(n,"class","c-diff-view__tree__header svelte-ibi4q5"),x(t,"class","c-diff-view__tree svelte-ibi4q5"),x(c,"class","c-diff-view__explanation svelte-ibi4q5"),x(e,"class","c-diff-view__layout svelte-ibi4q5")},m(m,D){g(m,e,D),q(e,t),q(t,n),v&&v.m(n,null),q(n,i),w&&w.m(n,null),q(n,s),M(o,n,null),q(n,l),M(a,n,null),q(e,u),q(e,c),~d&&b[d].m(c,null),F=!0},p(m,D){m[1]&&m[2]!==m[1]?v?(v.p(m,D),6&D[0]&&f(v,1)):(v=ki(m),v.c(),f(v,1),v.m(n,i)):v&&(Z(),h(v,1,1,()=>{v=null}),H()),m[2]?w?(w.p(m,D),4&D[0]&&f(w,1)):(w=wi(m),w.c(),f(w,1),w.m(n,s)):w&&(Z(),h(w,1,1,()=>{w=null}),H());const C={};67108864&D[1]&&(C.$$scope={dirty:D,ctx:m}),o.$set(C);const y={};1&D[0]&&(y.changedFiles=m[0]),a.$set(y);let _=d;d=z(m),d===_?~d&&b[d].p(m,D):(p&&(Z(),h(b[_],1,1,()=>{b[_]=null}),H()),~d?(p=b[d],p?p.p(m,D):(p=b[d]=E[d](m),p.c()),f(p,1),p.m(c,null)):p=null)},i(m){F||(f(v),f(w),f(o.$$.fragment,m),f(a.$$.fragment,m),f(p),F=!0)},o(m){h(v),h(w),h(o.$$.fragment,m),h(a.$$.fragment,m),h(p),F=!1},d(m){m&&$(e),v&&v.d(),w&&w.d(),R(o),R(a),~d&&b[d].d()}}}function hu(r){let e,t,n;return t=new ge({props:{size:2,color:"secondary",$$slots:{default:[Wu]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c-diff-view__empty svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};67108864&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function ki(r){let e,t,n,i;return e=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[mu]},$$scope:{ctx:r}}}),n=new ge({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Du]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),L(n.$$.fragment)},m(s,o){M(e,s,o),g(s,t,o),M(n,s,o),i=!0},p(s,o){const l={};67108864&o[1]&&(l.$$scope={dirty:o,ctx:s}),e.$set(l);const a={};2&o[0]|67108864&o[1]&&(a.$$scope={dirty:o,ctx:s}),n.$set(a)},i(s){i||(f(e.$$.fragment,s),f(n.$$.fragment,s),i=!0)},o(s){h(e.$$.fragment,s),h(n.$$.fragment,s),i=!1},d(s){s&&$(t),R(e,s),R(n,s)}}}function mu(r){let e;return{c(){e=T("Changes from agent")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Du(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n[0]&&le(e,t[1])},d(t){t&&$(e)}}}function wi(r){let e,t,n,i;return e=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Fu]},$$scope:{ctx:r}}}),n=new ge({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[xu]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=j(),L(n.$$.fragment)},m(s,o){M(e,s,o),g(s,t,o),M(n,s,o),i=!0},p(s,o){const l={};67108864&o[1]&&(l.$$scope={dirty:o,ctx:s}),e.$set(l);const a={};4&o[0]|67108864&o[1]&&(a.$$scope={dirty:o,ctx:s}),n.$set(a)},i(s){i||(f(e.$$.fragment,s),f(n.$$.fragment,s),i=!0)},o(s){h(e.$$.fragment,s),h(n.$$.fragment,s),i=!1},d(s){s&&$(t),R(e,s),R(n,s)}}}function Fu(r){let e;return{c(){e=T("Last user prompt")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function xu(r){let e;return{c(){e=T(r[2])},m(t,n){g(t,e,n)},p(t,n){4&n[0]&&le(e,t[2])},d(t){t&&$(e)}}}function Cu(r){let e;return{c(){e=T("Changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function ku(r){let e,t,n=pe(r[6]),i=[];for(let o=0;o<n.length;o+=1)i[o]=bi(mi(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(515532776&l[0]){let a;for(n=pe(o[6]),a=0;a<n.length;a+=1){const u=mi(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=bi(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(Z(),a=n.length;a<i.length;a+=1)s(a);H()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(cs);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Me(i,o)}}}function wu(r){let e,t,n,i,s;return t=new Ue({props:{content:r[9]?"Applying changes...":r[10]?"All changes applied":r[11]?"Apply all changes":"No changes to apply",$$slots:{default:[Hu]},$$scope:{ctx:r}}}),i=new sl({props:{count:2}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),L(i.$$.fragment),x(e,"class","c-diff-view__controls svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),g(o,n,l),M(i,o,l),s=!0},p(o,l){const a={};3584&l[0]&&(a.content=o[9]?"Applying changes...":o[10]?"All changes applied":o[11]?"Apply all changes":"No changes to apply"),7680&l[0]|67108864&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&($(e),$(n)),R(t),R(i,o)}}}function vu(r){let e,t=r[48].title+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){64&i[0]&&t!==(t=n[48].title+"")&&le(e,t)},d(n){n&&$(e)}}}function yu(r){let e;return{c(){e=A("div"),x(e,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(t,n){g(t,e,n)},p:X,d(t){t&&$(e)}}}function Au(r){let e,t;return e=new du({props:{markdown:r[48].description}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};64&i[0]&&(s.markdown=n[48].description),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function bu(r){let e,t,n;return{c(){e=A("div"),t=j(),n=A("div"),x(e,"class","c-diff-view__skeleton-text svelte-ibi4q5"),x(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(i,s){g(i,e,s),g(i,t,s),g(i,n,s)},p:X,i:X,o:X,d(i){i&&($(e),$(t),$(n))}}}function Eu(r){let e,t,n;return e=new qs({}),{c(){L(e.$$.fragment),t=T(`
                        Expand All`)},m(i,s){M(e,i,s),g(i,t,s),n=!0},i(i){n||(f(e.$$.fragment,i),n=!0)},o(i){h(e.$$.fragment,i),n=!1},d(i){i&&$(t),R(e,i)}}}function _u(r){let e,t,n;return e=new bs({}),{c(){L(e.$$.fragment),t=T(`
                        Collapse All`)},m(i,s){M(e,i,s),g(i,t,s),n=!0},i(i){n||(f(e.$$.fragment,i),n=!0)},o(i){h(e.$$.fragment,i),n=!1},d(i){i&&$(t),R(e,i)}}}function Bu(r){let e,t,n,i;const s=[_u,Eu],o=[];function l(a,u){return a[23]?1:0}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function zu(r){let e,t,n,i;return n=new ut({}),{c(){e=T(`Apply all
                          `),t=A("div"),L(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(s,o){g(s,e,o),g(s,t,o),M(n,t,null),i=!0},i(s){i||(f(n.$$.fragment,s),i=!0)},o(s){h(n.$$.fragment,s),i=!1},d(s){s&&($(e),$(t)),R(n)}}}function Lu(r){let e,t,n;return t=new ge({props:{size:2,$$slots:{default:[Ru]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c-diff-view__applied svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Mu(r){let e,t,n,i,s;return t=new Tt({props:{size:1,useCurrentColor:!0}}),i=new ge({props:{size:2,$$slots:{default:[qu]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),L(i.$$.fragment),x(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),M(i,e,null),s=!0},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&$(e),R(t),R(i)}}}function Ru(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=T(`Applied
                              `),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,i,s),n=!0},p:X,i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t,i)}}}function qu(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Nu(r){let e,t,n,i;const s=[Mu,Lu,zu],o=[];function l(a,u){return a[9]?0:a[10]?1:2}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Tu(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[13],$$slots:{default:[Nu]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};8192&i[0]&&(s.disabled=n[13]),1536&i[0]|67108864&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Ou(r){let e,t=r[51].title+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){64&i[0]&&t!==(t=n[51].title+"")&&le(e,t)},d(n){n&&$(e)}}}function Pu(r){let e;return{c(){e=A("div"),x(e,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(t,n){g(t,e,n)},p:X,d(t){t&&$(e)}}}function vi(r){let e,t,n,i,s,o=r[51].warning+"";return t=new Hi({}),{c(){e=A("div"),L(t.$$.fragment),n=j(),i=T(o),x(e,"class","c-diff-view__warning svelte-ibi4q5")},m(l,a){g(l,e,a),M(t,e,null),q(e,n),q(e,i),s=!0},p(l,a){(!s||64&a[0])&&o!==(o=l[51].warning+"")&&le(i,o)},i(l){s||(f(t.$$.fragment,l),s=!0)},o(l){h(t.$$.fragment,l),s=!1},d(l){l&&$(e),R(t)}}}function yi(r,e){let t,n,i,s,o,l=e[50],a=e[53],u=e[54];function c(...z){return e[37](e[54],...z)}function d(){return e[38](e[54])}function p(){return e[39](e[54])}function F(z){e[40](z,e[54])}const v=()=>e[41](n,l,a,u),w=()=>e[41](null,l,a,u);function E(z){e[42](z)}let b={path:e[54].path,change:e[54],descriptions:e[51].descriptions,isExpandedDefault:e[8][e[54].path]!==void 0?!e[8][e[54].path]:e[7],isApplying:e[14][e[54].path]==="pending",hasApplied:e[14][e[54].path]==="applied",onCodeChange:c,onApplyChanges:d,onOpenFile:e[3]?p:void 0,isAgentFromDifferentRepo:e[5]};return e[8][e[54].path]!==void 0&&(b.isCollapsed=e[8][e[54].path]),e[20]!==void 0&&(b.areDescriptionsVisible=e[20]),n=new Yo({props:b}),qe.push(()=>Je(n,"isCollapsed",F)),v(),qe.push(()=>Je(n,"areDescriptionsVisible",E)),{key:r,first:null,c(){t=A("div"),L(n.$$.fragment),x(t,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=t},m(z,m){g(z,t,m),M(n,t,null),o=!0},p(z,m){l===(e=z)[50]&&a===e[53]&&u===e[54]||(w(),l=e[50],a=e[53],u=e[54],v());const D={};64&m[0]&&(D.path=e[54].path),64&m[0]&&(D.change=e[54]),64&m[0]&&(D.descriptions=e[51].descriptions),448&m[0]&&(D.isExpandedDefault=e[8][e[54].path]!==void 0?!e[8][e[54].path]:e[7]),16448&m[0]&&(D.isApplying=e[14][e[54].path]==="pending"),16448&m[0]&&(D.hasApplied=e[14][e[54].path]==="applied"),64&m[0]&&(D.onCodeChange=c),64&m[0]&&(D.onApplyChanges=d),72&m[0]&&(D.onOpenFile=e[3]?p:void 0),32&m[0]&&(D.isAgentFromDifferentRepo=e[5]),!i&&320&m[0]&&(i=!0,D.isCollapsed=e[8][e[54].path],Ye(()=>i=!1)),!s&&1048576&m[0]&&(s=!0,D.areDescriptionsVisible=e[20],Ye(()=>s=!1)),n.$set(D)},i(z){o||(f(n.$$.fragment,z),o=!0)},o(z){h(n.$$.fragment,z),o=!1},d(z){z&&$(t),w(),R(n)}}}function Ai(r){let e,t,n,i,s,o,l,a,u,c,d,p=[],F=new pu;function v(D,C){return D[17]&&D[51].descriptions.length===0?Pu:Ou}s=new ro({props:{type:r[51].type}});let w=v(r),E=w(r),b=!r[17]&&r[51].warning&&vi(r),z=pe(r[51].changes);const m=D=>D[54].id;for(let D=0;D<z.length;D+=1){let C=Fi(r,z,D),y=m(C);F.set(y,p[D]=yi(y,C))}return{c(){e=A("div"),t=A("div"),n=A("div"),i=A("div"),L(s.$$.fragment),o=j(),l=A("h5"),E.c(),a=j(),b&&b.c(),u=j(),c=A("div");for(let D=0;D<p.length;D+=1)p[D].c();x(i,"class","c-diff-view__icon svelte-ibi4q5"),x(l,"class","c-diff-view__title svelte-ibi4q5"),x(n,"class","c-diff-view__content svelte-ibi4q5"),x(t,"class","c-diff-view__header svelte-ibi4q5"),x(c,"class","c-diff-view__changes svelte-ibi4q5"),x(e,"class","c-diff-view__subsection svelte-ibi4q5"),x(e,"id",`subsection-${r[50]}-${r[53]}`)},m(D,C){g(D,e,C),q(e,t),q(t,n),q(n,i),M(s,i,null),q(n,o),q(n,l),E.m(l,null),q(n,a),b&&b.m(n,null),q(e,u),q(e,c);for(let y=0;y<p.length;y+=1)p[y]&&p[y].m(c,null);d=!0},p(D,C){const y={};64&C[0]&&(y.type=D[51].type),s.$set(y),w===(w=v(D))&&E?E.p(D,C):(E.d(1),E=w(D),E&&(E.c(),E.m(l,null))),!D[17]&&D[51].warning?b?(b.p(D,C),131136&C[0]&&f(b,1)):(b=vi(D),b.c(),f(b,1),b.m(n,null)):b&&(Z(),h(b,1,1,()=>{b=null}),H()),202916328&C[0]&&(z=pe(D[51].changes),Z(),p=Oi(p,C,m,1,D,z,F,c,Pi,yi,null,Fi),H())},i(D){if(!d){f(s.$$.fragment,D),f(b);for(let C=0;C<z.length;C+=1)f(p[C]);d=!0}},o(D){h(s.$$.fragment,D),h(b);for(let C=0;C<p.length;C+=1)h(p[C]);d=!1},d(D){D&&$(e),R(s),E.d(),b&&b.d();for(let C=0;C<p.length;C+=1)p[C].d()}}}function bi(r){let e,t,n,i,s,o,l,a,u,c,d,p;function F(_,P){return _[17]&&_[48].title==="Loading..."?yu:vu}let v=F(r),w=v(r);const E=[bu,Au],b=[];function z(_,P){return _[17]&&_[48].description===""?0:1}l=z(r),a=b[l]=E[l](r);let m=r[50]===0&&function(_){let P,U,Q,G,oe;return U=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[Bu]},$$scope:{ctx:_}}}),U.$on("click",_[25]),G=new Ue({props:{content:_[21],$$slots:{default:[Tu]},$$scope:{ctx:_}}}),{c(){P=A("div"),L(U.$$.fragment),Q=j(),L(G.$$.fragment),x(P,"class","c-diff-view__controls svelte-ibi4q5")},m(ae,ce){g(ae,P,ce),M(U,P,null),q(P,Q),M(G,P,null),oe=!0},p(ae,ce){const de={};8388608&ce[0]|67108864&ce[1]&&(de.$$scope={dirty:ce,ctx:ae}),U.$set(de);const J={};2097152&ce[0]&&(J.content=ae[21]),9728&ce[0]|67108864&ce[1]&&(J.$$scope={dirty:ce,ctx:ae}),G.$set(J)},i(ae){oe||(f(U.$$.fragment,ae),f(G.$$.fragment,ae),oe=!0)},o(ae){h(U.$$.fragment,ae),h(G.$$.fragment,ae),oe=!1},d(ae){ae&&$(P),R(U),R(G)}}}(r),D=pe(r[48].sections||[]),C=[];for(let _=0;_<D.length;_+=1)C[_]=Ai(Di(r,D,_));const y=_=>h(C[_],1,1,()=>{C[_]=null});return{c(){e=A("div"),t=A("div"),n=A("div"),i=A("h5"),w.c(),s=j(),o=A("div"),a.c(),u=j(),m&&m.c(),c=j();for(let _=0;_<C.length;_+=1)C[_].c();d=j(),x(i,"class","c-diff-view__title svelte-ibi4q5"),x(o,"class","c-diff-view__description svelte-ibi4q5"),x(n,"class","c-diff-view__content svelte-ibi4q5"),x(t,"class","c-diff-view__header svelte-ibi4q5"),x(e,"class","c-diff-view__section svelte-ibi4q5"),x(e,"id",`section-${r[50]}`)},m(_,P){g(_,e,P),q(e,t),q(t,n),q(n,i),w.m(i,null),q(n,s),q(n,o),b[l].m(o,null),q(t,u),m&&m.m(t,null),q(e,c);for(let U=0;U<C.length;U+=1)C[U]&&C[U].m(e,null);q(e,d),p=!0},p(_,P){v===(v=F(_))&&w?w.p(_,P):(w.d(1),w=v(_),w&&(w.c(),w.m(i,null)));let U=l;if(l=z(_),l===U?b[l].p(_,P):(Z(),h(b[U],1,1,()=>{b[U]=null}),H(),a=b[l],a?a.p(_,P):(a=b[l]=E[l](_),a.c()),f(a,1),a.m(o,null)),_[50]===0&&m.p(_,P),203047400&P[0]){let Q;for(D=pe(_[48].sections||[]),Q=0;Q<D.length;Q+=1){const G=Di(_,D,Q);C[Q]?(C[Q].p(G,P),f(C[Q],1)):(C[Q]=Ai(G),C[Q].c(),f(C[Q],1),C[Q].m(e,d))}for(Z(),Q=D.length;Q<C.length;Q+=1)y(Q);H()}},i(_){if(!p){f(a),f(m);for(let P=0;P<D.length;P+=1)f(C[P]);p=!0}},o(_){h(a),h(m),C=C.filter(cs);for(let P=0;P<C.length;P+=1)h(C[P]);p=!1},d(_){_&&$(e),w.d(),b[l].d(),m&&m.d(),Me(C,_)}}}function Su(r){let e,t,n,i;return n=new ut({}),{c(){e=T(`Apply all
                  `),t=A("div"),L(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(s,o){g(s,e,o),g(s,t,o),M(n,t,null),i=!0},i(s){i||(f(n.$$.fragment,s),i=!0)},o(s){h(n.$$.fragment,s),i=!1},d(s){s&&($(e),$(t)),R(n)}}}function Iu(r){let e,t,n;return t=new ge({props:{size:2,$$slots:{default:[Vu]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),x(e,"class","c-diff-view__applied svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function ju(r){let e,t,n,i,s;return t=new Tt({props:{size:1,useCurrentColor:!0}}),i=new ge({props:{size:2,$$slots:{default:[Uu]},$$scope:{ctx:r}}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),L(i.$$.fragment),x(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),M(i,e,null),s=!0},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&$(e),R(t),R(i)}}}function Vu(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=T(`Applied
                      `),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,i,s),n=!0},p:X,i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t,i)}}}function Uu(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Zu(r){let e,t,n,i;const s=[ju,Iu,Su],o=[];function l(a,u){return a[9]?0:a[10]?1:2}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Hu(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[9]||r[10]||r[12].length>0||!r[11],$$slots:{default:[Zu]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};7680&i[0]&&(s.disabled=n[9]||n[10]||n[12].length>0||!n[11]),1536&i[0]|67108864&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Wu(r){let e;return{c(){e=T("No files changed")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Qu(r){let e,t,n,i,s,o=r[18]&&xi(r);const l=[hu,$u],a=[];function u(c,d){return c[22]?0:1}return n=u(r),i=a[n]=l[n](r),{c(){e=A("div"),o&&o.c(),t=j(),i.c(),x(e,"class","c-diff-view svelte-ibi4q5")},m(c,d){g(c,e,d),o&&o.m(e,null),q(e,t),a[n].m(e,null),s=!0},p(c,d){c[18]?o?(o.p(c,d),262144&d[0]&&f(o,1)):(o=xi(c),o.c(),f(o,1),o.m(e,t)):o&&(Z(),h(o,1,1,()=>{o=null}),H());let p=n;n=u(c),n===p?a[n].p(c,d):(Z(),h(a[p],1,1,()=>{a[p]=null}),H(),i=a[n],i?i.p(c,d):(i=a[n]=l[n](c),i.c()),f(i,1),i.m(e,null))},i(c){s||(f(o),f(i),s=!0)},o(c){h(o),h(i),s=!1},d(c){c&&$(e),o&&o.d(),a[n].d()}}}function Gu(r,e,t){let n,i,s,o,l,a,u,c,{changedFiles:d}=e,{agentLabel:p}=e,{latestUserPrompt:F}=e,{onApplyChanges:v}=e,{onOpenFile:w}=e,{onRenderBackup:E}=e,{preloadedExplanation:b}=e,{isAgentFromDifferentRepo:z=!1}=e;const m=Ge(nt.key);let D="",C=!1,y=[],_=[],P=!1,U=!1,Q=null,G=!0,oe={},ae=[],ce=!1,de=!1,J=!0;const Fe=He({});Le(r,Fe,O=>t(14,c=O));let $e={};function ie(O,B){t(33,$e[O]=B,$e)}async function we(O,B,se){if(v)return Fe.update(k=>(k[O]="pending",k)),new Promise(k=>{v==null||v(O,B,se).then(()=>{Fe.update(N=>(N[O]="applied",N)),k()})})}function ve(O){const B={title:"Changed Files",description:`${O.length} files were changed`,sections:[]},se=[],k=[],N=[];return O.forEach(S=>{S.old_path?S.new_path?k.push(S):N.push(S):se.push(S)}),se.length>0&&B.sections.push(Ce("Added files","feature",se)),k.length>0&&B.sections.push(Ce("Modified files","fix",k)),N.length>0&&B.sections.push(Ce("Deleted files","chore",N)),[B]}function Ce(O,B,se){const k=[];return se.forEach(N=>{const S=N.new_path||N.old_path,V=N.old_contents||"",I=N.new_contents||"",W=N.old_path?N.old_path:"",K=xt(W,N.new_path||"/dev/null",V,I,"","",{context:3}),ne=`${Ie(S)}-${Ie(V+I)}`;k.push({id:ne,path:S,diff:K,originalCode:V,modifiedCode:I})}),{title:O,descriptions:[],type:B,changes:k}}async function Re(){if(!C)return;if(t(16,P=!0),t(17,U=!1),t(18,Q=null),t(15,_=[]),t(6,y=[]),l)return void t(16,P=!1);const O=102400;let B=0;if(d.forEach(se=>{var k,N;B+=(((k=se.old_contents)==null?void 0:k.length)||0)+(((N=se.new_contents)==null?void 0:N.length)||0)}),d.length>12||B>512e3){try{t(6,y=ve(d))}catch(se){console.error("Failed to create simple explanation:",se),t(18,Q="Failed to create explanation for large changes.")}t(16,P=!1)}else try{const se=new Bs(S=>Si.postMessage(S)),k=new Map,N=d.map(S=>{const V=S.new_path||S.old_path,I=S.old_contents||"",W=S.new_contents||"",K=`${Ie(V)}-${Ie(I+W)}`;return k.set(K,{old_path:S.old_path,new_path:S.new_path,old_contents:I,new_contents:W,change_type:S.change_type}),{id:K,old_path:S.old_path,new_path:S.new_path,change_type:S.change_type}});try{const S=N.length===1;let V=[];S?V=N.map(I=>({path:I.new_path||I.old_path,changes:[{id:I.id,path:I.new_path||I.old_path,diff:`File: ${I.new_path||I.old_path}
Change type: ${I.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):V=(await se.send({type:"get-diff-group-changes-request",data:{changedFiles:N,changesById:!0,apikey:D}},3e4)).data.groupedChanges,t(15,_=V.map(I=>({path:I.path,changes:I.changes.map(W=>{if(W.id&&k.has(W.id)){const K=k.get(W.id);let ne=W.diff;return ne&&!ne.startsWith("File:")||(ne=xt(K.old_path||"",K.new_path||"",K.old_contents||"",K.new_contents||"")),{...W,diff:ne,old_path:K.old_path,new_path:K.new_path,old_contents:K.old_contents,new_contents:K.new_contents,change_type:K.change_type,originalCode:K.old_contents||"",modifiedCode:K.new_contents||""}}return W})})))}catch(S){console.error("Failed to group changes with LLM, falling back to simple grouping:",S);try{const V=N.map(I=>{if(I.id&&k.has(I.id)){const W=k.get(I.id);return{...I,old_path:W.old_path,new_path:W.new_path,old_contents:W.old_contents||"",new_contents:W.new_contents||"",change_type:W.change_type}}return I});t(6,y=ve(V)),t(15,_=y[0].sections.map(I=>({path:I.title,changes:I.changes}))),t(17,U=!1)}catch(V){console.error("Failed to create simple explanation:",V),t(18,Q="Failed to group changes. Please try again.")}}if(t(16,P=!1),!_||_.length===0)throw new Error("Failed to group changes");if(!y||y.length===0){t(6,y=function(V){const I={title:"Loading...",description:"",sections:[]};return V.forEach(W=>{const K=W.changes.map(De=>{if(De.id)return De;const ze=Ie(De.path),Pe=Ie(De.originalCode+De.modifiedCode);return{...De,id:`${ze}-${Pe}`}}),ne={title:W.path,descriptions:[],type:"other",changes:K};I.sections.push(ne)}),[I]}(_));const S=y[0].sections.map(V=>({path:V.title,changes:V.changes.map(I=>{var De,ze,Pe;const W=((De=I.originalCode)==null?void 0:De.length)||0,K=((ze=I.modifiedCode)==null?void 0:ze.length)||0,ne=((Pe=I.diff)==null?void 0:Pe.length)||0;return W>O||K>O||ne>O?{id:I.id,path:I.path,diff:`File: ${I.path}
Content too large to include in explanation request (${Math.max(W,K,ne)} bytes)`,originalCode:W>O?`[File content too large: ${W} bytes]`:I.originalCode,modifiedCode:K>O?`[File content too large: ${K} bytes]`:I.modifiedCode}:{id:I.id,path:I.path,diff:I.diff,originalCode:I.originalCode,modifiedCode:I.modifiedCode}})}));t(17,U=!0);try{const{explanation:V,error:I}=await m.getDescriptions(S,D);if(I==="Token limit exceeded")return t(6,y=ve(d)),t(16,P=!1),void t(17,U=!1);V&&V.length>0&&V.forEach((W,K)=>{W.sections&&W.sections.forEach((ne,De)=>{ne.changes&&ne.changes.forEach(ze=>{const Pe=y[K];if(Pe&&Pe.sections){const Te=Pe.sections[De];if(Te&&Te.changes){const ft=Te.changes.find(ds=>ds.id===ze.id);ft&&(ze.originalCode=ft.originalCode,ze.modifiedCode=ft.modifiedCode,ze.diff=ft.diff)}}})})}),t(6,y=V)}catch(V){console.error("Failed to get descriptions, using skeleton explanation:",V)}}y.length===0&&t(18,Q="Failed to generate explanation.")}catch(se){console.error("Failed to get explanation:",se),t(18,Q=se instanceof Error?se.message:"An error occurred while generating the explanation.")}finally{t(16,P=!1),t(17,U=!1)}}et(()=>{const O=localStorage.getItem("anthropic_apikey");O&&(D=O),t(32,C=!0)});let ue="",ke="Apply all changes locally";return r.$$set=O=>{"changedFiles"in O&&t(0,d=O.changedFiles),"agentLabel"in O&&t(1,p=O.agentLabel),"latestUserPrompt"in O&&t(2,F=O.latestUserPrompt),"onApplyChanges"in O&&t(30,v=O.onApplyChanges),"onOpenFile"in O&&t(3,w=O.onOpenFile),"onRenderBackup"in O&&t(4,E=O.onRenderBackup),"preloadedExplanation"in O&&t(31,b=O.preloadedExplanation),"isAgentFromDifferentRepo"in O&&t(5,z=O.isAgentFromDifferentRepo)},r.$$.update=()=>{if(16385&r.$$.dirty[0]&&d&&Fe.set(d.reduce((O,B)=>{const se=B.new_path||B.old_path;return O[se]=c[se]??"none",O},{})),1&r.$$.dirty[0]&&t(36,a=JSON.stringify(d)),43&r.$$.dirty[1]&&C&&a&&a!==ue&&(t(34,ue=a),b&&b.length>0?(t(6,y=b),t(16,P=!1),t(17,U=!1)):Re(),t(9,ce=!1),t(10,de=!1),t(33,$e={})),448&r.$$.dirty[0]&&y&&y.length>0){const O=$t(y);Array.from(O).forEach(k=>{oe[k]===void 0&&t(8,oe[k]=!G,oe)});const B=Object.keys(oe).filter(k=>oe[k]),se=Array.from(O);se.length>0&&t(7,G=!se.some(k=>B.includes(k)))}if(256&r.$$.dirty[0]&&t(23,n=Object.values(oe).some(Boolean)),64&r.$$.dirty[0]|4&r.$$.dirty[1]&&y&&y.length>0&&y.flatMap(O=>O.sections||[]).flatMap(O=>O.changes).forEach(O=>{$e[O.path]||t(33,$e[O.path]=O.modifiedCode,$e)}),64&r.$$.dirty[0]&&t(35,i=JSON.stringify(y)),16448&r.$$.dirty[0]|16&r.$$.dirty[1]&&t(11,s=(()=>{if(i&&c){const O=$t(y);return O.size!==0&&Array.from(O).some(B=>c[B]!=="applied")}return!1})()),16384&r.$$.dirty[0]&&t(10,de=Object.keys(c).every(O=>c[O]==="applied")),16384&r.$$.dirty[0]&&t(12,o=Object.keys(c).filter(O=>c[O]==="pending")),1&r.$$.dirty[0]&&t(22,l=d.length===0),17472&r.$$.dirty[0]|16&r.$$.dirty[1]&&i&&de){const O=$t(y);Array.from(O).every(B=>c[B]==="applied")||t(10,de=!1)}7712&r.$$.dirty[0]&&t(13,u=z||ce||de||o.length>0||!s),15904&r.$$.dirty[0]&&(u?z?t(21,ke="Cannot apply changes from a different repository locally"):ce?t(21,ke="Applying changes..."):de?t(21,ke="All changes applied"):o.length>0?t(21,ke="Waiting for changes to apply"):s||t(21,ke="No changes to apply"):t(21,ke="Apply all changes locally"))},[d,p,F,w,E,z,y,G,oe,ce,de,s,o,u,c,_,P,U,Q,ae,J,ke,l,n,Fe,function(){const O=$t(y),B=Object.values(oe).some(Boolean);t(7,G=B),Array.from(O).forEach(se=>{t(8,oe[se]=!G,oe)})},ie,we,function(){if(!v)return;m.reportApplyChangesEvent(),t(9,ce=!0),t(10,de=!1);const{filesToApply:O,areAllPathsApplied:B}=Fs(y,d,$e);B||O.length===0?t(10,de=B):xs(O,we).then(()=>{t(9,ce=!1),t(10,de=!0)})},Re,v,b,C,$e,ue,i,a,(O,B)=>{ie(O.path,B)},O=>{we(O.path,O.originalCode,O.modifiedCode)},O=>w(O.path),function(O,B){r.$$.not_equal(oe[B.path],O)&&(oe[B.path]=O,t(8,oe),t(6,y),t(7,G),t(32,C),t(36,a),t(34,ue),t(31,b),t(0,d))},function(O,B,se,k){qe[O?"unshift":"push"](()=>{ae[100*B+10*se+k.path.length%10]=O,t(19,ae)})},function(O){J=O,t(20,J)}]}class Ju extends ee{constructor(e){super(),te(this,e,Gu,Qu,Y,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:30,onOpenFile:3,onRenderBackup:4,preloadedExplanation:31,isAgentFromDifferentRepo:5},null,[-1,-1])}}function Ei(r){let e,t,n=r[7].opts,i=_i(r);return{c(){e=A("div"),i.c(),x(e,"class","file-explorer-contents svelte-5tfpo4")},m(s,o){g(s,e,o),i.m(e,null),t=!0},p(s,o){128&o&&Y(n,n=s[7].opts)?(Z(),h(i,1,1,X),H(),i=_i(s),i.c(),f(i,1),i.m(e,null)):i.p(s,o)},i(s){t||(f(i),t=!0)},o(s){h(i),t=!1},d(s){s&&$(e),i.d(s)}}}function Yu(r){var n,i;let e,t;return e=new Ju({props:{changedFiles:r[0],onApplyChanges:r[9],onOpenFile:r[10],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[11],preloadedExplanation:(i=(n=r[7])==null?void 0:n.opts)==null?void 0:i.preloadedExplanation,isAgentFromDifferentRepo:r[5]}}),{c(){L(e.$$.fragment)},m(s,o){M(e,s,o),t=!0},p(s,o){var a,u;const l={};1&o&&(l.changedFiles=s[0]),8&o&&(l.agentLabel=s[3]),16&o&&(l.latestUserPrompt=s[4]),64&o&&(l.onRenderBackup=s[11]),128&o&&(l.preloadedExplanation=(u=(a=s[7])==null?void 0:a.opts)==null?void 0:u.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=s[5]),e.$set(l)},i(s){t||(f(e.$$.fragment,s),t=!0)},o(s){h(e.$$.fragment,s),t=!1},d(s){R(e,s)}}}function Xu(r){let e,t;return e=new to({props:{changedFiles:r[0],onApplyChanges:r[9],onOpenFile:r[10],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.changedFiles=n[0]),2&i&&(s.pendingFiles=n[1]),4&i&&(s.appliedFiles=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function _i(r){let e,t,n,i;const s=[Xu,Yu],o=[];function l(a,u){return a[6]==="changedFiles"?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),t=o[e],t?t.p(a,u):(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Ku(r){let e,t,n,i=r[0]&&Ei(r);return{c(){e=A("div"),t=A("div"),i&&i.c(),x(t,"class","file-explorer-main svelte-5tfpo4"),x(e,"class","diff-page svelte-5tfpo4")},m(s,o){g(s,e,o),q(e,t),i&&i.m(t,null),n=!0},p(s,[o]){s[0]?i?(i.p(s,o),1&o&&f(i,1)):(i=Ei(s),i.c(),f(i,1),i.m(t,null)):i&&(Z(),h(i,1,1,()=>{i=null}),H())},i(s){n||(f(i),n=!0)},o(s){h(i),n=!1},d(s){s&&$(e),i&&i.d()}}}function ec(r,e,t){let n,{changedFiles:i=[]}=e,{pendingFiles:s=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:a}=e,{isAgentFromDifferentRepo:u=!1}=e;const c=Ge(nt.key),d=Ge(Ct.key);Le(r,d,F=>t(7,n=F));let p="summary";return function(F){F.subscribe(v=>{if(v){const w=document.getElementById(Mt(v));w&&w.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(F=null){const v=He(F);return zt(Yi,v),v}(null)),r.$$set=F=>{"changedFiles"in F&&t(0,i=F.changedFiles),"pendingFiles"in F&&t(1,s=F.pendingFiles),"appliedFiles"in F&&t(2,o=F.appliedFiles),"agentLabel"in F&&t(3,l=F.agentLabel),"latestUserPrompt"in F&&t(4,a=F.latestUserPrompt),"isAgentFromDifferentRepo"in F&&t(5,u=F.isAgentFromDifferentRepo)},[i,s,o,l,a,u,p,n,d,async(F,v,w)=>{await c.applyChanges(F,v,w)},F=>c.openFile(F),()=>{t(6,p="changedFiles")}]}class tc extends ee{constructor(e){super(),te(this,e,ec,Ku,Y,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function nc(r){let e,t,n,i,s;return t=new Tt({props:{size:1}}),{c(){e=A("div"),L(t.$$.fragment),n=j(),i=A("p"),i.textContent="Loading diff view...",x(e,"class","l-center svelte-ccste2")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),q(e,i),s=!0},p:X,i(o){s||(f(t.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),s=!1},d(o){o&&$(e),R(t)}}}function ic(r){let e,t;return e=new tc({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.changedFiles=n[0].changedFiles),1&i&&(s.agentLabel=n[0].sessionSummary),1&i&&(s.latestUserPrompt=n[0].userPrompt),1&i&&(s.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function sc(r){let e,t,n,i;const s=[ic,nc],o=[];function l(a,u){return a[0]?0:1}return t=l(r),n=o[t]=s[t](r),{c(){e=A("div"),n.c(),x(e,"class","l-main svelte-ccste2")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(Z(),h(o[c],1,1,()=>{o[c]=null}),H(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function rc(r){let e,t,n,i;return e=new _s.Root({props:{$$slots:{default:[sc]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(s,o){M(e,s,o),t=!0,n||(i=ot(window,"message",r[1].onMessageFromExtension),n=!0)},p(s,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:s}),e.$set(l)},i(s){t||(f(e.$$.fragment,s),t=!0)},o(s){h(e.$$.fragment,s),t=!1},d(s){R(e,s),n=!1,i()}}}function oc(r,e,t){let n,i,s=new Cs(Si),o=new Ct(s);Le(r,o,a=>t(4,i=a)),s.registerConsumer(o);let l=new nt(s);return zt(nt.key,l),zt(Ct.key,o),et(()=>(o.onPanelLoaded(),()=>{s.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=i.opts)},[n,s,o,l,i]}new class extends ee{constructor(r){super(),te(this,r,oc,rc,Y,{})}}({target:document.getElementById("app")});
