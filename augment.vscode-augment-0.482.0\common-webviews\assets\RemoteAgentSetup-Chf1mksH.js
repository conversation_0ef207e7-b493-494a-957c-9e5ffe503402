import{S as se,i as ce,s as ie,a as oe,b as Be,H as Ue,w as Me,x as Te,y as Ge,h as b,d as xe,z as He,g as Oe,n as M,j as _e,_ as K,V as R,I as D,J as ae,c as y,e as x,f as I,$ as X,a0 as W,a1 as J,u as p,q as H,t as f,r as O,Z as mt,T as Le,C as L,D as C,F as A,L as T,M as q,ao as De,a8 as Re,aa as pt,A as ve,B as be,E as ye,X as we,ag as Fe,a6 as tt,a5 as ln,am as Ve,ab as Je,ac as Cn,a7 as yt,ax as Ye,ah as dt,ae as un,af as Ze}from"./SpinnerAugment-Cx9dt_ox.js";import{C as nt}from"./CalloutAugment-BFrX0piu.js";import{j as An}from"./types-8LwCBeyq.js";import{f as Qe}from"./index-DUiNNixO.js";import{B as je}from"./ButtonAugment-DhtPLzGu.js";import{B as Rn,A as $n,a as at,b as lt}from"./main-panel-CD6EXLpt.js";import{C as mn,G as ft,R as pn,T as Sn}from"./github-7gPAsyj4.js";import{M as gt}from"./magnifying-glass-Fv6Gz5Ea.js";import{d as xt,T as dn}from"./Content-BiWRcmeV.js";import{G as fn}from"./folder-CEjIF7oG.js";import{e as et,u as gn,o as hn}from"./BaseButton-BqzdgpkK.js";import{D as $e,C as kn,T as In}from"./index-CGnj6T3o.js";import{C as Fn}from"./CopyButton-CugjC8Pf.js";import{R as ot}from"./check-BrrMO4vE.js";import{T as Ie}from"./TextTooltipAugment-DTMpOwfF.js";import{I as rt}from"./IconButtonAugment-BjDqXmYl.js";import"./lodash-Drc0SN5U.js";import{T as wn}from"./terminal-BjJSzToG.js";import{A as zn}from"./arrow-up-right-from-square-D2UwhhNo.js";import{P as En}from"./pen-to-square-CZwCjcp0.js";import{T as vn}from"./Keybindings-C3J8hU1V.js";import{R as _t}from"./types-DDm27S8B.js";import{E as Nn}from"./exclamation-triangle-BbVpV4C-.js";import{T as Dn}from"./StatusIndicator-BAEKlH2H.js";import"./chat-types-B-te1sXh.js";import"./design-system-init-BCZOObrS.js";import"./open-in-new-window-C_TwPNdv.js";import"./diff-utils-C7XQLqYW.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-8X-F_Twk.js";import"./index-BxQII05L.js";import"./isObjectLike-BWVRxMGM.js";import"./globals-D0QH3NT1.js";import"./await_block-H61A9-v_.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-D-BqE8vd.js";import"./folder-opened-CX_GXeEO.js";import"./ellipsis-Cm0UKVWz.js";import"./MaterialIcon-8-Z76Y2_.js";import"./utils-DJhaageo.js";import"./CollapseButtonAugment-D3vAw6HE.js";import"./autofix-state-d-ymFdyn.js";import"./types-CGlLNakm.js";import"./VSCodeCodicon-B3px2_jp.js";import"./augment-logo-DdgjewTP.js";import"./chat-flags-model-GjgruWjX.js";import"./TextAreaAugment-DEYj-_0J.js";import"./CardAugment-RumqAz-v.js";function Lt(r){const e=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function ut(r){const e=r.match(/^[^/]+\/HEAD\s*->\s*(.+)$/);if(e)return ut(e[1]);const n=function(t){const o=t.match(/^refs\/remotes\/([^/]+)\/(.+)$/);if(o)return{remote:o[1],branch:o[2]};const s=t.match(/^([^/]+)\/(.+)$/);if(s){const[,c,a]=s;if(["origin","upstream","fork","github","gitlab","bitbucket"].includes(c)||c.includes("."))return{remote:c,branch:a}}return null}(r);return n?n.branch:r}function Pn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=oe(o,t[s]);return{c(){e=Be("svg"),n=new Ue(!0),this.h()},l(s){e=Me(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Te(e);n=Ge(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){He(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(s,[c]){xe(e,o=Oe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(e)}}}function Bn(r,e,n){return r.$$set=t=>{n(0,e=oe(oe({},e),_e(t)))},[e=_e(e)]}class Un extends se{constructor(e){super(),ce(this,e,Bn,Pn,ie,{})}}function Mn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=oe(o,t[s]);return{c(){e=Be("svg"),n=new Ue(!0),this.h()},l(s){e=Me(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Te(e);n=Ge(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){He(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 465c9.4 9.4 24.6 9.4 33.9 0L465 273c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9zM47 81l192 192c9.4 9.4 24.6 9.4 33.9 0L465 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',e)},p(s,[c]){xe(e,o=Oe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(e)}}}function Tn(r,e,n){return r.$$set=t=>{n(0,e=oe(oe({},e),_e(t)))},[e=_e(e)]}class Gn extends se{constructor(e){super(),ce(this,e,Tn,Mn,ie,{})}}const Hn=r=>({}),Ct=r=>({}),On=r=>({}),At=r=>({}),Vn=r=>({}),Rt=r=>({}),jn=r=>({}),St=r=>({});function qn(r){let e;return{c(){e=T(r[0])},m(n,t){x(n,e,t)},p(n,t){1&t&&q(e,n[0])},d(n){n&&b(e)}}}function kt(r){let e,n;const t=r[3].subtitle,o=K(t,r,r[4],At),s=o||function(c){let a,i;return a=new Le({props:{size:2,$$slots:{default:[Kn]},$$scope:{ctx:c}}}),{c(){L(a.$$.fragment)},m(l,$){C(a,l,$),i=!0},p(l,$){const u={};18&$&&(u.$$scope={dirty:$,ctx:l}),a.$set(u)},i(l){i||(p(a.$$.fragment,l),i=!0)},o(l){f(a.$$.fragment,l),i=!1},d(l){A(a,l)}}}(r);return{c(){e=R("div"),s&&s.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,a){x(c,e,a),s&&s.m(e,null),n=!0},p(c,a){o?o.p&&(!n||16&a)&&X(o,t,c,c[4],n?J(t,c[4],a,On):W(c[4]),At):s&&s.p&&(!n||2&a)&&s.p(c,n?a:-1)},i(c){n||(p(s,c),n=!0)},o(c){f(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Kn(r){let e;return{c(){e=T(r[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&q(e,n[1])},d(n){n&&b(e)}}}function It(r){let e,n;const t=r[3].iconRight,o=K(t,r,r[4],Ct);return{c(){e=R("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(s,c){x(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||16&c)&&X(o,t,s,s[4],n?J(t,s[4],c,Hn):W(s[4]),Ct)},i(s){n||(p(o,s),n=!0)},o(s){f(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Xn(r){let e,n,t,o,s,c,a,i;const l=r[3].iconLeft,$=K(l,r,r[4],St),u=r[3].title,w=K(u,r,r[4],Rt),g=w||function(m){let v,_;return v=new Le({props:{size:2,$$slots:{default:[qn]},$$scope:{ctx:m}}}),{c(){L(v.$$.fragment)},m(z,N){C(v,z,N),_=!0},p(z,N){const E={};17&N&&(E.$$scope={dirty:N,ctx:z}),v.$set(E)},i(z){_||(p(v.$$.fragment,z),_=!0)},o(z){f(v.$$.fragment,z),_=!1},d(z){A(v,z)}}}(r);let h=r[1]&&kt(r),d=r[2].iconRight&&It(r);return{c(){e=R("div"),$&&$.c(),n=D(),t=R("div"),o=R("div"),g&&g.c(),s=D(),h&&h.c(),c=D(),d&&d.c(),a=ae(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m(m,v){x(m,e,v),$&&$.m(e,null),x(m,n,v),x(m,t,v),I(t,o),g&&g.m(o,null),I(t,s),h&&h.m(t,null),x(m,c,v),d&&d.m(m,v),x(m,a,v),i=!0},p(m,[v]){$&&$.p&&(!i||16&v)&&X($,l,m,m[4],i?J(l,m[4],v,jn):W(m[4]),St),w?w.p&&(!i||16&v)&&X(w,u,m,m[4],i?J(u,m[4],v,Vn):W(m[4]),Rt):g&&g.p&&(!i||1&v)&&g.p(m,i?v:-1),m[1]?h?(h.p(m,v),2&v&&p(h,1)):(h=kt(m),h.c(),p(h,1),h.m(t,null)):h&&(H(),f(h,1,1,()=>{h=null}),O()),m[2].iconRight?d?(d.p(m,v),4&v&&p(d,1)):(d=It(m),d.c(),p(d,1),d.m(a.parentNode,a)):d&&(H(),f(d,1,1,()=>{d=null}),O())},i(m){i||(p($,m),p(g,m),p(h),p(d),i=!0)},o(m){f($,m),f(g,m),f(h),f(d),i=!1},d(m){m&&(b(e),b(n),b(t),b(c),b(a)),$&&$.d(m),g&&g.d(m),h&&h.d(),d&&d.d(m)}}}function Wn(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=mt(t);let{title:c="Select an option"}=e,{subtitle:a=""}=e;return r.$$set=i=>{"title"in i&&n(0,c=i.title),"subtitle"in i&&n(1,a=i.subtitle),"$$scope"in i&&n(4,o=i.$$scope)},[c,a,s,t,o]}class bn extends se{constructor(e){super(),ce(this,e,Wn,Xn,ie,{title:0,subtitle:1})}}const Jn=r=>({}),Ft=r=>({slot:"iconLeft"}),Yn=r=>({}),zt=r=>({slot:"iconRight"});function Et(r,e,n){const t=r.slice();return t[19]=e[n],t}const Zn=r=>({}),Nt=r=>({}),Qn=r=>({}),Dt=r=>({}),eo=r=>({}),Pt=r=>({slot:"iconLeft"}),to=r=>({}),Bt=r=>({slot:"title"}),no=r=>({}),Ut=r=>({slot:"iconRight"});function oo(r){let e,n,t,o,s;return n=new bn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[co],iconLeft:[so]},$$scope:{ctx:r}}}),{c(){e=R("button"),L(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=r[10]},m(c,a){x(c,e,a),C(n,e,null),t=!0,o||(s=[Re(e,"click",r[16]),Re(e,"keydown",r[17])],o=!0)},p(c,a){const i={};8&a&&(i.title=c[3]),16&a&&(i.subtitle=c[4]),262144&a&&(i.$$scope={dirty:a,ctx:c}),n.$set(i),(!t||1024&a)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){f(n.$$.fragment,c),t=!1},d(c){c&&b(e),A(n),o=!1,pt(s)}}}function ro(r){let e,n,t;function o(c){r[15](c)}let s={onOpenChange:r[9],$$slots:{default:[ho]},$$scope:{ctx:r}};return r[1]!==void 0&&(s.requestClose=r[1]),e=new $e.Root({props:s}),ve.push(()=>be(e,"requestClose",o)),{c(){L(e.$$.fragment)},m(c,a){C(e,c,a),t=!0},p(c,a){const i={};512&a&&(i.onOpenChange=c[9]),263641&a&&(i.$$scope={dirty:a,ctx:c}),!n&&2&a&&(n=!0,i.requestClose=c[1],ye(()=>n=!1)),e.$set(i)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){f(e.$$.fragment,c),t=!1},d(c){A(e,c)}}}function so(r){let e;const n=r[13].iconLeft,t=K(n,r,r[18],Ft);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&X(t,n,o,o[18],e?J(n,o[18],s,Jn):W(o[18]),Ft)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function co(r){let e;const n=r[13].iconRight,t=K(n,r,r[18],zt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&X(t,n,o,o[18],e?J(n,o[18],s,Yn):W(o[18]),zt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function io(r){let e;const n=r[13].iconLeft,t=K(n,r,r[18],Pt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&X(t,n,o,o[18],e?J(n,o[18],s,eo):W(o[18]),Pt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function ao(r){let e;const n=r[13].title,t=K(n,r,r[18],Bt),o=t||function(s){let c;return{c(){c=T(s[3])},m(a,i){x(a,c,i)},p(a,i){8&i&&q(c,a[3])},d(a){a&&b(c)}}}(r);return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t?t.p&&(!e||262144&c)&&X(t,n,s,s[18],e?J(n,s[18],c,to):W(s[18]),Bt):o&&o.p&&(!e||8&c)&&o.p(s,e?c:-1)},i(s){e||(p(o,s),e=!0)},o(s){f(o,s),e=!1},d(s){o&&o.d(s)}}}function lo(r){let e;const n=r[13].iconRight,t=K(n,r,r[18],Ut),o=t||function(s){let c,a;return c=new mn({}),{c(){L(c.$$.fragment)},m(i,l){C(c,i,l),a=!0},i(i){a||(p(c.$$.fragment,i),a=!0)},o(i){f(c.$$.fragment,i),a=!1},d(i){A(c,i)}}}();return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t&&t.p&&(!e||262144&c)&&X(t,n,s,s[18],e?J(n,s[18],c,no):W(s[18]),Ut)},i(s){e||(p(o,s),e=!0)},o(s){f(o,s),e=!1},d(s){o&&o.d(s)}}}function uo(r){let e,n,t,o;return n=new bn({props:{subtitle:r[4],$$slots:{iconRight:[lo],title:[ao],iconLeft:[io]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=r[10]?-1:0),we(e,"disabled",r[10])},m(s,c){x(s,e,c),C(n,e,null),o=!0},p(s,c){const a={};16&c&&(a.subtitle=s[4]),262152&c&&(a.$$scope={dirty:c,ctx:s}),n.$set(a),(!o||1024&c&&t!==(t=s[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&we(e,"disabled",s[10])},i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){f(n.$$.fragment,s),o=!1},d(s){s&&b(e),A(n)}}}function $o(r){let e,n;return e=new $e.Label({props:{$$slots:{default:[po]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};262400&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function mo(r){let e,n,t=[],o=new Map,s=et(r[6]);const c=a=>a[7](a[19]);for(let a=0;a<s.length;a+=1){let i=Et(r,s,a),l=c(i);o.set(l,t[a]=Mt(l,i))}return{c(){for(let a=0;a<t.length;a+=1)t[a].c();e=ae()},m(a,i){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(a,i);x(a,e,i),n=!0},p(a,i){2241&i&&(s=et(a[6]),H(),t=gn(t,i,c,1,a,s,o,e.parentNode,hn,Mt,e,Et),O())},i(a){if(!n){for(let i=0;i<s.length;i+=1)p(t[i]);n=!0}},o(a){for(let i=0;i<t.length;i+=1)f(t[i]);n=!1},d(a){a&&b(e);for(let i=0;i<t.length;i+=1)t[i].d(a)}}}function po(r){let e;return{c(){e=T(r[8])},m(n,t){x(n,e,t)},p(n,t){256&t&&q(e,n[8])},d(n){n&&b(e)}}}function fo(r){let e,n,t=r[7](r[19])+"";return{c(){e=T(t),n=D()},m(o,s){x(o,e,s),x(o,n,s)},p(o,s){192&s&&t!==(t=o[7](o[19])+"")&&q(e,t)},d(o){o&&(b(e),b(n))}}}function Mt(r,e){let n,t,o;function s(){return e[14](e[19])}return t=new $e.Item({props:{onSelect:s,highlight:e[0]===e[19],$$slots:{default:[fo]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ae(),L(t.$$.fragment),this.first=n},m(c,a){x(c,n,a),C(t,c,a),o=!0},p(c,a){e=c;const i={};64&a&&(i.onSelect=s),65&a&&(i.highlight=e[0]===e[19]),262336&a&&(i.$$scope={dirty:a,ctx:e}),t.$set(i)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){f(t.$$.fragment,c),o=!1},d(c){c&&b(n),A(t,c)}}}function go(r){let e,n,t,o,s;const c=r[13]["dropdown-top"],a=K(c,r,r[18],Dt),i=r[13]["dropdown-content"],l=K(i,r,r[18],Nt),$=l||function(u){let w,g,h,d;const m=[mo,$o],v=[];function _(z,N){return z[6].length>0?0:1}return w=_(u),g=v[w]=m[w](u),{c(){g.c(),h=ae()},m(z,N){v[w].m(z,N),x(z,h,N),d=!0},p(z,N){let E=w;w=_(z),w===E?v[w].p(z,N):(H(),f(v[E],1,1,()=>{v[E]=null}),O(),g=v[w],g?g.p(z,N):(g=v[w]=m[w](z),g.c()),p(g,1),g.m(h.parentNode,h))},i(z){d||(p(g),d=!0)},o(z){f(g),d=!1},d(z){z&&b(h),v[w].d(z)}}}(r);return{c(){e=R("div"),n=R("div"),a&&a.c(),t=D(),o=R("div"),$&&$.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(u,w){x(u,e,w),I(e,n),a&&a.m(n,null),I(e,t),I(e,o),$&&$.m(o,null),s=!0},p(u,w){a&&a.p&&(!s||262144&w)&&X(a,c,u,u[18],s?J(c,u[18],w,Qn):W(u[18]),Dt),l?l.p&&(!s||262144&w)&&X(l,i,u,u[18],s?J(i,u[18],w,Zn):W(u[18]),Nt):$&&$.p&&(!s||449&w)&&$.p(u,s?w:-1)},i(u){s||(p(a,u),p($,u),s=!0)},o(u){f(a,u),f($,u),s=!1},d(u){u&&b(e),a&&a.d(u),$&&$.d(u)}}}function ho(r){let e,n,t,o;return e=new $e.Trigger({props:{$$slots:{default:[uo]},$$scope:{ctx:r}}}),t=new $e.Content({props:{align:"start",side:"bottom",$$slots:{default:[go]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=D(),L(t.$$.fragment)},m(s,c){C(e,s,c),x(s,n,c),C(t,s,c),o=!0},p(s,c){const a={};263192&c&&(a.$$scope={dirty:c,ctx:s}),e.$set(a);const i={};262593&c&&(i.$$scope={dirty:c,ctx:s}),t.$set(i)},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){f(e.$$.fragment,s),f(t.$$.fragment,s),o=!1},d(s){s&&b(n),A(e,s),A(t,s)}}}function wo(r){let e,n,t,o;const s=[ro,oo],c=[];function a(i,l){return i[2]==="dropdown"?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=R("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,[l]){let $=n;n=a(i),n===$?c[n].p(i,l):(H(),f(c[$],1,1,()=>{c[$]=null}),O(),t=c[n],t?t.p(i,l):(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){f(t),o=!1},d(i){i&&b(e),c[n].d()}}}function vo(r,e,n){let{$$slots:t={},$$scope:o}=e,{type:s="button"}=e,{title:c="Select an option"}=e,{subtitle:a=""}=e,{onClick:i=()=>{}}=e,{items:l=[]}=e,{selectedItem:$}=e,{formatItemLabel:u=_=>(_==null?void 0:_.toString())||""}=e,{noItemsLabel:w="No items found"}=e,{onDropdownOpenChange:g=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:d=!1}=e;function m(_){n(0,$=_),v("select",_)}const v=De();return r.$$set=_=>{"type"in _&&n(2,s=_.type),"title"in _&&n(3,c=_.title),"subtitle"in _&&n(4,a=_.subtitle),"onClick"in _&&n(5,i=_.onClick),"items"in _&&n(6,l=_.items),"selectedItem"in _&&n(0,$=_.selectedItem),"formatItemLabel"in _&&n(7,u=_.formatItemLabel),"noItemsLabel"in _&&n(8,w=_.noItemsLabel),"onDropdownOpenChange"in _&&n(9,g=_.onDropdownOpenChange),"requestClose"in _&&n(1,h=_.requestClose),"disabled"in _&&n(10,d=_.disabled),"$$scope"in _&&n(18,o=_.$$scope)},[$,h,s,c,a,i,l,u,w,g,d,m,v,t,_=>m(_),function(_){h=_,n(1,h)},()=>{i(),v("click")},_=>{_.key!=="Enter"&&_.key!==" "||(i(),v("click"))},o]}class yn extends se{constructor(e){super(),ce(this,e,vo,wo,ie,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function bo(r){let e,n;return e=new yn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[So],iconLeft:[xo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};16388&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function yo(r){let e,n,t,o;e=new yn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[6],$$slots:{iconRight:[zo],iconLeft:[ko]},$$scope:{ctx:r}}});let s=r[3]&&Tt(r);return{c(){L(e.$$.fragment),n=D(),s&&s.c(),t=ae()},m(c,a){C(e,c,a),x(c,n,a),s&&s.m(c,a),x(c,t,a),o=!0},p(c,a){const i={};2&a&&(i.title=c[1]?"Cancel":"Connect to GitHub"),16386&a&&(i.$$scope={dirty:a,ctx:c}),e.$set(i),c[3]?s?(s.p(c,a),8&a&&p(s,1)):(s=Tt(c),s.c(),p(s,1),s.m(t.parentNode,t)):s&&(H(),f(s,1,1,()=>{s=null}),O())},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){f(e.$$.fragment,c),f(s),o=!1},d(c){c&&(b(n),b(t)),A(e,c),s&&s.d(c)}}}function xo(r){let e,n;return e=new ft({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function _o(r){let e,n;return e=new Le({props:{size:1,weight:"medium",$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Lo(r){let e,n,t,o;return e=new Ve({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new Le({props:{size:1,weight:"medium",$$slots:{default:[Ao]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=D(),L(t.$$.fragment)},m(s,c){C(e,s,c),x(s,n,c),C(t,s,c),o=!0},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){f(e.$$.fragment,s),f(t.$$.fragment,s),o=!1},d(s){s&&b(n),A(e,s),A(t,s)}}}function Co(r){let e;return{c(){e=T("Revoke Access")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Ao(r){let e;return{c(){e=T("Revoking...")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Ro(r){let e,n,t,o;const s=[Lo,_o],c=[];function a(i,l){return i[2]?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=ae()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let $=e;e=a(i),e!==$&&(H(),f(c[$],1,1,()=>{c[$]=null}),O(),n=c[e],n||(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){f(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function So(r){let e,n,t;return n=new $e.Item({props:{color:"error",onSelect:r[8],$$slots:{default:[Ro]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};16388&s&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function ko(r){let e,n;return e=new ft({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Io(r){let e,n;return e=new kn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Fo(r){let e,n;return e=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function zo(r){let e,n,t,o;const s=[Fo,Io],c=[];function a(i,l){return i[1]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=R("div"),t.c(),y(e,"slot","iconRight")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let $=n;n=a(i),n!==$&&(H(),f(c[$],1,1,()=>{c[$]=null}),O(),t=c[n],t||(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){f(t),o=!1},d(i){i&&b(e),c[n].d()}}}function Tt(r){let e,n,t,o,s,c;n=new Le({props:{size:1,$$slots:{default:[Eo]},$$scope:{ctx:r}}});let a=r[4]&&Gt(r);return s=new Le({props:{size:1,$$slots:{default:[Po]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),t=D(),a&&a.c(),o=D(),L(s.$$.fragment),y(e,"class","github-auth-error svelte-c4wp0y")},m(i,l){x(i,e,l),C(n,e,null),I(e,t),a&&a.m(e,null),I(e,o),C(s,e,null),c=!0},p(i,l){const $={};16384&l&&($.$$scope={dirty:l,ctx:i}),n.$set($),i[4]?a?(a.p(i,l),16&l&&p(a,1)):(a=Gt(i),a.c(),p(a,1),a.m(e,o)):a&&(H(),f(a,1,1,()=>{a=null}),O());const u={};16392&l&&(u.$$scope={dirty:l,ctx:i}),s.$set(u)},i(i){c||(p(n.$$.fragment,i),p(a),p(s.$$.fragment,i),c=!0)},o(i){f(n.$$.fragment,i),f(a),f(s.$$.fragment,i),c=!1},d(i){i&&b(e),A(n),a&&a.d(),A(s)}}}function Eo(r){let e;return{c(){e=T("An error occurred while authenticating with GitHub.")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Gt(r){let e,n,t,o,s,c,a;return e=new Le({props:{size:1,$$slots:{default:[No]},$$scope:{ctx:r}}}),o=new Le({props:{size:1,class:"github-auth-error-url",$$slots:{default:[Do]},$$scope:{ctx:r}}}),c=new Fn({props:{text:r[4],tooltipNested:!1}}),{c(){L(e.$$.fragment),n=D(),t=R("div"),L(o.$$.fragment),s=D(),L(c.$$.fragment),y(t,"class","github-auth-error-url-container svelte-c4wp0y")},m(i,l){C(e,i,l),x(i,n,l),x(i,t,l),C(o,t,null),I(t,s),C(c,t,null),a=!0},p(i,l){const $={};16384&l&&($.$$scope={dirty:l,ctx:i}),e.$set($);const u={};16400&l&&(u.$$scope={dirty:l,ctx:i}),o.$set(u);const w={};16&l&&(w.text=i[4]),c.$set(w)},i(i){a||(p(e.$$.fragment,i),p(o.$$.fragment,i),p(c.$$.fragment,i),a=!0)},o(i){f(e.$$.fragment,i),f(o.$$.fragment,i),f(c.$$.fragment,i),a=!1},d(i){i&&(b(n),b(t)),A(e,i),A(o),A(c)}}}function No(r){let e;return{c(){e=T("Visit or copy the following URL in your browser to authenticate manually:")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Do(r){let e,n;return{c(){e=R("a"),n=T(r[4]),y(e,"href",r[4]),y(e,"target","_blank")},m(t,o){x(t,e,o),I(e,n)},p(t,o){16&o&&q(n,t[4]),16&o&&y(e,"href",t[4])},d(t){t&&b(e)}}}function Po(r){let e;return{c(){e=T(r[3])},m(n,t){x(n,e,t)},p(n,t){8&t&&q(e,n[3])},d(n){n&&b(e)}}}function Bo(r){let e,n,t,o,s;const c=[yo,bo],a=[];function i(l,$){return l[0]?1:0}return t=i(r),o=a[t]=c[t](r),{c(){e=R("div"),n=R("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-c4wp0y")},m(l,$){x(l,e,$),I(e,n),a[t].m(n,null),s=!0},p(l,[$]){let u=t;t=i(l),t===u?a[t].p(l,$):(H(),f(a[u],1,1,()=>{a[u]=null}),O(),o=a[t],o?o.p(l,$):(o=a[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){s||(p(o),s=!0)},o(l){f(o),s=!1},d(l){l&&b(e),a[t].d()}}}function Uo(r,e,n){const t=De(),o=Fe(fn.key);let s,c,a=!1,i=!1,l=!1,$=null,u=null;async function w(){if(!l){n(2,l=!0);try{const g=await o.revokeGithubAccess();g.success?(n(0,a=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",g.message)}catch(g){console.error("Error revoking GitHub access:",g)}finally{n(2,l=!1)}}}return tt(async()=>{await async function(){try{const g=await o.isGithubAuthenticated();g!==a?(n(0,a=g),t("authStateChange",{isAuthenticated:a})):n(0,a=g)}catch(g){console.error("Failed to check GitHub authentication status:",g),n(0,a=!1),t("authStateChange",{isAuthenticated:!1})}}()}),ln(()=>{$&&(clearTimeout($),$=null),u&&(clearInterval(u),u=null)}),[a,i,l,s,c,()=>{},async function(){if(n(3,s=void 0),n(4,c=void 0),i)return n(1,i=!1),void($&&(clearTimeout($),$=null));n(1,i=!0);try{const{success:g,message:h,url:d}=await o.authenticateGithub();if(!g)throw n(4,c=d),new Error(h);u=setInterval(async()=>{try{await o.isGithubAuthenticated()&&(n(0,a=!0),n(1,i=!1),t("authStateChange",{isAuthenticated:!0}),u&&clearInterval(u),$&&(clearTimeout($),$=null))}catch(m){console.error("Failed to check GitHub authentication status:",m)}},5e3),$=setTimeout(()=>{u&&clearInterval(u),n(1,i=!1),$=null},6e4)}catch(g){console.error("Failed to authenticate with GitHub:",g),n(3,s=`Error: ${g instanceof Error?g.message:String(g)}`),n(1,i=!1)}},w,()=>{w()}]}class Mo extends se{constructor(e){super(),ce(this,e,Uo,Bo,ie,{})}}const To=r=>({}),Ht=r=>({});function Ot(r,e,n){const t=r.slice();return t[27]=e[n],t[29]=n,t}const Go=r=>({item:64&r}),Vt=r=>({item:r[27]}),Ho=r=>({}),jt=r=>({}),Oo=r=>({}),qt=r=>({}),Vo=r=>({}),Kt=r=>({}),jo=r=>({}),Xt=r=>({}),qo=r=>({}),Wt=r=>({});function Ko(r){let e,n,t,o,s,c,a,i,l,$,u,w,g,h;const d=[Jo,Wo],m=[];function v(E,P){return E[4]?0:1}o=v(r),s=m[o]=d[o](r);const _=[Zo,Yo],z=[];function N(E,P){return E[17].title?0:1}return i=N(r),l=z[i]=_[i](r),w=new mn({}),{c(){e=R("div"),n=R("div"),t=R("div"),s.c(),c=D(),a=R("span"),l.c(),$=D(),u=R("div"),L(w.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-145zgu0"),y(a,"class","c-searchable-dropdown__button-text svelte-145zgu0"),y(n,"class","c-searchable-dropdown__icon-text svelte-145zgu0"),y(u,"class","c-searchable-dropdown__chevron svelte-145zgu0"),y(e,"class","c-searchable-dropdown__button svelte-145zgu0"),y(e,"role","button"),y(e,"tabindex",g=r[5]?-1:0),we(e,"c-searchable-dropdown__button--disabled",r[5])},m(E,P){x(E,e,P),I(e,n),I(n,t),m[o].m(t,null),I(n,c),I(n,a),z[i].m(a,null),I(e,$),I(e,u),C(w,u,null),h=!0},p(E,P){let k=o;o=v(E),o===k?m[o].p(E,P):(H(),f(m[k],1,1,()=>{m[k]=null}),O(),s=m[o],s?s.p(E,P):(s=m[o]=d[o](E),s.c()),p(s,1),s.m(t,null));let Q=i;i=N(E),i===Q?z[i].p(E,P):(H(),f(z[Q],1,1,()=>{z[Q]=null}),O(),l=z[i],l?l.p(E,P):(l=z[i]=_[i](E),l.c()),p(l,1),l.m(a,null)),(!h||32&P&&g!==(g=E[5]?-1:0))&&y(e,"tabindex",g),(!h||32&P)&&we(e,"c-searchable-dropdown__button--disabled",E[5])},i(E){h||(p(s),p(l),p(w.$$.fragment,E),h=!0)},o(E){f(s),f(l),f(w.$$.fragment,E),h=!1},d(E){E&&b(e),m[o].d(),z[i].d(),A(w)}}}function Xo(r){let e,n,t,o,s,c,a,i;const l=r[18].searchIcon,$=K(l,r,r[25],Wt),u=$||function(g){let h;const d=g[18].icon,m=K(d,g,g[25],Xt);return{c(){m&&m.c()},m(v,_){m&&m.m(v,_),h=!0},p(v,_){m&&m.p&&(!h||33554432&_)&&X(m,d,v,v[25],h?J(d,v[25],_,jo):W(v[25]),Xt)},i(v){h||(p(m,v),h=!0)},o(v){f(m,v),h=!1},d(v){m&&m.d(v)}}}(r);let w=r[17].inputButton&&Jt(r);return{c(){e=R("div"),n=R("div"),u&&u.c(),t=D(),o=R("input"),s=D(),w&&w.c(),y(n,"class","c-searchable-dropdown__icon svelte-145zgu0"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-145zgu0"),y(o,"placeholder",r[3]),y(e,"class","c-searchable-dropdown__input-container svelte-145zgu0")},m(g,h){x(g,e,h),I(e,n),u&&u.m(n,null),I(e,t),I(e,o),yt(o,r[0]),I(e,s),w&&w.m(e,null),c=!0,a||(i=[Re(o,"input",r[21]),Re(o,"input",r[22]),Re(o,"click",Ye(r[19])),Re(o,"mousedown",Ye(r[20]))],a=!0)},p(g,h){$?$.p&&(!c||33554432&h)&&X($,l,g,g[25],c?J(l,g[25],h,qo):W(g[25]),Wt):u&&u.p&&(!c||33554432&h)&&u.p(g,c?h:-1),(!c||8&h)&&y(o,"placeholder",g[3]),1&h&&o.value!==g[0]&&yt(o,g[0]),g[17].inputButton?w?(w.p(g,h),131072&h&&p(w,1)):(w=Jt(g),w.c(),p(w,1),w.m(e,null)):w&&(H(),f(w,1,1,()=>{w=null}),O())},i(g){c||(p(u,g),p(w),c=!0)},o(g){f(u,g),f(w),c=!1},d(g){g&&b(e),u&&u.d(g),w&&w.d(),a=!1,pt(i)}}}function Wo(r){let e;const n=r[18].icon,t=K(n,r,r[25],qt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&X(t,n,o,o[25],e?J(n,o[25],s,Oo):W(o[25]),qt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Jo(r){let e,n;return e=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Yo(r){let e,n=(r[4]?r[11]:r[2])+"";return{c(){e=T(n)},m(t,o){x(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&q(e,n)},i:M,o:M,d(t){t&&b(e)}}}function Zo(r){let e;const n=r[18].title,t=K(n,r,r[25],jt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&X(t,n,o,o[25],e?J(n,o[25],s,Ho):W(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Jt(r){let e;const n=r[18].inputButton,t=K(n,r,r[25],Kt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&X(t,n,o,o[25],e?J(n,o[25],s,Vo):W(o[25]),Kt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Qo(r){let e,n,t,o;const s=[Xo,Ko],c=[];function a(i,l){return i[12]?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=ae()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let $=e;e=a(i),e===$?c[e].p(i,l):(H(),f(c[$],1,1,()=>{c[$]=null}),O(),n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){f(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function Yt(r){let e,n;return e=new $e.Content({props:{side:"bottom",align:"start",$$slots:{default:[cr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33689298&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function er(r){let e,n;return e=new $e.Item({props:{disabled:!0,$$slots:{default:[or]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33555456&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function tr(r){let e,n,t=[],o=new Map,s=et(r[6]);const c=a=>a[27]===null?`null-item-${a[29]}`:a[8](a[27],a[29]);for(let a=0;a<s.length;a+=1){let i=Ot(r,s,a),l=c(i);o.set(l,t[a]=Zt(l,i))}return{c(){for(let a=0;a<t.length;a+=1)t[a].c();e=ae()},m(a,i){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(a,i);x(a,e,i),n=!0},p(a,i){33620930&i&&(s=et(a[6]),H(),t=gn(t,i,c,1,a,s,o,e.parentNode,hn,Zt,e,Ot),O())},i(a){if(!n){for(let i=0;i<s.length;i+=1)p(t[i]);n=!0}},o(a){for(let i=0;i<t.length;i+=1)f(t[i]);n=!1},d(a){a&&b(e);for(let i=0;i<t.length;i+=1)t[i].d(a)}}}function nr(r){let e,n;return e=new $e.Item({props:{disabled:!0,$$slots:{default:[sr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33556480&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function or(r){let e;return{c(){e=T(r[10])},m(n,t){x(n,e,t)},p(n,t){1024&t&&q(e,n[10])},d(n){n&&b(e)}}}function rr(r){let e,n;const t=r[18].item,o=K(t,r,r[25],Vt),s=o||function(c){let a,i=c[7](c[27])+"";return{c(){a=T(i)},m(l,$){x(l,a,$)},p(l,$){192&$&&i!==(i=l[7](l[27])+"")&&q(a,i)},d(l){l&&b(a)}}}(r);return{c(){s&&s.c(),e=D()},m(c,a){s&&s.m(c,a),x(c,e,a),n=!0},p(c,a){o?o.p&&(!n||33554496&a)&&X(o,t,c,c[25],n?J(t,c[25],a,Go):W(c[25]),Vt):s&&s.p&&(!n||192&a)&&s.p(c,n?a:-1)},i(c){n||(p(s,c),n=!0)},o(c){f(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Zt(r,e){let n,t,o;function s(){return e[23](e[27])}return t=new $e.Item({props:{onSelect:s,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[rr]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ae(),L(t.$$.fragment),this.first=n},m(c,a){x(c,n,a),C(t,c,a),o=!0},p(c,a){e=c;const i={};64&a&&(i.onSelect=s),706&a&&(i.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&a&&(i.$$scope={dirty:a,ctx:e}),t.$set(i)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){f(t.$$.fragment,c),o=!1},d(c){c&&b(n),A(t,c)}}}function sr(r){let e,n,t,o,s,c;return n=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){e=R("div"),L(n.$$.fragment),t=D(),o=R("span"),s=T(r[11]),y(e,"class","c-searchable-dropdown__loading svelte-145zgu0")},m(a,i){x(a,e,i),C(n,e,null),I(e,t),I(e,o),I(o,s),c=!0},p(a,i){(!c||2048&i)&&q(s,a[11])},i(a){c||(p(n.$$.fragment,a),c=!0)},o(a){f(n.$$.fragment,a),c=!1},d(a){a&&b(e),A(n)}}}function Qt(r){let e;const n=r[18].footer,t=K(n,r,r[25],Ht);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&X(t,n,o,o[25],e?J(n,o[25],s,To):W(o[25]),Ht)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function cr(r){let e,n,t,o,s,c;const a=[nr,tr,er],i=[];function l(u,w){return u[4]?0:u[6].length>0?1:u[10]?2:-1}~(e=l(r))&&(n=i[e]=a[e](r));let $=r[17].footer&&Qt(r);return{c(){n&&n.c(),t=D(),$&&$.c(),o=D(),s=R("div"),Cn(s,"margin-bottom","var(--ds-spacing-2)")},m(u,w){~e&&i[e].m(u,w),x(u,t,w),$&&$.m(u,w),x(u,o,w),x(u,s,w),c=!0},p(u,w){let g=e;e=l(u),e===g?~e&&i[e].p(u,w):(n&&(H(),f(i[g],1,1,()=>{i[g]=null}),O()),~e?(n=i[e],n?n.p(u,w):(n=i[e]=a[e](u),n.c()),p(n,1),n.m(t.parentNode,t)):n=null),u[17].footer?$?($.p(u,w),131072&w&&p($,1)):($=Qt(u),$.c(),p($,1),$.m(o.parentNode,o)):$&&(H(),f($,1,1,()=>{$=null}),O())},i(u){c||(p(n),p($),c=!0)},o(u){f(n),f($),c=!1},d(u){u&&(b(t),b(o),b(s)),~e&&i[e].d(u),$&&$.d(u)}}}function ir(r){let e,n,t,o;e=new $e.Trigger({props:{$$slots:{default:[Qo]},$$scope:{ctx:r}}});let s=!r[5]&&Yt(r);return{c(){L(e.$$.fragment),n=D(),s&&s.c(),t=ae()},m(c,a){C(e,c,a),x(c,n,a),s&&s.m(c,a),x(c,t,a),o=!0},p(c,a){const i={};33691709&a&&(i.$$scope={dirty:a,ctx:c}),e.$set(i),c[5]?s&&(H(),f(s,1,1,()=>{s=null}),O()):s?(s.p(c,a),32&a&&p(s,1)):(s=Yt(c),s.c(),p(s,1),s.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){f(e.$$.fragment,c),f(s),o=!1},d(c){c&&(b(n),b(t)),A(e,c),s&&s.d(c)}}}function ar(r){let e,n,t,o;function s(a){r[24](a)}let c={onOpenChange:r[14],$$slots:{default:[ir]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new $e.Root({props:c}),ve.push(()=>be(n,"requestClose",s)),{c(){e=R("div"),L(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-145zgu0")},m(a,i){x(a,e,i),C(n,e,null),o=!0},p(a,[i]){const l={};33693439&i&&(l.$$scope={dirty:i,ctx:a}),!t&&8192&i&&(t=!0,l.requestClose=a[13],ye(()=>t=!1)),n.$set(l)},i(a){o||(p(n.$$.fragment,a),o=!0)},o(a){f(n.$$.fragment,a),o=!1},d(a){a&&b(e),A(n)}}}function lr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=mt(t);let{title:c=""}=e,{placeholder:a="Search..."}=e,{isLoading:i=!1}=e,{disabled:l=!1}=e,{searchValue:$=""}=e,{items:u=[]}=e,{selectedItem:w=null}=e,{itemLabelFn:g=k=>(k==null?void 0:k.toString())||""}=e,{itemKeyFn:h=k=>(k==null?void 0:k.toString())||""}=e,{isItemSelected:d}=e,{noItemsLabel:m="No items found"}=e,{loadingLabel:v="Loading..."}=e,_=!1,z=()=>{};const N=De();function E(k){n(0,$=k),N("search",k)}function P(k){n(1,w=k),N("select",k),z()}return r.$$set=k=>{"title"in k&&n(2,c=k.title),"placeholder"in k&&n(3,a=k.placeholder),"isLoading"in k&&n(4,i=k.isLoading),"disabled"in k&&n(5,l=k.disabled),"searchValue"in k&&n(0,$=k.searchValue),"items"in k&&n(6,u=k.items),"selectedItem"in k&&n(1,w=k.selectedItem),"itemLabelFn"in k&&n(7,g=k.itemLabelFn),"itemKeyFn"in k&&n(8,h=k.itemKeyFn),"isItemSelected"in k&&n(9,d=k.isItemSelected),"noItemsLabel"in k&&n(10,m=k.noItemsLabel),"loadingLabel"in k&&n(11,v=k.loadingLabel),"$$scope"in k&&n(25,o=k.$$scope)},[$,w,c,a,i,l,u,g,h,d,m,v,_,z,function(k){if(!l){if(n(12,_=k),k&&w){const Q=g(w);n(0,$=Q),N("search",""),setTimeout(()=>{const le=document.querySelector(".c-searchable-dropdown__trigger-input");le&&le.select()},0)}else k&&(n(0,$=""),N("search",""));N("openChange",k)}},E,P,s,t,function(k){Je.call(this,r,k)},function(k){Je.call(this,r,k)},function(){$=this.value,n(0,$)},k=>E(k.currentTarget.value),k=>P(k),function(k){z=k,n(13,z)},o]}class $t extends se{constructor(e){super(),ce(this,e,lr,ar,ie,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function ur(r){let e,n,t;return n=new nt({props:{color:r[7],variant:"soft",size:2,$$slots:{default:[gr]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-btbfel")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};128&s[0]&&(c.color=o[7]),16387&s[0]|32&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function $r(r){var h;let e,n,t,o,s,c,a,i,l;function $(d){r[32](d)}let u={title:r[18],placeholder:"Search repositories...",itemKeyFn:Fr,isLoading:r[10],disabled:!r[8].length,items:r[9],selectedItem:r[2],itemLabelFn:zr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[wr],icon:[hr]},$$scope:{ctx:r}};function w(d){r[37](d)}r[13]!==void 0&&(u.searchValue=r[13]),t=new $t({props:u}),ve.push(()=>be(t,"searchValue",$)),t.$on("openChange",r[33]),t.$on("search",r[34]),t.$on("select",r[35]);let g={title:((h=r[4])==null?void 0:h.name)||"Choose branch...",itemKeyFn:Er,placeholder:"Search branches...",isLoading:r[19],disabled:r[15],items:r[17],selectedItem:r[4],itemLabelFn:Nr,noItemsLabel:r[5]?"":"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[kr],inputButton:[_r],searchIcon:[br],icon:[vr]},$$scope:{ctx:r}};return r[6]!==void 0&&(g.searchValue=r[6]),a=new $t({props:g}),ve.push(()=>be(a,"searchValue",w)),a.$on("openChange",r[38]),a.$on("search",r[39]),a.$on("select",r[40]),{c(){e=R("div"),n=R("div"),L(t.$$.fragment),s=D(),c=R("div"),L(a.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-btbfel"),y(c,"class","c-commit-ref-selector__selector svelte-btbfel"),y(e,"class","c-commit-ref-selector__selectors-container svelte-btbfel")},m(d,m){x(d,e,m),I(e,n),C(t,n,null),I(e,s),I(e,c),C(a,c,null),l=!0},p(d,m){var z;const v={};262144&m[0]&&(v.title=d[18]),1024&m[0]&&(v.isLoading=d[10]),256&m[0]&&(v.disabled=!d[8].length),512&m[0]&&(v.items=d[9]),4&m[0]&&(v.selectedItem=d[2]),32&m[2]&&(v.$$scope={dirty:m,ctx:d}),!o&&8192&m[0]&&(o=!0,v.searchValue=d[13],ye(()=>o=!1)),t.$set(v);const _={};16&m[0]&&(_.title=((z=d[4])==null?void 0:z.name)||"Choose branch..."),524288&m[0]&&(_.isLoading=d[19]),32768&m[0]&&(_.disabled=d[15]),131072&m[0]&&(_.items=d[17]),16&m[0]&&(_.selectedItem=d[4]),32&m[0]&&(_.noItemsLabel=d[5]?"":"No branches found"),6184&m[0]|32&m[2]&&(_.$$scope={dirty:m,ctx:d}),!i&&64&m[0]&&(i=!0,_.searchValue=d[6],ye(()=>i=!1)),a.$set(_)},i(d){l||(p(t.$$.fragment,d),p(a.$$.fragment,d),l=!0)},o(d){f(t.$$.fragment,d),f(a.$$.fragment,d),l=!1},d(d){d&&b(e),A(t),A(a)}}}function mr(r){let e,n;return e=new je({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[fr],default:[dr]},$$scope:{ctx:r}}}),e.$on("click",r[23]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};2&o[0]&&(s.loading=t[1]),32&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function pr(r){let e,n;return e=new Mo({}),e.$on("authStateChange",r[41]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function dr(r){let e;return{c(){e=T("Reload available repos and branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function fr(r){let e,n,t;return n=new pn({}),{c(){e=R("span"),L(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-btbfel")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p:M,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function gr(r){let e,n,t,o,s,c,a;const i=[pr,mr],l=[];function $(u,w){return u[14]?1:0}return s=$(r),c=l[s]=i[s](r),{c(){e=R("div"),n=R("div"),t=T(r[0]),o=D(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-btbfel"),y(e,"class","c-commit-ref-selector__error-content svelte-btbfel")},m(u,w){x(u,e,w),I(e,n),I(n,t),I(e,o),l[s].m(e,null),a=!0},p(u,w){(!a||1&w[0])&&q(t,u[0]);let g=s;s=$(u),s===g?l[s].p(u,w):(H(),f(l[g],1,1,()=>{l[g]=null}),O(),c=l[s],c?c.p(u,w):(c=l[s]=i[s](u),c.c()),p(c,1),c.m(e,null))},i(u){a||(p(c),a=!0)},o(u){f(c),a=!1},d(u){u&&b(e),l[s].d()}}}function hr(r){let e,n;return e=new ft({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function wr(r){let e,n;return e=new gt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function vr(r){let e,n;return e=new Rn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function br(r){let e,n;return e=new gt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function yr(r){let e,n;return e=new Un({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function xr(r){let e,n;return e=new rt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[yr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};32&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function _r(r){let e,n,t;return n=new Ie({props:{content:"Refresh branches",triggerOn:[dn.Hover],nested:!1,$$slots:{default:[xr]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"slot","inputButton"),y(e,"class","c-commit-ref-selector__refresh-button svelte-btbfel")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};32&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function Lr(r){let e,n,t,o,s,c,a;return t=new Ve({props:{size:1}}),c=new Le({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Ar]},$$scope:{ctx:r}}}),{c(){e=R("div"),n=R("div"),L(t.$$.fragment),o=D(),s=R("div"),L(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled svelte-btbfel")},m(i,l){x(i,e,l),I(e,n),C(t,n,null),I(e,o),I(e,s),C(c,s,null),a=!0},p(i,l){const $={};32&l[2]&&($.$$scope={dirty:l,ctx:i}),c.$set($)},i(i){a||(p(t.$$.fragment,i),p(c.$$.fragment,i),a=!0)},o(i){f(t.$$.fragment,i),f(c.$$.fragment,i),a=!1},d(i){i&&b(e),A(t),A(c)}}}function Cr(r){let e,n;return e=new Ie({props:{content:`${r[3].length} branches loaded`,triggerOn:[dn.Hover],nested:!1,$$slots:{default:[Sr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};8&o[0]&&(s.content=`${t[3].length} branches loaded`),2048&o[0]|32&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ar(r){let e;return{c(){e=T("Loading branches...")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Rr(r){let e;return{c(){e=T("Load more branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Sr(r){let e,n,t,o,s,c,a,i,l;return t=new Gn({}),c=new Le({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Rr]},$$scope:{ctx:r}}}),{c(){e=R("button"),n=R("div"),L(t.$$.fragment),o=D(),s=R("div"),L(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-btbfel")},m($,u){x($,e,u),I(e,n),C(t,n,null),I(e,o),I(e,s),C(c,s,null),a=!0,i||(l=Re(e,"click",r[36]),i=!0)},p($,u){const w={};32&u[2]&&(w.$$scope={dirty:u,ctx:$}),c.$set(w)},i($){a||(p(t.$$.fragment,$),p(c.$$.fragment,$),a=!0)},o($){f(t.$$.fragment,$),f(c.$$.fragment,$),a=!1},d($){$&&b(e),A(t),A(c),i=!1,l()}}}function kr(r){let e,n,t,o;const s=[Cr,Lr],c=[];function a(i,l){return i[12]&&!i[5]?0:i[5]?1:-1}return~(e=a(r))&&(n=c[e]=s[e](r)),{c(){n&&n.c(),t=ae()},m(i,l){~e&&c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let $=e;e=a(i),e===$?~e&&c[e].p(i,l):(n&&(H(),f(c[$],1,1,()=>{c[$]=null}),O()),~e?(n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t)):n=null)},i(i){o||(p(n),o=!0)},o(i){f(n),o=!1},d(i){i&&b(t),~e&&c[e].d(i)}}}function Ir(r){let e,n,t,o,s;const c=[$r,ur],a=[];function i(l,$){return l[16]?l[16]?1:-1:0}return~(t=i(r))&&(o=a[t]=c[t](r)),{c(){e=R("div"),n=R("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-btbfel"),y(e,"class","c-commit-ref-selector svelte-btbfel")},m(l,$){x(l,e,$),I(e,n),~t&&a[t].m(n,null),s=!0},p(l,$){let u=t;t=i(l),t===u?~t&&a[t].p(l,$):(o&&(H(),f(a[u],1,1,()=>{a[u]=null}),O()),~t?(o=a[t],o?o.p(l,$):(o=a[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){s||(p(o),s=!0)},o(l){f(o),s=!1},d(l){l&&b(e),~t&&a[t].d()}}}const Fr=r=>`${r.owner}-${r.name}`,zr=r=>`${r.owner}/${r.name}`,Er=(r,e)=>`${r.name}-${r.commit.sha}=${e}`,Nr=r=>r.name.replace("origin/","");function Dr(r,e,n){let t,o,s,c,a;const i=Fe(fn.key),l=De(),$=Fe(ot.key);let u,w,{errorMessage:g=""}=e,{isLoading:h=!1}=e,{lastUsedBranchName:d=null}=e,{lastUsedRepoUrl:m=null}=e,v="warning",_=[],z=_,N=[],E=!1,P=!1,k=0,Q=!1;const le=new Set;let V=!1,ee=!1,me="",re="";function U(S){n(13,me=S),V=!0,_n(S)}const te=xt(function(S){n(6,re=S),n(31,ee=!0)},300,{leading:!1,trailing:!0}),pe={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function fe(){n(10,P=!0),n(5,E=!0);const{repos:S,error:B,isDevDeploy:ne}=await i.listUserRepos();if(ne)return await async function(){var bt;console.warn("Fetching branches from local git environment.");const{remoteUrl:ge,error:he}=await i.getRemoteUrl();n(1,h=!0);const Ne=Lt(ge);if(!Ne||he)return Y(he??pe.failedToParseRemoteUrl),void n(1,h=!1);n(2,u={name:Ne.name,owner:Ne.owner,html_url:ge}),n(8,_=[u]),n(9,z=_);const it=function(Pe){return Pe.filter(Ln=>Ln.isRemote)}((await i.listBranches()).branches),We=it.find(Pe=>Pe.isDefault);n(4,w={name:ut(We!=null&&We.name?We.name:((bt=it[0])==null?void 0:bt.name)||""),commit:{sha:"",url:""},protected:!1}),n(3,N=it.map(Pe=>({name:ut(Pe.name),commit:{sha:"",url:""},protected:!1}))),t||ke(),n(1,h=!1)}(),void n(10,P=!1);if(B)return Y(`An error occured while fetching your repos. If this continues, please contact support. Error: ${B}`),n(1,h=!1),void n(10,P=!1);n(8,_=S),n(9,z=_);const{remoteUrl:Ae,error:ze}=await i.getRemoteUrl(),Xe=Lt(Ae);if(ze)return n(1,h=!1),void n(10,P=!1);const{owner:Ee,name:st}=Xe||{},ct=_.find(ge=>ge.name===st&&ge.owner===Ee);if(ct&&!u)n(2,u=ct);else if(!ct&&st&&Ee){const ge={name:st,owner:Ee,html_url:Ae};try{const{repo:he,error:Ne}=await i.getGithubRepo(ge);Ne?(console.warn("Failed to fetch GitHub repo details:",Ne),n(2,u=_[0])):(n(2,u=he),n(8,_=[u,..._]))}catch(he){console.error("Error fetching GitHub repo:",he),n(2,u=_[0])}}else if(!u)return n(1,h=!1),void n(10,P=!1);n(10,P=!1),function(){le.clear();const ge=new Set;_.forEach(he=>{ge.has(he.name)?le.add(he.name):ge.add(he.name)})}()}if(tt(async()=>{await j()}),!u&&m){const S=_.find(B=>B.html_url===m);S&&(u=S)}async function Ce(S){if(!u)return;n(5,E=!0);const B=u;do{if(B!==u){n(5,E=!1),n(3,N=[]);break}const ne=await i.listRepoBranches(u,S);if(ne.error)return Y(`Failed to fetch branches for the repo ${u.owner}/${u.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${ne.error}`),void n(1,h=!1);if(n(3,N=[...N,...ne.branches]),n(12,Q=ne.hasNextPage),qe(),!Q)break;S=ne.nextPage,n(11,k++,k)}while(k%20!=0&&Q);n(5,E=!1)}function qe(){if(u&&!w){if(d){const S=N.find(B=>B.name===d);if(S)return n(4,w=S),void ke()}if(u.default_branch){const S=u.default_branch;if(N.length===0)return n(4,w={name:S,commit:{sha:"",url:""},protected:!1}),void ke();const B=o.find(ne=>ne.name===S);if(B)return n(4,w=B),void ke()}E||n(4,w=N[0]),ke()}}function ue(){u&&async function(){u&&(n(11,k=0),await Ce(k+1))}().then(()=>{n(1,h=!1),t||ke()}).catch(S=>{console.error("Error fetching all branches:",S),Y(`Failed to fetch branches: ${S instanceof Error?S.message:String(S)}`)})}let de=!0;const F=async()=>{try{n(14,de=await i.isGithubAuthenticated()),de||Y("Please authenticate with GitHub to use this feature.","info")}catch(S){console.error("Failed to check GitHub authentication status:",S),Y("Please authenticate with GitHub to use this feature."),n(14,de=!1)}};async function G(){n(1,h=!0),n(16,t=!1),n(0,g=""),n(7,v="warning");try{if(await F(),!de)return void n(1,h=!1);await fe(),t||ue(),t||ke()}catch(S){console.error("Error fetching git data:",S),Y(pe.failedToFetchBranches)}finally{n(1,h=!1)}}async function j(){n(1,h=!0);try{await G()}catch(S){console.error("Error fetching and syncing branches:",S),Y("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,h=!1)}}function Y(S,B="warning"){console.error("Error:",S),n(16,t=!0),n(0,g=S),n(7,v=B)}async function Z(S){n(4,w=S),n(31,ee=!1),ht((w==null?void 0:w.name)??"");const B=$.creationMetrics;$.setCreationMetrics({changedRepo:(B==null?void 0:B.changedRepo)??!1,changedBranch:!0}),ke()}async function Se(S){n(5,E=!0),n(2,u=S),n(4,w=void 0),n(3,N=[]),V=!1,xn(""),n(9,z=_),ue();const B=$.creationMetrics;$.setCreationMetrics({changedRepo:!0,changedBranch:(B==null?void 0:B.changedBranch)??!1})}function Ke(S,B){S||(B==="repo"?V=!1:(B==="branch"||(V=!1),n(31,ee=!1)))}function ke(){if(!(u!=null&&u.html_url)||!w)return;const S={github_commit_ref:{repository_url:u.html_url,git_ref:w.name}};l("commitRefChange",{commitRef:S,selectedBranch:w})}const ht=S=>{n(6,re=S)},xn=S=>{n(13,me=S)},_n=xt(async function(S=""){n(16,t=!1);try{V?n(9,z=function(B,ne){if(ne==="")return B;const Ae=ne.toLowerCase().split("/",2).filter(ze=>ze.length>0);return B.filter(ze=>{const Xe=ze.owner.toLowerCase(),Ee=ze.name.toLowerCase();return Ae.length===1?Xe.includes(Ae[0])||Ee.includes(Ae[0]):Ae.length!==2||Ae[0]===Xe&&Ee.includes(Ae[1])})}(_,S||me)):n(9,z=_)}catch(B){console.error("Error fetching repos:",B),n(9,z=[]),Y(pe.failedToFetchFromRemote)}},300,{leading:!1,trailing:!0});function wt(S){S&&a||Ke(S,"branch")}function vt(S){S&&!_.length||Ke(S,"repo")}return r.$$set=S=>{"errorMessage"in S&&n(0,g=S.errorMessage),"isLoading"in S&&n(1,h=S.isLoading),"lastUsedBranchName"in S&&n(29,d=S.lastUsedBranchName),"lastUsedRepoUrl"in S&&n(30,m=S.lastUsedRepoUrl)},r.$$.update=()=>{1&r.$$.dirty[0]&&n(16,t=g!==""),76&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(17,o=(()=>{try{return ee&&re.trim()!==""?(S=re,N.filter(B=>B.name.toLowerCase().includes(S.toLowerCase()))):u!=null&&u.default_branch?[N.find(ne=>ne.name===(u==null?void 0:u.default_branch))||{name:u.default_branch,commit:{sha:"",url:""},protected:!1},...N.filter(ne=>ne.name!==(u==null?void 0:u.default_branch))]:N}catch(B){return console.error("Error computing displayedBranches:",B),[]}var S})()),52&r.$$.dirty[0]&&n(19,s=u&&E&&!w),4&r.$$.dirty[0]&&n(18,c=u?le.has(u.name)?`${u.owner}/${u.name}`:u.name:"Choose repository..."),16&r.$$.dirty[0]|1&r.$$.dirty[1]&&ht(ee?"":(w==null?void 0:w.name)??""),12&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(15,a=!u||!ee&&!N.length)},[g,h,u,N,w,E,re,v,_,z,P,k,Q,me,de,a,t,o,c,s,U,te,Ce,j,Z,Se,wt,vt,function(){n(3,N=[]),ue()},d,m,ee,function(S){me=S,n(13,me)},S=>vt(S.detail),S=>U(S.detail),S=>Se(S.detail),()=>{Ce(k+1)},function(S){re=S,n(6,re)},S=>wt(S.detail),S=>te(S.detail),S=>Z(S.detail),async S=>{const{isAuthenticated:B}=S.detail;B!==de&&(n(14,de=B),B&&await j())}]}class Pr extends se{constructor(e){super(),ce(this,e,Dr,Ir,ie,{errorMessage:0,isLoading:1,lastUsedBranchName:29,lastUsedRepoUrl:30},null,[-1,-1,-1])}}function Br(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=oe(o,t[s]);return{c(){e=Be("svg"),n=new Ue(!0),this.h()},l(s){e=Me(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Te(e);n=Ge(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){He(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(s,[c]){xe(e,o=Oe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(e)}}}function Ur(r,e,n){return r.$$set=t=>{n(0,e=oe(oe({},e),_e(t)))},[e=_e(e)]}class Mr extends se{constructor(e){super(),ce(this,e,Ur,Br,ie,{})}}function Tr(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=oe(o,t[s]);return{c(){e=Be("svg"),n=new Ue(!0),this.h()},l(s){e=Me(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Te(e);n=Ge(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){He(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(s,[c]){xe(e,o=Oe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(e)}}}function Gr(r,e,n){return r.$$set=t=>{n(0,e=oe(oe({},e),_e(t)))},[e=_e(e)]}class Hr extends se{constructor(e){super(),ce(this,e,Gr,Tr,ie,{})}}const Or=r=>({}),en=r=>({});function tn(r){let e,n;const t=r[12].icon,o=K(t,r,r[11],en);return{c(){e=R("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(s,c){x(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||2048&c)&&X(o,t,s,s[11],n?J(t,s[11],c,Or):W(s[11]),en)},i(s){n||(p(o,s),n=!0)},o(s){f(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Vr(r){let e,n,t,o,s;return{c(){e=R("span"),n=T(r[0]),t=D(),o=R("span"),s=T(r[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,a){x(c,e,a),I(e,n),x(c,t,a),x(c,o,a),I(o,s)},p(c,a){1&a&&q(n,c[0]),2&a&&q(s,c[1])},i:M,o:M,d(c){c&&(b(e),b(t),b(o))}}}function jr(r){let e,n,t,o,s,c,a,i,l;function $(h){r[15](h)}function u(h){r[16](h)}let w={size:1,variant:"surface"};r[6]!==void 0&&(w.value=r[6]),r[5]!==void 0&&(w.textInput=r[5]),t=new In({props:w}),ve.push(()=>be(t,"value",$)),ve.push(()=>be(t,"textInput",u)),t.$on("keydown",r[8]),t.$on("blur",r[9]);let g=r[7]&&function(h){let d;return{c(){d=R("span"),d.textContent=`${h[7]}`,y(d,"class","c-setup-script-selector__extension svelte-udt6j8")},m(m,v){x(m,d,v)},p:M,d(m){m&&b(d)}}}(r);return{c(){e=R("div"),n=R("div"),L(t.$$.fragment),c=D(),g&&g.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(h,d){x(h,e,d),I(e,n),C(t,n,null),I(n,c),g&&g.m(n,null),a=!0,i||(l=[Re(e,"click",Ye(r[13])),Re(e,"mousedown",Ye(r[14]))],i=!0)},p(h,d){const m={};!o&&64&d&&(o=!0,m.value=h[6],ye(()=>o=!1)),!s&&32&d&&(s=!0,m.textInput=h[5],ye(()=>s=!1)),t.$set(m),h[7]&&g.p(h,d)},i(h){a||(p(t.$$.fragment,h),a=!0)},o(h){f(t.$$.fragment,h),a=!1},d(h){h&&b(e),A(t),g&&g.d(),i=!1,pt(l)}}}function qr(r){let e,n,t,o,s,c,a,i,l=r[10].icon&&tn(r);const $=[jr,Vr],u=[];function w(d,m){return d[3]?0:1}o=w(r),s=u[o]=$[o](r);const g=r[12].default,h=K(g,r,r[11],null);return{c(){e=R("div"),l&&l.c(),n=D(),t=R("div"),s.c(),c=D(),a=R("div"),h&&h.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(a,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),we(e,"c-setup-script-selector__script-item-content--renaming",r[3]),we(e,"c-setup-script-selector__script-item-content--is-path",r[2]),we(e,"c-setup-script-selector__script-item-content--selected",r[4])},m(d,m){x(d,e,m),l&&l.m(e,null),I(e,n),I(e,t),u[o].m(t,null),I(e,c),I(e,a),h&&h.m(a,null),i=!0},p(d,[m]){d[10].icon?l?(l.p(d,m),1024&m&&p(l,1)):(l=tn(d),l.c(),p(l,1),l.m(e,n)):l&&(H(),f(l,1,1,()=>{l=null}),O());let v=o;o=w(d),o===v?u[o].p(d,m):(H(),f(u[v],1,1,()=>{u[v]=null}),O(),s=u[o],s?s.p(d,m):(s=u[o]=$[o](d),s.c()),p(s,1),s.m(t,null)),h&&h.p&&(!i||2048&m)&&X(h,g,d,d[11],i?J(g,d[11],m,null):W(d[11]),null),(!i||8&m)&&we(e,"c-setup-script-selector__script-item-content--renaming",d[3]),(!i||4&m)&&we(e,"c-setup-script-selector__script-item-content--is-path",d[2]),(!i||16&m)&&we(e,"c-setup-script-selector__script-item-content--selected",d[4])},i(d){i||(p(l),p(s),p(h,d),i=!0)},o(d){f(l),f(s),f(h,d),i=!1},d(d){d&&b(e),l&&l.d(),u[o].d(),h&&h.d(d)}}}function Kr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=mt(t);let{name:c}=e,{path:a}=e,{isPath:i=!1}=e,{isRenaming:l=!1}=e,{isSelected:$=!1}=e;const u=De(),{baseName:w,extension:g}=function(m){const v=m.lastIndexOf(".");return v===-1?{baseName:m,extension:""}:{baseName:m.substring(0,v),extension:m.substring(v)}}(c);let h,d=w;return r.$$set=m=>{"name"in m&&n(0,c=m.name),"path"in m&&n(1,a=m.path),"isPath"in m&&n(2,i=m.isPath),"isRenaming"in m&&n(3,l=m.isRenaming),"isSelected"in m&&n(4,$=m.isSelected),"$$scope"in m&&n(11,o=m.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,a,i,l,$,h,d,g,function(m){if(m.key!=="ArrowLeft"&&m.key!=="ArrowRight"&&m.key!=="ArrowUp"&&m.key!=="ArrowDown")if(m.key==="Enter")if(m.preventDefault(),d.trim()&&d!==w){const v=d.trim()+g;u("rename",{oldName:c,newName:v})}else u("cancelRename");else m.key==="Escape"&&(m.preventDefault(),m.stopPropagation(),u("cancelRename"));else m.stopPropagation()},function(){u("cancelRename")},s,o,t,function(m){Je.call(this,r,m)},function(m){Je.call(this,r,m)},function(m){d=m,n(6,d)},function(m){h=m,n(5,h)}]}class Xr extends se{constructor(e){super(),ce(this,e,Kr,qr,ie,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function nn(r){let e,n,t,o,s,c,a,i,l,$;function u(g){r[36](g)}let w={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[5],itemLabelFn:Cs,itemKeyFn:As,isItemSelected:Rs,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[ms,({item:g})=>({46:g}),({item:g})=>[0,g?32768:0]],searchIcon:[ns],icon:[ts],title:[es]},$$scope:{ctx:r}};return r[2]!==void 0&&(w.searchValue=r[2]),t=new $t({props:w}),ve.push(()=>be(t,"searchValue",u)),t.$on("openChange",r[37]),t.$on("search",r[38]),t.$on("select",r[39]),a=new Ie({props:{content:r[10],nested:!1,$$slots:{default:[gs]},$$scope:{ctx:r}}}),l=new Ie({props:{content:"Open a new file for you to write a setup script that you can edit directly.",nested:!1,$$slots:{default:[bs]},$$scope:{ctx:r}}}),{c(){e=R("div"),n=R("div"),L(t.$$.fragment),s=D(),c=R("div"),L(a.$$.fragment),i=D(),L(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(g,h){x(g,e,h),I(e,n),C(t,n,null),I(n,s),I(n,c),C(a,c,null),I(c,i),C(l,c,null),$=!0},p(g,h){const d={};2&h[0]&&(d.isLoading=g[1]),128&h[0]&&(d.items=g[7]),32&h[0]&&(d.selectedItem=g[5]),888&h[0]|98304&h[1]&&(d.$$scope={dirty:h,ctx:g}),!o&&4&h[0]&&(o=!0,d.searchValue=g[2],ye(()=>o=!1)),t.$set(d);const m={};1024&h[0]&&(m.content=g[10]),2048&h[0]|65536&h[1]&&(m.$$scope={dirty:h,ctx:g}),a.$set(m);const v={};65536&h[1]&&(v.$$scope={dirty:h,ctx:g}),l.$set(v)},i(g){$||(p(t.$$.fragment,g),p(a.$$.fragment,g),p(l.$$.fragment,g),$=!0)},o(g){f(t.$$.fragment,g),f(a.$$.fragment,g),f(l.$$.fragment,g),$=!1},d(g){g&&b(e),A(t),A(a),A(l)}}}function Wr(r){let e,n;return e=new vn({props:{$$slots:{text:[Yr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};8&o[0]|65536&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Jr(r){let e,n;return e=new vn({props:{$$slots:{grayText:[Qr],text:[Zr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};768&o[0]|65536&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Yr(r){let e,n;return{c(){e=R("span"),n=T(r[3]),y(e,"slot","text")},m(t,o){x(t,e,o),I(e,n)},p(t,o){8&o[0]&&q(n,t[3])},d(t){t&&b(e)}}}function Zr(r){let e,n;return{c(){e=R("span"),n=T(r[9]),y(e,"slot","text")},m(t,o){x(t,e,o),I(e,n)},p(t,o){512&o[0]&&q(n,t[9])},d(t){t&&b(e)}}}function Qr(r){let e,n;return{c(){e=R("span"),n=T(r[8]),y(e,"slot","grayText")},m(t,o){x(t,e,o),I(e,n)},p(t,o){256&o[0]&&q(n,t[8])},d(t){t&&b(e)}}}function es(r){let e,n,t,o;const s=[Jr,Wr],c=[];function a(i,l){return i[4]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=R("div"),t.c(),y(e,"slot","title")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let $=n;n=a(i),n===$?c[n].p(i,l):(H(),f(c[$],1,1,()=>{c[$]=null}),O(),t=c[n],t?t.p(i,l):(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){f(t),o=!1},d(i){i&&b(e),c[n].d()}}}function ts(r){let e,n;return e=new wn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ns(r){let e,n;return e=new gt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function os(r){var t;let e,n;return e=new Xr({props:{name:r[46].name,path:r[46].path,isPath:!0,isRenaming:((t=r[6])==null?void 0:t.path)===r[46].path,isSelected:!(!r[5]||r[5].path!==r[46].path),$$slots:{default:[$s]},$$scope:{ctx:r}}}),e.$on("rename",function(...o){return r[35](r[46],...o)}),e.$on("cancelRename",r[22]),{c(){L(e.$$.fragment)},m(o,s){C(e,o,s),n=!0},p(o,s){var a;r=o;const c={};32768&s[1]&&(c.name=r[46].name),32768&s[1]&&(c.path=r[46].path),64&s[0]|32768&s[1]&&(c.isRenaming=((a=r[6])==null?void 0:a.path)===r[46].path),32&s[0]|32768&s[1]&&(c.isSelected=!(!r[5]||r[5].path!==r[46].path)),98304&s[1]&&(c.$$scope={dirty:s,ctx:r}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){f(e.$$.fragment,o),n=!1},d(o){A(e,o)}}}function rs(r){let e,n,t,o;return n=new wn({}),{c(){e=R("div"),L(n.$$.fragment),t=T(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(s,c){x(s,e,c),C(n,e,null),I(e,t),o=!0},p:M,i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){f(n.$$.fragment,s),o=!1},d(s){s&&b(e),A(n)}}}function ss(r){let e,n;return e=new zn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function cs(r){let e,n;return e=new rt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[ss]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[32](r[46],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};65536&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function is(r){let e,n;return e=new Mr({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function as(r){let e,n;return e=new rt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[is]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[33](r[46],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};65536&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ls(r){let e,n;return e=new Sn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function us(r){let e,n;return e=new rt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[ls]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[34](r[46],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};65536&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function $s(r){let e,n,t,o,s,c;return e=new Ie({props:{content:"Open script in editor",nested:!1,$$slots:{default:[cs]},$$scope:{ctx:r}}}),t=new Ie({props:{content:"Rename script",nested:!1,$$slots:{default:[as]},$$scope:{ctx:r}}}),s=new Ie({props:{content:"Delete script",nested:!1,$$slots:{default:[us]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=D(),L(t.$$.fragment),o=D(),L(s.$$.fragment)},m(a,i){C(e,a,i),x(a,n,i),C(t,a,i),x(a,o,i),C(s,a,i),c=!0},p(a,i){const l={};98304&i[1]&&(l.$$scope={dirty:i,ctx:a}),e.$set(l);const $={};98304&i[1]&&($.$$scope={dirty:i,ctx:a}),t.$set($);const u={};98304&i[1]&&(u.$$scope={dirty:i,ctx:a}),s.$set(u)},i(a){c||(p(e.$$.fragment,a),p(t.$$.fragment,a),p(s.$$.fragment,a),c=!0)},o(a){f(e.$$.fragment,a),f(t.$$.fragment,a),f(s.$$.fragment,a),c=!1},d(a){a&&(b(n),b(o)),A(e,a),A(t,a),A(s,a)}}}function ms(r){let e,n,t,o;const s=[rs,os],c=[];function a(i,l){return i[46]===null?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=ae()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let $=e;e=a(i),e===$?c[e].p(i,l):(H(),f(c[$],1,1,()=>{c[$]=null}),O(),n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){f(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function ps(r){let e,n;return{c(){e=T("Auto-generate"),n=R("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){x(t,e,o),x(t,n,o)},p:M,d(t){t&&(b(e),b(n))}}}function ds(r){let e,n;return e=new Hr({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function fs(r){let e,n;return e=new $n({props:{slot:"iconRight"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function gs(r){let e,n;return e=new je({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[fs],iconLeft:[ds],default:[ps]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};2048&o[0]&&(s.disabled=t[11]),65536&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function hs(r){let e,n,t;return{c(){e=T("Write "),n=R("span"),n.textContent="a script",t=T("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,s){x(o,e,s),x(o,n,s),x(o,t,s)},p:M,d(o){o&&(b(e),b(n),b(t))}}}function ws(r){let e,n;return e=new En({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function vs(r){let e,n;return e=new $n({props:{slot:"iconRight"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function bs(r){let e,n;return e=new je({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[vs],iconLeft:[ws],default:[hs]},$$scope:{ctx:r}}}),e.$on("click",r[16]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};65536&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function on(r){let e,n,t;return n=new nt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[_s]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};3&s[0]|65536&s[1]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function ys(r){let e;return{c(){e=T("Refresh")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function xs(r){let e,n,t;return n=new pn({}),{c(){e=R("span"),L(n.$$.fragment),y(e,"slot","iconLeft")},m(o,s){x(o,e,s),C(n,e,null),t=!0},p:M,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function _s(r){let e,n,t,o,s,c;return s=new je({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[xs],default:[ys]},$$scope:{ctx:r}}}),s.$on("click",r[18]),{c(){e=R("div"),n=R("div"),t=T(r[0]),o=D(),L(s.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(a,i){x(a,e,i),I(e,n),I(n,t),I(e,o),C(s,e,null),c=!0},p(a,i){(!c||1&i[0])&&q(t,a[0]);const l={};2&i[0]&&(l.loading=a[1]),65536&i[1]&&(l.$$scope={dirty:i,ctx:a}),s.$set(l)},i(a){c||(p(s.$$.fragment,a),c=!0)},o(a){f(s.$$.fragment,a),c=!1},d(a){a&&b(e),A(s)}}}function Ls(r){let e,n,t,o,s=(!r[12]||r[0]===r[17].noScriptsFound)&&nn(r),c=r[12]&&r[0]!==r[17].noScriptsFound&&on(r);return{c(){e=R("div"),n=R("div"),s&&s.c(),t=D(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(a,i){x(a,e,i),I(e,n),s&&s.m(n,null),I(n,t),c&&c.m(n,null),o=!0},p(a,i){a[12]&&a[0]!==a[17].noScriptsFound?s&&(H(),f(s,1,1,()=>{s=null}),O()):s?(s.p(a,i),4097&i[0]&&p(s,1)):(s=nn(a),s.c(),p(s,1),s.m(n,t)),a[12]&&a[0]!==a[17].noScriptsFound?c?(c.p(a,i),4097&i[0]&&p(c,1)):(c=on(a),c.c(),p(c,1),c.m(n,null)):c&&(H(),f(c,1,1,()=>{c=null}),O())},i(a){o||(p(s),p(c),o=!0)},o(a){f(s),f(c),o=!1},d(a){a&&b(e),s&&s.d(),c&&c.d()}}}const Cs=r=>(r==null?void 0:r.name)||"",As=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,Rs=(r,e)=>r===null&&e===null||!(!r||!e)&&r.path===e.path;function Ss(r,e,n){let t,o,s,c,a,i,l,$,u,w,{errorMessage:g=""}=e,{isLoading:h=!1}=e,{lastUsedScriptPath:d=null}=e,{disableNewAgentCreation:m=!1}=e;const v=Fe(ot.key);dt(r,v,F=>n(31,w=F));const _=De(),z=Fe("chatModel").extensionClient,N=F=>{z.openFile({repoRoot:"",pathName:F.path,allowOutOfWorkspace:!0,openLocalUri:F.location==="home"})};let E=[],P="",k=null,Q=E,le=!0;const V={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function ee(){n(0,g="");try{const F=t==null?void 0:t.path;if(n(29,E=await v.listSetupScripts()),le)if(d&&E.length>0){const G=E.find(j=>j.path===d);G&&(n(5,t=G),de())}else d===null&&(n(5,t=null),de());else if(F){const G=E.find(j=>j.path===F);G&&n(5,t=G)}le=!1,E.length===0?n(0,g=V.noScriptsFound):n(0,g="")}catch(F){console.error("Error fetching setup scripts:",F),n(0,g=V.failedToFetchScripts)}}async function me(F,G){G&&G.stopPropagation();try{const j=await v.deleteSetupScript(F.name,F.location);j.success?((t==null?void 0:t.path)===F.path&&ue(null),await ee()):(console.error("Failed to delete script:",j.error),pe(`Failed to delete script: ${j.error||"Unknown error"}`))}catch(j){console.error("Error deleting script:",j),pe(`Error deleting script: ${j instanceof Error?j.message:String(j)}`)}}async function re(F,G){G&&G.stopPropagation(),n(6,k=F)}async function U(F,G){const{oldName:j,newName:Y}=G.detail;try{const Z=await v.renameSetupScript(j,Y,F.location);if(Z.success){await ee();const Se=E.find(Ke=>Ke.path===Z.path);Se&&ue(Se)}else console.error("Failed to rename script:",Z.error),pe(`Failed to rename script: ${Z.error||"Unknown error"}`)}catch(Z){console.error("Error renaming script:",Z),pe(`Error renaming script: ${Z instanceof Error?Z.message:String(Z)}`)}finally{te()}}function te(){n(6,k=null)}function pe(F){n(0,g=F)}function fe(F){n(2,P=F)}function Ce(F){ue(F)}function qe(F){F&&(ee(),n(2,P=""))}async function ue(F){n(5,t=F),de(),v.saveLastRemoteAgentSetup(null,null,(t==null?void 0:t.path)||null)}function de(){_("setupScriptChange",{script:t})}return tt(async()=>{var F;await ee(),d===null?ue(null):(F=w.newAgentDraft)!=null&&F.setupScript&&!t&&ue(w.newAgentDraft.setupScript)}),r.$$set=F=>{"errorMessage"in F&&n(0,g=F.errorMessage),"isLoading"in F&&n(1,h=F.isLoading),"lastUsedScriptPath"in F&&n(27,d=F.lastUsedScriptPath),"disableNewAgentCreation"in F&&n(28,m=F.disableNewAgentCreation)},r.$$.update=()=>{var F,G,j;if(1&r.$$.dirty[1]&&n(5,t=((F=w.newAgentDraft)==null?void 0:F.setupScript)??null),1&r.$$.dirty[0]&&n(12,o=g!==""),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(11,s=m||((G=w.newAgentDraft)==null?void 0:G.isDisabled)||!w.newAgentDraft),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(10,c=w.newAgentDraft?(j=w.newAgentDraft)!=null&&j.isDisabled?"Please resolve the issues with your workspace selection":m?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),536870916&r.$$.dirty[0])if(P.trim()!==""){const Y="Use basic environment".toLowerCase().includes(P.toLowerCase()),Z=E.filter(Se=>Se.name.toLowerCase().includes(P.toLowerCase())||Se.path.toLowerCase().includes(P.toLowerCase()));n(7,Q=Y?[null,...Z]:Z)}else n(7,Q=[null,...E]);34&r.$$.dirty[0]&&n(30,a=()=>h?"...":t?t.isGenerateOption?t.name:t.location==="home"?"~/.augment/env/"+t.name:t.path:"Use basic environment"),1073741824&r.$$.dirty[0]&&n(3,i=a()),32&r.$$.dirty[0]&&n(4,l=!!(t!=null&&t.path)),24&r.$$.dirty[0]&&n(9,$=l?i.split("/").pop():i),24&r.$$.dirty[0]&&n(8,u=l?i.slice(0,i.lastIndexOf("/")):"")},[g,h,P,i,l,t,k,Q,u,$,c,s,o,v,N,async()=>{try{const F=w.newAgentDraft;F&&v.setNewAgentDraft({...F,isSetupScriptAgent:!0});const G=await v.createRemoteAgentFromDraft("SETUP_MODE");return G&&v.setCurrentAgent(G),G}catch(F){console.error("Failed to select setup script generation:",F)}},async()=>{try{const F="setup.sh",G=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,j=await v.saveSetupScript(F,G,"home");if(j.success&&j.path){await ee();const Y=E.find(Z=>Z.path===j.path);Y&&(ue(Y),N(Y))}else console.error("Failed to create manual setup script:",j.error),n(0,g=`Failed to create manual setup script: ${j.error||"Unknown error"}`)}catch(F){console.error("Error creating manual setup script:",F),n(0,g=`Error creating manual setup script: ${F instanceof Error?F.message:String(F)}`)}},V,ee,me,re,U,te,fe,Ce,qe,ue,d,m,E,a,w,(F,G)=>{G.stopPropagation(),N(F),ue(F)},(F,G)=>{G.stopPropagation(),re(F)},(F,G)=>{G.stopPropagation(),me(F)},(F,G)=>U(F,G),function(F){P=F,n(2,P)},F=>qe(F.detail),F=>fe(F.detail),F=>Ce(F.detail)]}class ks extends se{constructor(e){super(),ce(this,e,Ss,Ls,ie,{errorMessage:0,isLoading:1,lastUsedScriptPath:27,disableNewAgentCreation:28},null,[-1,-1])}}function Is(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=oe(o,t[s]);return{c(){e=Be("svg"),n=new Ue(!0),this.h()},l(s){e=Me(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Te(e);n=Ge(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){He(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(s,[c]){xe(e,o=Oe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(e)}}}function Fs(r,e,n){return r.$$set=t=>{n(0,e=oe(oe({},e),_e(t)))},[e=_e(e)]}class zs extends se{constructor(e){super(),ce(this,e,Fs,Is,ie,{})}}function rn(r){let e,n;return e=new nt({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[Us],default:[Bs]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};16414&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Es(r){let e;return{c(){e=T(r[4])},m(n,t){x(n,e,t)},p(n,t){16&t&&q(e,n[4])},d(n){n&&b(e)}}}function Ns(r){let e,n;return e=new Dn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ds(r){let e,n;return e=new zs({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ps(r){let e,n,t,o;const s=[Ds,Ns],c=[];function a(i,l){return i[1]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=R("div"),t.c(),y(e,"slot","iconLeft")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let $=n;n=a(i),n!==$&&(H(),f(c[$],1,1,()=>{c[$]=null}),O(),t=c[n],t||(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){f(t),o=!1},d(i){i&&b(e),c[n].d()}}}function Bs(r){let e,n,t,o,s,c,a=(r[2]?at:lt).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return s=new je({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[Ps],default:[Es]},$$scope:{ctx:r}}}),s.$on("click",r[11]),{c(){e=R("div"),n=R("p"),t=T(a),o=D(),L(s.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(i,l){x(i,e,l),I(e,n),I(n,t),I(e,o),C(s,e,null),c=!0},p(i,l){(!c||12&l)&&a!==(a=(i[2]?at:lt).replace("%MAX_AGENTS%",(i[2]?i[3].maxRemoteAgents:i[3].maxActiveRemoteAgents).toString())+"")&&q(t,a);const $={};16402&l&&($.$$scope={dirty:l,ctx:i}),s.$set($)},i(i){c||(p(s.$$.fragment,i),c=!0)},o(i){f(s.$$.fragment,i),c=!1},d(i){i&&b(e),A(s)}}}function Us(r){let e,n;return e=new Nn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:M,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ms(r){let e,n,t=!!r[0]&&rn(r);return{c(){t&&t.c(),e=ae()},m(o,s){t&&t.m(o,s),x(o,e,s),n=!0},p(o,[s]){o[0]?t?(t.p(o,s),1&s&&p(t,1)):(t=rn(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(H(),f(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){f(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function sn(r){if(!r)return;const e=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function cn(r,e){return r.replace("%MAX_AGENTS%",e.toString())}function Ts(r,e,n){let t,o,s,{agentLimitErrorMessage:c}=e;const a=Fe(ot.key);dt(r,a,m=>n(3,s=m));let i,l,$,u=!1,w=[];function g(){return s.agentOverviews.sort((m,v)=>new Date(m.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!u&&(i!=null&&i.id))try{u=!0,await a.deleteAgent(i.id)}catch(m){console.error("Failed to delete oldest agent:",m)}finally{u=!1}}async function d(){if(!u&&(l!=null&&l.id))try{u=!0,await a.pauseRemoteAgentWorkspace(l.id)}catch(m){console.error("Failed to pause oldest active agent:",m)}finally{u=!1}}return r.$$set=m=>{"agentLimitErrorMessage"in m&&n(0,c=m.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,t=!!s.maxRemoteAgents&&s.agentOverviews.length>=s.maxRemoteAgents),8&r.$$.dirty&&n(1,o=!!s.maxActiveRemoteAgents&&s.agentOverviews.filter(m=>m.workspace_status===_t.workspaceRunning).length>=s.maxActiveRemoteAgents),1806&r.$$.dirty)if(t)n(10,w=g()),n(8,i=sn(w[0])),n(0,c=cn(at,s.maxRemoteAgents)),n(4,$="Delete Oldest Agent"+(i?`: ${i.title}`:""));else if(o){n(10,w=g());const m=w.filter(v=>v.workspace_status===_t.workspaceRunning);n(9,l=sn(m[0])),n(0,c=cn(lt,s.maxActiveRemoteAgents)),n(4,$="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,i=void 0),n(0,c=void 0)},[c,o,t,s,$,a,h,d,i,l,w,()=>{o?d():h()}]}class Gs extends se{constructor(e){super(),ce(this,e,Ts,Ms,ie,{agentLimitErrorMessage:0})}}function an(r){let e,n,t,o;return n=new nt({props:{color:"error",variant:"soft",size:2,$$slots:{default:[Hs]},$$scope:{ctx:r}}}),{c(){e=R("div"),L(n.$$.fragment),y(e,"class","error-message svelte-gbnap0")},m(s,c){x(s,e,c),C(n,e,null),o=!0},p(s,c){const a={};131080&c&&(a.$$scope={dirty:c,ctx:s}),n.$set(a)},i(s){o||(p(n.$$.fragment,s),s&&un(()=>{o&&(t||(t=Ze(e,Qe,{y:10},!0)),t.run(1))}),o=!0)},o(s){f(n.$$.fragment,s),s&&(t||(t=Ze(e,Qe,{y:10},!1)),t.run(0)),o=!1},d(s){s&&b(e),A(n),s&&t&&t.end()}}}function Hs(r){let e,n=r[3].remoteAgentCreationError+"";return{c(){e=T(n)},m(t,o){x(t,e,o)},p(t,o){8&o&&n!==(n=t[3].remoteAgentCreationError+"")&&q(e,n)},d(t){t&&b(e)}}}function Os(r){let e,n,t,o,s,c,a,i,l,$,u,w,g,h,d,m,v,_,z,N,E,P,k;function Q(U){r[14](U)}let le={};r[2]!==void 0&&(le.agentLimitErrorMessage=r[2]),a=new Gs({props:le}),ve.push(()=>be(a,"agentLimitErrorMessage",Q));let V=r[3].remoteAgentCreationError&&an(r);function ee(U){r[15](U)}function me(U){r[16](U)}let re={lastUsedRepoUrl:r[4],lastUsedBranchName:r[5]};return r[0]!==void 0&&(re.errorMessage=r[0]),r[1]!==void 0&&(re.isLoading=r[1]),d=new Pr({props:re}),ve.push(()=>be(d,"errorMessage",ee)),ve.push(()=>be(d,"isLoading",me)),d.$on("commitRefChange",r[8]),P=new ks({props:{lastUsedScriptPath:r[6]}}),P.$on("setupScriptChange",r[9]),{c(){e=R("div"),n=R("div"),t=R("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-gbnap0">in parallel</strong>, in an
        <strong class="svelte-gbnap0">isolated environment</strong>
        that will keep running, <strong class="svelte-gbnap0">even when you shut off your laptop</strong>.</p>`,o=D(),s=R("div"),c=R("div"),L(a.$$.fragment),$=D(),V&&V.c(),u=D(),w=R("div"),w.textContent="Start from any GitHub repo and branch:",g=D(),h=R("div"),L(d.$$.fragment),_=D(),z=R("div"),z.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,N=D(),E=R("div"),L(P.$$.fragment),y(t,"class","main-description svelte-gbnap0"),y(c,"class","error-message svelte-gbnap0"),y(w,"class","description svelte-gbnap0"),y(h,"class","commit-ref-selector svelte-gbnap0"),y(z,"class","description svelte-gbnap0"),y(E,"class","setup-script svelte-gbnap0"),y(s,"class","form-fields"),y(n,"class","content svelte-gbnap0"),y(e,"class","remote-agent-setup svelte-gbnap0")},m(U,te){x(U,e,te),I(e,n),I(n,t),I(n,o),I(n,s),I(s,c),C(a,c,null),I(s,$),V&&V.m(s,null),I(s,u),I(s,w),I(s,g),I(s,h),C(d,h,null),I(s,_),I(s,z),I(s,N),I(s,E),C(P,E,null),k=!0},p(U,[te]){const pe={};!i&&4&te&&(i=!0,pe.agentLimitErrorMessage=U[2],ye(()=>i=!1)),a.$set(pe),U[3].remoteAgentCreationError?V?(V.p(U,te),8&te&&p(V,1)):(V=an(U),V.c(),p(V,1),V.m(s,u)):V&&(H(),f(V,1,1,()=>{V=null}),O());const fe={};16&te&&(fe.lastUsedRepoUrl=U[4]),32&te&&(fe.lastUsedBranchName=U[5]),!m&&1&te&&(m=!0,fe.errorMessage=U[0],ye(()=>m=!1)),!v&&2&te&&(v=!0,fe.isLoading=U[1],ye(()=>v=!1)),d.$set(fe);const Ce={};64&te&&(Ce.lastUsedScriptPath=U[6]),P.$set(Ce)},i(U){k||(p(a.$$.fragment,U),U&&un(()=>{k&&(l||(l=Ze(c,Qe,{y:10},!0)),l.run(1))}),p(V),p(d.$$.fragment,U),p(P.$$.fragment,U),k=!0)},o(U){f(a.$$.fragment,U),U&&(l||(l=Ze(c,Qe,{y:10},!1)),l.run(0)),f(V),f(d.$$.fragment,U),f(P.$$.fragment,U),k=!1},d(U){U&&b(e),A(a),U&&l&&l.end(),V&&V.d(),A(d),A(P)}}}function Vs(r,e,n){let t,o,s,c,a;const i=Fe(ot.key);dt(r,i,d=>n(3,a=d));let l,$="",u=!1,w=null,g=null,h=null;return tt(async()=>{try{const d=await i.getLastRemoteAgentSetup();n(4,w=d.lastRemoteAgentGitRepoUrl),n(5,g=d.lastRemoteAgentGitBranch),n(6,h=d.lastRemoteAgentSetupScript),i.setHasEverUsedRemoteAgent(!0),await i.reportRemoteAgentEvent({eventName:An.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(d){console.error("Failed to load last remote agent setup:",d)}}),ln(()=>{i.setNewAgentDraft(null),i.setCreationMetrics(void 0)}),r.$$.update=()=>{var d,m;8&r.$$.dirty&&n(13,t=((d=a.newAgentDraft)==null?void 0:d.commitRef)??null),8&r.$$.dirty&&n(12,o=((m=a.newAgentDraft)==null?void 0:m.selectedBranch)??null),12295&r.$$.dirty&&n(11,s=(()=>{var z;const v=(z=t==null?void 0:t.github_commit_ref)==null?void 0:z.repository_url,_=o==null?void 0:o.name;return $||l||(u?"Loading repos and branches...":"")||!v&&"Please select a repository"||!_&&"Please select a branch"||(!(!u&&v&&_)&&v&&_?"Loading branch data...":"")||""})()),2048&r.$$.dirty&&n(10,c=!!s),1024&r.$$.dirty&&i.newAgentDraft&&i.newAgentDraft.isDisabled!==c&&i.setNewAgentDraft({...i.newAgentDraft,isDisabled:c})},[$,u,l,a,w,g,h,i,async function(d){i.setRemoteAgentCreationError(null);const m=i.newAgentDraft;m?i.setNewAgentDraft({...m,commitRef:d.detail.commitRef,selectedBranch:d.detail.selectedBranch,isDisabled:c}):i.setNewAgentDraft({commitRef:d.detail.commitRef,selectedBranch:d.detail.selectedBranch,setupScript:null,isDisabled:c,enableNotification:!0})},function(d){i.setRemoteAgentCreationError(null);const m=i.newAgentDraft;m?i.setNewAgentDraft({...m,setupScript:d.detail.script,isDisabled:c}):i.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:d.detail.script,isDisabled:c,enableNotification:!0})},c,s,o,t,function(d){l=d,n(2,l)},function(d){$=d,n(0,$)},function(d){u=d,n(1,u)}]}class Gc extends se{constructor(e){super(),ce(this,e,Vs,Os,ie,{})}}export{Gc as default};
