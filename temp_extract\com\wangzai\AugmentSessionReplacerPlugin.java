/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.ide.AppLifecycleListener
 *  com.intellij.openapi.application.ApplicationManager
 *  com.intellij.openapi.diagnostic.Logger
 *  org.jetbrains.annotations.NotNull
 */
package com.wangzai;

import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.wangzai.SessionIdReplacer;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class AugmentSessionReplacerPlugin
implements AppLifecycleListener {
    private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
    private static volatile boolean isInitialized = false;

    public AugmentSessionReplacerPlugin() {
        this.initialize();
    }

    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        if (commandLineArgs == null) {
            AugmentSessionReplacerPlugin.$$$reportNull$$$0(0);
        }
        this.initialize();
    }

    public void appStarted() {
        this.initialize();
    }

    private void initialize() {
        if (isInitialized) {
            return;
        }
        try {
            LOG.info("Starting Augment Session ID Replacer Plugin...");
            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                try {
                    SessionIdReplacer replacer = new SessionIdReplacer();
                    if (replacer.replaceSessionIdClass()) {
                        LOG.info("Successfully replaced SessionId class");
                        isInitialized = true;
                    } else {
                        LOG.warn("Failed to replace SessionId class");
                    }
                }
                catch (Exception e) {
                    LOG.error("Error during SessionId class replacement", (Throwable)e);
                }
            });
        }
        catch (Exception e) {
            LOG.error("Failed to initialize Augment Session ID Replacer", (Throwable)e);
        }
    }

    public static AugmentSessionReplacerPlugin getInstance() {
        return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
    }

    private static /* synthetic */ void $$$reportNull$$$0(int n) {
        throw new IllegalArgumentException(String.format("Argument for @NotNull parameter '%s' of %s.%s must not be null", "commandLineArgs", "com/wangzai/AugmentSessionReplacerPlugin", "appFrameCreated"));
    }
}

