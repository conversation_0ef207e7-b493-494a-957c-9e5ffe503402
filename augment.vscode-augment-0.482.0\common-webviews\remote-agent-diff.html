<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-hAolFNc71jnKc95TcryusQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-CgXpsGxF.js" nonce="nonce-hAolFNc71jnKc95TcryusQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Cx9dt_ox.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BCZOObrS.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-DhtTPDph.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-y6tm-B4G.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-BqzdgpkK.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BjDqXmYl.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-BiWRcmeV.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-DTMpOwfF.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-DUiNNixO.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BxQII05L.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-8X-F_Twk.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-C7XQLqYW.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-D3vAw6HE.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-B3px2_jp.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DhtPLzGu.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-8-Z76Y2_.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-C_TwPNdv.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-FVMxq7uD.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/expand-CURYX9ur.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BbVpV4C-.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Th-X2FgN.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CrDNmo5Z.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-DL-lqibn.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DTcQ2vsq.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-B1-rUMk8.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-C9S5rKqy.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
