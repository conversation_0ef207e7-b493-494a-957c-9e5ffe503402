var Xt=Object.defineProperty;var Kt=(i,t,s)=>t in i?Xt(i,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[t]=s;var U=(i,t,s)=>Kt(i,typeof t!="symbol"?t+"":t,s);import{S as j,i as X,s as K,a as gt,b as Qt,H as Yt,w as Zt,x as te,y as ee,h as p,d as ht,z as se,g as ne,n as tt,j as wt,T as G,V as I,C as v,I as B,c as k,e as $,D as y,f as E,u as m,t as u,F as _,a5 as Ut,L as C,M as V,J as D,q as M,r as O,X as Z,ag as mt,ah as Gt,a8 as Vt,an as vt,a6 as re}from"./SpinnerAugment-Cx9dt_ox.js";import"./design-system-init-BCZOObrS.js";import{s as ae}from"./index-DhtTPDph.js";import"./design-system-init-y6tm-B4G.js";import{W as S,e as F,u as Q,o as Y,h as oe}from"./BaseButton-BqzdgpkK.js";import{T as at,M as ie}from"./TextTooltipAugment-DTMpOwfF.js";import{S as ut,a as le,T as ce,b as de,v as ge,c as me}from"./StatusIndicator-BAEKlH2H.js";import{a as N,R as dt}from"./types-DDm27S8B.js";import{T as ot}from"./Content-BiWRcmeV.js";import{C as ue}from"./CardAugment-RumqAz-v.js";import{I as pt}from"./IconButtonAugment-BjDqXmYl.js";import{C as pe}from"./CalloutAugment-BFrX0piu.js";import{T as Jt}from"./terminal-BjJSzToG.js";import{E as $e}from"./exclamation-triangle-BbVpV4C-.js";import{S as fe,a as he}from"./types-CGlLNakm.js";import{A as we}from"./augment-logo-DdgjewTP.js";import"./utils-DJhaageo.js";import"./chat-types-B-te1sXh.js";import"./index-DUiNNixO.js";import"./globals-D0QH3NT1.js";class ve{constructor(t,s=void 0,e,n){U(this,"subscribers",new Set);this._msgBroker=t,this._state=s,this.validateState=e,this._storeId=n,s&&this.setStateInternal(s)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,s){return t.id===this.storeId&&this.validateState(s)}update(t){const s=t(this._state);s!==void 0&&this.setStateInternal(s)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:S.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:S.getSharedWebviewState,id:this.storeId,data:{}});t.type===S.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case S.updateSharedWebviewState:case S.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}function ye(i){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},i[0]],n={};for(let r=0;r<e.length;r+=1)n=gt(n,e[r]);return{c(){t=Qt("svg"),s=new Yt(!0),this.h()},l(r){t=Zt(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=te(t);s=ee(o,!0),o.forEach(p),this.h()},h(){s.a=null,ht(t,n)},m(r,o){se(r,t,o),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',t)},p(r,[o]){ht(t,n=ne(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&r[0]]))},i:tt,o:tt,d(r){r&&p(t)}}}function _e(i,t,s){return i.$$set=e=>{s(0,t=gt(gt({},t),wt(e)))},[t=wt(t)]}class Se extends j{constructor(t){super(),X(this,t,_e,ye,K,{})}}class Re extends Error{constructor(t){super(t),this.name="StreamRetryExhaustedError"}}class jt{constructor(t,s,e,n,r=5,o=4e3,d=2,l){U(this,"_isCancelled",!1);U(this,"streamId");this.agentId=t,this.lastProcessedValue=s,this.startStreamFn=e,this.cancelStreamFn=n,this.maxRetries=r,this.baseDelay=o,this.attemptErrorThreshold=d,this.unhandledErrorMessage=l,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let t=0;for(;!this._isCancelled;){const s=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedValue);try{for await(const e of s){if(this._isCancelled)return;t=0,yield e}return}catch(e){const n=e instanceof Error?e.message:String(e);if(n===fe&&(this._isCancelled=!0),this._isCancelled)return;if(t++,t>this.maxRetries)throw new Re(`Failed after ${this.maxRetries} attempts: ${n}`);let r=this.baseDelay*2**(t-1);n===he?r=0:t>this.attemptErrorThreshold&&(yield{errorMessage:this.unhandledErrorMessage??n,retryAt:new Date(Date.now()+r)}),console.warn(`Retrying remote agent stream in ${r/1e3} seconds... (Attempt ${t} of ${this.maxRetries})`),await new Promise(o=>setTimeout(o,r));continue}}}}class Ae extends jt{constructor(t,s,e,n,r=5,o=4e3){super(t,s,e,n,r,o,1,"There was an error connecting to the remote agent.")}}class xe extends jt{constructor(t,s,e,n=5,r=4e3){super("overviews",t,s,e,n,r,2,void 0)}}class it{constructor(t){U(this,"_msgBroker");U(this,"_activeRetryStreams",new Map);U(this,"_activeOverviewsStream");this._msgBroker=t}hasActiveHistoryStream(t){return this._activeRetryStreams.has(t)}getActiveHistoryStream(t){return this._activeRetryStreams.get(t)}get activeHistoryStreams(){return this._activeRetryStreams}hasActiveOverviewsStream(){return this._activeOverviewsStream!==void 0&&!this._activeOverviewsStream.isCancelled}getActiveOverviewsStream(){return this._activeOverviewsStream}async sshToRemoteAgent(t){const s=await this._msgBroker.send({type:S.remoteAgentSshRequest,data:{agentId:t}},1e4);return!!s.data.success||(console.error("Failed to connect to remote agent:",s.data.error),!1)}async deleteRemoteAgent(t,s=!1){return(await this._msgBroker.send({type:S.deleteRemoteAgentRequest,data:{agentId:t,doSkipConfirmation:s}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:S.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:S.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(t){return(await this._msgBroker.send({type:S.getRemoteAgentNotificationEnabledRequest,data:{agentIds:t}})).data}async setRemoteAgentNotificationEnabled(t,s){await this._msgBroker.send({type:S.setRemoteAgentNotificationEnabled,data:{agentId:t,enabled:s}})}async deleteRemoteAgentNotificationEnabled(t){await this._msgBroker.send({type:S.deleteRemoteAgentNotificationEnabled,data:{agentId:t}})}async notifyRemoteAgentReady(t){await this._msgBroker.send({type:S.remoteAgentNotifyReady,data:t})}showRemoteAgentDiffPanel(t){this._msgBroker.postMessage({type:S.showRemoteAgentDiffPanel,data:t})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:S.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(t,s,e=1e4){return await this._msgBroker.send({type:S.getRemoteAgentChatHistoryRequest,data:{agentId:t,lastProcessedSequenceId:s}},e)}async sendRemoteAgentChatRequest(t,s,e=9e4){return this._msgBroker.send({type:S.remoteAgentChatRequest,data:{agentId:t,requestDetails:s,timeoutMs:e}},e)}async interruptRemoteAgent(t,s=1e4){return await this._msgBroker.send({type:S.remoteAgentInterruptRequest,data:{agentId:t}},s)}async createRemoteAgent(t,s,e,n,r,o,d=1e4){return await this._msgBroker.send({type:S.createRemoteAgentRequest,data:{prompt:t,workspaceSetup:s,setupScript:e,isSetupScriptAgent:n,modelId:r,remoteAgentCreationMetrics:o}},d)}async getRemoteAgentOverviews(t=1e4){return await this._msgBroker.send({type:S.getRemoteAgentOverviewsRequest},t)}async listSetupScripts(t=5e3){return await this._msgBroker.send({type:S.listSetupScriptsRequest},t)}async saveSetupScript(t,s,e,n=5e3){return await this._msgBroker.send({type:S.saveSetupScriptRequest,data:{name:t,content:s,location:e}},n)}async deleteSetupScript(t,s,e=5e3){return await this._msgBroker.send({type:S.deleteSetupScriptRequest,data:{name:t,location:s}},e)}async renameSetupScript(t,s,e,n=5e3){return await this._msgBroker.send({type:S.renameSetupScriptRequest,data:{oldName:t,newName:s,location:e}},n)}async getRemoteAgentWorkspaceLogs(t,s,e,n=1e4){return await this._msgBroker.send({type:S.remoteAgentWorkspaceLogsRequest,data:{agentId:t,lastProcessedStep:s,lastProcessedSequenceId:e}},n)}async saveLastRemoteAgentSetup(t,s,e){return await this._msgBroker.send({type:S.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:t,lastRemoteAgentGitBranch:s,lastRemoteAgentSetupScript:e}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:S.getLastRemoteAgentSetupRequest})}async*startRemoteAgentOverviewsStream(t,s,e=6e4,n=3e5){const r={type:S.remoteAgentOverviewsStreamRequest,data:{streamId:t,lastUpdateTimestamp:s}},o=this._msgBroker.stream(r,e,n);for await(const d of o)yield d.data}async*startRemoteAgentHistoryStream(t,s,e,n=6e4,r=3e5){const o={type:S.remoteAgentHistoryStreamRequest,data:{streamId:s,agentId:t,lastProcessedSequenceId:e}},d=this._msgBroker.stream(o,n,r);for await(const l of d)yield l.data}async*startRemoteAgentsListStreamWithRetry(t,s=5,e=4e3){var r;const n=new xe(t,(o,d,l)=>this.startRemoteAgentOverviewsStream(d,l),o=>this._closeRemoteAgentsStream(o),s,e);(r=this._activeOverviewsStream)==null||r.cancel(),this._activeOverviewsStream=n;try{yield*n.getStream()}finally{n.isCancelled||(this._activeOverviewsStream=void 0)}}async*startRemoteAgentHistoryStreamWithRetry(t,s,e=5,n=4e3){var o;const r=new Ae(t,s,(d,l,a)=>this.startRemoteAgentHistoryStream(d,l,a),d=>this._closeRemoteAgentsStream(d),e,n);(o=this._activeRetryStreams.get(t))==null||o.cancel(),this._activeRetryStreams.set(t,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(t)}}cancelRemoteAgentOverviewsStream(){this._activeOverviewsStream&&(this._activeOverviewsStream.cancel(),this._activeOverviewsStream=void 0)}cancelRemoteAgentHistoryStream(t){const s=this._activeRetryStreams.get(t);s&&(s.cancel(),this._activeRetryStreams.delete(t))}async _closeRemoteAgentsStream(t){await this._msgBroker.send({type:S.cancelRemoteAgentsStreamRequest,data:{streamId:t}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(t=>{t.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelRemoteAgentOverviewsStream(),this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:S.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(t){return console.error("Failed to get pinned agents from store:",t),{}}}async savePinnedAgentToStore(t,s){try{await this._msgBroker.send({type:S.setRemoteAgentPinnedStatus,data:{agentId:t,isPinned:s}})}catch(e){console.error("Failed to save pinned agent to store:",e)}}async deletePinnedAgentFromStore(t){try{await this._msgBroker.send({type:S.deleteRemoteAgentPinnedStatus,data:{agentId:t}})}catch(s){console.error("Failed to delete pinned agent from store:",s)}}async openDiffInBuffer(t,s,e){return await this._msgBroker.send({type:S.openDiffInBuffer,data:{oldContents:t,newContents:s,filePath:e}})}async pauseRemoteAgentWorkspace(t){return await this._msgBroker.send({type:S.remoteAgentPauseRequest,data:{agentId:t}},3e4)}async resumeRemoteAgentWorkspace(t){return await this._msgBroker.send({type:S.remoteAgentResumeRequest,data:{agentId:t}},9e4)}async reportRemoteAgentEvent(t){await this._msgBroker.send({type:S.reportRemoteAgentEvent,data:t})}async getRemoteAgentStatus(){return await this._msgBroker.send({type:S.getRemoteAgentStatus})}}U(it,"key","remoteAgentsClient");function yt(i){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const s=new Date().getTime()-t.getTime(),e=Math.floor(s/1e3),n=Math.floor(e/60),r=Math.floor(n/60),o=Math.floor(r/24);return e<60?`${e}s ago`:n<60?`${n}m ago`:r<24?`${r}h ago`:o<30?`${o}d ago`:t.toLocaleDateString()}catch(s){return console.error("Error formatting date:",s),"Unknown time"}}(new Date(i))}function ke(i){let t,s=i[0]?"Running in the cloud":"Running locally";return{c(){t=C(s)},m(e,n){$(e,t,n)},p(e,n){1&n&&s!==(s=e[0]?"Running in the cloud":"Running locally")&&V(t,s)},d(e){e&&p(t)}}}function be(i){let t;return{c(){t=C("Unknown time")},m(s,e){$(s,t,e)},p:tt,d(s){s&&p(t)}}}function Ie(i){let t;return{c(){t=C(i[3])},m(s,e){$(s,t,e)},p(s,e){8&e&&V(t,s[3])},d(s){s&&p(t)}}}function Be(i){let t,s,e,n=i[1]===N.agentRunning?"Last updated":"Started";function r(l,a){return l[2]?Ie:be}let o=r(i),d=o(i);return{c(){t=C(n),s=B(),d.c(),e=D()},m(l,a){$(l,t,a),$(l,s,a),d.m(l,a),$(l,e,a)},p(l,a){2&a&&n!==(n=l[1]===N.agentRunning?"Last updated":"Started")&&V(t,n),o===(o=r(l))&&d?d.p(l,a):(d.d(1),d=o(l),d&&(d.c(),d.m(e.parentNode,e)))},d(l){l&&(p(t),p(s),p(e)),d.d(l)}}}function Pe(i){let t,s,e,n,r,o;return s=new G({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[ke]},$$scope:{ctx:i}}}),r=new G({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[Be]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),e=B(),n=I("div"),v(r.$$.fragment),k(n,"class","time-container"),k(t,"class","agent-card-footer svelte-1qwlkoj")},m(d,l){$(d,t,l),y(s,t,null),E(t,e),E(t,n),y(r,n,null),o=!0},p(d,[l]){const a={};33&l&&(a.$$scope={dirty:l,ctx:d}),s.$set(a);const c={};46&l&&(c.$$scope={dirty:l,ctx:d}),r.$set(c)},i(d){o||(m(s.$$.fragment,d),m(r.$$.fragment,d),o=!0)},o(d){u(s.$$.fragment,d),u(r.$$.fragment,d),o=!1},d(d){d&&p(t),_(s),_(r)}}}function Me(i,t,s){let{isRemote:e=!1}=t,{status:n}=t,{timestamp:r}=t,o=yt(r);const d=function(l,a){let c=1e3;const g=new Date(l),w=setInterval(()=>{const R=Math.floor((new Date().getTime()-g.getTime())/1e3/60);R>=1&&(c=6e4),R>=60&&(c=36e5),R>=1440&&(c=864e5),a(yt(l))},c);return()=>clearInterval(w)}(r,l=>{s(3,o=l)});return Ut(()=>{d()}),i.$$set=l=>{"isRemote"in l&&s(0,e=l.isRemote),"status"in l&&s(1,n=l.status),"timestamp"in l&&s(2,r=l.timestamp)},[e,n,r,o]}class Oe extends j{constructor(t){super(),X(this,t,Me,Pe,K,{isRemote:0,status:1,timestamp:2})}}function Ee(i){let t;return{c(){t=C(i[0])},m(s,e){$(s,t,e)},p(s,e){1&e&&V(t,s[0])},d(s){s&&p(t)}}}function qe(i){let t,s,e;return s=new G({props:{size:1,color:"secondary",$$slots:{default:[Ee]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),k(t,"class","task-text-container svelte-1tatwxk")},m(n,r){$(n,t,r),y(s,t,null),e=!0},p(n,r){const o={};9&r&&(o.$$scope={dirty:r,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),_(s)}}}function _t(i){let t,s,e;return s=new G({props:{size:1,color:i[1]==="error"?"error":"neutral",$$slots:{default:[Fe]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),k(t,"class","task-status-indicator svelte-1tatwxk")},m(n,r){$(n,t,r),y(s,t,null),e=!0},p(n,r){const o={};2&r&&(o.color=n[1]==="error"?"error":"neutral"),10&r&&(o.$$scope={dirty:r,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),_(s)}}}function Fe(i){let t,s=i[1]==="error"?"!":i[1]==="warning"?"⚠":"";return{c(){t=C(s)},m(e,n){$(e,t,n)},p(e,n){2&n&&s!==(s=e[1]==="error"?"!":e[1]==="warning"?"⚠":"")&&V(t,s)},d(e){e&&p(t)}}}function Ce(i){let t,s,e,n,r,o,d;r=new at({props:{content:i[0],triggerOn:[ot.Hover],maxWidth:"400px",$$slots:{default:[qe]},$$scope:{ctx:i}}});let l=(i[1]==="error"||i[1]==="warning")&&_t(i);return{c(){t=I("div"),s=I("div"),n=B(),v(r.$$.fragment),o=B(),l&&l.c(),k(s,"class",e="bullet-point "+i[2]+" svelte-1tatwxk"),k(t,"class","task-item svelte-1tatwxk")},m(a,c){$(a,t,c),E(t,s),E(t,n),y(r,t,null),E(t,o),l&&l.m(t,null),d=!0},p(a,[c]){(!d||4&c&&e!==(e="bullet-point "+a[2]+" svelte-1tatwxk"))&&k(s,"class",e);const g={};1&c&&(g.content=a[0]),9&c&&(g.$$scope={dirty:c,ctx:a}),r.$set(g),a[1]==="error"||a[1]==="warning"?l?(l.p(a,c),2&c&&m(l,1)):(l=_t(a),l.c(),m(l,1),l.m(t,null)):l&&(M(),u(l,1,1,()=>{l=null}),O())},i(a){d||(m(r.$$.fragment,a),m(l),d=!0)},o(a){u(r.$$.fragment,a),u(l),d=!1},d(a){a&&p(t),_(r),l&&l.d()}}}function He(i,t,s){let e,{text:n}=t,{status:r="info"}=t;return i.$$set=o=>{"text"in o&&s(0,n=o.text),"status"in o&&s(1,r=o.status)},i.$$.update=()=>{2&i.$$.dirty&&s(2,e=function(o){switch(o){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[n,r,e]}class Te extends j{constructor(t){super(),X(this,t,He,Ce,K,{text:0,status:1})}}function St(i,t,s){const e=i.slice();return e[24]=t[s],e[26]=s,e}function Rt(i){let t,s,e;return s=new pe({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Ne],default:[De]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),k(t,"class","deletion-error svelte-1bxdvw4")},m(n,r){$(n,t,r),y(s,t,null),e=!0},p(n,r){const o={};134217744&r&&(o.$$scope={dirty:r,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),_(s)}}}function De(i){let t,s,e,n,r;return{c(){t=C(i[4]),s=B(),e=I("button"),e.textContent="×",k(e,"class","error-dismiss svelte-1bxdvw4"),k(e,"aria-label","Dismiss error")},m(o,d){$(o,t,d),$(o,s,d),$(o,e,d),n||(r=Vt(e,"click",i[11]),n=!0)},p(o,d){16&d&&V(t,o[4])},d(o){o&&(p(t),p(s),p(e)),n=!1,r()}}}function Ne(i){let t,s;return t=new $e({props:{slot:"icon"}}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},p:tt,i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function ze(i){let t,s;return t=new G({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Le]},$$scope:{ctx:i}}}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},p(e,n){const r={};134217729&n&&(r.$$scope={dirty:n,ctx:e}),t.$set(r)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function We(i){let t,s,e,n,r,o;return e=new Jt({}),r=new G({props:{size:2,weight:"medium",$$slots:{default:[Ue]},$$scope:{ctx:i}}}),{c(){t=I("div"),s=I("div"),v(e.$$.fragment),n=B(),v(r.$$.fragment),k(s,"class","setup-script-badge svelte-1bxdvw4"),k(t,"class","setup-script-title-container svelte-1bxdvw4")},m(d,l){$(d,t,l),E(t,s),y(e,s,null),E(t,n),y(r,t,null),o=!0},p(d,l){const a={};134217728&l&&(a.$$scope={dirty:l,ctx:d}),r.$set(a)},i(d){o||(m(e.$$.fragment,d),m(r.$$.fragment,d),o=!0)},o(d){u(e.$$.fragment,d),u(r.$$.fragment,d),o=!1},d(d){d&&p(t),_(e),_(r)}}}function Le(i){let t,s=i[0].session_summary+"";return{c(){t=C(s)},m(e,n){$(e,t,n)},p(e,n){1&n&&s!==(s=e[0].session_summary+"")&&V(t,s)},d(e){e&&p(t)}}}function Ue(i){let t;return{c(){t=I("span"),t.textContent="Generate a setup script",k(t,"class","setup-script-title svelte-1bxdvw4")},m(s,e){$(s,t,e)},p:tt,d(s){s&&p(t)}}}function At(i){let t,s,e=[],n=new Map,r=F(i[8].slice(0,3));const o=d=>d[26];for(let d=0;d<r.length;d+=1){let l=St(i,r,d),a=o(l);n.set(a,e[d]=xt(a,l))}return{c(){t=I("div");for(let d=0;d<e.length;d+=1)e[d].c();k(t,"class","tasks-list svelte-1bxdvw4")},m(d,l){$(d,t,l);for(let a=0;a<e.length;a+=1)e[a]&&e[a].m(t,null);s=!0},p(d,l){256&l&&(r=F(d[8].slice(0,3)),M(),e=Q(e,l,o,1,d,r,n,t,Y,xt,null,St),O())},i(d){if(!s){for(let l=0;l<r.length;l+=1)m(e[l]);s=!0}},o(d){for(let l=0;l<e.length;l+=1)u(e[l]);s=!1},d(d){d&&p(t);for(let l=0;l<e.length;l+=1)e[l].d()}}}function xt(i,t){let s,e,n;return e=new Te({props:{text:t[24],status:"success"}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(r,o){$(r,s,o),y(e,r,o),n=!0},p(r,o){t=r;const d={};256&o&&(d.text=t[24]),e.$set(d)},i(r){n||(m(e.$$.fragment,r),n=!0)},o(r){u(e.$$.fragment,r),n=!1},d(r){r&&p(s),_(e,r)}}}function Ge(i){let t,s;return t=new de({}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Ve(i){let t,s;return t=new Se({}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Je(i){let t,s,e,n;const r=[Ve,Ge],o=[];function d(l,a){return l[7]?0:1}return t=d(i),s=o[t]=r[t](i),{c(){s.c(),e=D()},m(l,a){o[t].m(l,a),$(l,e,a),n=!0},p(l,a){let c=t;t=d(l),t!==c&&(M(),u(o[c],1,1,()=>{o[c]=null}),O(),s=o[t],s||(s=o[t]=r[t](l),s.c()),m(s,1),s.m(e.parentNode,e))},i(l){n||(m(s),n=!0)},o(l){u(s),n=!1},d(l){l&&p(e),o[t].d(l)}}}function je(i){let t,s;return t=new pt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Je]},$$scope:{ctx:i}}}),t.$on("click",i[16]),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},p(e,n){const r={};134217856&n&&(r.$$scope={dirty:n,ctx:e}),t.$set(r)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Xe(i){let t,s;return t=new Jt({}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Ke(i){let t,s;return t=new pt({props:{disabled:!i[5],variant:"ghost",color:"neutral",size:1,title:i[5]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Xe]},$$scope:{ctx:i}}}),t.$on("click",i[17]),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},p(e,n){const r={};32&n&&(r.disabled=!e[5]),32&n&&(r.title=e[5]?"SSH to agent":"SSH to agent (agent must be running or idle)"),134217728&n&&(r.$$scope={dirty:n,ctx:e}),t.$set(r)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Qe(i){let t,s;return t=new ce({}),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Ye(i){let t,s;return t=new pt({props:{variant:"ghost",color:"neutral",size:1,disabled:i[3],title:i[3]?"Deleting agent...":"Delete agent",$$slots:{default:[Qe]},$$scope:{ctx:i}}}),t.$on("click",i[18]),{c(){v(t.$$.fragment)},m(e,n){y(t,e,n),s=!0},p(e,n){const r={};8&n&&(r.disabled=e[3]),8&n&&(r.title=e[3]?"Deleting agent...":"Delete agent"),134217728&n&&(r.$$scope={dirty:n,ctx:e}),t.$set(r)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){_(t,e)}}}function Ze(i){let t,s,e,n,r,o,d,l,a,c,g,w,R,h,A,J,H,z,T,W;const b=[We,ze],q=[];function L(f,x){return f[0].is_setup_script_agent?0:1}e=L(i),n=q[e]=b[e](i),l=new le({props:{status:i[0].status,workspaceStatus:i[0].workspace_status,isExpanded:!0,hasUpdates:i[0].has_updates}});let P=i[8].length>0&&At(i);return R=new at({props:{content:i[7]?"Unpin agent":"Pin agent",triggerOn:[ot.Hover],side:"top",$$slots:{default:[je]},$$scope:{ctx:i}}}),A=new at({props:{content:"SSH to agent",triggerOn:[ot.Hover],side:"top",$$slots:{default:[Ke]},$$scope:{ctx:i}}}),H=new at({props:{content:"Delete agent",triggerOn:[ot.Hover],side:"top",$$slots:{default:[Ye]},$$scope:{ctx:i}}}),T=new Oe({props:{isRemote:i[6],status:i[0].status,timestamp:i[0].updated_at||i[0].started_at}}),{c(){t=I("div"),s=I("div"),n.c(),o=B(),d=I("div"),v(l.$$.fragment),a=B(),c=I("div"),P&&P.c(),g=B(),w=I("div"),v(R.$$.fragment),h=B(),v(A.$$.fragment),J=B(),v(H.$$.fragment),z=B(),v(T.$$.fragment),k(s,"class","session-summary-container svelte-1bxdvw4"),k(s,"title",r=i[0].is_setup_script_agent?"Generate a setup script":i[0].session_summary),k(d,"class","card-info"),k(t,"class","card-header svelte-1bxdvw4"),k(c,"class","card-content svelte-1bxdvw4"),k(w,"class","card-actions svelte-1bxdvw4")},m(f,x){$(f,t,x),E(t,s),q[e].m(s,null),E(t,o),E(t,d),y(l,d,null),$(f,a,x),$(f,c,x),P&&P.m(c,null),$(f,g,x),$(f,w,x),y(R,w,null),E(w,h),y(A,w,null),E(w,J),y(H,w,null),$(f,z,x),y(T,f,x),W=!0},p(f,x){let lt=e;e=L(f),e===lt?q[e].p(f,x):(M(),u(q[lt],1,1,()=>{q[lt]=null}),O(),n=q[e],n?n.p(f,x):(n=q[e]=b[e](f),n.c()),m(n,1),n.m(s,null)),(!W||1&x&&r!==(r=f[0].is_setup_script_agent?"Generate a setup script":f[0].session_summary))&&k(s,"title",r);const nt={};1&x&&(nt.status=f[0].status),1&x&&(nt.workspaceStatus=f[0].workspace_status),1&x&&(nt.hasUpdates=f[0].has_updates),l.$set(nt),f[8].length>0?P?(P.p(f,x),256&x&&m(P,1)):(P=At(f),P.c(),m(P,1),P.m(c,null)):P&&(M(),u(P,1,1,()=>{P=null}),O());const ct={};128&x&&(ct.content=f[7]?"Unpin agent":"Pin agent"),134217857&x&&(ct.$$scope={dirty:x,ctx:f}),R.$set(ct);const $t={};134217760&x&&($t.$$scope={dirty:x,ctx:f}),A.$set($t);const ft={};134217737&x&&(ft.$$scope={dirty:x,ctx:f}),H.$set(ft);const rt={};64&x&&(rt.isRemote=f[6]),1&x&&(rt.status=f[0].status),1&x&&(rt.timestamp=f[0].updated_at||f[0].started_at),T.$set(rt)},i(f){W||(m(n),m(l.$$.fragment,f),m(P),m(R.$$.fragment,f),m(A.$$.fragment,f),m(H.$$.fragment,f),m(T.$$.fragment,f),W=!0)},o(f){u(n),u(l.$$.fragment,f),u(P),u(R.$$.fragment,f),u(A.$$.fragment,f),u(H.$$.fragment,f),u(T.$$.fragment,f),W=!1},d(f){f&&(p(t),p(a),p(c),p(g),p(w),p(z)),q[e].d(),_(l),P&&P.d(),_(R),_(A),_(H),_(T,f)}}}function ts(i){let t,s,e,n,r=i[4]&&Rt(i);return e=new ue({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[Ze]},$$scope:{ctx:i}}}),e.$on("click",i[19]),e.$on("keydown",i[20]),{c(){t=I("div"),r&&r.c(),s=B(),v(e.$$.fragment),k(t,"class","card-wrapper svelte-1bxdvw4"),Z(t,"selected-card",i[1]),Z(t,"setup-script-card",i[0].is_setup_script_agent),Z(t,"deleting",i[3])},m(o,d){$(o,t,d),r&&r.m(t,null),E(t,s),y(e,t,null),n=!0},p(o,[d]){o[4]?r?(r.p(o,d),16&d&&m(r,1)):(r=Rt(o),r.c(),m(r,1),r.m(t,s)):r&&(M(),u(r,1,1,()=>{r=null}),O());const l={};134218217&d&&(l.$$scope={dirty:d,ctx:o}),e.$set(l),(!n||2&d)&&Z(t,"selected-card",o[1]),(!n||1&d)&&Z(t,"setup-script-card",o[0].is_setup_script_agent),(!n||8&d)&&Z(t,"deleting",o[3])},i(o){n||(m(r),m(e.$$.fragment,o),n=!0)},o(o){u(r),u(e.$$.fragment,o),n=!1},d(o){o&&p(t),r&&r.d(),_(e)}}}function es(i,t,s){let e,n,r,o,d,l,{agent:a}=t,{selected:c=!1}=t,{onSelect:g}=t;const w=mt(it.key),R=mt(ut);Gt(i,R,b=>s(15,l=b));let h=!1,A=null,J=null;async function H(b){var L,P;z(),s(3,h=!0);const q=((L=l.state)==null?void 0:L.agentOverviews)||[];try{if(!await w.deleteRemoteAgent(b))throw new Error("Failed to delete agent");if(R.update(f=>{if(f)return{...f,agentOverviews:f.agentOverviews.filter(x=>x.remote_agent_id!==b)}}),(((P=l.state)==null?void 0:P.pinnedAgents)||{})[b])try{await w.deletePinnedAgentFromStore(b);const f=await w.getPinnedAgentsFromStore();R.update(x=>{if(x)return{...x,pinnedAgents:f}})}catch(f){console.error("Failed to remove pinned status:",f)}}catch(f){console.error("Failed to delete agent:",f),R.update(x=>{if(x)return{...x,agentOverviews:q}}),s(4,A=f instanceof Error?f.message:"Failed to delete agent"),J=setTimeout(()=>{z()},5e3)}finally{s(3,h=!1)}}function z(){s(4,A=null),J&&(clearTimeout(J),J=null)}async function T(b){try{r?await w.deletePinnedAgentFromStore(b):await w.savePinnedAgentToStore(b,!0);const q=await w.getPinnedAgentsFromStore();R.update(L=>{if(L)return{...L,pinnedAgents:q}})}catch(q){console.error("Failed to toggle pinned status:",q)}}function W(){o&&(async b=>{await w.sshToRemoteAgent(b.remote_agent_id)})(a)}return Ut(()=>{z()}),i.$$set=b=>{"agent"in b&&s(0,a=b.agent),"selected"in b&&s(1,c=b.selected),"onSelect"in b&&s(2,g=b.onSelect)},i.$$.update=()=>{var b;1&i.$$.dirty&&s(8,e=a.turn_summaries||[]),32768&i.$$.dirty&&s(14,n=((b=l.state)==null?void 0:b.pinnedAgents)||{}),16385&i.$$.dirty&&s(7,r=(n==null?void 0:n[a.remote_agent_id])===!0),1&i.$$.dirty&&s(5,o=a.status===N.agentRunning||a.status===N.agentIdle)},s(6,d=!0),[a,c,g,h,A,o,!0,r,e,R,H,z,T,W,n,l,b=>{b.stopPropagation(),T(a.remote_agent_id)},b=>{b.stopPropagation(),W()},b=>{b.stopPropagation(),H(a.remote_agent_id)},()=>g(a.remote_agent_id),b=>b.key==="Enter"&&g(a.remote_agent_id)]}class et extends j{constructor(t){super(),X(this,t,es,ts,K,{agent:0,selected:1,onSelect:2})}}function ss(i){let t;return{c(){t=C(i[0])},m(s,e){$(s,t,e)},p(s,e){1&e&&V(t,s[0])},d(s){s&&p(t)}}}function ns(i){let t,s,e;return s=new G({props:{size:2,color:"secondary",$$slots:{default:[ss]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),k(t,"class","section-header svelte-1tegnqi")},m(n,r){$(n,t,r),y(s,t,null),e=!0},p(n,[r]){const o={};3&r&&(o.$$scope={dirty:r,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),_(s)}}}function rs(i,t,s){let{title:e}=t;return i.$$set=n=>{"title"in n&&s(0,e=n.title)},[e]}class st extends j{constructor(t){super(),X(this,t,rs,ns,K,{title:0})}}function kt(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function bt(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function It(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function Bt(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function Pt(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function Mt(i,t,s){const e=i.slice();return e[6]=t[s],e[8]=s,e}function as(i){let t,s,e,n,r,o,d,l=i[1].pinned.length>0&&Ot(i),a=i[1].readyToReview.length>0&&qt(i),c=i[1].running.length>0&&Ct(i),g=i[1].idle.length>0&&Tt(i),w=i[1].failed.length>0&&Nt(i),R=i[1].additional.length>0&&Wt(i);return{c(){l&&l.c(),t=B(),a&&a.c(),s=B(),c&&c.c(),e=B(),g&&g.c(),n=B(),w&&w.c(),r=B(),R&&R.c(),o=D()},m(h,A){l&&l.m(h,A),$(h,t,A),a&&a.m(h,A),$(h,s,A),c&&c.m(h,A),$(h,e,A),g&&g.m(h,A),$(h,n,A),w&&w.m(h,A),$(h,r,A),R&&R.m(h,A),$(h,o,A),d=!0},p(h,A){h[1].pinned.length>0?l?(l.p(h,A),2&A&&m(l,1)):(l=Ot(h),l.c(),m(l,1),l.m(t.parentNode,t)):l&&(M(),u(l,1,1,()=>{l=null}),O()),h[1].readyToReview.length>0?a?(a.p(h,A),2&A&&m(a,1)):(a=qt(h),a.c(),m(a,1),a.m(s.parentNode,s)):a&&(M(),u(a,1,1,()=>{a=null}),O()),h[1].running.length>0?c?(c.p(h,A),2&A&&m(c,1)):(c=Ct(h),c.c(),m(c,1),c.m(e.parentNode,e)):c&&(M(),u(c,1,1,()=>{c=null}),O()),h[1].idle.length>0?g?(g.p(h,A),2&A&&m(g,1)):(g=Tt(h),g.c(),m(g,1),g.m(n.parentNode,n)):g&&(M(),u(g,1,1,()=>{g=null}),O()),h[1].failed.length>0?w?(w.p(h,A),2&A&&m(w,1)):(w=Nt(h),w.c(),m(w,1),w.m(r.parentNode,r)):w&&(M(),u(w,1,1,()=>{w=null}),O()),h[1].additional.length>0?R?(R.p(h,A),2&A&&m(R,1)):(R=Wt(h),R.c(),m(R,1),R.m(o.parentNode,o)):R&&(M(),u(R,1,1,()=>{R=null}),O())},i(h){d||(m(l),m(a),m(c),m(g),m(w),m(R),d=!0)},o(h){u(l),u(a),u(c),u(g),u(w),u(R),d=!1},d(h){h&&(p(t),p(s),p(e),p(n),p(r),p(o)),l&&l.d(h),a&&a.d(h),c&&c.d(h),g&&g.d(h),w&&w.d(h),R&&R.d(h)}}}function os(i){let t,s,e;return s=new G({props:{size:3,color:"secondary",$$slots:{default:[is]},$$scope:{ctx:i}}}),{c(){t=I("div"),v(s.$$.fragment),k(t,"class","empty-state svelte-5a9boh")},m(n,r){$(n,t,r),y(s,t,null),e=!0},p(n,r){const o={};16384&r&&(o.$$scope={dirty:r,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),_(s)}}}function Ot(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Pinned"}});let d=F(i[1].pinned);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=Mt(i,d,a),g=l(c);o.set(g,r[a]=Et(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].pinned),M(),r=Q(r,c,l,1,a,d,o,e,Y,Et,null,Mt),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Et(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function qt(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Ready to review"}});let d=F(i[1].readyToReview);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=Pt(i,d,a),g=l(c);o.set(g,r[a]=Ft(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].readyToReview),M(),r=Q(r,c,l,1,a,d,o,e,Y,Ft,null,Pt),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Ft(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function Ct(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Running agents"}});let d=F(i[1].running);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=Bt(i,d,a),g=l(c);o.set(g,r[a]=Ht(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].running),M(),r=Q(r,c,l,1,a,d,o,e,Y,Ht,null,Bt),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Ht(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function Tt(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Idle agents"}});let d=F(i[1].idle);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=It(i,d,a),g=l(c);o.set(g,r[a]=Dt(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].idle),M(),r=Q(r,c,l,1,a,d,o,e,Y,Dt,null,It),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Dt(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function Nt(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Failed agents"}});let d=F(i[1].failed);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=bt(i,d,a),g=l(c);o.set(g,r[a]=zt(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].failed),M(),r=Q(r,c,l,1,a,d,o,e,Y,zt,null,bt),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function zt(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function Wt(i){let t,s,e,n,r=[],o=new Map;t=new st({props:{title:"Other agents"}});let d=F(i[1].additional);const l=a=>a[6].remote_agent_id+a[8];for(let a=0;a<d.length;a+=1){let c=kt(i,d,a),g=l(c);o.set(g,r[a]=Lt(g,c))}return{c(){v(t.$$.fragment),s=B(),e=I("div");for(let a=0;a<r.length;a+=1)r[a].c();k(e,"class","agent-grid svelte-5a9boh")},m(a,c){y(t,a,c),$(a,s,c),$(a,e,c);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);n=!0},p(a,c){11&c&&(d=F(a[1].additional),M(),r=Q(r,c,l,1,a,d,o,e,Y,Lt,null,kt),O())},i(a){if(!n){m(t.$$.fragment,a);for(let c=0;c<d.length;c+=1)m(r[c]);n=!0}},o(a){u(t.$$.fragment,a);for(let c=0;c<r.length;c+=1)u(r[c]);n=!1},d(a){a&&(p(s),p(e)),_(t,a);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Lt(i,t){var r;let s,e,n;return e=new et({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),v(e.$$.fragment),this.first=s},m(o,d){$(o,s,d),y(e,o,d),n=!0},p(o,d){var a;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId)),e.$set(l)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),_(e,o)}}}function is(i){let t;return{c(){t=C("No agents available")},m(s,e){$(s,t,e)},d(s){s&&p(t)}}}function ls(i){let t,s,e,n;const r=[os,as],o=[];function d(l,a){var c;return((c=l[0].state)==null?void 0:c.agentOverviews.length)===0?0:1}return s=d(i),e=o[s]=r[s](i),{c(){t=I("div"),e.c(),k(t,"class","agent-list svelte-5a9boh")},m(l,a){$(l,t,a),o[s].m(t,null),n=!0},p(l,[a]){let c=s;s=d(l),s===c?o[s].p(l,a):(M(),u(o[c],1,1,()=>{o[c]=null}),O(),e=o[s],e?e.p(l,a):(e=o[s]=r[s](l),e.c()),m(e,1),e.m(t,null))},i(l){n||(m(e),n=!0)},o(l){u(e),n=!1},d(l){l&&p(t),o[s].d()}}}function cs(i,t,s){let e,n,r,o;const d=mt(ut);return Gt(i,d,l=>s(0,o=l)),i.$$.update=()=>{var l,a;1&i.$$.dirty&&s(5,e=ae(((l=o.state)==null?void 0:l.agentOverviews)||[])),1&i.$$.dirty&&s(4,n=((a=o.state)==null?void 0:a.pinnedAgents)||{}),48&i.$$.dirty&&s(1,r=e.reduce((c,g)=>((n==null?void 0:n[g.remote_agent_id])===!0?c.pinned.push(g):g.status===N.agentIdle&&g.has_updates?c.readyToReview.push(g):g.status===N.agentRunning||g.status===N.agentStarting||g.workspace_status===dt.workspaceResuming?c.running.push(g):g.status===N.agentFailed?c.failed.push(g):g.status===N.agentIdle||g.workspace_status===dt.workspacePaused||g.workspace_status===dt.workspacePausing?c.idle.push(g):c.additional.push(g),c),{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))},[o,r,d,function(l){d.update(a=>{if(a)return{...a,selectedAgentId:l}})},n,e]}class ds extends j{constructor(t){super(),X(this,t,cs,ls,K,{})}}function gs(i){let t,s,e,n,r,o,d,l,a,c;return n=new we({}),d=new ds({}),{c(){t=I("div"),s=I("h1"),e=I("span"),v(n.$$.fragment),r=C(`
    Remote Agents`),o=B(),v(d.$$.fragment),k(e,"class","l-main__title-logo svelte-1941nw6"),k(s,"class","l-main__title svelte-1941nw6"),k(t,"class","l-main svelte-1941nw6")},m(g,w){$(g,t,w),E(t,s),E(s,e),y(n,e,null),E(s,r),E(t,o),y(d,t,null),l=!0,a||(c=Vt(window,"message",i[0].onMessageFromExtension),a=!0)},p:tt,i(g){l||(m(n.$$.fragment,g),m(d.$$.fragment,g),l=!0)},o(g){u(n.$$.fragment,g),u(d.$$.fragment,g),l=!1},d(g){g&&p(t),_(n),_(d),a=!1,c()}}}function ms(i){const t=new ie(oe),s=new ve(t,void 0,ge,me);t.registerConsumer(s),vt(ut,s);const e=new it(t);return vt(it.key,e),re(()=>(s.fetchStateFromExtension().then(()=>{s.update(n=>{if(!n)return;const r=[...n.activeWebviews,"home"];return n.pinnedAgents?{...n,activeWebviews:r}:{...n,activeWebviews:r,pinnedAgents:{}}})}),()=>{t.dispose(),e.dispose()})),[t]}new class extends j{constructor(i){super(),X(this,i,ms,gs,K,{})}}({target:document.getElementById("app")});
