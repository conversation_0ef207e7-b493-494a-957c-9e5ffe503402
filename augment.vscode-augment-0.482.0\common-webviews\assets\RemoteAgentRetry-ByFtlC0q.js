var ut=Object.defineProperty;var dt=(o,e,t)=>e in o?ut(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var te=(o,e,t)=>dt(o,typeof e!="symbol"?e+"":e,t);import{S as Gn,T as De,R as qe}from"./check-BrrMO4vE.js";import{R as We,b as ge,a as Te}from"./types-DDm27S8B.js";import{g as Ke,a as $t,h as mt,b as pt,c as gt,d as ft,e as ht,f as vt,j as Ve,k as Ye,m as Zn,A as Le,E as oe,o as Xe,S as xt,p as Ae}from"./lodash-Drc0SN5U.js";import{S as Z,i as j,s as W,J as Q,e as v,u as $,q as U,t as p,r as V,h,ag as ie,ah as le,a as Ie,R as Qe,V as E,C as y,I as B,L as z,c as x,W as en,X as D,D as C,f as R,a8 as ee,g as Je,a4 as wt,M as K,F as _,aa as Be,_ as he,ac as nn,$ as ve,a0 as xe,a1 as we,ax as yt,n as N,G as fe,al as jn,ab as Ct,A as re,a5 as Wn,B as Pe,E as Ne,aE as ye,a2 as tn,a9 as Kn,ad as pe,a6 as Ce,am as _e,T as ne,b as me,ap as _t,K as Yn,aA as sn,H as kt,w as bt,x as It,y as St,d as on,z as Tt,j as rn,ae as cn,af as Re}from"./SpinnerAugment-Cx9dt_ox.js";import{M as Rt}from"./MaterialIcon-8-Z76Y2_.js";import{o as Xn}from"./keypress-DD1aQVr0.js";import{A as an,a as Y}from"./autofix-state-d-ymFdyn.js";import{bb as Et,bc as je,bd as Mt,aq as Lt,be as At,bf as Qn,bg as zt,bh as Ft,bi as qt,bj as Ue,bk as Bt}from"./AugmentMessage-kCRDis1x.js";import{t as Pt}from"./index-BxQII05L.js";import{T as He}from"./Content-BiWRcmeV.js";import{E as Nt,a as et,b as Ht}from"./folder-CEjIF7oG.js";import{e as Oe,f as Ot,g as Dt,E as Ut,h as Vt,T as nt}from"./Keybindings-C3J8hU1V.js";import{c as tt,I as Jt,R as Gt,D as Zt,C as jt,d as st,e as Wt,f as ze,g as Kt}from"./main-panel-CD6EXLpt.js";import{B as ot}from"./ButtonAugment-DhtPLzGu.js";import{P as Yt,C as Xt}from"./folder-opened-CX_GXeEO.js";import{E as Qt}from"./expand-CURYX9ur.js";import{P as es}from"./pen-to-square-CZwCjcp0.js";import{T as Se}from"./TextTooltipAugment-DTMpOwfF.js";import{I as ke}from"./IconButtonAugment-BjDqXmYl.js";import{C as ns,a as ts,M as ss}from"./diff-utils-C7XQLqYW.js";import{B as rt}from"./arrow-up-right-from-square-D2UwhhNo.js";import{e as Fe}from"./BaseButton-BqzdgpkK.js";import{C as os}from"./CardAugment-RumqAz-v.js";import{C as it,a as ct}from"./CollapseButtonAugment-D3vAw6HE.js";import{M as rs,S as is}from"./index-8X-F_Twk.js";import{O as cs}from"./open-in-new-window-C_TwPNdv.js";import{C as as}from"./github-7gPAsyj4.js";import{a as Ee}from"./index-DUiNNixO.js";import{s as ls}from"./utils-DJhaageo.js";const us=(o,e,t,n)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showResumingRemoteAgent:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1,remoteAgentErrorConfig:void 0,showPaused:!1};if(e===Le.running){const r=o==null?void 0:o.lastExchange;if(r!=null&&r.isRetriable&&(r!=null&&r.display_error_message))s.retryMessage=r.display_error_message;else if(t||n.isActive){const i=n.isActive?n.getLastToolUseState():o.getLastToolUseState();if(n.isActive){const c=n.currentAgent;(c==null?void 0:c.workspace_status)===We.workspaceResuming?s.showResumingRemoteAgent=!0:i.phase!==De.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else i.phase!==De.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else s.showGeneratingResponse=!0}else e===Le.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((r,i)=>{var d;const c=(d=r==null?void 0:r.lastExchange)==null?void 0:d.status,a=c===oe.cancelled,l=r==null?void 0:r.getLastToolUseState().phase,u=l===De.cancelled;return!i.isActive&&(a||u)})(o,n)&&(s.showStopped=!0);if(n.isActive){const r=n.currentAgent;(r==null?void 0:r.workspace_status)===We.workspacePaused&&(s.showPaused=!0)}return s},_i=(o,e,t,n)=>{const s=o.currentConversationModel,r=((d,m)=>m.isActive?m.getCurrentChatHistory():d.chatHistory.filter(g=>Ke(g)||$t(g)||mt(g)||pt(g)||gt(g)||ft(g)||ht(g)||vt(g)||Ve(g)||Ye(g)))(s,n),i=(d=>d.reduce((m,g,k)=>(Ke(g)&&Zn(g)&&m.length>0||Ye(g)&&m.length>0?m[m.length-1].push({turn:g,idx:k}):m.push([{turn:g,idx:k}]),m),[]))(r),c=((d,m)=>m.isActive?m.isCurrentAgentRunning?Le.running:Le.notRunning:d)(e,n),a=us(s,c,t,n),l=!n.isActive,u=!!n.isActive;if(n.isActive){if(n.sendMessageError&&n.currentAgentId){const d=n.currentAgentId,m=n.sendMessageError;a.remoteAgentErrorConfig={error:m,onRetry:m.canRetry&&m.failedExchangeId?()=>n.retryFailedMessage(d,m.failedExchangeId):void 0,onDelete:m.type===Gn.agentFailed?()=>n.deleteAgent(d):void 0}}else if(n.agentChatHistoryError&&n.currentAgentId){const d=n.currentAgentId;a.remoteAgentErrorConfig={error:n.agentChatHistoryError,onRetry:()=>n.refreshAgentChatHistory(d)}}}return{chatHistory:r,groupedChatHistory:i,lastGroupConfig:a,doShowFloatingButtons:l,doShowAgentSetupLogs:u}};function ln(o){let e,t,n,s,r,i,c,a;const l=[o[4][o[1]]];let u={};for(let g=0;g<l.length;g+=1)u=Ie(u,l[g]);t=new Rt({props:u});let d=[{class:"stage-container"},o[1]?Qe(o[3][o[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<d.length;g+=1)m=Ie(m,d[g]);return{c(){e=E("div"),y(t.$$.fragment),n=B(),s=E("div"),r=z(o[1]),x(s,"class","message svelte-1etsput"),en(e,m),D(e,"active",o[0]),D(e,"svelte-1etsput",!0)},m(g,k){v(g,e,k),C(t,e,null),R(e,n),R(e,s),R(s,r),i=!0,c||(a=[ee(e,"click",o[5]),ee(e,"keydown",Xn("Enter",o[5]))],c=!0)},p(g,k){const L=18&k?Je(l,[wt(g[4][g[1]])]):{};t.$set(L),(!i||2&k)&&K(r,g[1]),en(e,m=Je(d,[{class:"stage-container"},2&k&&(g[1]?Qe(g[3][g[1]]):{}),{role:"button"},{tabindex:"0"}])),D(e,"active",g[0]),D(e,"svelte-1etsput",!0)},i(g){i||($(t.$$.fragment,g),i=!0)},o(g){p(t.$$.fragment,g),i=!1},d(g){g&&h(e),_(t),c=!1,Be(a)}}}function ds(o){let e,t,n=o[1]&&ln(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){s[1]?n?(n.p(s,r),2&r&&$(n,1)):(n=ln(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function $s(o,e,t){let n,s,r,i,{stage:c}=e,{iterationId:a}=e,{stageCount:l}=e;const u=ie("autofixConversationModel");le(o,u,g=>t(10,i=g));const d={[Y.retesting]:"info",[Y.testRunning]:"info",[Y.testFailed]:"error",[Y.testPassed]:"success",[Y.generatingSolutions]:"info",[Y.suggestedSolutions]:"warning",[Y.selectedSolutions]:"success"},m={[Y.retesting]:{iconName:"cached",color:"#FFFFFF"},[Y.testRunning]:{iconName:"cached",color:"#FFFFFF"},[Y.testFailed]:{iconName:"error",color:"#DB3B4B"},[Y.testPassed]:{iconName:"check_circle",color:"#388A34"},[Y.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[Y.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[Y.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return o.$$set=g=>{"stage"in g&&t(6,c=g.stage),"iterationId"in g&&t(7,a=g.iterationId),"stageCount"in g&&t(8,l=g.stageCount)},o.$$.update=()=>{var g,k,L;1152&o.$$.dirty&&t(9,n=i==null?void 0:i.getAutofixIteration(a)),1600&o.$$.dirty&&t(0,s=n&&((L=(k=(g=i.extraData)==null?void 0:g.autofixIterations)==null?void 0:k.at(-1))==null?void 0:L.id)===n.id&&n.currentStage===c),833&o.$$.dirty&&t(1,r=function(f,I,A,P){var M;return f?I===an.runTest?f.commandFailed===void 0&&P?f.isFirstIteration?Y.testRunning:Y.retesting:f.commandFailed===!0?Y.testFailed:Y.testPassed:I===an.applyFix?A===(((M=f.suggestedSolutions)==null?void 0:M.length)||0)?f.selectedSolutions?Y.selectedSolutions:Y.generatingSolutions:Y.suggestedSolutions:null:null}(n,c,l,s))},[s,r,u,d,m,()=>{r!==Y.generatingSolutions&&u.launchAutofixPanel(a,c)},c,a,l,n,i]}class ki extends Z{constructor(e){super(),j(this,e,$s,ds,W,{stage:6,iterationId:7,stageCount:8})}}function bi(o,e){const t=Math.abs(o);let n=200,s=500;typeof e=="number"?n=e:e&&(n=e.baseThreshold??200,s=e.predictTime??500);const r=t*s/1e3;return Math.max(n,r)}function Ii(o,e=10){const t=Math.abs(o);return t>1e3?2*e:t>500?1.5*e:t>200?e:.5*e}function un(o){const{scrollTop:e,clientHeight:t,scrollHeight:n}=o;return n-e-t}function Si(o,e={}){let t=e,n={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var f,I,A;const{scrollTop:r,scrollHeight:i,offsetHeight:c}=o,a=un(o),l=r>n.scrollTop+1,u=i-n.scrollHeight,d=!(u<0&&n.scrollBottom<-u)&&r<n.scrollTop-1&&a>n.scrollBottom+1,m=i>c,g=((P,M=40)=>un(P)<=M)(o),k=g&&m&&l,L=d||!m;k&&!n.scrolledIntoBottom?(f=t.onScrollIntoBottom)==null||f.call(t):L&&!n.scrolledAwayFromBottom&&((I=t.onScrollAwayFromBottom)==null||I.call(t)),n={scrollTop:r,scrollBottom:a,scrolledIntoBottom:k,scrolledAwayFromBottom:L,scrollHeight:i},(A=t.onScroll)==null||A.call(t,r)};return o.addEventListener("scroll",s),{update(r){t=r},destroy(){o.removeEventListener("scroll",s)}}}function ms(o){let e,t,n;const s=o[4].default,r=he(s,o,o[3],null);return{c(){e=E("div"),r&&r.c(),x(e,"class",t="c-gradient-mask "+o[2]+" svelte-say8yn"),nn(e,"--fade-size",o[1]+"px"),D(e,"is-horizontal",o[0]==="horizontal")},m(i,c){v(i,e,c),r&&r.m(e,null),n=!0},p(i,[c]){r&&r.p&&(!n||8&c)&&ve(r,s,i,i[3],n?we(s,i[3],c,null):xe(i[3]),null),(!n||4&c&&t!==(t="c-gradient-mask "+i[2]+" svelte-say8yn"))&&x(e,"class",t),(!n||2&c)&&nn(e,"--fade-size",i[1]+"px"),(!n||5&c)&&D(e,"is-horizontal",i[0]==="horizontal")},i(i){n||($(r,i),n=!0)},o(i){p(r,i),n=!1},d(i){i&&h(e),r&&r.d(i)}}}function ps(o,e,t){let{$$slots:n={},$$scope:s}=e,{direction:r="vertical"}=e,{fadeSize:i=Et}=e,{class:c=""}=e;return o.$$set=a=>{"direction"in a&&t(0,r=a.direction),"fadeSize"in a&&t(1,i=a.fadeSize),"class"in a&&t(2,c=a.class),"$$scope"in a&&t(3,s=a.$$scope)},[r,i,c,s,n]}class Ti extends Z{constructor(e){super(),j(this,e,ps,ms,W,{direction:0,fadeSize:1,class:2})}}function Ri(o,e){var r;let t=o.offsetHeight,n=e;const s=new ResizeObserver(i=>{var a;const c=i[0].contentRect.height;i.length===1?c!==t&&((a=n.onHeightChange)==null||a.call(n,c),t=c):console.warn("Unexpected number of resize entries: ",i)});return s.observe(o),(r=n==null?void 0:n.onHeightChange)==null||r.call(n,t),{update(i){n=i},destroy:()=>s.disconnect()}}function Ei(o,e){let t=e;const n=Pt(()=>{t.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(i=>{i.length===1?i[0].isIntersecting&&n():console.warn("Unexpected number of intersection entries: ",i)},{threshold:.5}),r=()=>{s.disconnect(),t.track&&s.observe(o)};return r(),{update(i){const c=t;t=i,t.track!==c.track&&r()},destroy:()=>{s.disconnect(),t.onSeen()}}}function dn(o){let e,t,n,s=o[6]&&$n();return t=new Gt({props:{changeImageMode:o[32],saveImage:o[9].saveImage,deleteImage:o[9].deleteImage,renderImage:o[9].renderImage,isEditable:o[33]}}),{c(){s&&s.c(),e=B(),y(t.$$.fragment)},m(r,i){s&&s.m(r,i),v(r,e,i),C(t,r,i),n=!0},p(r,i){r[6]?s?64&i[0]&&$(s,1):(s=$n(),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V());const c={};258&i[0]&&(c.changeImageMode=r[32]),512&i[0]&&(c.saveImage=r[9].saveImage),512&i[0]&&(c.deleteImage=r[9].deleteImage),512&i[0]&&(c.renderImage=r[9].renderImage),64&i[0]&&(c.isEditable=r[33]),t.$set(c)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),_(t,r)}}}function $n(o){let e,t;return e=new Zt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function gs(o){var L;let e,t,n,s,r,i,c,a,l,u,d=o[9].flags.enableChatMultimodal&&dn(o);t=new Ot({props:{shortcuts:o[13]}});let m={requestEditorFocus:o[22],onMentionItemsUpdated:o[21]};s=new tt({props:m}),o[34](s),i=new Dt({props:{placeholder:o[2]}}),a=new Oe.Content({props:{content:((L=o[7])==null?void 0:L.richTextJsonRepr)??o[3],onContentChanged:o[20]}});const g=o[29].default,k=he(g,o,o[37],null);return{c(){d&&d.c(),e=B(),y(t.$$.fragment),n=B(),y(s.$$.fragment),r=B(),y(i.$$.fragment),c=B(),y(a.$$.fragment),l=B(),k&&k.c()},m(f,I){d&&d.m(f,I),v(f,e,I),C(t,f,I),v(f,n,I),C(s,f,I),v(f,r,I),C(i,f,I),v(f,c,I),C(a,f,I),v(f,l,I),k&&k.m(f,I),u=!0},p(f,I){var J;f[9].flags.enableChatMultimodal?d?(d.p(f,I),512&I[0]&&$(d,1)):(d=dn(f),d.c(),$(d,1),d.m(e.parentNode,e)):d&&(U(),p(d,1,1,()=>{d=null}),V());const A={};8192&I[0]&&(A.shortcuts=f[13]),t.$set(A),s.$set({});const P={};4&I[0]&&(P.placeholder=f[2]),i.$set(P);const M={};136&I[0]&&(M.content=((J=f[7])==null?void 0:J.richTextJsonRepr)??f[3]),a.$set(M),k&&k.p&&(!u||64&I[1])&&ve(k,g,f,f[37],u?we(g,f[37],I,null):xe(f[37]),null)},i(f){u||($(d),$(t.$$.fragment,f),$(s.$$.fragment,f),$(i.$$.fragment,f),$(a.$$.fragment,f),$(k,f),u=!0)},o(f){p(d),p(t.$$.fragment,f),p(s.$$.fragment,f),p(i.$$.fragment,f),p(a.$$.fragment,f),p(k,f),u=!1},d(f){f&&(h(e),h(n),h(r),h(c),h(l)),d&&d.d(f),_(t,f),o[34](null),_(s,f),_(i,f),_(a,f),k&&k.d(f)}}}function mn(o){let e,t;return e=new jt({props:{chatModel:o[16]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function fs(o){let e,t,n=o[6]&&mn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]?n?(n.p(s,r),64&r[0]&&$(n,1)):(n=mn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function pn(o){let e,t,n,s=o[6]&&gn(o),r=o[4]&&fn(o);return{c(){s&&s.c(),e=Q(),r&&r.c(),t=Q()},m(i,c){s&&s.m(i,c),v(i,e,c),r&&r.m(i,c),v(i,t,c),n=!0},p(i,c){i[6]?s?(s.p(i,c),64&c[0]&&$(s,1)):(s=gn(i),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V()),i[4]?r?(r.p(i,c),16&c[0]&&$(r,1)):(r=fn(i),r.c(),$(r,1),r.m(t.parentNode,t)):r&&(U(),p(r,1,1,()=>{r=null}),V())},i(i){n||($(s),$(r),n=!0)},o(i){p(s),p(r),n=!1},d(i){i&&(h(e),h(t)),s&&s.d(i),r&&r.d(i)}}}function gn(o){let e,t,n;return e=new st.Root({props:{$$slots:{rightAlign:[xs],leftAlign:[hs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment),t=B()},m(s,r){C(e,s,r),v(s,t,r),n=!0},p(s,r){const i={};17408&r[0]|64&r[1]&&(i.$$scope={dirty:r,ctx:s}),e.$set(i)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),_(e,s)}}}function hs(o){var n;let e,t;return e=new st.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:o[22],onInsertMentionable:(n=o[10])==null?void 0:n.insertMentionNode}}),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),t=!0},p(s,r){var c;const i={};1024&r[0]&&(i.onInsertMentionable=(c=s[10])==null?void 0:c.insertMentionNode),e.$set(i)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){_(e,s)}}}function vs(o){let e,t;return e=new Yt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function xs(o){let e,t;return e=new ot({props:{size:1,variant:"solid",disabled:!o[14],$$slots:{default:[vs]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16384&s[0]&&(r.disabled=!n[14]),64&s[1]&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function fn(o){let e,t,n;return t=new ot({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[ys],default:[ws]},$$scope:{ctx:o}}}),t.$on("click",o[31]),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-user-msg__collapse-button svelte-q0brxu")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const i={};64&r[1]&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function ws(o){let e;return{c(){e=E("span"),e.textContent="Expand"},m(t,n){v(t,e,n)},p:N,d(t){t&&h(e)}}}function ys(o){let e,t;return e=new Qt({props:{slot:"iconLeft"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Cs(o){let e,t,n=(o[6]||!o[4])&&pn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]||!s[4]?n?(n.p(s,r),80&r[0]&&$(n,1)):(n=pn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function _s(o){let e,t,n,s,r,i={editable:o[6],$$slots:{footer:[Cs],header:[fs],default:[gs]},$$scope:{ctx:o}};return t=new Oe.Root({props:i}),o[35](t),t.$on("click",ks),t.$on("dblclick",o[17]),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-chat-input svelte-q0brxu"),x(e,"role","button"),x(e,"tabindex","-1"),D(e,"is-collapsed",o[4]),D(e,"is-editing",o[6])},m(c,a){v(c,e,a),C(t,e,null),o[36](e),n=!0,s||(r=[ee(window,"mousedown",o[19]),ee(e,"mousedown",yt(o[30]))],s=!0)},p(c,a){const l={};64&a[0]&&(l.editable=c[6]),26623&a[0]|64&a[1]&&(l.$$scope={dirty:a,ctx:c}),t.$set(l),(!n||16&a[0])&&D(e,"is-collapsed",c[4]),(!n||64&a[0])&&D(e,"is-editing",c[6])},i(c){n||($(t.$$.fragment,c),n=!0)},o(c){p(t.$$.fragment,c),n=!1},d(c){c&&h(e),o[35](null),_(t),o[36](null),s=!1,Be(r)}}}const ks=o=>o.stopPropagation();function bs(o,e,t){let n,s,r,i,c,a,l,u=N;o.$$.on_destroy.push(()=>u());let{$$slots:d={},$$scope:m}=e;const g=ie("chatModel");le(o,g,b=>t(9,l=b));const k=ie(qe.key);let{requestId:L}=e,{placeholder:f="Edit your message..."}=e,{content:I}=e,{collapsed:A=!1}=e,{onSubmitEdit:P}=e,{onCancelEdit:M}=e,{setIsCollapsed:J}=e,{userExpanded:T}=e,S,H,F,w=!1,X=[];async function q(){r&&(t(0,T=!0),A&&J(!1),t(6,w=!0),await jn(),$e())}function G(){return!(!i||!S)&&(P(S,X),!0)}function O(){return t(0,T=!1),t(6,w=!1),t(7,S=void 0),M(),!0}const $e=()=>F==null?void 0:F.forceFocus();let se;return o.$$set=b=>{"requestId"in b&&t(1,L=b.requestId),"placeholder"in b&&t(2,f=b.placeholder),"content"in b&&t(3,I=b.content),"collapsed"in b&&t(4,A=b.collapsed),"onSubmitEdit"in b&&t(23,P=b.onSubmitEdit),"onCancelEdit"in b&&t(24,M=b.onCancelEdit),"setIsCollapsed"in b&&t(5,J=b.setIsCollapsed),"userExpanded"in b&&t(0,T=b.userExpanded),"$$scope"in b&&t(37,m=b.$$scope)},o.$$.update=()=>{512&o.$$.dirty[0]&&(t(15,n=l.currentConversationModel),u(),u=fe(n,b=>t(8,a=b))),268435968&o.$$.dirty[0]&&t(27,r=l.flags.enableEditableHistory&&!s),134218184&o.$$.dirty[0]&&t(14,i=w&&r&&S!==void 0&&S.rawText.trim()!==""&&S.rawText!==I&&S.richTextJsonRepr!==I&&!a.awaitingReply&&!Jt.hasLoadingImages(S.richTextJsonRepr))},t(28,s=!!(k!=null&&k.isActive)),t(13,c={Enter:G,Escape:O}),[T,L,f,I,A,J,w,S,a,l,H,F,se,c,i,n,g,q,G,O,function(b){b!==S&&t(7,S=b)},function(b){X=b.current},()=>F==null?void 0:F.requestFocus(),P,M,()=>{q()},function(){return se},r,s,d,function(b){Ct.call(this,o,b)},()=>{t(0,T=!0),J(!1)},b=>{L&&b&&a.updateChatItem(L,{rich_text_json_repr:b})},()=>w,function(b){re[b?"unshift":"push"](()=>{H=b,t(10,H)})},function(b){re[b?"unshift":"push"](()=>{F=b,t(11,F)})},function(b){re[b?"unshift":"push"](()=>{se=b,t(12,se)})},m]}class Is extends Z{constructor(e){super(),j(this,e,bs,_s,W,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ae=class ae{constructor(e){te(this,"_tipTapExtension");te(this,"_resizeObserver");te(this,"_checkHeight",e=>{var n,s;const t=e.getBoundingClientRect().height;(s=(n=this._options).onResize)==null||s.call(n,t)});te(this,"_setResizeObserver",e=>{var n;const t=(n=e.view)==null?void 0:n.dom;t&&(this._resizeObserver=new ResizeObserver(s=>{for(const r of s)this._checkHeight(r.target)}),this._resizeObserver.observe(t),this._checkHeight(t))});te(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});te(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const t=ae._getNextPluginId(),n=this._setResizeObserver,s=this._clearResizeObserver,r=this._checkHeight;this._tipTapExtension=Ut.create({name:t,onCreate:function(){var c;((c=this.editor.view)==null?void 0:c.dom)&&(n(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var c;const i=(c=this.editor.view)==null?void 0:c.dom;i&&r(i)},onDestroy:()=>{var i;(i=this._resizeObserver)==null||i.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};te(ae,"_sequenceId",0),te(ae,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),te(ae,"_getSequenceId",()=>ae._sequenceId++),te(ae,"_getNextPluginId",()=>{const e=ae._getSequenceId().toString();return ae.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ge=ae;function Ss(o,e,t){let{height:n=0}=e;const s=a=>{t(0,n=a)},r=ie(Vt.CONTEXT_KEY),i=new Ge({onResize:s}),c=r.pluginManager.registerPlugin(i);return Wn(c),o.$$set=a=>{"height"in a&&t(0,n=a.height)},i.updateOptions({onResize:s}),[n]}let Ts=class extends Z{constructor(o){super(),j(this,o,Ss,null,W,{height:0})}};function Rs(o){let e,t,n;function s(i){o[21](i)}let r={};return o[6]!==void 0&&(r.height=o[6]),e=new Ts({props:r}),re.push(()=>Pe(e,"height",s)),{c(){y(e.$$.fragment)},m(i,c){C(e,i,c),n=!0},p(i,c){const a={};!t&&64&c&&(t=!0,a.height=i[6],Ne(()=>t=!1)),e.$set(a)},i(i){n||($(e.$$.fragment,i),n=!0)},o(i){p(e.$$.fragment,i),n=!1},d(i){_(e,i)}}}function Es(o){let e,t,n;function s(i){o[23](i)}let r={collapsed:o[7],content:o[3]??o[1],requestId:o[2],onSubmitEdit:o[13],onCancelEdit:o[5],setIsCollapsed:o[11],$$slots:{default:[Rs]},$$scope:{ctx:o}};return o[8]!==void 0&&(r.userExpanded=o[8]),e=new Is({props:r}),o[22](e),re.push(()=>Pe(e,"userExpanded",s)),{c(){y(e.$$.fragment)},m(i,c){C(e,i,c),n=!0},p(i,c){const a={};128&c&&(a.collapsed=i[7]),10&c&&(a.content=i[3]??i[1]),4&c&&(a.requestId=i[2]),32&c&&(a.onCancelEdit=i[5]),134217792&c&&(a.$$scope={dirty:c,ctx:i}),!t&&256&c&&(t=!0,a.userExpanded=i[8],Ne(()=>t=!1)),e.$set(a)},i(i){n||($(e.$$.fragment,i),n=!0)},o(i){p(e.$$.fragment,i),n=!1},d(i){o[22](null),_(e,i)}}}function Ms(o){let e,t,n;return t=new Mt({props:{items:o[10]}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"slot","edit"),x(e,"class","c-user-msg__actions svelte-1dv083n")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const i={};1024&r&&(i.items=s[10]),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Ls(o){let e,t,n,s;return e=new je({props:{timestamp:o[4],$$slots:{edit:[Ms],content:[Es]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(r,i){C(e,r,i),t=!0,n||(s=[ee(window,"keydown",Xn("Escape",o[12])),ee(window,"mousedown",o[12])],n=!0)},p(r,[i]){const c={};16&i&&(c.timestamp=r[4]),134219758&i&&(c.$$scope={dirty:i,ctx:r}),e.$set(c)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){_(e,r),n=!1,Be(s)}}}function As(o,e,t){let n,s,r,i,c,a,l,u=N,d=()=>(u(),u=fe(m,w=>t(20,l=w)),m);o.$$.on_destroy.push(()=>u());let{chatModel:m}=e;d();let{msg:g}=e,{requestId:k}=e,{richTextJsonRepr:L}=e,{timestamp:f}=e,{onStartEdit:I=()=>{}}=e,{onAcceptEdit:A=()=>{}}=e,{onCancelEdit:P=()=>{}}=e;const M=ie(qe.key);let J=!1,T=!1;async function S(w){await jn(),t(7,J=w&&r&&!T)}const H=w=>{c&&k&&(S(!1),F==null||F.requestStartEdit(),I(),w.stopPropagation())};let F;return o.$$set=w=>{"chatModel"in w&&d(t(0,m=w.chatModel)),"msg"in w&&t(1,g=w.msg),"requestId"in w&&t(2,k=w.requestId),"richTextJsonRepr"in w&&t(3,L=w.richTextJsonRepr),"timestamp"in w&&t(4,f=w.timestamp),"onStartEdit"in w&&t(14,I=w.onStartEdit),"onAcceptEdit"in w&&t(15,A=w.onAcceptEdit),"onCancelEdit"in w&&t(5,P=w.onCancelEdit)},o.$$.update=()=>{var w,X;1048580&o.$$.dirty&&t(19,s=k===void 0||k===((X=(w=l==null?void 0:l.currentConversationModel)==null?void 0:w.lastExchange)==null?void 0:X.request_id)),524288&o.$$.dirty&&t(16,r=!s&&!0),1310724&o.$$.dirty&&t(17,c=k!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!n),131072&o.$$.dirty&&t(10,a=[...c?[{label:"Edit message",action:H,id:"edit-message",disabled:!1,icon:es}]:[]]),65600&o.$$.dirty&&i&&r&&(F!=null&&F.getEditorContainer())&&i&&r&&S(!(J&&i<120)&&i>120)},t(18,n=!!(M!=null&&M.isActive)),t(6,i=0),[m,g,k,L,f,P,i,J,T,F,a,S,()=>{},function(w,X){if(!k)return;m.currentConversationModel.clearHistoryFrom(k);const q=l.flags.enableChatMultimodal&&w.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(w.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:w.rawText,rich_text_json_repr:w.richTextJsonRepr,status:oe.draft,mentioned_items:X,structured_request_nodes:q}),A()},I,A,r,c,n,s,l,function(w){i=w,t(6,i)},function(w){re[w?"unshift":"push"](()=>{F=w,t(9,F)})},function(w){T=w,t(8,T)}]}class zs extends Z{constructor(e){super(),j(this,e,As,Ls,W,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function hn(o){let e,t;return e=new zs({props:{msg:o[1].request_message??"",richTextJsonRepr:o[11],chatModel:o[0],requestId:o[9],timestamp:o[1].timestamp}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};2&s&&(r.msg=n[1].request_message??""),2048&s&&(r.richTextJsonRepr=n[11]),1&s&&(r.chatModel=n[0]),512&s&&(r.requestId=n[9]),2&s&&(r.timestamp=n[1].timestamp),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function vn(o){let e,t,n;function s(a,l){return a[1].display_error_message?Bs:a[1].response_text&&a[1].response_text.length>0?qs:Fs}let r=s(o),i=r(o),c=o[9]&&xn(o);return{c(){e=E("div"),i.c(),t=B(),c&&c.c(),x(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(a,l){v(a,e,l),i.m(e,null),R(e,t),c&&c.m(e,null),n=!0},p(a,l){r===(r=s(a))&&i?i.p(a,l):(i.d(1),i=r(a),i&&(i.c(),i.m(e,t))),a[9]?c?(c.p(a,l),512&l&&$(c,1)):(c=xn(a),c.c(),$(c,1),c.m(e,null)):c&&(U(),p(c,1,1,()=>{c=null}),V())},i(a){n||($(c),n=!0)},o(a){p(c),n=!1},d(a){a&&h(e),i.d(),c&&c.d()}}}function Fs(o){let e,t,n,s;return{c(){e=z(`We encountered an issue sending your message. Please
        `),t=E("button"),t.textContent="try again",x(t,"class","svelte-1d1manc")},m(r,i){v(r,e,i),v(r,t,i),n||(s=ee(t,"click",ye(o[15])),n=!0)},p:N,d(r){r&&(h(e),h(t)),n=!1,s()}}}function qs(o){let e,t,n,s,r;return{c(){e=z(`Connection lost. Please
        `),t=E("button"),t.textContent="try again",n=z(`
        to restart the conversation!`),x(t,"class","svelte-1d1manc")},m(i,c){v(i,e,c),v(i,t,c),v(i,n,c),s||(r=ee(t,"click",ye(o[15])),s=!0)},p:N,d(i){i&&(h(e),h(t),h(n)),s=!1,r()}}}function Bs(o){let e,t=o[1].display_error_message+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){2&s&&t!==(t=n[1].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function xn(o){let e,t,n,s,r,i,c,a;function l(d){o[21](d)}let u={onOpenChange:o[16],content:o[7],triggerOn:[He.Hover],$$slots:{default:[Ns]},$$scope:{ctx:o}};return o[8]!==void 0&&(u.requestClose=o[8]),i=new Se({props:u}),re.push(()=>Pe(i,"requestClose",l)),{c(){e=E("div"),t=E("span"),n=z("Request ID: "),s=z(o[9]),r=B(),y(i.$$.fragment),x(e,"class","c-msg-list__request-id svelte-1d1manc")},m(d,m){v(d,e,m),R(e,t),R(t,n),R(t,s),R(e,r),C(i,e,null),a=!0},p(d,m){(!a||512&m)&&K(s,d[9]);const g={};128&m&&(g.content=d[7]),16777216&m&&(g.$$scope={dirty:m,ctx:d}),!c&&256&m&&(c=!0,g.requestClose=d[8],Ne(()=>c=!1)),i.$set(g)},i(d){a||($(i.$$.fragment,d),a=!0)},o(d){p(i.$$.fragment,d),a=!1},d(d){d&&h(e),_(i)}}}function Ps(o){let e,t;return e=new Xt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ns(o){let e,t;return e=new ke({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ps]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16777216&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Hs(o){let e,t,n,s,r,i=!o[10]&&!Ve(o[1])&&!o[12],c=i&&hn(o);n=new Lt({props:{chatModel:o[6],turn:o[1],turnIndex:o[3],requestId:o[9],isLastTurn:o[2],showName:!o[10],group:o[5],showFooter:!Xe(o[1])||o[1].status===oe.cancelled,markdown:o[1].response_text??"",timestamp:o[1].timestamp,messageListContainer:o[4]}});let a=o[1].status===oe.failed&&vn(o);return{c(){e=E("div"),c&&c.c(),t=B(),y(n.$$.fragment),s=B(),a&&a.c(),x(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){v(l,e,u),c&&c.m(e,null),R(e,t),C(n,e,null),R(e,s),a&&a.m(e,null),r=!0},p(l,[u]){5122&u&&(i=!l[10]&&!Ve(l[1])&&!l[12]),i?c?(c.p(l,u),5122&u&&$(c,1)):(c=hn(l),c.c(),$(c,1),c.m(e,t)):c&&(U(),p(c,1,1,()=>{c=null}),V());const d={};64&u&&(d.chatModel=l[6]),2&u&&(d.turn=l[1]),8&u&&(d.turnIndex=l[3]),512&u&&(d.requestId=l[9]),4&u&&(d.isLastTurn=l[2]),1024&u&&(d.showName=!l[10]),32&u&&(d.group=l[5]),2&u&&(d.showFooter=!Xe(l[1])||l[1].status===oe.cancelled),2&u&&(d.markdown=l[1].response_text??""),2&u&&(d.timestamp=l[1].timestamp),16&u&&(d.messageListContainer=l[4]),n.$set(d),l[1].status===oe.failed?a?(a.p(l,u),2&u&&$(a,1)):(a=vn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){r||($(c),$(n.$$.fragment,l),$(a),r=!0)},o(l){p(c),p(n.$$.fragment,l),p(a),r=!1},d(l){l&&h(e),c&&c.d(),_(n),a&&a.d()}}}function Os(o,e,t){let n,s,r,i,c,a,l,u,d,m,g=N,k=()=>(g(),g=fe(f,w=>t(6,u=w)),f),L=N;o.$$.on_destroy.push(()=>g()),o.$$.on_destroy.push(()=>L());let{chatModel:f}=e;k();let{turn:I}=e,{isLastTurn:A=!1}=e,{turnIndex:P=0}=e,{messageListContainer:M}=e,{group:J}=e;const T=ie(qe.key);le(o,T,w=>t(20,m=w));let S,H="Copy request ID",F=()=>{};return o.$$set=w=>{"chatModel"in w&&k(t(0,f=w.chatModel)),"turn"in w&&t(1,I=w.turn),"isLastTurn"in w&&t(2,A=w.isLastTurn),"turnIndex"in w&&t(3,P=w.turnIndex),"messageListContainer"in w&&t(4,M=w.messageListContainer),"group"in w&&t(5,J=w.group)},o.$$.update=()=>{var w;2&o.$$.dirty&&t(9,n=I.request_id),64&o.$$.dirty&&(t(13,s=u==null?void 0:u.currentConversationModel),L(),L=fe(s,X=>t(23,d=X))),1048576&o.$$.dirty&&t(19,r=(m==null?void 0:m.isActive)&&((w=m==null?void 0:m.currentAgent)==null?void 0:w.is_setup_script_agent)===!0),8&o.$$.dirty&&t(18,i=P===0),786432&o.$$.dirty&&t(12,c=r&&i),66&o.$$.dirty&&t(11,a=u.flags.enableRichTextHistory?I.rich_text_json_repr:void 0),2&o.$$.dirty&&t(10,l=Zn(I))},[f,I,A,P,M,J,u,H,F,n,l,a,c,s,T,()=>{d.resendTurn(I)},function(w){w||(clearTimeout(S),S=void 0,t(7,H="Copy request ID"))},async function(){n&&(await navigator.clipboard.writeText(n),t(7,H="Copied!"),clearTimeout(S),S=setTimeout(F,1500))},i,r,m,function(w){F=w,t(8,F)}]}class Mi extends Z{constructor(e){super(),j(this,e,Os,Hs,W,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4,group:5})}}function Ds(o){let e,t,n,s,r,i,c;const a=o[15].default,l=he(a,o,o[14],null);return{c(){e=E("div"),l&&l.c(),x(e,"class",t=tn(`c-msg-list__item ${o[5]}`)+" svelte-1s0uz2w"),x(e,"style",n=`min-height: calc(${o[4]}px - (var(--msg-list-item-spacing) * 2));`),x(e,"data-request-id",o[6])},m(u,d){v(u,e,d),l&&l.m(e,null),o[16](e),r=!0,i||(c=Kn(s=At.call(null,e,{follow:!o[2]&&o[1],scrollContainer:o[3],disableScrollUp:!0,smooth:!0,bottom:!0})),i=!0)},p(u,[d]){l&&l.p&&(!r||16384&d)&&ve(l,a,u,u[14],r?we(a,u[14],d,null):xe(u[14]),null),(!r||32&d&&t!==(t=tn(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&x(e,"class",t),(!r||16&d&&n!==(n=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&x(e,"style",n),(!r||64&d)&&x(e,"data-request-id",u[6]),s&&pe(s.update)&&14&d&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){r||($(l,u),r=!0)},o(u){p(l,u),r=!1},d(u){u&&h(e),l&&l.d(u),o[16](null),i=!1,c()}}}function Us(o,e,t){let n,s,r,i,c=N,a=N,l=()=>(a(),a=fe(g,T=>t(13,i=T)),g);o.$$.on_destroy.push(()=>c()),o.$$.on_destroy.push(()=>a());let{$$slots:u={},$$scope:d}=e,{requestId:m}=e,{chatModel:g}=e;l();let k,{isLastItem:L=!1}=e,{userControlsScroll:f=!1}=e,{releaseScroll:I=()=>{}}=e,{messageListContainer:A}=e,{minHeight:P}=e,{class:M=""}=e,{dataRequestId:J}=e;return Ce(()=>{A&&L&&Qn(A,{smooth:!0,onScrollFinish:I})}),o.$$set=T=>{"requestId"in T&&t(9,m=T.requestId),"chatModel"in T&&l(t(0,g=T.chatModel)),"isLastItem"in T&&t(1,L=T.isLastItem),"userControlsScroll"in T&&t(2,f=T.userControlsScroll),"releaseScroll"in T&&t(10,I=T.releaseScroll),"messageListContainer"in T&&t(3,A=T.messageListContainer),"minHeight"in T&&t(4,P=T.minHeight),"class"in T&&t(5,M=T.class),"dataRequestId"in T&&t(6,J=T.dataRequestId),"$$scope"in T&&t(14,d=T.$$scope)},o.$$.update=()=>{var T,S;8192&o.$$.dirty&&(t(8,n=(T=i==null?void 0:i.currentConversationModel)==null?void 0:T.focusModel),c(),c=fe(n,H=>t(12,r=H))),4608&o.$$.dirty&&t(11,s=((S=r.focusedItem)==null?void 0:S.request_id)===m),2048&o.$$.dirty&&s&&A&&k&&zt(A,k,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:I})},[g,L,f,A,P,M,J,k,n,m,I,s,r,i,d,u,function(T){re[T?"unshift":"push"](()=>{k=T,t(7,k)})}]}class Li extends Z{constructor(e){super(),j(this,e,Us,Ds,W,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function Vs(o){let e;return{c(){e=z("Generating response...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function wn(o){let e,t;return e=new rt.Root({props:{color:"neutral",size:1,$$slots:{default:[Gs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Js(o){let e,t;return{c(){e=E("span"),t=z(o[2]),x(e,"class","c-gen-response__timer svelte-148snxl"),D(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&D(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function Gs(o){let e,t;return e=new ne({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Js]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Zs(o){let e,t,n,s,r,i,c;n=new _e({props:{size:1}}),r=new ne({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[Vs]},$$scope:{ctx:o}}});let a=o[1]&&wn(o);return{c(){e=E("div"),t=E("span"),y(n.$$.fragment),s=B(),y(r.$$.fragment),i=B(),a&&a.c(),x(t,"class","c-gen-response__text svelte-148snxl"),x(e,"class","c-gen-response svelte-148snxl")},m(l,u){v(l,e,u),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,i),a&&a.m(e,null),c=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=wn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){c||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),c=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),c=!1},d(l){l&&h(e),_(n),_(r),a&&a.d()}}}function js(o,e,t){let n,s,r,{timeToTimerMs:i=5e3}=e,c=0,a=Date.now(),l=!1;function u(){t(0,c=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,i),a=Date.now(),()=>{t(0,c=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,i=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(c))},[c,l,n,i]}class Ws extends Z{constructor(e){super(),j(this,e,js,Zs,W,{timeToTimerMs:3})}}class de{constructor(e){te(this,"type","plainText");this.text=e}}class Me{constructor(e){te(this,"type","specialBlock");this.text=e}}function Ze(o){return o.map(e=>e.text).join("")}function Ks(o){let e,t,n,s,r=(!o[0].status||o[0].status===oe.success)&&o[4]===Ze(o[3]);e=new ss({props:{renderers:o[5],markdown:o[1]+o[4]}});let i=r&&yn(o);return{c(){y(e.$$.fragment),t=B(),i&&i.c(),n=Q()},m(c,a){C(e,c,a),v(c,t,a),i&&i.m(c,a),v(c,n,a),s=!0},p(c,a){const l={};18&a&&(l.markdown=c[1]+c[4]),e.$set(l),25&a&&(r=(!c[0].status||c[0].status===oe.success)&&c[4]===Ze(c[3])),r?i?(i.p(c,a),25&a&&$(i,1)):(i=yn(c),i.c(),$(i,1),i.m(n.parentNode,n)):i&&(U(),p(i,1,1,()=>{i=null}),V())},i(c){s||($(e.$$.fragment,c),$(i),s=!0)},o(c){p(e.$$.fragment,c),p(i),s=!1},d(c){c&&(h(t),h(n)),_(e,c),i&&i.d(c)}}}function Ys(o){let e;function t(r,i){return r[0].display_error_message?no:r[0].response_text&&r[0].response_text.length>0?eo:Qs}let n=t(o),s=n(o);return{c(){e=E("div"),s.c(),x(e,"class","c-msg-failure svelte-9a9fi8")},m(r,i){v(r,e,i),s.m(e,null)},p(r,i){n===(n=t(r))&&s?s.p(r,i):(s.d(1),s=n(r),s&&(s.c(),s.m(e,null)))},i:N,o:N,d(r){r&&h(e),s.d()}}}function Xs(o){let e,t;return e=new Ws({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function yn(o){let e;const t=o[7].default,n=he(t,o,o[8],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||256&r)&&ve(n,t,s,s[8],e?we(t,s[8],r,null):xe(s[8]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function Qs(o){let e,t,n=o[2]&&Cn(o);return{c(){e=z("We encountered an issue sending your message."),n&&n.c(),t=z(".")},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=Cn(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function eo(o){let e,t,n=o[2]&&_n(o);return{c(){e=z("Connection lost."),n&&n.c(),t=Q()},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=_n(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function no(o){let e,t=o[0].display_error_message+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){1&s&&t!==(t=n[0].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function Cn(o){let e,t,n,s;return{c(){e=z(`Please
            `),t=E("button"),t.textContent="try again",x(t,"class","svelte-9a9fi8")},m(r,i){v(r,e,i),v(r,t,i),n||(s=ee(t,"click",ye(function(){pe(o[2])&&o[2].apply(this,arguments)})),n=!0)},p(r,i){o=r},d(r){r&&(h(e),h(t)),n=!1,s()}}}function _n(o){let e,t,n,s,r;return{c(){e=z(`Please
            `),t=E("button"),t.textContent="try again",n=z("."),x(t,"class","svelte-9a9fi8")},m(i,c){v(i,e,c),v(i,t,c),v(i,n,c),s||(r=ee(t,"click",ye(function(){pe(o[2])&&o[2].apply(this,arguments)})),s=!0)},p(i,c){o=i},d(i){i&&(h(e),h(t),h(n)),s=!1,r()}}}function to(o){let e,t,n,s;const r=[Xs,Ys,Ks],i=[];function c(a,l){return(!a[0].status||a[0].status===oe.sent)&&a[4].length<=0?0:a[0].status===oe.failed?1:2}return e=c(o),t=i[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){i[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=c(a),e===u?i[e].p(a,l):(U(),p(i[u],1,1,()=>{i[u]=null}),V(),t=i[e],t?t.p(a,l):(t=i[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),i[e].d(a)}}}function so(o){let e,t;return e=new je({props:{isAugment:!0,$$slots:{content:[to]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};287&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function oo(o,e,t){let n,s,{$$slots:r={},$$scope:i}=e,{turn:c}=e,{preamble:a=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,d={code:ns,codespan:ts,link:Ft};return Ce(()=>{if(c.seen_state===xt.seen)return void t(0,c.response_text=Ze(n),c);let m=Date.now();const g=function*(f){for(const I of f)if(I.type==="specialBlock")yield I.text;else for(const A of I.text)yield A}(n);let k=g.next();const L=()=>{let f=Date.now();const I=Math.round((f-m)/8);let A="";for(let P=0;P<I&&!k.done;P++)A+=k.value,k=g.next();t(0,c.response_text+=A,c),m=f,k.done||requestAnimationFrame(L)};L()}),o.$$set=m=>{"turn"in m&&t(0,c=m.turn),"preamble"in m&&t(1,a=m.preamble),"resendTurn"in m&&t(2,l=m.resendTurn),"markdownBlocks"in m&&t(6,u=m.markdownBlocks),"$$scope"in m&&t(8,i=m.$$scope)},o.$$.update=()=>{1&o.$$.dirty&&t(0,c.response_text=c.response_text??"",c),65&o.$$.dirty&&t(3,n=c.response_text?[new de(c.response_text)]:u),1&o.$$.dirty&&t(4,s=c.response_text??"")},[c,a,l,n,s,d,u,r,i]}class ro extends Z{constructor(e){super(),j(this,e,oo,so,W,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function io(o){let e,t,n,s;return e=new Oe.Content({props:{content:o[2]}}),n=new tt({props:{requestEditorFocus:o[4]}}),{c(){y(e.$$.fragment),t=B(),y(n.$$.fragment)},m(r,i){C(e,r,i),v(r,t,i),C(n,r,i),s=!0},p:N,i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),_(e,r),_(n,r)}}}function co(o){let e,t,n={slot:"content",$$slots:{default:[io]},$$scope:{ctx:o}};return e=new Oe.Root({props:n}),o[7](e),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),t=!0},p(s,r){const i={};512&r&&(i.$$scope={dirty:r,ctx:s}),e.$set(i)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){o[7](null),_(e,s)}}}function ao(o){let e,t,n,s;return e=new je({props:{$$slots:{content:[co]},$$scope:{ctx:o}}}),n=new ro({props:{turn:o[1],markdownBlocks:o[3]}}),{c(){y(e.$$.fragment),t=B(),y(n.$$.fragment)},m(r,i){C(e,r,i),v(r,t,i),C(n,r,i),s=!0},p(r,[i]){const c={};513&i&&(c.$$scope={dirty:i,ctx:r}),e.$set(c)},i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),_(e,r),_(n,r)}}}function lo(o,e,t){let{flagsModel:n}=e,{turn:s}=e;const r={seen_state:s.seen_state,status:oe.success},i=[[new Me("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new de(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new Me("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new de(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new Me("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new de(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];n.suggestedEditsAvailable&&i.push([new Me("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new de(": Take your completions beyond the cursor and across your workspace.")]);let c,a=[new de(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...i.flatMap((l,u)=>[new de(`${u+1}. `),...l,new de(`

`)]),new de("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return o.$$set=l=>{"flagsModel"in l&&t(5,n=l.flagsModel),"turn"in l&&t(6,s=l.turn)},[c,r,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},a,function(){c==null||c.requestFocus()},n,s,function(l){re[l?"unshift":"push"](()=>{c=l,t(0,c)})}]}class Ai extends Z{constructor(e){super(),j(this,e,lo,ao,W,{flagsModel:5,turn:6})}}function uo(o){let e,t;return{c(){e=me("svg"),t=me("path"),x(t,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),x(t,"fill","currentColor"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:N,i:N,o:N,d(n){n&&h(e)}}}class $o extends Z{constructor(e){super(),j(this,e,null,uo,W,{})}}function mo(o){let e,t,n,s,r;return t=new $o({}),{c(){e=E("span"),y(t.$$.fragment),n=z(`
  Waiting for user input`),s=z(o[0]),x(e,"class","c-gen-response svelte-5is5us")},m(i,c){v(i,e,c),C(t,e,null),R(e,n),R(e,s),r=!0},p(i,[c]){(!r||1&c)&&K(s,i[0])},i(i){r||($(t.$$.fragment,i),r=!0)},o(i){p(t.$$.fragment,i),r=!1},d(i){i&&h(e),_(t)}}}function po(o,e,t){let n=".";return Ce(()=>{const s=setInterval(()=>{t(0,n=n.length>=3?".":n+".")},500);return()=>clearInterval(s)}),[n]}class zi extends Z{constructor(e){super(),j(this,e,po,mo,W,{})}}function go(o){let e;return{c(){e=z("Resuming remote agent...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function kn(o){let e,t;return e=new rt.Root({props:{color:"neutral",size:1,$$slots:{default:[ho]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function fo(o){let e,t;return{c(){e=E("span"),t=z(o[2]),x(e,"class","c-resuming-agent__timer svelte-16pyinb"),D(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&D(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function ho(o){let e,t;return e=new ne({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[fo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function vo(o){let e,t,n,s,r,i,c;n=new _e({props:{size:1}}),r=new ne({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[go]},$$scope:{ctx:o}}});let a=o[1]&&kn(o);return{c(){e=E("div"),t=E("span"),y(n.$$.fragment),s=B(),y(r.$$.fragment),i=B(),a&&a.c(),x(t,"class","c-resuming-agent__text svelte-16pyinb"),x(e,"class","c-resuming-agent svelte-16pyinb")},m(l,u){v(l,e,u),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,i),a&&a.m(e,null),c=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=kn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){c||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),c=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),c=!1},d(l){l&&h(e),_(n),_(r),a&&a.d()}}}function xo(o,e,t){let n,s,r,{timeToTimerMs:i=5e3}=e,c=0,a=Date.now(),l=!1;function u(){t(0,c=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,i),a=Date.now(),()=>{t(0,c=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,i=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(c))},[c,l,n,i]}class Fi extends Z{constructor(e){super(),j(this,e,xo,vo,W,{timeToTimerMs:3})}}function wo(o){let e,t,n,s,r;return t=new _e({props:{size:1}}),{c(){e=E("span"),y(t.$$.fragment),n=B(),s=z(o[0]),x(e,"class","c-retry-response svelte-1lxm8qk")},m(i,c){v(i,e,c),C(t,e,null),R(e,n),R(e,s),r=!0},p(i,[c]){(!r||1&c)&&K(s,i[0])},i(i){r||($(t.$$.fragment,i),r=!0)},o(i){p(t.$$.fragment,i),r=!1},d(i){i&&h(e),_(t)}}}function yo(o,e,t){let{message:n="Retrying..."}=e;return o.$$set=s=>{"message"in s&&t(0,n=s.message)},[n]}class qi extends Z{constructor(e){super(),j(this,e,yo,wo,W,{message:0})}}function Co(o){let e,t,n;return e=new Wt({}),{c(){y(e.$$.fragment),t=z(`
    Stopped`)},m(s,r){C(e,s,r),v(s,t,r),n=!0},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),_(e,s)}}}function _o(o){let e,t,n;return t=new ne({props:{size:1,$$slots:{default:[Co]},$$scope:{ctx:o}}}),{c(){e=E("span"),y(t.$$.fragment),x(e,"class","c-stopped svelte-lv19x6")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,[r]){const i={};1&r&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}class Bi extends Z{constructor(e){super(),j(this,e,null,_o,W,{})}}function ko(o){let e,t;return{c(){e=me("svg"),t=me("path"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),x(t,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:N,i:N,o:N,d(n){n&&h(e)}}}class at extends Z{constructor(e){super(),j(this,e,null,ko,W,{})}}function bo(o){let e,t,n;return{c(){e=me("svg"),t=me("path"),n=me("path"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),x(t,"fill","currentColor"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),x(n,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(s,r){v(s,e,r),R(e,t),R(e,n)},p:N,i:N,o:N,d(s){s&&h(e)}}}class lt extends Z{constructor(e){super(),j(this,e,null,bo,W,{})}}function bn(o){let e,t,n,s,r;return t=new os({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[Mo]},$$scope:{ctx:o}}}),t.$on("click",o[17]),s=new ke({props:{variant:o[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:o[6]||o[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Ao]},$$scope:{ctx:o}}}),s.$on("click",o[12]),{c(){e=E("div"),y(t.$$.fragment),n=B(),y(s.$$.fragment),x(e,"class","c-checkpoint-container svelte-q20gs5"),x(e,"data-checkpoint-number",o[0]),D(e,"c-checkpoint-container--target-checkpoint",o[6]),D(e,"c-checkpoint-container--dimmed-marker",o[5])},m(i,c){v(i,e,c),C(t,e,null),R(e,n),C(s,e,null),r=!0},p(i,c){const a={};1048778&c&&(a.$$scope={dirty:c,ctx:i}),t.$set(a);const l={};64&c&&(l.variant=i[6]?"soft":"ghost-block"),80&c&&(l.disabled=i[6]||i[4]),1048656&c&&(l.$$scope={dirty:c,ctx:i}),s.$set(l),(!r||1&c)&&x(e,"data-checkpoint-number",i[0]),(!r||64&c)&&D(e,"c-checkpoint-container--target-checkpoint",i[6]),(!r||32&c)&&D(e,"c-checkpoint-container--dimmed-marker",i[5])},i(i){r||($(t.$$.fragment,i),$(s.$$.fragment,i),r=!0)},o(i){p(t.$$.fragment,i),p(s.$$.fragment,i),r=!1},d(i){i&&h(e),_(t),_(s)}}}function Io(o){let e,t;return e=new lt({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function So(o){let e,t;return{c(){e=z("Checkpoint "),t=z(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function To(o){let e,t=ze(o[7])+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){128&s&&t!==(t=ze(n[7])+"")&&K(e,t)},d(n){n&&h(e)}}}function Ro(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function Eo(o){let e;function t(r,i){return r[1]?Ro:r[6]?To:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,i){s&&s.m(r,i),v(r,e,i)},p(r,i){n===(n=t(r))&&s?s.p(r,i):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function Mo(o){let e,t;return e=new nt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[Eo],text:[So],leftIcon:[Io]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1048778&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Lo(o){let e,t;return e=new at({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ao(o){let e,t;return e=new Se({props:{triggerOn:[He.Hover],content:o[6]||o[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[Lo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};80&s&&(r.content=n[6]||n[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function zo(o){let e,t,n=(!o[4]||o[2])&&bn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){!s[4]||s[2]?n?(n.p(s,r),20&r&&$(n,1)):(n=bn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Fo(o,e,t){let n,s,r,i,c,a,l,u,d,m,g,{turn:k}=e;const L=ie("checkpointStore"),{targetCheckpointIdx:f,totalCheckpointCount:I,uuidToIdx:A}=L;function P(M){_t(f,m=M,m)}return le(o,f,M=>t(15,m=M)),le(o,I,M=>t(14,d=M)),le(o,A,M=>t(16,g=M)),o.$$set=M=>{"turn"in M&&t(13,k=M.turn)},o.$$.update=()=>{var M,J,T;73728&o.$$.dirty&&t(0,n=g.get(k.uuid)??-1),8192&o.$$.dirty&&t(7,s=k.toTimestamp),49153&o.$$.dirty&&t(6,(J=d,r=(M=n)===(T=m)||T===void 0&&M===J-1)),49153&o.$$.dirty&&t(5,i=function(S,H,F){return S===F&&F!==void 0&&F<H-1}(n,d,m)),16385&o.$$.dirty&&t(4,c=n===d-1),1&o.$$.dirty&&t(3,a=n+1),8192&o.$$.dirty&&t(2,l=Ae(k)),8192&o.$$.dirty&&t(1,u=Ae(k)?function(S){var H,F;if((H=S.revertTarget)!=null&&H.uuid){const w=g.get(S.revertTarget.uuid);return w===void 0?void 0:`Reverted to Checkpoint ${w+1}`}return(F=S.revertTarget)!=null&&F.filePath?`Undid changes to ${S.revertTarget.filePath.relPath}`:void 0}(k):void 0)},[n,u,l,a,c,i,r,s,f,I,A,P,async function(){await L.revertToCheckpoint(k.uuid)},k,d,m,g,()=>P(n)]}class Pi extends Z{constructor(e){super(),j(this,e,Fo,zo,W,{turn:13})}}function qo(o){let e,t;return e=new lt({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function In(o){let e,t,n,s,r,i,c=o[1]===1?"":"s";return{c(){e=E("span"),t=z("("),n=z(o[1]),s=z(" file"),r=z(c),i=z(")"),x(e,"class","c-checkpoint-files-count")},m(a,l){v(a,e,l),R(e,t),R(e,n),R(e,s),R(e,r),R(e,i)},p(a,l){2&l&&K(n,a[1]),2&l&&c!==(c=a[1]===1?"":"s")&&K(r,c)},d(a){a&&h(e)}}}function Bo(o){let e,t,n,s,r=o[1]>0&&In(o);return{c(){e=z("Checkpoint "),t=z(o[0]),n=B(),r&&r.c(),s=Q()},m(i,c){v(i,e,c),v(i,t,c),v(i,n,c),r&&r.m(i,c),v(i,s,c)},p(i,c){1&c&&K(t,i[0]),i[1]>0?r?r.p(i,c):(r=In(i),r.c(),r.m(s.parentNode,s)):r&&(r.d(1),r=null)},d(i){i&&(h(e),h(t),h(n),h(s)),r&&r.d(i)}}}function Po(o){let e;return{c(){e=z(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function No(o){let e;return{c(){e=z(o[3])},m(t,n){v(t,e,n)},p(t,n){8&n&&K(e,t[3])},d(t){t&&h(e)}}}function Ho(o){let e;function t(r,i){return r[3]?No:r[2]?Po:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,i){s&&s.m(r,i),v(r,e,i)},p(r,i){n===(n=t(r))&&s?s.p(r,i):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function Sn(o){let e,t,n;return t=new Nt({props:{totalAddedLines:o[4].totalAddedLines,totalRemovedLines:o[4].totalRemovedLines}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-checkpoint-summary")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const i={};16&r&&(i.totalAddedLines=s[4].totalAddedLines),16&r&&(i.totalRemovedLines=s[4].totalRemovedLines),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Tn(o){let e,t;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Do]},$$scope:{ctx:o}}}),e.$on("click",function(){pe(o[7])&&o[7].apply(this,arguments)}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){o=n;const r={};256&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Oo(o){let e,t;return e=new at({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Do(o){let e,t;return e=new Se({props:{triggerOn:[He.Hover],content:"Revert to this Checkpoint",$$slots:{default:[Oo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};256&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Uo(o){let e,t,n,s,r,i,c,a;n=new it({}),r=new nt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[Ho],text:[Bo],leftIcon:[qo]},$$scope:{ctx:o}}});let l=o[5]&&Sn(o),u=!o[6]&&Tn(o);return{c(){e=E("div"),t=E("div"),y(n.$$.fragment),s=B(),y(r.$$.fragment),i=B(),l&&l.c(),c=B(),u&&u.c(),x(t,"class","c-checkpoint-tag"),x(e,"class","c-checkpoint-header svelte-htx8xt")},m(d,m){v(d,e,m),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,i),l&&l.m(e,null),R(e,c),u&&u.m(e,null),a=!0},p(d,[m]){const g={};271&m&&(g.$$scope={dirty:m,ctx:d}),r.$set(g),d[5]?l?(l.p(d,m),32&m&&$(l,1)):(l=Sn(d),l.c(),$(l,1),l.m(e,c)):l&&(U(),p(l,1,1,()=>{l=null}),V()),d[6]?u&&(U(),p(u,1,1,()=>{u=null}),V()):u?(u.p(d,m),64&m&&$(u,1)):(u=Tn(d),u.c(),$(u,1),u.m(e,null))},i(d){a||($(n.$$.fragment,d),$(r.$$.fragment,d),$(l),$(u),a=!0)},o(d){p(n.$$.fragment,d),p(r.$$.fragment,d),p(l),p(u),a=!1},d(d){d&&h(e),_(n),_(r),l&&l.d(),u&&u.d()}}}function Vo(o,e,t){let{displayCheckpointIdx:n}=e,{filesCount:s=0}=e,{timestamp:r=""}=e,{revertMessage:i}=e,{diffSummary:c={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:a=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return o.$$set=d=>{"displayCheckpointIdx"in d&&t(0,n=d.displayCheckpointIdx),"filesCount"in d&&t(1,s=d.filesCount),"timestamp"in d&&t(2,r=d.timestamp),"revertMessage"in d&&t(3,i=d.revertMessage),"diffSummary"in d&&t(4,c=d.diffSummary),"hasChanges"in d&&t(5,a=d.hasChanges),"isTarget"in d&&t(6,l=d.isTarget),"onRevertClick"in d&&t(7,u=d.onRevertClick)},[n,s,r,i,c,a,l,u]}class Jo extends Z{constructor(e){super(),j(this,e,Vo,Uo,W,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function Rn(o,e,t){const n=o.slice();return n[33]=e[t],n}function En(o){let e,t,n,s,r,i,c;function a(u){o[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Ko],default:[Wo]},$$scope:{ctx:o}};return o[3]!==void 0&&(l.collapsed=o[3]),t=new ct({props:l}),re.push(()=>Pe(t,"collapsed",a)),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-checkpoint-container svelte-mxd32u"),x(e,"data-checkpoint-number",o[2]),D(e,"c-checkpoint-container--target-checkpoint",o[11]),D(e,"c-checkpoint-container--dimmed-marker",o[10])},m(u,d){v(u,e,d),C(t,e,null),r=!0,i||(c=Kn(s=qt.call(null,e,{onVisible:o[28],scrollTarget:document.body})),i=!0)},p(u,d){const m={};6522&d[0]|32&d[1]&&(m.$$scope={dirty:d,ctx:u}),!n&&8&d[0]&&(n=!0,m.collapsed=u[3],Ne(()=>n=!1)),t.$set(m),(!r||4&d[0])&&x(e,"data-checkpoint-number",u[2]),s&&pe(s.update)&&1&d[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!r||2048&d[0])&&D(e,"c-checkpoint-container--target-checkpoint",u[11]),(!r||1024&d[0])&&D(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){r||($(t.$$.fragment,u),r=!0)},o(u){p(t.$$.fragment,u),r=!1},d(u){u&&h(e),_(t),i=!1,c()}}}function Go(o){let e,t,n;return t=new ne({props:{size:1,color:"neutral",$$slots:{default:[jo]},$$scope:{ctx:o}}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-mxd32u")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const i={};32&r[1]&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Zo(o){let e,t,n=Fe(o[1]),s=[];for(let i=0;i<n.length;i+=1)s[i]=Mn(Rn(o,n,i));const r=i=>p(s[i],1,1,()=>{s[i]=null});return{c(){e=E("div");for(let i=0;i<s.length;i+=1)s[i].c();x(e,"class","c-edits-list svelte-mxd32u")},m(i,c){v(i,e,c);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(i,c){if(196610&c[0]){let a;for(n=Fe(i[1]),a=0;a<n.length;a+=1){const l=Rn(i,n,a);s[a]?(s[a].p(l,c),$(s[a],1)):(s[a]=Mn(l),s[a].c(),$(s[a],1),s[a].m(e,null))}for(U(),a=n.length;a<s.length;a+=1)r(a);V()}},i(i){if(!t){for(let c=0;c<n.length;c+=1)$(s[c]);t=!0}},o(i){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)p(s[c]);t=!1},d(i){i&&h(e),Yn(s,i)}}}function jo(o){let e;return{c(){e=z("No changes to show")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Mn(o){let e,t;function n(){return o[25](o[33])}function s(){return o[26](o[33])}return e=new Kt({props:{qualifiedPathName:o[33].qualifiedPathName,lineChanges:o[33].changesSummary,onClickFile:n,onClickReview:s}}),{c(){y(e.$$.fragment)},m(r,i){C(e,r,i),t=!0},p(r,i){o=r;const c={};2&i[0]&&(c.qualifiedPathName=o[33].qualifiedPathName),2&i[0]&&(c.lineChanges=o[33].changesSummary),2&i[0]&&(c.onClickFile=n),2&i[0]&&(c.onClickReview=s),e.$set(c)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){_(e,r)}}}function Wo(o){let e,t,n,s;const r=[Zo,Go],i=[];function c(a,l){return a[4]?0:a[3]?-1:1}return~(e=c(o))&&(t=i[e]=r[e](o)),{c(){t&&t.c(),n=Q()},m(a,l){~e&&i[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=c(a),e===u?~e&&i[e].p(a,l):(t&&(U(),p(i[u],1,1,()=>{i[u]=null}),V()),~e?(t=i[e],t?t.p(a,l):(t=i[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n)):t=null)},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),~e&&i[e].d(a)}}}function Ko(o){let e,t;return e=new Jo({props:{slot:"header",displayCheckpointIdx:o[8],filesCount:o[1].length,timestamp:ze(o[12]),revertMessage:o[6],diffSummary:o[5],hasChanges:o[4],isTarget:o[11],onRevertClick:o[18]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};256&s[0]&&(r.displayCheckpointIdx=n[8]),2&s[0]&&(r.filesCount=n[1].length),4096&s[0]&&(r.timestamp=ze(n[12])),64&s[0]&&(r.revertMessage=n[6]),32&s[0]&&(r.diffSummary=n[5]),16&s[0]&&(r.hasChanges=n[4]),2048&s[0]&&(r.isTarget=n[11]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Yo(o){let e,t,n=(!o[9]||o[7])&&En(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){!s[9]||s[7]?n?(n.p(s,r),640&r[0]&&$(n,1)):(n=En(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Xo(o,e,t){let n,s,r,i,c,a,l,u,d,m,g,k,L,f,I,{turn:A}=e;const P=ie("checkpointStore"),M=ie("chatModel"),{targetCheckpointIdx:J,totalCheckpointCount:T,uuidToIdx:S}=P;le(o,J,O=>t(23,f=O)),le(o,T,O=>t(22,L=O)),le(o,S,O=>t(24,I=O));let H=!0;function F(O){M==null||M.extensionClient.openFile({repoRoot:O.rootPath,pathName:O.relPath,allowOutOfWorkspace:!0})}function w(O){M==null||M.extensionClient.showAgentReview(O,r,s,!1)}let X=[],q=!1,G=!1;return o.$$set=O=>{"turn"in O&&t(19,A=O.turn)},o.$$.update=()=>{var O,$e,se;17301504&o.$$.dirty[0]&&t(2,n=I.get(A.uuid)??-1),524288&o.$$.dirty[0]&&t(12,s=A.toTimestamp),524288&o.$$.dirty[0]&&(r=A.fromTimestamp),12582916&o.$$.dirty[0]&&t(11,($e=L,i=(O=n)===(se=f)||se===void 0&&O===$e-1)),12582916&o.$$.dirty[0]&&t(10,c=function(b,ce,ue){return b===ue&&ue!==void 0&&ue<ce-1}(n,L,f)),4194308&o.$$.dirty[0]&&t(9,a=n===L-1),4&o.$$.dirty[0]&&t(8,l=n+1),524288&o.$$.dirty[0]&&t(7,u=Ae(A)),524288&o.$$.dirty[0]&&t(6,d=Ae(A)?function(b){var ce,ue;if((ce=b.revertTarget)!=null&&ce.uuid){const be=I.get(b.revertTarget.uuid);return be===void 0?void 0:`Reverted to Checkpoint ${be+1}`}return(ue=b.revertTarget)!=null&&ue.filePath?`Undid changes to ${b.revertTarget.filePath.relPath}`:void 0}(A):void 0),2621441&o.$$.dirty[0]&&q&&A&&!G&&P.getCheckpointSummary(A).then(b=>{t(20,X=b),t(21,G=!0)}),1048576&o.$$.dirty[0]&&t(1,m=X.filter(b=>b.changesSummary&&(b.changesSummary.totalAddedLines>0||b.changesSummary.totalRemovedLines>0))),2&o.$$.dirty[0]&&t(5,g=m.reduce((b,ce)=>{var ue,be;return b.totalAddedLines+=((ue=ce.changesSummary)==null?void 0:ue.totalAddedLines)??0,b.totalRemovedLines+=((be=ce.changesSummary)==null?void 0:be.totalRemovedLines)??0,b},{totalAddedLines:0,totalRemovedLines:0})),2&o.$$.dirty[0]&&t(4,k=m.length>0)},[q,m,n,H,k,g,d,u,l,a,c,i,s,J,T,S,F,w,function(){P.revertToCheckpoint(A.uuid)},A,X,G,L,f,I,O=>F(O.qualifiedPathName),O=>w(O.qualifiedPathName),function(O){H=O,t(3,H)},()=>t(0,q=!0)]}class Ni extends Z{constructor(e){super(),j(this,e,Xo,Yo,W,{turn:19},null,[-1,-1])}}const Ln={[Ue.SUCCESS]:"success",[Ue.FAILED]:"error",[Ue.SKIPPED]:"skipped"},An={[ge.success]:"success",[ge.failure]:"error",[ge.running]:null,[ge.unknown]:"unknown",[ge.skipped]:"skipped"};function zn(o){return o in Ln?Ln[o]:o in An?An[o]:null}function Fn(o){switch(o){case"success":return"Success";case"error":return"Failed";case"skipped":return"Skipped";case"unknown":return"Unknown";case null:return"Running"}}function qn(o){let e,t,n;return t=new ne({props:{size:1,type:"monospace",$$slots:{default:[Qo]},$$scope:{ctx:o}}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-command-output__code-block svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const i={};16386&r&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Qo(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function Bn(o){let e,t,n,s;const r=[nr,er],i=[];function c(a,l){return a[5]?0:1}return t=c(o),n=i[t]=r[t](o),{c(){e=E("div"),n.c(),x(e,"class","c-command-output__code-block c-command-output__code-block--output svelte-1t0700a")},m(a,l){v(a,e,l),i[t].m(e,null),s=!0},p(a,l){let u=t;t=c(a),t===u?i[t].p(a,l):(U(),p(i[u],1,1,()=>{i[u]=null}),V(),n=i[t],n?n.p(a,l):(n=i[t]=r[t](a),n.c()),$(n,1),n.m(e,null))},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(e),i[t].d()}}}function er(o){let e,t;return e=new ne({props:{size:1,type:"monospace",$$slots:{default:[tr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16388&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function nr(o){let e,t;return e=new rs.Root({props:{$$slots:{default:[sr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16452&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function tr(o){let e;return{c(){e=z(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function sr(o){let e,t;return e=new is({props:{text:o[2],lang:o[6]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};4&s&&(r.text=n[2]),64&s&&(r.lang=n[6]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function or(o){let e,t,n,s,r=o[1]&&!o[0]&&qn(o),i=o[2]&&(!o[3]||o[3]!=="skipped")&&Bn(o);const c=o[12].default,a=he(c,o,o[14],null);return{c(){e=E("div"),r&&r.c(),t=B(),i&&i.c(),n=B(),a&&a.c(),x(e,"class","c-command-output__command-details")},m(l,u){v(l,e,u),r&&r.m(e,null),R(e,t),i&&i.m(e,null),R(e,n),a&&a.m(e,null),s=!0},p(l,u){l[1]&&!l[0]?r?(r.p(l,u),3&u&&$(r,1)):(r=qn(l),r.c(),$(r,1),r.m(e,t)):r&&(U(),p(r,1,1,()=>{r=null}),V()),!l[2]||l[3]&&l[3]==="skipped"?i&&(U(),p(i,1,1,()=>{i=null}),V()):i?(i.p(l,u),12&u&&$(i,1)):(i=Bn(l),i.c(),$(i,1),i.m(e,n)),a&&a.p&&(!s||16384&u)&&ve(a,c,l,l[14],s?we(c,l[14],u,null):xe(l[14]),null)},i(l){s||($(r),$(i),$(a,l),s=!0)},o(l){p(r),p(i),p(a,l),s=!1},d(l){l&&h(e),r&&r.d(),i&&i.d(),a&&a.d(l)}}}function rr(o){let e;return{c(){e=E("div"),x(e,"class","c-command-output__collapsible-header__spacer svelte-1t0700a")},m(t,n){v(t,e,n)},i:N,o:N,d(t){t&&h(e)}}}function ir(o){let e,t;return e=new it({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function cr(o){let e,t;return e=new ne({props:{size:1,type:"monospace",$$slots:{default:[lr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16386&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function ar(o){let e,t;return e=new ne({props:{size:1,weight:"medium",$$slots:{default:[ur]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16385&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function lr(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function ur(o){let e;return{c(){e=z(o[0])},m(t,n){v(t,e,n)},p(t,n){1&n&&K(e,t[0])},d(t){t&&h(e)}}}function dr(o){let e,t,n,s=o[3]==="skipped"&&Pn(o);return t=new Se({props:{content:Fn(o[3]),triggerOn:[He.Hover],$$slots:{default:[pr]},$$scope:{ctx:o}}}),{c(){s&&s.c(),e=B(),y(t.$$.fragment)},m(r,i){s&&s.m(r,i),v(r,e,i),C(t,r,i),n=!0},p(r,i){r[3]==="skipped"?s?8&i&&$(s,1):(s=Pn(r),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V());const c={};8&i&&(c.content=Fn(r[3])),16392&i&&(c.$$scope={dirty:i,ctx:r}),t.$set(c)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),_(t,r)}}}function $r(o){let e,t,n;return t=new _e({props:{size:1}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-command-output__status-icon c-command-output__status-icon--loading svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p:N,i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Pn(o){let e,t;return e=new ne({props:{size:1,$$slots:{default:[mr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function mr(o){let e;return{c(){e=z("Skipped")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function pr(o){let e,t,n;var s=o[11](o[3]);return s&&(t=sn(s,{})),{c(){e=E("div"),t&&y(t.$$.fragment),x(e,"class","c-command-output__status-icon svelte-1t0700a"),D(e,"c-command-output__status-icon--success",o[3]==="success"),D(e,"c-command-output__status-icon--error",o[3]==="error"),D(e,"c-command-output__status-icon--warning",o[3]==="skipped")},m(r,i){v(r,e,i),t&&C(t,e,null),n=!0},p(r,i){if(8&i&&s!==(s=r[11](r[3]))){if(t){U();const c=t;p(c.$$.fragment,1,0,()=>{_(c,1)}),V()}s?(t=sn(s,{}),y(t.$$.fragment),$(t.$$.fragment,1),C(t,e,null)):t=null}(!n||8&i)&&D(e,"c-command-output__status-icon--success",r[3]==="success"),(!n||8&i)&&D(e,"c-command-output__status-icon--error",r[3]==="error"),(!n||8&i)&&D(e,"c-command-output__status-icon--warning",r[3]==="skipped")},i(r){n||(t&&$(t.$$.fragment,r),n=!0)},o(r){t&&p(t.$$.fragment,r),n=!1},d(r){r&&h(e),t&&_(t)}}}function Nn(o){let e,t;return e=new Se({props:{content:o[7],align:"end",$$slots:{default:[fr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};128&s&&(r.content=n[7]),17412&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function gr(o){let e,t;return e=new cs({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function fr(o){let e,t;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[gr]},$$scope:{ctx:o}}}),e.$on("click",o[13]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16384&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function hr(o){let e,t,n,s,r,i,c,a,l,u,d,m;const g=[ir,rr],k=[];function L(S,H){return S[8]&&S[3]!=="skipped"?0:1}t=L(o),n=k[t]=g[t](o);const f=[ar,cr],I=[];function A(S,H){return S[0]?0:1}r=A(o),i=I[r]=f[r](o);const P=[$r,dr],M=[];function J(S,H){return S[9]?0:S[3]!==null?1:-1}~(l=J(o))&&(u=M[l]=P[l](o));let T=o[2]&&(!o[3]||o[3]!=="skipped")&&!o[9]&&Nn(o);return{c(){e=E("div"),n.c(),s=B(),i.c(),c=B(),a=E("div"),u&&u.c(),d=B(),T&&T.c(),x(a,"class","c-command-output__status-indicator svelte-1t0700a"),x(e,"slot","header"),x(e,"class","c-command-output__collapsible-header svelte-1t0700a")},m(S,H){v(S,e,H),k[t].m(e,null),R(e,s),I[r].m(e,null),R(e,c),R(e,a),~l&&M[l].m(a,null),R(a,d),T&&T.m(a,null),m=!0},p(S,H){let F=t;t=L(S),t!==F&&(U(),p(k[F],1,1,()=>{k[F]=null}),V(),n=k[t],n||(n=k[t]=g[t](S),n.c()),$(n,1),n.m(e,s));let w=r;r=A(S),r===w?I[r].p(S,H):(U(),p(I[w],1,1,()=>{I[w]=null}),V(),i=I[r],i?i.p(S,H):(i=I[r]=f[r](S),i.c()),$(i,1),i.m(e,c));let X=l;l=J(S),l===X?~l&&M[l].p(S,H):(u&&(U(),p(M[X],1,1,()=>{M[X]=null}),V()),~l?(u=M[l],u?u.p(S,H):(u=M[l]=P[l](S),u.c()),$(u,1),u.m(a,d)):u=null),!S[2]||S[3]&&S[3]==="skipped"||S[9]?T&&(U(),p(T,1,1,()=>{T=null}),V()):T?(T.p(S,H),524&H&&$(T,1)):(T=Nn(S),T.c(),$(T,1),T.m(a,null))},i(S){m||($(n),$(i),$(u),$(T),m=!0)},o(S){p(n),p(i),p(u),p(T),m=!1},d(S){S&&h(e),k[t].d(),I[r].d(),~l&&M[l].d(),T&&T.d()}}}function vr(o){let e,t,n;return t=new ct({props:{collapsed:o[4],$$slots:{header:[hr],default:[or]},$$scope:{ctx:o}}}),{c(){e=E("div"),y(t.$$.fragment),x(e,"class","c-command-output__container svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,[r]){const i={};16&r&&(i.collapsed=s[4]),18415&r&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function xr(o,e,t){let{$$slots:n={},$$scope:s}=e,{title:r=""}=e,{command:i=""}=e,{output:c=null}=e,{status:a=null}=e,{collapsed:l=!1}=e,{useMonaco:u=!1}=e,{monacoLang:d="bash"}=e,{viewButtonTooltip:m="View full output in editor"}=e,{showCollapseButton:g=!0}=e,{isLoading:k=!1}=e,{onViewOutput:L}=e;return o.$$set=f=>{"title"in f&&t(0,r=f.title),"command"in f&&t(1,i=f.command),"output"in f&&t(2,c=f.output),"status"in f&&t(3,a=f.status),"collapsed"in f&&t(4,l=f.collapsed),"useMonaco"in f&&t(5,u=f.useMonaco),"monacoLang"in f&&t(6,d=f.monacoLang),"viewButtonTooltip"in f&&t(7,m=f.viewButtonTooltip),"showCollapseButton"in f&&t(8,g=f.showCollapseButton),"isLoading"in f&&t(9,k=f.isLoading),"onViewOutput"in f&&t(10,L=f.onViewOutput),"$$scope"in f&&t(14,s=f.$$scope)},[r,i,c,a,l,u,d,m,g,k,L,function(f){return f==="success"||f==="skipped"?et:Ht},n,f=>L(f,c),s]}class wr extends Z{constructor(e){super(),j(this,e,xr,vr,W,{title:0,command:1,output:2,status:3,collapsed:4,useMonaco:5,monacoLang:6,viewButtonTooltip:7,showCollapseButton:8,isLoading:9,onViewOutput:10})}}function Hn(o,e,t){const n=o.slice();return n[15]=e[t],n}function yr(o){let e,t,n,s,r;return t=new ne({props:{size:1,color:"secondary",$$slots:{default:[_r]},$$scope:{ctx:o}}}),s=new _e({props:{size:1}}),{c(){e=E("div"),y(t.$$.fragment),n=B(),y(s.$$.fragment),x(e,"class","c-agent-no-setup-logs svelte-12293rd")},m(i,c){v(i,e,c),C(t,e,null),R(e,n),C(s,e,null),r=!0},p(i,c){const a={};262144&c&&(a.$$scope={dirty:c,ctx:i}),t.$set(a)},i(i){r||($(t.$$.fragment,i),$(s.$$.fragment,i),r=!0)},o(i){p(t.$$.fragment,i),p(s.$$.fragment,i),r=!1},d(i){i&&h(e),_(t),_(s)}}}function Cr(o){let e,t,n,s,r,i,c,a,l,u,d,m,g,k,L,f,I,A;r=new ke({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button "+(o[2]?"c-agent-setup-logs-toggle-button--expanded":""),$$slots:{default:[kr]},$$scope:{ctx:o}}}),r.$on("click",o[7]);const P=[Ir,br],M=[];function J(q,G){return q[0]?0:1}c=J(o),a=M[c]=P[c](o);const T=[Er,Rr],S=[];function H(q,G){return q[0]?0:1}d=H(o),m=S[d]=T[d](o);let F=Fe(o[3].steps),w=[];for(let q=0;q<F.length;q+=1)w[q]=On(Hn(o,F,q));const X=q=>p(w[q],1,1,()=>{w[q]=null});return{c(){e=E("div"),t=E("div"),n=E("div"),s=E("div"),y(r.$$.fragment),i=B(),a.c(),l=B(),u=E("div"),m.c(),g=B(),k=E("div"),L=E("div");for(let q=0;q<w.length;q+=1)w[q].c();x(s,"class","c-agent-setup-logs-summary-left svelte-12293rd"),x(u,"class","c-agent-setup-logs-summary-icon svelte-12293rd"),x(n,"class","c-agent-setup-logs-summary-content svelte-12293rd"),x(t,"class","c-agent-setup-logs-summary svelte-12293rd"),x(t,"role","button"),x(t,"tabindex","0"),x(t,"aria-expanded",o[2]),x(t,"aria-controls","agent-setup-logs-details"),x(L,"class","c-agent-setup-logs svelte-12293rd"),x(k,"class","c-agent-setup-logs-wrapper svelte-12293rd"),D(k,"is-hidden",!o[2]),x(e,"class","c-agent-setup-logs-container svelte-12293rd"),D(e,"c-agent-setup-logs-container--loading",!o[0])},m(q,G){v(q,e,G),R(e,t),R(t,n),R(n,s),C(r,s,null),R(s,i),M[c].m(s,null),R(n,l),R(n,u),S[d].m(u,null),R(e,g),R(e,k),R(k,L);for(let O=0;O<w.length;O+=1)w[O]&&w[O].m(L,null);o[13](e),f=!0,I||(A=[ee(t,"click",o[6]),ee(t,"keydown",o[8])],I=!0)},p(q,G){const O={};4&G&&(O.class="c-agent-setup-logs-toggle-button "+(q[2]?"c-agent-setup-logs-toggle-button--expanded":"")),262144&G&&(O.$$scope={dirty:G,ctx:q}),r.$set(O);let $e=c;c=J(q),c!==$e&&(U(),p(M[$e],1,1,()=>{M[$e]=null}),V(),a=M[c],a||(a=M[c]=P[c](q),a.c()),$(a,1),a.m(s,null));let se=d;if(d=H(q),d!==se&&(U(),p(S[se],1,1,()=>{S[se]=null}),V(),m=S[d],m||(m=S[d]=T[d](q),m.c()),$(m,1),m.m(u,null)),(!f||4&G)&&x(t,"aria-expanded",q[2]),552&G){let b;for(F=Fe(q[3].steps),b=0;b<F.length;b+=1){const ce=Hn(q,F,b);w[b]?(w[b].p(ce,G),$(w[b],1)):(w[b]=On(ce),w[b].c(),$(w[b],1),w[b].m(L,null))}for(U(),b=F.length;b<w.length;b+=1)X(b);V()}(!f||4&G)&&D(k,"is-hidden",!q[2]),(!f||1&G)&&D(e,"c-agent-setup-logs-container--loading",!q[0])},i(q){if(!f){$(r.$$.fragment,q),$(a),$(m);for(let G=0;G<F.length;G+=1)$(w[G]);f=!0}},o(q){p(r.$$.fragment,q),p(a),p(m),w=w.filter(Boolean);for(let G=0;G<w.length;G+=1)p(w[G]);f=!1},d(q){q&&h(e),_(r),M[c].d(),S[d].d(),Yn(w,q),o[13](null),I=!1,Be(A)}}}function _r(o){let e;return{c(){e=z("Waiting to start agent environment...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function kr(o){let e,t;return e=new as({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function br(o){let e,t;return e=new ne({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[Sr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ir(o){let e,t;return e=new ne({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[Tr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Sr(o){let e;return{c(){e=z("Environment is being created...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Tr(o){let e;return{c(){e=z("Environment created")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Rr(o){let e,t;return e=new _e({props:{size:1}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Er(o){let e,t;return e=new et({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function On(o){let e,t;return e=new wr({props:{title:o[15].step_description,output:o[15].logs,status:zn(o[15].status),isLoading:o[9](o[15].status),collapsed:!o[9](o[15].status),showCollapseButton:!!o[15].logs,viewButtonTooltip:"View full output in editor",onViewOutput:o[12]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};8&s&&(r.title=n[15].step_description),8&s&&(r.output=n[15].logs),8&s&&(r.status=zn(n[15].status)),8&s&&(r.isLoading=n[9](n[15].status)),8&s&&(r.collapsed=!n[9](n[15].status)),8&s&&(r.showCollapseButton=!!n[15].logs),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Mr(o){let e,t,n,s;const r=[Cr,yr],i=[];function c(a,l){return a[3]&&a[3].steps&&a[3].steps.length>0?0:1}return e=c(o),t=i[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){i[e].m(a,l),v(a,n,l),s=!0},p(a,[l]){let u=e;e=c(a),e===u?i[e].p(a,l):(U(),p(i[u],1,1,()=>{i[u]=null}),V(),t=i[e],t?t.p(a,l):(t=i[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),i[e].d(a)}}}function Lr(o,e,t){let n,s,r,i,c;const a=ie(qe.key);le(o,a,g=>t(11,i=g));const l=ie("chatModel");let u=!1;function d(g){g&&l&&l.extensionClient.openScratchFile(g,"plaintext")}function m(){t(2,u=!u)}return o.$$.update=()=>{var g;2048&o.$$.dirty&&t(10,n=((g=i==null?void 0:i.currentAgent)==null?void 0:g.status)||Te.agentUnspecified),1024&o.$$.dirty&&t(0,s=[Te.agentIdle,Te.agentRunning,Te.agentFailed].includes(n)),2048&o.$$.dirty&&t(3,r=i==null?void 0:i.agentSetupLogs),1&o.$$.dirty&&t(2,u=!s)},[s,c,u,r,a,d,m,function(g){g.stopPropagation(),m()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),m())},function(g){return g===ge.running&&!s},n,i,(g,k)=>d(k),function(g){re[g?"unshift":"push"](()=>{c=g,t(1,c)})}]}class Hi extends Z{constructor(e){super(),j(this,e,Lr,Mr,W,{})}}function Ar(o){let e;const t=o[3].default,n=he(t,o,o[4],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||16&r)&&ve(n,t,s,s[4],e?we(t,s[4],r,null):xe(s[4]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function zr(o){let e,t;return e=new Bt({props:{class:"c-chat-floating-container c-chat-floating-container--"+o[0],xPos:o[1],yPos:o[2],$$slots:{default:[Ar]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};1&s&&(r.class="c-chat-floating-container c-chat-floating-container--"+n[0]),2&s&&(r.xPos=n[1]),4&s&&(r.yPos=n[2]),16&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Fr(o,e,t){let{$$slots:n={},$$scope:s}=e,{position:r="bottom"}=e,{xPos:i="middle"}=e,{yPos:c=r==="top"?"top":"bottom"}=e;return o.$$set=a=>{"position"in a&&t(0,r=a.position),"xPos"in a&&t(1,i=a.xPos),"yPos"in a&&t(2,c=a.yPos),"$$scope"in a&&t(4,s=a.$$scope)},[r,i,c,n,s]}class qr extends Z{constructor(e){super(),j(this,e,Fr,zr,W,{position:0,xPos:1,yPos:2})}}function Br(o){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],s={};for(let r=0;r<n.length;r+=1)s=Ie(s,n[r]);return{c(){e=me("svg"),t=new kt(!0),this.h()},l(r){e=bt(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=It(e);t=St(i,!0),i.forEach(h),this.h()},h(){t.a=null,on(e,s)},m(r,i){Tt(r,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(r,[i]){on(e,s=Je(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&i&&r[0]]))},i:N,o:N,d(r){r&&h(e)}}}function Pr(o,e,t){return o.$$set=n=>{t(0,e=Ie(Ie({},e),rn(n)))},[e=rn(e)]}class Nr extends Z{constructor(e){super(),j(this,e,Pr,Br,W,{})}}function Dn(o){let e,t,n,s,r,i;return n=new ke({props:{class:"c-chat-floating-button",variant:"outline",color:"neutral",size:1,radius:"full",$$slots:{default:[Hr]},$$scope:{ctx:o}}}),n.$on("click",o[1]),{c(){e=E("div"),t=E("div"),y(n.$$.fragment),x(e,"class","c-msg-list-bottom-button svelte-1eg3it6")},m(c,a){v(c,e,a),R(e,t),C(n,t,null),i=!0},p(c,a){const l={};8&a&&(l.$$scope={dirty:a,ctx:c}),n.$set(l)},i(c){i||($(n.$$.fragment,c),c&&cn(()=>{i&&(s||(s=Re(t,Ee,{duration:150},!0)),s.run(1))}),c&&cn(()=>{i&&(r||(r=Re(e,Ee,{duration:150},!0)),r.run(1))}),i=!0)},o(c){p(n.$$.fragment,c),c&&(s||(s=Re(t,Ee,{duration:150},!1)),s.run(0)),c&&(r||(r=Re(e,Ee,{duration:150},!1)),r.run(0)),i=!1},d(c){c&&h(e),_(n),c&&s&&s.end(),c&&r&&r.end()}}}function Hr(o){let e,t;return e=new Nr({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Or(o){let e,t,n=o[0]&&Dn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[0]?n?(n.p(s,r),1&r&&$(n,1)):(n=Dn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Dr(o){let e,t;return e=new qr({props:{position:"bottom",$$slots:{default:[Or]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};9&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ur(o,e,t){let{showScrollDown:n=!1}=e,{messageListElement:s=null}=e;return o.$$set=r=>{"showScrollDown"in r&&t(0,n=r.showScrollDown),"messageListElement"in r&&t(2,s=r.messageListElement)},[n,()=>{s&&Qn(s,{smooth:!0})},s]}class Oi extends Z{constructor(e){super(),j(this,e,Ur,Dr,W,{showScrollDown:0,messageListElement:2})}}function Vr(o){let e;return{c(){e=E("div"),e.innerHTML='<span class="c-paused-remote-agent__text svelte-av8nea">This agent is paused and will resume when you send a new message</span>',x(e,"class","c-paused-remote-agent svelte-av8nea")},m(t,n){v(t,e,n)},p:N,i:N,o:N,d(t){t&&h(e)}}}class Di extends Z{constructor(e){super(),j(this,e,null,Vr,W,{})}}function Un(o){let e,t;return{c(){e=z("Retrying in "),t=z(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function Vn(o){let e,t,n,s,r,i=o[6]?" now":"";return{c(){e=E("button"),t=z("Retry"),n=z(i),x(e,"class","c-remote-agent-error__button c-remote-agent-error__button--retry")},m(c,a){v(c,e,a),R(e,t),R(e,n),s||(r=ee(e,"click",ye(function(){pe(o[0])&&o[0].apply(this,arguments)})),s=!0)},p(c,a){o=c,64&a&&i!==(i=o[6]?" now":"")&&K(n,i)},d(c){c&&h(e),s=!1,r()}}}function Jn(o){let e,t,n;return{c(){e=E("button"),e.textContent="Delete Agent",x(e,"class","c-remote-agent-error__button c-remote-agent-error__button--delete")},m(s,r){v(s,e,r),t||(n=ee(e,"click",ye(function(){pe(o[1])&&o[1].apply(this,arguments)})),t=!0)},p(s,r){o=s},d(s){s&&h(e),t=!1,n()}}}function Jr(o){let e,t,n,s,r,i,c,a,l=o[6]&&o[3]&&Un(o),u=o[4]&&Vn(o),d=o[5]&&Jn(o);return{c(){e=E("div"),t=E("div"),n=E("span"),s=z(o[7]),r=B(),l&&l.c(),i=B(),c=E("div"),u&&u.c(),a=B(),d&&d.c(),x(n,"class","c-remote-agent-error__message"),x(c,"class","c-remote-agent-error__actions"),x(t,"class","c-remote-agent-error__content svelte-g0g7z3"),x(e,"class","c-remote-agent-error svelte-g0g7z3"),D(e,"c-remote-agent-error--unrecoverable",o[2])},m(m,g){v(m,e,g),R(e,t),R(t,n),R(n,s),R(n,r),l&&l.m(n,null),R(t,i),R(t,c),u&&u.m(c,null),R(c,a),d&&d.m(c,null)},p(m,[g]){128&g&&K(s,m[7]),m[6]&&m[3]?l?l.p(m,g):(l=Un(m),l.c(),l.m(n,null)):l&&(l.d(1),l=null),m[4]?u?u.p(m,g):(u=Vn(m),u.c(),u.m(c,a)):u&&(u.d(1),u=null),m[5]?d?d.p(m,g):(d=Jn(m),d.c(),d.m(c,null)):d&&(d.d(1),d=null),4&g&&D(e,"c-remote-agent-error--unrecoverable",m[2])},i:N,o:N,d(m){m&&h(e),l&&l.d(),u&&u.d(),d&&d.d()}}}function Gr(o,e,t){let n,s,r,i,c,a,l,u,d,{error:m}=e,{onRetry:g}=e,{onDelete:k}=e;function L(){d&&(d(),d=void 0),i&&(d=ls(i,f=>{t(3,u=f)}))}return Ce(L),Wn(()=>{d==null||d()}),o.$$set=f=>{"error"in f&&t(8,m=f.error),"onRetry"in f&&t(0,g=f.onRetry),"onDelete"in f&&t(1,k=f.onDelete)},o.$$.update=()=>{256&o.$$.dirty&&t(10,n="type"in m),1280&o.$$.dirty&&t(2,s=n&&m.type===Gn.agentFailed),256&o.$$.dirty&&t(7,r=m.errorMessage),1280&o.$$.dirty&&t(9,i=n?void 0:m.retryAt),512&o.$$.dirty&&t(6,c=i!==void 0),6&o.$$.dirty&&t(5,a=s&&k),1&o.$$.dirty&&t(4,l=g),512&o.$$.dirty&&i&&L()},[g,k,s,u,l,a,c,r,m,i,n]}class Ui extends Z{constructor(e){super(),j(this,e,Gr,Jr,W,{error:8,onRetry:0,onDelete:1})}}export{Hi as A,Mi as C,Ai as E,Ti as G,Oi as M,Di as P,Ui as R,Bi as S,zs as U,Ri as a,Li as b,Ei as c,zi as d,Ws as e,Fi as f,_i as g,qi as h,ki as i,ro as j,Pi as k,Ni as l,un as m,Ii as n,bi as o,Si as t};
