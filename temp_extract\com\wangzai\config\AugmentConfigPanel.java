/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.openapi.diagnostic.Logger
 *  com.intellij.openapi.ui.Messages
 *  com.intellij.ui.components.JBLabel
 *  com.intellij.ui.components.JBTextField
 *  com.intellij.util.ui.FormBuilder
 *  com.intellij.util.ui.JBUI$Borders
 */
package com.wangzai.config;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.ui.Messages;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBTextField;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI;
import com.wangzai.AuthenticationManager;
import com.wangzai.SessionId;
import com.wangzai.SessionIdReplacer;
import com.wangzai.TrialSessionManager;
import java.awt.Color;
import java.awt.Component;
import java.awt.Desktop;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.net.URI;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.swing.Icon;
import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;

public class AugmentConfigPanel {
    private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
    private JPanel mainPanel;
    private JBTextField sessionIdField;
    private JBLabel sessionIdSourceLabel;
    private JButton trialActivateButton;
    private JBLabel trialStatusLabel;
    private JPanel trialPanel;
    private JBTextField formalCodeField;
    private JButton formalVerifyButton;
    private JButton formalHelpButton;
    private JButton formalTutorialButton;
    private JBLabel formalStatusLabel;
    private JButton generateButton;
    private JButton copyButton;
    private JButton debugClearButton;
    private String originalSessionId;
    private boolean modified = false;
    private AuthenticationManager authManager = AuthenticationManager.getInstance();
    private TrialSessionManager trialManager = TrialSessionManager.getInstance();
    private ScheduledExecutorService statusUpdateScheduler;
    private volatile boolean isDisposed = false;

    public AugmentConfigPanel() {
        this.initializeComponents();
        this.setupLayout();
        this.setupEventHandlers();
        this.loadAuthenticationStatus();
        this.loadCurrentSettings();
        this.startStatusUpdateScheduler();
    }

    private void initializeComponents() {
        this.trialActivateButton = new JButton("\u6fc0\u6d3b5\u5929\u8bd5\u7528");
        this.trialActivateButton.setToolTipText("\u70b9\u51fb\u6fc0\u6d3b5\u5929\u8bd5\u7528\u671f\uff08\u9a8c\u8bc1\u7801\uff1a1024\uff09");
        this.trialStatusLabel = new JBLabel("\u70b9\u51fb\u6309\u94ae\u6fc0\u6d3b5\u5929\u8bd5\u7528\u671f");
        this.trialStatusLabel.setFont(this.trialStatusLabel.getFont().deriveFont(2));
        this.trialStatusLabel.setForeground(Color.ORANGE);
        this.formalCodeField = new JBTextField();
        this.formalCodeField.setColumns(15);
        this.formalCodeField.setToolTipText("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\uff0c\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
        this.formalVerifyButton = new JButton("\u6b63\u5f0f\u9a8c\u8bc1");
        this.formalHelpButton = new JButton("\u5982\u4f55\u83b7\u53d6\u6b63\u5f0f\u9a8c\u8bc1\u7801\uff1f");
        this.formalHelpButton.setBackground(new Color(76, 175, 80));
        this.formalHelpButton.setForeground(Color.WHITE);
        this.formalHelpButton.setToolTipText("\u70b9\u51fb\u67e5\u770b\u5982\u4f55\u83b7\u53d6\u6b63\u5f0f\u9a8c\u8bc1\u7801\u7684\u8be6\u7ec6\u8bf4\u660e");
        this.formalTutorialButton = new JButton("\u4f7f\u7528\u6559\u7a0b");
        this.formalTutorialButton.setBackground(new Color(255, 193, 7));
        this.formalTutorialButton.setForeground(Color.BLACK);
        this.formalTutorialButton.setToolTipText("\u70b9\u51fb\u67e5\u770b\u8be6\u7ec6\u7684\u4f7f\u7528\u6559\u7a0b");
        this.formalStatusLabel = new JBLabel("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
        this.formalStatusLabel.setFont(this.formalStatusLabel.getFont().deriveFont(2));
        this.formalStatusLabel.setForeground(Color.ORANGE);
        this.sessionIdField = new JBTextField();
        this.sessionIdField.setEditable(false);
        this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
        this.sessionIdSourceLabel = new JBLabel();
        this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
        this.generateButton = new JButton("\u751f\u6210\u65b0\u7684SessionId");
        this.copyButton = new JButton("\u590d\u5236\u5230\u526a\u8d34\u677f");
        this.debugClearButton = new JButton("\u6e05\u9664\u6240\u6709\u8ba4\u8bc1\u72b6\u6001(\u8c03\u8bd5)");
        this.debugClearButton.setToolTipText("\u6e05\u9664\u6240\u6709\u8bd5\u7528\u548c\u6b63\u5f0f\u9a8c\u8bc1\u72b6\u6001\uff0c\u4ec5\u7528\u4e8e\u8c03\u8bd5");
        this.debugClearButton.setForeground(Color.RED);
        this.debugClearButton.setVisible(false);
        this.updateButtonStates();
    }

    private void setupLayout() {
        this.trialPanel = new JPanel(new FlowLayout(0, 5, 0));
        this.trialPanel.add(this.trialActivateButton);
        JPanel formalPanel = new JPanel(new FlowLayout(0, 5, 0));
        formalPanel.add((Component)this.formalCodeField);
        formalPanel.add(this.formalVerifyButton);
        formalPanel.add(this.formalHelpButton);
        formalPanel.add(this.formalTutorialButton);
        JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
        buttonPanel.add(this.generateButton);
        buttonPanel.add(this.copyButton);
        JPanel debugPanel = new JPanel(new FlowLayout(0, 5, 0));
        debugPanel.add(this.debugClearButton);
        FormBuilder formBuilder = FormBuilder.createFormBuilder();
        formBuilder.addLabeledComponent("\u8bd5\u7528\u6fc0\u6d3b:", (JComponent)this.trialPanel, 1, false).addComponentToRightColumn((JComponent)this.trialStatusLabel, 1).addSeparator().addLabeledComponent("\u6b63\u5f0f\u9a8c\u8bc1\u7801:", (JComponent)formalPanel, 1, false).addComponentToRightColumn((JComponent)this.formalStatusLabel, 1).addSeparator();
        formBuilder.addLabeledComponent("\u5f53\u524dSessionId:", (JComponent)this.sessionIdField, 1, false).addComponentToRightColumn((JComponent)this.sessionIdSourceLabel, 1).addComponent((JComponent)buttonPanel, 1).addSeparator().addLabeledComponent("\u8c03\u8bd5\u529f\u80fd:", (JComponent)debugPanel, 1, false).addComponentFillVertically((JComponent)new JPanel(), 0);
        this.mainPanel = formBuilder.getPanel();
        this.mainPanel.setBorder(JBUI.Borders.empty((int)10));
        this.updateTrialSectionVisibility();
    }

    private void setupEventHandlers() {
        this.trialActivateButton.addActionListener(e -> this.activateTrialMode());
        this.formalVerifyButton.addActionListener(e -> this.verifyFormalCode());
        this.formalCodeField.addActionListener(e -> this.verifyFormalCode());
        this.formalHelpButton.addActionListener(e -> this.openFormalCodeHelpPage());
        this.formalTutorialButton.addActionListener(e -> this.openTutorialPage());
        this.generateButton.addActionListener(e -> {
            if (this.authManager.hasValidAuthentication()) {
                this.generateNewSessionId();
            } else {
                this.showAuthenticationRequiredMessage();
            }
        });
        this.copyButton.addActionListener(e -> {
            if (this.authManager.hasValidAuthentication()) {
                this.copySessionIdToClipboard();
            } else {
                this.showAuthenticationRequiredMessage();
            }
        });
        this.debugClearButton.addActionListener(e -> {
            int result = Messages.showYesNoDialog((String)"\u786e\u5b9a\u8981\u6e05\u9664\u6240\u6709\u8ba4\u8bc1\u72b6\u6001\u5417\uff1f\n\n\u8fd9\u5c06\u6e05\u9664\uff1a\n\u2022 \u8bd5\u7528\u9a8c\u8bc1\u72b6\u6001\u548c\u5269\u4f59\u65f6\u95f4\n\u2022 \u6b63\u5f0f\u9a8c\u8bc1\u72b6\u6001\n\u2022 \u6240\u6709\u76f8\u5173\u7684SessionId\u6570\u636e\n\n\u6b64\u64cd\u4f5c\u4e0d\u53ef\u64a4\u9500\uff01", (String)"\u786e\u8ba4\u6e05\u9664\u8ba4\u8bc1\u72b6\u6001", (String)"\u786e\u5b9a\u6e05\u9664", (String)"\u53d6\u6d88", (Icon)Messages.getWarningIcon());
            if (result == 0) {
                this.clearAllAuthenticationStatus();
                Messages.showInfoMessage((String)"\u6240\u6709\u8ba4\u8bc1\u72b6\u6001\u5df2\u6e05\u9664\uff01\n\n\u73b0\u5728\u53ef\u4ee5\u91cd\u65b0\u6fc0\u6d3b\u8bd5\u7528\u6216\u8fdb\u884c\u6b63\u5f0f\u9a8c\u8bc1\u3002", (String)"\u6e05\u9664\u6210\u529f");
            }
        });
    }

    private void loadCurrentSettings() {
        try {
            SessionId sessionIdInstance = SessionId.INSTANCE;
            String currentSessionId = sessionIdInstance.getSessionId();
            String source = sessionIdInstance.getSessionIdSource();
            this.sessionIdField.setText(currentSessionId);
            this.sessionIdSourceLabel.setText("\u6765\u6e90: " + this.getSourceDescription(source));
            this.originalSessionId = currentSessionId;
            this.modified = false;
            LOG.info("\u52a0\u8f7d\u5f53\u524dSessionId\u914d\u7f6e: " + currentSessionId + " (\u6765\u6e90: " + source + ")");
        }
        catch (Exception e) {
            LOG.error("\u52a0\u8f7dSessionId\u914d\u7f6e\u5931\u8d25", (Throwable)e);
            this.sessionIdField.setText("\u52a0\u8f7d\u5931\u8d25");
            this.sessionIdSourceLabel.setText("\u6765\u6e90: \u672a\u77e5");
        }
    }

    private String getSourceDescription(String source) {
        switch (source) {
            case "TrialSession": {
                return "\u8bd5\u7528\u4f1a\u8bdd (\u5269\u4f59 " + this.trialManager.getRemainingDays() + " \u5929)";
            }
            case "PermanentInstallationID": {
                return "\u6c38\u4e45\u5b89\u88c5ID";
            }
            case "PropertiesComponent": {
                return "\u5df2\u4fdd\u5b58\u7684\u914d\u7f6e";
            }
            case "Generated": {
                return "\u81ea\u52a8\u751f\u6210";
            }
        }
        return source;
    }

    private void generateNewSessionId() {
        block5: {
            try {
                String newSessionId = SessionId.INSTANCE.resetSessionId();
                this.sessionIdField.setText(newSessionId);
                this.sessionIdSourceLabel.setText("\u6765\u6e90: " + this.getSourceDescription("PropertiesComponent"));
                this.modified = true;
                LOG.info("\u751f\u6210\u65b0\u7684SessionId: " + newSessionId);
                try {
                    SessionIdReplacer replacer = new SessionIdReplacer();
                    boolean success = replacer.replaceSessionIdClass();
                    if (success) {
                        Messages.showInfoMessage((String)("\u65b0\u7684SessionId\u5df2\u751f\u6210\u5e76\u7acb\u5373\u751f\u6548\uff01\n\n" + newSessionId), (String)"SessionId\u751f\u6210\u6210\u529f");
                        LOG.info("SessionId\u66ff\u6362\u6210\u529f\uff0c\u65b0SessionId\u5df2\u751f\u6548");
                        break block5;
                    }
                    Messages.showWarningDialog((String)("\u65b0\u7684SessionId\u5df2\u751f\u6210\u5e76\u4fdd\u5b58\uff0c\u4f46\u66ff\u6362\u5931\u8d25\u3002\n\nSessionId: " + newSessionId + "\n\n\u8bf7\u91cd\u542fIDE\u4ee5\u786e\u4fdd\u65b0SessionId\u751f\u6548\u3002"), (String)"SessionId\u751f\u6210\u6210\u529f\uff0c\u4f46\u66ff\u6362\u5931\u8d25");
                    LOG.warn("SessionId\u751f\u6210\u6210\u529f\uff0c\u4f46\u66ff\u6362\u5931\u8d25");
                }
                catch (Exception replaceException) {
                    LOG.error("\u8c03\u7528SessionIdReplacer\u5931\u8d25", (Throwable)replaceException);
                    Messages.showWarningDialog((String)("\u65b0\u7684SessionId\u5df2\u751f\u6210\u5e76\u4fdd\u5b58\uff0c\u4f46\u66ff\u6362\u8fc7\u7a0b\u51fa\u73b0\u5f02\u5e38\u3002\n\nSessionId: " + newSessionId + "\n\n\u8bf7\u91cd\u542fIDE\u4ee5\u786e\u4fdd\u65b0SessionId\u751f\u6548\u3002\n\n\u9519\u8bef\u8be6\u60c5: " + replaceException.getMessage()), (String)"SessionId\u751f\u6210\u6210\u529f\uff0c\u4f46\u66ff\u6362\u5f02\u5e38");
                }
            }
            catch (Exception e) {
                LOG.error("\u751f\u6210SessionId\u5931\u8d25", (Throwable)e);
                Messages.showErrorDialog((String)("\u751f\u6210SessionId\u5931\u8d25: " + e.getMessage()), (String)"\u9519\u8bef");
            }
        }
    }

    private void copySessionIdToClipboard() {
        try {
            String sessionId = this.sessionIdField.getText();
            if (sessionId != null && !sessionId.trim().isEmpty()) {
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), null);
                Messages.showInfoMessage((String)"SessionId\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f\uff01", (String)"\u590d\u5236\u6210\u529f");
                LOG.info("SessionId\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f");
            }
        }
        catch (Exception e) {
            LOG.error("\u590d\u5236SessionId\u5931\u8d25", (Throwable)e);
            Messages.showErrorDialog((String)("\u590d\u5236SessionId\u5931\u8d25: " + e.getMessage()), (String)"\u9519\u8bef");
        }
    }

    public JPanel getMainPanel() {
        return this.mainPanel;
    }

    public boolean isModified() {
        return this.modified;
    }

    public void apply() {
        this.modified = false;
        this.originalSessionId = this.sessionIdField.getText();
        LOG.info("\u914d\u7f6e\u5df2\u5e94\u7528");
    }

    public void reset() {
        this.loadCurrentSettings();
        LOG.info("\u914d\u7f6e\u5df2\u91cd\u7f6e");
    }

    private void activateTrialMode() {
        this.trialActivateButton.setEnabled(false);
        this.trialActivateButton.setText("\u6fc0\u6d3b\u4e2d...");
        this.trialStatusLabel.setText("\u6b63\u5728\u6fc0\u6d3b\u8bd5\u7528\u6a21\u5f0f\uff0c\u8bf7\u7a0d\u5019...");
        this.trialStatusLabel.setForeground(Color.BLUE);
        SwingUtilities.invokeLater(() -> new Thread(() -> {
            try {
                boolean isValid = this.authManager.activateTrialMode();
                SwingUtilities.invokeLater(() -> {
                    if (isValid) {
                        this.trialManager.activateTrialCode("1024");
                        this.trialStatusLabel.setText("\u8bd5\u7528\u6fc0\u6d3b\u6210\u529f\uff01\u5269\u4f59 " + this.trialManager.getRemainingDays() + " \u5929");
                        this.trialStatusLabel.setForeground(Color.GREEN);
                        this.trialActivateButton.setEnabled(false);
                        this.trialActivateButton.setText("\u5df2\u6fc0\u6d3b");
                        this.refreshUIAfterAuthentication();
                        Messages.showInfoMessage((String)"\u8bd5\u7528\u6a21\u5f0f\u6fc0\u6d3b\u6210\u529f\uff01\n\n\u60a8\u73b0\u5728\u53ef\u4ee5\u4f7f\u75285\u5929\u8bd5\u7528\u671f\u3002\n\u9a8c\u8bc1\u7801\uff1a1024", (String)"\u8bd5\u7528\u6fc0\u6d3b\u6210\u529f");
                        LOG.info("\u8bd5\u7528\u6a21\u5f0f\u6fc0\u6d3b\u6210\u529f\uff0c\u4f7f\u7528\u56fa\u5b9a\u9a8c\u8bc1\u7801\uff1a1024");
                    } else {
                        this.trialActivateButton.setEnabled(true);
                        this.trialActivateButton.setText("\u6fc0\u6d3b5\u5929\u8bd5\u7528");
                        this.trialStatusLabel.setText("\u8bd5\u7528\u6fc0\u6d3b\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5");
                        this.trialStatusLabel.setForeground(Color.RED);
                        Messages.showErrorDialog((String)"\u8bd5\u7528\u6a21\u5f0f\u6fc0\u6d3b\u5931\u8d25\uff01\n\n\u8bf7\u91cd\u8bd5\u6216\u8054\u7cfb\u6280\u672f\u652f\u6301\u3002", (String)"\u6fc0\u6d3b\u5931\u8d25");
                        LOG.warn("\u8bd5\u7528\u6a21\u5f0f\u6fc0\u6d3b\u5931\u8d25");
                    }
                });
            }
            catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    this.trialActivateButton.setEnabled(true);
                    this.trialActivateButton.setText("\u6fc0\u6d3b5\u5929\u8bd5\u7528");
                    this.trialStatusLabel.setText("\u6fc0\u6d3b\u5931\u8d25\uff0c\u7cfb\u7edf\u9519\u8bef");
                    this.trialStatusLabel.setForeground(Color.RED);
                    Messages.showErrorDialog((String)("\u6fc0\u6d3b\u8fc7\u7a0b\u4e2d\u53d1\u751f\u7cfb\u7edf\u9519\u8bef\uff01\n\n\u9519\u8bef\u8be6\u60c5: " + e.getMessage()), (String)"\u7cfb\u7edf\u9519\u8bef");
                    LOG.error("\u8bd5\u7528\u6a21\u5f0f\u6fc0\u6d3b\u5931\u8d25", (Throwable)e);
                });
            }
        }).start());
    }

    private void verifyFormalCode() {
        String inputCode = this.formalCodeField.getText().trim();
        if (inputCode.isEmpty()) {
            Messages.showWarningDialog((String)"\u8bf7\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\uff01", (String)"\u9a8c\u8bc1\u7801\u4e3a\u7a7a");
            return;
        }
        this.formalVerifyButton.setEnabled(false);
        this.formalVerifyButton.setText("\u9a8c\u8bc1\u4e2d...");
        this.formalStatusLabel.setText("\u6b63\u5728\u9a8c\u8bc1\u6b63\u5f0f\u9a8c\u8bc1\u7801\uff0c\u8bf7\u7a0d\u5019...");
        this.formalStatusLabel.setForeground(Color.BLUE);
        SwingUtilities.invokeLater(() -> new Thread(() -> {
            try {
                boolean isValid = this.authManager.verifyFormalCode(inputCode);
                SwingUtilities.invokeLater(() -> {
                    this.formalVerifyButton.setEnabled(true);
                    this.formalVerifyButton.setText("\u6b63\u5f0f\u9a8c\u8bc1");
                    if (isValid) {
                        this.authManager.saveFormalVerificationStatus(true);
                        this.updateTrialSectionVisibility();
                        this.loadAuthenticationStatus();
                        this.refreshUIAfterAuthentication();
                        Messages.showInfoMessage((String)"\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9a8c\u8bc1\u6210\u529f\uff01\n\n\u529f\u80fd\u5df2\u6c38\u4e45\u89e3\u9501\uff0c\u73b0\u5728\u53ef\u4ee5\u4f7f\u7528SessionId\u529f\u80fd\u3002", (String)"\u6b63\u5f0f\u9a8c\u8bc1\u6210\u529f");
                        LOG.info("\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9a8c\u8bc1\u6210\u529f\uff0c\u9a8c\u8bc1\u7801: " + inputCode);
                    } else {
                        this.formalStatusLabel.setText("\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9519\u8bef\uff0c\u8bf7\u91cd\u65b0\u8f93\u5165");
                        this.formalStatusLabel.setForeground(Color.RED);
                        this.formalCodeField.selectAll();
                        Messages.showErrorDialog((String)"\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9519\u8bef\uff01\n\n\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u6b63\u5f0f\u9a8c\u8bc1\u7801\u3002", (String)"\u9a8c\u8bc1\u5931\u8d25");
                        LOG.warn("\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9a8c\u8bc1\u5931\u8d25\uff0c\u8f93\u5165\u7684\u9a8c\u8bc1\u7801: " + inputCode);
                    }
                });
            }
            catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    this.formalVerifyButton.setEnabled(true);
                    this.formalVerifyButton.setText("\u6b63\u5f0f\u9a8c\u8bc1");
                    this.formalStatusLabel.setText("\u9a8c\u8bc1\u5931\u8d25\uff0c\u7f51\u7edc\u9519\u8bef");
                    this.formalStatusLabel.setForeground(Color.RED);
                    Messages.showErrorDialog((String)("\u9a8c\u8bc1\u8fc7\u7a0b\u4e2d\u53d1\u751f\u7f51\u7edc\u9519\u8bef\uff01\n\n\u9519\u8bef\u8be6\u60c5: " + e.getMessage()), (String)"\u7f51\u7edc\u9519\u8bef");
                    LOG.error("\u6b63\u5f0f\u9a8c\u8bc1\u7801API\u8c03\u7528\u5931\u8d25", (Throwable)e);
                });
            }
        }).start());
    }

    private void showAuthenticationRequiredMessage() {
        Messages.showWarningDialog((String)"\u8bf7\u5148\u8fdb\u884c\u8bd5\u7528\u9a8c\u8bc1\u6216\u6b63\u5f0f\u9a8c\u8bc1\u4ee5\u542f\u7528SessionId\u529f\u80fd\uff01", (String)"\u9700\u8981\u8ba4\u8bc1");
    }

    private void openFormalCodeHelpPage() {
        try {
            String helpUrl = "https://docs.qq.com/doc/DV21nUEVTSVZOTG96";
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                if (desktop.isSupported(Desktop.Action.BROWSE)) {
                    desktop.browse(new URI(helpUrl));
                    LOG.info("\u5df2\u6253\u5f00\u6b63\u5f0f\u9a8c\u8bc1\u7801\u5e2e\u52a9\u9875\u9762: " + helpUrl);
                } else {
                    this.showBrowserNotSupportedMessage(helpUrl);
                }
            } else {
                this.showBrowserNotSupportedMessage(helpUrl);
            }
        }
        catch (Exception e) {
            LOG.error("\u6253\u5f00\u5e2e\u52a9\u9875\u9762\u5931\u8d25", (Throwable)e);
            Messages.showErrorDialog((String)("\u65e0\u6cd5\u6253\u5f00\u5e2e\u52a9\u9875\u9762\uff01\n\n\u8bf7\u624b\u52a8\u8bbf\u95ee\u4ee5\u4e0b\u94fe\u63a5\u83b7\u53d6\u6b63\u5f0f\u9a8c\u8bc1\u7801\uff1a\nhttps://docs.qq.com/doc/DV21nUEVTSVZOTG96\n\n\u9519\u8bef\u8be6\u60c5: " + e.getMessage()), (String)"\u6253\u5f00\u5931\u8d25");
        }
    }

    private void openTutorialPage() {
        try {
            String tutorialUrl = "https://docs.qq.com/doc/DV2xqSnJGb2VrYUpt";
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                if (desktop.isSupported(Desktop.Action.BROWSE)) {
                    desktop.browse(new URI(tutorialUrl));
                    LOG.info("\u5df2\u6253\u5f00\u4f7f\u7528\u6559\u7a0b\u9875\u9762: " + tutorialUrl);
                } else {
                    this.showBrowserNotSupportedMessage(tutorialUrl);
                }
            } else {
                this.showBrowserNotSupportedMessage(tutorialUrl);
            }
        }
        catch (Exception e) {
            LOG.error("\u6253\u5f00\u6559\u7a0b\u9875\u9762\u5931\u8d25", (Throwable)e);
            Messages.showErrorDialog((String)("\u65e0\u6cd5\u6253\u5f00\u6559\u7a0b\u9875\u9762\uff01\n\n\u8bf7\u624b\u52a8\u8bbf\u95ee\u4ee5\u4e0b\u94fe\u63a5\u67e5\u770b\u4f7f\u7528\u6559\u7a0b\uff1a\nhttps://docs.qq.com/doc/DV2xqSnJGb2VrYUpt\n\n\u9519\u8bef\u8be6\u60c5: " + e.getMessage()), (String)"\u6253\u5f00\u5931\u8d25");
        }
    }

    private void showBrowserNotSupportedMessage(String url) {
        Messages.showInfoMessage((String)("\u7cfb\u7edf\u4e0d\u652f\u6301\u81ea\u52a8\u6253\u5f00\u6d4f\u89c8\u5668\uff01\n\n\u8bf7\u624b\u52a8\u590d\u5236\u4ee5\u4e0b\u94fe\u63a5\u5230\u6d4f\u89c8\u5668\u4e2d\u8bbf\u95ee\uff1a\n" + url), (String)"\u624b\u52a8\u8bbf\u95ee");
        LOG.warn("\u7cfb\u7edf\u4e0d\u652f\u6301\u81ea\u52a8\u6253\u5f00\u6d4f\u89c8\u5668\uff0c\u9700\u8981\u624b\u52a8\u8bbf\u95ee: " + url);
    }

    private void updateTrialSectionVisibility() {
        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        if (this.trialPanel != null) {
            this.trialPanel.setVisible(!isFormallyVerified);
            this.trialStatusLabel.setVisible(!isFormallyVerified);
        }
        LOG.info("\u8bd5\u7528\u90e8\u5206\u53ef\u89c1\u6027\u5df2\u66f4\u65b0: " + (!isFormallyVerified ? "\u663e\u793a" : "\u9690\u85cf"));
    }

    private void updateButtonStates() {
        boolean hasAuth = this.authManager.hasValidAuthentication();
        this.generateButton.setEnabled(hasAuth);
        this.copyButton.setEnabled(hasAuth);
        if (hasAuth) {
            this.generateButton.setToolTipText("\u751f\u6210\u65b0\u7684SessionId");
            this.copyButton.setToolTipText("\u590d\u5236SessionId\u5230\u526a\u8d34\u677f");
        } else {
            this.generateButton.setToolTipText("\u8bf7\u5148\u8fdb\u884c\u8bd5\u7528\u6216\u6b63\u5f0f\u9a8c\u8bc1");
            this.copyButton.setToolTipText("\u8bf7\u5148\u8fdb\u884c\u8bd5\u7528\u6216\u6b63\u5f0f\u9a8c\u8bc1");
        }
        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        boolean hasTrialCode = this.trialManager.hasTrialCode();
        this.trialActivateButton.setEnabled(!isFormallyVerified && !hasTrialCode);
        this.formalVerifyButton.setEnabled(!isFormallyVerified);
        this.formalCodeField.setEnabled(!isFormallyVerified);
        this.formalHelpButton.setEnabled(true);
        this.formalTutorialButton.setEnabled(true);
    }

    private void loadAuthenticationStatus() {
        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        boolean hasValidTrial = this.trialManager.hasValidTrialSession();
        boolean hasTrialCode = this.trialManager.hasTrialCode();
        if (isFormallyVerified) {
            this.formalStatusLabel.setText("\u5df2\u6b63\u5f0f\u9a8c\u8bc1\uff01\u529f\u80fd\u5df2\u6c38\u4e45\u89e3\u9501");
            this.formalStatusLabel.setForeground(Color.GREEN);
            this.formalCodeField.setText("\u5df2\u9a8c\u8bc1");
            this.formalCodeField.setEnabled(false);
            this.formalVerifyButton.setEnabled(false);
            this.formalVerifyButton.setText("\u5df2\u9a8c\u8bc1");
            this.formalHelpButton.setToolTipText("\u67e5\u770b\u6b63\u5f0f\u9a8c\u8bc1\u7801\u83b7\u53d6\u8bf4\u660e\uff08\u5df2\u9a8c\u8bc1\uff09");
            this.formalTutorialButton.setToolTipText("\u67e5\u770b\u8be6\u7ec6\u7684\u4f7f\u7528\u6559\u7a0b\uff08\u5df2\u9a8c\u8bc1\uff09");
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("\u5df2\u6b63\u5f0f\u9a8c\u8bc1");
            this.trialStatusLabel.setText("\u5df2\u6b63\u5f0f\u9a8c\u8bc1\uff0c\u8bd5\u7528\u529f\u80fd\u5df2\u7981\u7528");
            this.trialStatusLabel.setForeground(Color.GRAY);
            LOG.info("\u52a0\u8f7d\u5df2\u4fdd\u5b58\u7684\u8ba4\u8bc1\u72b6\u6001\uff1a\u5df2\u6b63\u5f0f\u9a8c\u8bc1");
        } else if (hasValidTrial) {
            int remainingDays = this.trialManager.getRemainingDays();
            this.trialStatusLabel.setText("\u8bd5\u7528\u5df2\u6fc0\u6d3b\uff0c\u5269\u4f59 " + remainingDays + " \u5929");
            this.trialStatusLabel.setForeground(Color.GREEN);
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("\u5df2\u6fc0\u6d3b");
            this.formalStatusLabel.setText("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("\u52a0\u8f7d\u5df2\u4fdd\u5b58\u7684\u8ba4\u8bc1\u72b6\u6001\uff1a\u8bd5\u7528\u671f\u5185\uff0c\u5269\u4f59 " + remainingDays + " \u5929");
        } else if (hasTrialCode) {
            this.trialStatusLabel.setText("\u8bd5\u7528\u671f\u5df2\u8fc7\u671f\uff0c\u8bf7\u4f7f\u7528\u6b63\u5f0f\u9a8c\u8bc1\u7801");
            this.trialStatusLabel.setForeground(Color.RED);
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("\u5df2\u8fc7\u671f");
            this.formalStatusLabel.setText("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("\u52a0\u8f7d\u5df2\u4fdd\u5b58\u7684\u8ba4\u8bc1\u72b6\u6001\uff1a\u8bd5\u7528\u671f\u5df2\u8fc7\u671f");
        } else {
            this.trialStatusLabel.setText("\u70b9\u51fb\u6309\u94ae\u6fc0\u6d3b5\u5929\u8bd5\u7528\u671f");
            this.trialStatusLabel.setForeground(Color.ORANGE);
            this.formalStatusLabel.setText("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("\u52a0\u8f7d\u5df2\u4fdd\u5b58\u7684\u8ba4\u8bc1\u72b6\u6001\uff1a\u672a\u8ba4\u8bc1");
        }
        this.updateButtonStates();
        this.updateTrialSectionVisibility();
    }

    private void refreshUIAfterAuthentication() {
        this.updateTrialSectionVisibility();
        this.updateButtonStates();
        this.loadCurrentSettings();
        this.mainPanel.revalidate();
        this.mainPanel.repaint();
        LOG.info("\u8ba4\u8bc1\u6210\u529f\u540eUI\u72b6\u6001\u5df2\u5237\u65b0");
    }

    public void clearAllAuthenticationStatus() {
        this.authManager.clearFormalVerificationStatus();
        this.trialManager.clearTrialData();
        this.trialActivateButton.setEnabled(true);
        this.trialActivateButton.setText("\u6fc0\u6d3b5\u5929\u8bd5\u7528");
        this.trialStatusLabel.setText("\u70b9\u51fb\u6309\u94ae\u6fc0\u6d3b5\u5929\u8bd5\u7528\u671f");
        this.trialStatusLabel.setForeground(Color.ORANGE);
        this.formalCodeField.setText("");
        this.formalCodeField.setEnabled(true);
        this.formalVerifyButton.setEnabled(true);
        this.formalVerifyButton.setText("\u6b63\u5f0f\u9a8c\u8bc1");
        this.formalHelpButton.setToolTipText("\u70b9\u51fb\u67e5\u770b\u5982\u4f55\u83b7\u53d6\u6b63\u5f0f\u9a8c\u8bc1\u7801\u7684\u8be6\u7ec6\u8bf4\u660e");
        this.formalTutorialButton.setToolTipText("\u70b9\u51fb\u67e5\u770b\u8be6\u7ec6\u7684\u4f7f\u7528\u6559\u7a0b");
        this.formalStatusLabel.setText("\u8f93\u5165\u6b63\u5f0f\u9a8c\u8bc1\u7801\u6c38\u4e45\u89e3\u9501\u529f\u80fd");
        this.formalStatusLabel.setForeground(Color.ORANGE);
        this.updateButtonStates();
        this.loadCurrentSettings();
        this.updateTrialSectionVisibility();
        this.mainPanel.revalidate();
        this.mainPanel.repaint();
        LOG.info("\u5df2\u6e05\u9664\u6240\u6709\u8ba4\u8bc1\u72b6\u6001");
    }

    private void startStatusUpdateScheduler() {
        if (this.statusUpdateScheduler != null && !this.statusUpdateScheduler.isShutdown()) {
            return;
        }
        this.statusUpdateScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "AugmentConfigPanel-StatusUpdater");
            thread.setDaemon(true);
            return thread;
        });
        this.statusUpdateScheduler.scheduleWithFixedDelay(() -> {
            if (this.isDisposed) {
                return;
            }
            try {
                SwingUtilities.invokeLater(() -> {
                    if (!this.isDisposed) {
                        this.updateAuthenticationStatusSilently();
                    }
                });
            }
            catch (Exception e) {
                LOG.warn("\u540e\u53f0\u72b6\u6001\u66f4\u65b0\u5931\u8d25", (Throwable)e);
            }
        }, 1L, 1L, TimeUnit.HOURS);
        LOG.info("\u540e\u53f0\u72b6\u6001\u66f4\u65b0\u8c03\u5ea6\u5668\u5df2\u542f\u52a8\uff0c\u6bcf1\u5c0f\u65f6\u68c0\u67e5\u4e00\u6b21");
    }

    private void updateAuthenticationStatusSilently() {
        try {
            boolean previousAuthState = this.generateButton.isEnabled();
            boolean isFormallyVerified = this.authManager.isFormallyVerified();
            boolean hasValidTrial = this.trialManager.hasValidTrialSession();
            boolean hasTrialCode = this.trialManager.hasTrialCode();
            boolean currentAuthState = this.authManager.hasValidAuthentication();
            if (previousAuthState != currentAuthState) {
                LOG.info("\u68c0\u6d4b\u5230\u8ba4\u8bc1\u72b6\u6001\u53d8\u5316: " + previousAuthState + " -> " + currentAuthState);
                this.updateButtonStates();
                this.loadCurrentSettings();
                if (!isFormallyVerified) {
                    if (hasValidTrial) {
                        int remainingDays = this.trialManager.getRemainingDays();
                        this.trialStatusLabel.setText("\u8bd5\u7528\u5df2\u6fc0\u6d3b\uff0c\u5269\u4f59 " + remainingDays + " \u5929");
                        this.trialStatusLabel.setForeground(Color.GREEN);
                    } else if (hasTrialCode && !this.trialStatusLabel.getText().contains("\u5df2\u8fc7\u671f")) {
                        this.trialStatusLabel.setText("\u8bd5\u7528\u671f\u5df2\u8fc7\u671f\uff0c\u8bf7\u4f7f\u7528\u6b63\u5f0f\u9a8c\u8bc1\u7801");
                        this.trialStatusLabel.setForeground(Color.RED);
                        this.trialActivateButton.setEnabled(false);
                        this.trialActivateButton.setText("\u5df2\u8fc7\u671f");
                        LOG.info("\u8bd5\u7528\u671f\u5df2\u8fc7\u671f\uff0cUI\u72b6\u6001\u5df2\u66f4\u65b0");
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.warn("\u9759\u9ed8\u66f4\u65b0\u8ba4\u8bc1\u72b6\u6001\u5931\u8d25", (Throwable)e);
        }
    }

    public void dispose() {
        this.isDisposed = true;
        if (this.statusUpdateScheduler != null && !this.statusUpdateScheduler.isShutdown()) {
            this.statusUpdateScheduler.shutdown();
            try {
                if (!this.statusUpdateScheduler.awaitTermination(5L, TimeUnit.SECONDS)) {
                    this.statusUpdateScheduler.shutdownNow();
                }
                LOG.info("\u540e\u53f0\u72b6\u6001\u66f4\u65b0\u8c03\u5ea6\u5668\u5df2\u505c\u6b62");
            }
            catch (InterruptedException e) {
                this.statusUpdateScheduler.shutdownNow();
                Thread.currentThread().interrupt();
                LOG.warn("\u505c\u6b62\u540e\u53f0\u72b6\u6001\u66f4\u65b0\u8c03\u5ea6\u5668\u65f6\u88ab\u4e2d\u65ad", (Throwable)e);
            }
        }
    }
}

