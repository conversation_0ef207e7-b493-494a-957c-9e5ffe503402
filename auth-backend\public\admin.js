// 全局变量
let currentPage = 'auth-codes';
let authCodesData = [];

// 检查登录状态
async function checkLoginStatus() {
    try {
        const response = await fetch('/api/admin/check', {
            credentials: 'include'
        });
        const result = await response.json();

        if (result.success && result.data.isLoggedIn) {
            // 已登录，显示用户名并初始化应用
            document.getElementById('username-display').textContent = result.data.username;
            initializeApp();
        } else {
            // 未登录，跳转到登录页面
            window.location.href = '/login';
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
        window.location.href = '/login';
    }
}

// 登出功能
async function logout() {
    try {
        const response = await fetch('/api/admin/logout', {
            method: 'POST',
            credentials: 'include'
        });
        const result = await response.json();

        if (result.success) {
            window.location.href = '/login';
        } else {
            showError('登出失败: ' + result.message);
        }
    } catch (error) {
        console.error('登出失败:', error);
        showError('登出失败，请稍后重试');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkLoginStatus();
});

// 初始化应用
function initializeApp() {
    // 绑定菜单点击事件
    bindMenuEvents();
    
    // 绑定表单事件
    bindFormEvents();
    
    // 加载默认页面
    loadAuthCodes();
}

// 绑定菜单事件
function bindMenuEvents() {
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            switchPage(page);
        });
    });
}

// 切换页面
function switchPage(page) {
    // 更新菜单状态
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-page="${page}"]`).classList.add('active');
    
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(pageEl => {
        pageEl.classList.add('hidden');
    });
    
    // 显示目标页面
    document.getElementById(`${page}-page`).classList.remove('hidden');
    
    currentPage = page;
    
    // 根据页面加载数据
    switch(page) {
        case 'auth-codes':
            loadAuthCodes();
            break;
        case 'generate':
            resetGenerateForm();
            break;
        case 'usage-logs':
            loadUsageLogs();
            break;
        case 'announcements':
            loadAnnouncements();
            break;
        case 'update-manager':
            loadUpdateManager();
            break;
        case 'augment-accounts':
            loadAugmentAccounts();
            break;
        case 'settings':
            // loadSettings();
            break;
    }
}

// 绑定表单事件
function bindFormEvents() {
    // 生成授权码表单
    const generateForm = document.getElementById('generate-form');
    generateForm.addEventListener('submit', function(e) {
        e.preventDefault();
        generateAuthCode();
    });
    
    // 搜索输入框回车事件
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadAuthCodes();
        }
    });
}

// 加载授权码列表
async function loadAuthCodes() {
    try {
        const container = document.getElementById('auth-codes-container');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                正在加载授权码列表...
            </div>
        `;
        
        const search = document.getElementById('search-input').value;
        const status = document.getElementById('status-filter').value;
        
        const params = new URLSearchParams({
            page: 1,
            limit: 50,
            search: search,
            status: status
        });
        
        const response = await fetch(`/api/auth/list?${params}`, {
            credentials: 'include'
        });
        const result = await response.json();
        
        if (result.success) {
            authCodesData = result.data.list;
            renderAuthCodesTable(result.data.list);
        } else {
            showError('加载授权码列表失败: ' + result.message);
        }
        
    } catch (error) {
        console.error('加载授权码列表错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 渲染授权码表格
function renderAuthCodesTable(authCodes) {
    const container = document.getElementById('auth-codes-container');
    
    if (authCodes.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #666;">
                <p>暂无授权码数据</p>
                <button class="btn btn-primary" onclick="switchPage('generate')">
                    ➕ 生成第一个授权码
                </button>
            </div>
        `;
        return;
    }
    
    let tableHTML = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>授权码</th>
                        <th>名称</th>
                        <th>状态</th>
                        <th>使用情况</th>
                        <th>设备绑定</th>
                        <th>过期时间</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    authCodes.forEach(code => {
        // 重新计算实际状态
        let currentStatus = code.status;
        let isExpiredNow = false;
        let timeRemaining = '';

        // 1. 首先检查是否被禁用
        if (code.status === 'disabled') {
            currentStatus = 'disabled';
        }
        // 2. 检查是否从未使用过（未激活）
        else if (code.used_count === 0) {
            // 即使数据库状态是active，如果从未使用过就是未激活
            currentStatus = 'inactive';
        }
        // 3. 检查时间是否过期
        else if (code.expire_date) {
            const expireTime = new Date(code.expire_date);
            const now = new Date();
            const timeDiff = expireTime.getTime() - now.getTime();
            const secondsRemaining = Math.floor(timeDiff / 1000);

            if (now > expireTime) {
                currentStatus = 'expired';
                isExpiredNow = true;
            } else if (secondsRemaining <= 60 && secondsRemaining > 0) {
                // 剩余时间少于1分钟时显示倒计时
                timeRemaining = ` (${secondsRemaining}秒后过期)`;
                currentStatus = 'active';
            } else {
                currentStatus = 'active';
            }
        }
        // 4. 其他情况为激活状态
        else {
            currentStatus = 'active';
        }

        // 5. 最后检查使用次数是否已用完（这个检查应该在最后，因为可能覆盖其他状态）
        if (currentStatus === 'active' && code.max_uses > 0 && code.used_count >= code.max_uses) {
            currentStatus = 'expired';
            isExpiredNow = true;
        }

        const statusClass = `status-${currentStatus}`;
        const statusText = getStatusText(currentStatus);
        const expireDate = code.expire_date ? formatDate(code.expire_date) : '永不过期';
        const createDate = formatDate(code.created_at);
        const usageText = code.max_uses > 0 ? `${code.used_count}/${code.max_uses}` : `${code.used_count}/无限制`;

        // 生成设备绑定信息
        let deviceBindingInfo = '';
        if (code.device_binding_enabled) {
            const deviceCount = code.deviceCount || 0;
            const maxDevices = code.max_devices || 1;
            deviceBindingInfo = `
                <div style="font-size: 12px;">
                    <div style="margin-bottom: 4px;">
                        <span style="color: #28a745;">🔒 已启用</span>
                        <span style="color: #666;">(${deviceCount}/${maxDevices})</span>
                    </div>
                    ${generateDeviceList(code.boundDevices || [])}
                </div>
            `;
        } else {
            deviceBindingInfo = '<span style="color: #6c757d;">🔓 已关闭</span>';
        }

        // 如果刚刚过期，添加特殊样式
        const rowClass = isExpiredNow ? 'style="background-color: #fff3cd;"' : '';
        const expiredWarning = isExpiredNow ? ' <span style="color: #856404; font-size: 12px;">(刚过期)</span>' : timeRemaining;

        tableHTML += `
            <tr ${rowClass}>
                <td>
                    <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-family: monospace;">
                        ${code.code}
                    </code>
                    <button class="btn" style="margin-left: 10px; padding: 2px 8px; font-size: 12px;"
                            onclick="copyCode('${code.code}')">📋</button>
                </td>
                <td>${code.name}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span>${expiredWarning}</td>
                <td>${usageText}</td>
                <td style="max-width: 200px;">${deviceBindingInfo}</td>
                <td>${expireDate}</td>
                <td>${createDate}</td>
                <td>
                    <button class="btn btn-danger" style="padding: 4px 12px; font-size: 12px;"
                            onclick="showDeleteConfirm(${code.id}, '${code.code}')">删除</button>
                </td>
            </tr>
        `;
    });
    
    tableHTML += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHTML;
}

// 生成授权码
async function generateAuthCode() {
    try {
        const expireType = document.getElementById('expire-type').value;
        let expireDate = null;
        let expireDays = 0;

        // 根据过期类型处理过期时间
        if (expireType === 'days') {
            expireDays = parseInt(document.getElementById('expire-days').value) || 30;
        } else if (expireType === 'datetime') {
            const datetimeValue = document.getElementById('expire-datetime').value;
            if (!datetimeValue) {
                showError('请选择过期时间');
                return;
            }

            const selectedDate = new Date(datetimeValue);
            const now = new Date();

            if (selectedDate <= now) {
                showError('过期时间必须大于当前时间');
                return;
            }

            expireDate = selectedDate.toISOString();
            expireDays = 0; // 使用具体时间时，天数设为0
        } else if (expireType === 'never') {
            expireDays = 0; // 永不过期
        }

        // 获取自定义授权码
        const customCodeEnabled = document.getElementById('custom-code-enable')?.checked;
        const customCodeValue = customCodeEnabled ? document.getElementById('custom-code').value : null;

        // 检查是否启用永久使用
        const unlimitedUses = document.getElementById('unlimited-uses')?.checked;
        const maxUses = unlimitedUses ? 0 : (parseInt(document.getElementById('max-uses').value) || 1);

        const formData = {
            name: document.getElementById('auth-name').value,
            maxUses: maxUses,
            maxDevices: parseInt(document.getElementById('max-devices').value) || 1,
            expireDays: expireDays,
            expireDate: expireDate,
            notes: document.getElementById('notes').value,
            customCode: customCodeValue,
            deviceBindingEnabled: document.getElementById('device-binding')?.checked !== false
        };

        if (!formData.name.trim()) {
            showError('请输入授权码名称');
            return;
        }

        const response = await fetch('/api/auth/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showGenerateResult(result.data);
            showSuccess('授权码生成成功！');
        } else {
            showError('生成授权码失败: ' + result.message);
        }

    } catch (error) {
        console.error('生成授权码错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 显示生成结果
function showGenerateResult(data) {
    document.getElementById('generated-code').textContent = data.code;
    document.getElementById('generated-name').textContent = data.name;
    document.getElementById('generated-uses').textContent = data.maxUses === 0 ? '无限制' : data.maxUses;
    document.getElementById('generated-max-devices').textContent = data.maxDevices || 1;
    document.getElementById('generated-expire').textContent = data.expireDate || '永不过期';

    // 显示设备绑定状态
    const deviceInfo = data.deviceBindingEnabled
        ? '🔒 启用'
        : '🔓 已关闭';

    document.getElementById('generated-device-binding').textContent = deviceInfo;
    document.getElementById('generate-result').classList.remove('hidden');
}

// 切换永久使用选项
function toggleUnlimitedUses() {
    const unlimitedCheckbox = document.getElementById('unlimited-uses');
    const maxUsesInput = document.getElementById('max-uses');

    if (unlimitedCheckbox.checked) {
        maxUsesInput.disabled = true;
        maxUsesInput.style.opacity = '0.5';
        maxUsesInput.value = '';
        maxUsesInput.placeholder = '永久使用';
    } else {
        maxUsesInput.disabled = false;
        maxUsesInput.style.opacity = '1';
        maxUsesInput.value = '1';
        maxUsesInput.placeholder = '1';
    }
}

// 重置生成表单
function resetGenerateForm() {
    document.getElementById('generate-form').reset();
    document.getElementById('generate-result').classList.add('hidden');
    document.getElementById('max-uses').value = '1';
    document.getElementById('expire-days').value = '30';
    document.getElementById('expire-type').value = 'days';
    document.getElementById('unlimited-uses').checked = false;

    // 重置时间模式显示
    toggleExpireMode();
    toggleUnlimitedUses();
}

// 生成设备列表HTML
function generateDeviceList(devices) {
    if (!devices || devices.length === 0) {
        return '<div style="color: #999; font-size: 11px;">暂无绑定设备</div>';
    }

    return devices.map(device => {
        const deviceId = device.device_id.substring(0, 12) + '...'; // 显示前12位
        const firstUsed = formatDate(device.first_used_at);
        const lastUsed = formatDate(device.last_used_at);

        return `
            <div style="background: #f8f9fa; padding: 4px 6px; margin: 2px 0; border-radius: 3px; border-left: 3px solid #28a745;">
                <div style="font-weight: bold; color: #495057;">${device.device_name || '未知设备'}</div>
                <div style="color: #6c757d; font-size: 10px;">ID: ${deviceId}</div>
                <div style="color: #6c757d; font-size: 10px;">首次: ${firstUsed}</div>
                <div style="color: #6c757d; font-size: 10px;">最近: ${lastUsed}</div>
            </div>
        `;
    }).join('');
}

// 复制授权码到剪贴板
function copyCode(code) {
    // 尝试使用现代API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(code).then(() => {
            showSuccess('授权码已复制到剪贴板');
        }).catch(() => {
            fallbackCopyTextToClipboard(code);
        });
    } else {
        // 使用备用方法
        fallbackCopyTextToClipboard(code);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    try {
        // 创建临时文本区域
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 避免滚动到底部
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showSuccess('授权码已复制到剪贴板');
            } else {
                showError('复制失败，请手动复制');
            }
        } catch (err) {
            showError('复制失败，请手动复制');
        }

        document.body.removeChild(textArea);
    } catch (err) {
        showError('复制失败，请手动复制');
    }
}

// 复制到剪贴板（生成结果页面）
function copyToClipboard() {
    const code = document.getElementById('generated-code').textContent;
    copyCode(code);
}

// 删除授权码
async function deleteAuthCode(id) {
    console.log(`🗑️ 准备删除授权码，ID: ${id}`);
    console.log(`🔍 ID类型: ${typeof id}, 值: ${id}`);

    const confirmResult = confirm('确定要删除这个授权码吗？此操作不可恢复。');
    console.log(`🤔 confirm结果: ${confirmResult}`);

    if (!confirmResult) {
        console.log('🚫 用户取消删除操作');
        return;
    }

    console.log('✅ 用户确认删除操作');

    try {
        console.log(`📡 发送删除请求: DELETE /api/auth/delete/${id}`);

        const response = await fetch(`/api/auth/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log(`📡 删除响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            console.error(`❌ HTTP错误: ${response.status}`);
            showError(`删除失败: HTTP ${response.status}`);
            return;
        }

        const result = await response.json();
        console.log(`📡 删除响应内容:`, result);

        if (result.success) {
            console.log('✅ 删除成功');
            showSuccess('授权码删除成功');
            loadAuthCodes(); // 重新加载列表
        } else {
            console.error('❌ 删除失败:', result.message);
            showError('删除失败: ' + result.message);
        }

    } catch (error) {
        console.error('❌ 删除授权码网络错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 简化版删除函数（备用）
async function forceDeleteAuthCode(id) {
    console.log(`🚀 强制删除授权码，ID: ${id}`);

    try {
        const response = await fetch(`/api/auth/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log(`📡 响应状态: ${response.status}`);

        const result = await response.json();
        console.log(`📡 响应内容:`, result);

        if (result.success) {
            showSuccess('授权码删除成功');
            loadAuthCodes();
        } else {
            showError('删除失败: ' + result.message);
        }

    } catch (error) {
        console.error('删除错误:', error);
        showError('删除失败');
    }
}

// 显示删除确认对话框
function showDeleteConfirm(id, code) {
    console.log(`🗑️ 显示删除确认对话框，ID: ${id}, Code: ${code}`);

    // 创建自定义确认对话框
    const confirmHtml = `
        <div id="delete-confirm-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                text-align: center;
            ">
                <h3 style="color: #dc3545; margin-bottom: 15px;">⚠️ 确认删除</h3>
                <p style="margin-bottom: 20px;">确定要删除授权码 <strong>${code}</strong> 吗？</p>
                <p style="color: #666; font-size: 14px; margin-bottom: 25px;">此操作不可恢复！</p>
                <div>
                    <button onclick="confirmDelete(${id})" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        margin-right: 10px;
                        cursor: pointer;
                    ">确认删除</button>
                    <button onclick="cancelDelete()" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">取消</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', confirmHtml);
}

// 确认删除
function confirmDelete(id) {
    console.log(`✅ 用户确认删除，ID: ${id}`);

    // 移除确认对话框
    const modal = document.getElementById('delete-confirm-modal');
    if (modal) {
        modal.remove();
    }

    // 执行删除
    forceDeleteAuthCode(id);
}

// 取消删除
function cancelDelete() {
    console.log(`🚫 用户取消删除`);

    // 移除确认对话框
    const modal = document.getElementById('delete-confirm-modal');
    if (modal) {
        modal.remove();
    }
}

// ==================== 使用记录相关函数 ====================

// 加载使用记录
async function loadUsageLogs() {
    try {
        const container = document.getElementById('usage-logs-container');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                正在加载使用记录...
            </div>
        `;

        // 获取筛选条件
        const authCode = document.getElementById('filter-auth-code').value;
        const success = document.getElementById('filter-success').value;
        const startDate = document.getElementById('filter-start-date').value;
        const endDate = document.getElementById('filter-end-date').value;

        const params = new URLSearchParams({
            page: 1,
            limit: 50,
            authCode: authCode,
            success: success,
            startDate: startDate,
            endDate: endDate
        });

        const response = await fetch(`/api/auth/usage-logs?${params}`);
        const result = await response.json();

        if (result.success) {
            renderUsageStatistics(result.data.statistics);
            renderUsageLogsTable(result.data.list);
        } else {
            showError('加载使用记录失败: ' + result.message);
        }

    } catch (error) {
        console.error('加载使用记录错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 渲染统计信息
function renderUsageStatistics(stats) {
    document.getElementById('stat-total').textContent = stats.total_requests || 0;
    document.getElementById('stat-success').textContent = stats.successful_requests || 0;
    document.getElementById('stat-failed').textContent = stats.failed_requests || 0;
    document.getElementById('stat-codes').textContent = stats.unique_codes || 0;
    document.getElementById('stat-ips').textContent = stats.unique_ips || 0;
}

// 渲染使用记录表格
function renderUsageLogsTable(logs) {
    const container = document.getElementById('usage-logs-container');

    if (logs.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #666;">
                <p>暂无使用记录</p>
            </div>
        `;
        return;
    }

    let tableHTML = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>授权码</th>
                        <th>客户端IP</th>
                        <th>使用时间</th>
                        <th>验证状态</th>
                        <th>错误信息</th>
                        <th>客户端信息</th>
                    </tr>
                </thead>
                <tbody>
    `;

    logs.forEach(log => {
        const statusClass = log.success ? 'status-active' : 'status-disabled';
        const statusText = log.success ? '成功' : '失败';
        const usedTime = formatDate(log.used_at);
        const errorMessage = log.error_message || '-';
        const clientInfo = log.client_info ? JSON.parse(log.client_info) : {};

        // 处理桌面应用的客户端信息显示
        let clientInfoDisplay = '-';
        if (clientInfo.appType === 'desktop') {
            clientInfoDisplay = `${clientInfo.osName || 'Unknown'} ${clientInfo.osVersion || ''} (桌面应用)`;
        } else if (clientInfo.userAgent) {
            clientInfoDisplay = clientInfo.userAgent.substring(0, 50) + '...';
        }

        tableHTML += `
            <tr>
                <td>
                    <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-family: monospace;">
                        ${log.auth_code}
                    </code>
                </td>
                <td>${log.client_ip || '-'}</td>
                <td>${usedTime}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${errorMessage}">
                    ${errorMessage}
                </td>
                <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;" title="${clientInfoDisplay}">
                    ${clientInfoDisplay}
                </td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHTML;
}

// 清除筛选条件
function clearFilters() {
    document.getElementById('filter-auth-code').value = '';
    document.getElementById('filter-success').value = '';
    document.getElementById('filter-start-date').value = '';
    document.getElementById('filter-end-date').value = '';
    loadUsageLogs();
}

// 显示清空记录确认对话框
function showClearLogsModal() {
    const confirmHtml = `
        <div id="clear-logs-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                max-width: 500px;
                text-align: center;
            ">
                <h3 style="color: #f57c00; margin-bottom: 20px;">🗑️ 清空使用记录</h3>
                <p style="margin-bottom: 20px;">请选择清空方式：</p>

                <div style="margin-bottom: 25px;">
                    <button onclick="clearUsageLogs(0)" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 5px;
                        margin: 5px;
                        cursor: pointer;
                        width: 120px;
                    ">清空全部</button>

                    <button onclick="clearUsageLogs(7)" style="
                        background: #fd7e14;
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 5px;
                        margin: 5px;
                        cursor: pointer;
                        width: 120px;
                    ">清空7天前</button>

                    <button onclick="clearUsageLogs(30)" style="
                        background: #ffc107;
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 5px;
                        margin: 5px;
                        cursor: pointer;
                        width: 120px;
                    ">清空30天前</button>
                </div>

                <div>
                    <button onclick="closeClearLogsModal()" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">取消</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', confirmHtml);
}

// 关闭清空记录对话框
function closeClearLogsModal() {
    const modal = document.getElementById('clear-logs-modal');
    if (modal) {
        modal.remove();
    }
}

// 清空使用记录
async function clearUsageLogs(days) {
    try {
        closeClearLogsModal();

        const url = days > 0
            ? `/api/auth/usage-logs/clear?days=${days}`
            : '/api/auth/usage-logs/clear';

        const response = await fetch(url, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`${result.message}，共删除 ${result.deletedCount} 条记录`);
            loadUsageLogs(); // 重新加载列表
        } else {
            showError('清空失败: ' + result.message);
        }

    } catch (error) {
        console.error('清空使用记录错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// ==================== 页面初始化 ====================

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 自定义授权码开关
    const customCodeEnable = document.getElementById('custom-code-enable');
    const customCodeInput = document.getElementById('custom-code');

    if (customCodeEnable && customCodeInput) {
        customCodeEnable.addEventListener('change', function() {
            customCodeInput.disabled = !this.checked;
            if (!this.checked) {
                customCodeInput.value = '';
            }
        });
    }
});

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'disabled': '禁用',
        'expired': '过期'
    };
    return statusMap[status] || status;
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化日期时间（公告专用）
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 显示成功消息
function showSuccess(message) {
    showMessage(message, 'success');
}

// 显示错误消息
function showError(message) {
    showMessage(message, 'error');
}

// 显示消息
function showMessage(message, type) {
    // 移除现有的消息
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新消息
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;

    // 插入到内容区域顶部
    const contentBody = document.querySelector('.content-body');
    contentBody.insertBefore(alert, contentBody.firstChild);

    // 3秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}

// 切换过期时间模式
function toggleExpireMode() {
    const expireType = document.getElementById('expire-type').value;
    const daysGroup = document.getElementById('expire-days-group');
    const datetimeGroup = document.getElementById('expire-datetime-group');

    // 隐藏所有组
    daysGroup.classList.add('hidden');
    datetimeGroup.classList.add('hidden');

    // 根据选择显示对应组
    if (expireType === 'days') {
        daysGroup.classList.remove('hidden');
    } else if (expireType === 'datetime') {
        datetimeGroup.classList.remove('hidden');
        // 设置默认时间为当前时间+1小时
        setDefaultDateTime();
    }
}

// 设置默认日期时间
function setDefaultDateTime() {
    // 获取当前时间
    const now = new Date();

    // 直接使用当前时间，不添加额外时间
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    const datetimeString = `${year}-${month}-${day}T${hours}:${minutes}`;
    document.getElementById('expire-datetime').value = datetimeString;

    console.log(`🕐 当前本地时间: ${now.toLocaleString('zh-CN')}`);
    console.log(`🕐 设置默认时间: ${now.toLocaleString('zh-CN')} (当前时间)`);
    console.log(`🕐 datetime-local值: ${datetimeString}`);
}

// 快捷时间设置
function setQuickTime(hours) {
    // 获取当前时间
    const now = new Date();

    // 创建一个新的Date对象用于+指定小时，避免修改原始时间
    const targetTime = new Date(now.getTime() + hours * 60 * 60 * 1000);

    // 直接格式化本地时间，不进行时区转换
    const year = targetTime.getFullYear();
    const month = String(targetTime.getMonth() + 1).padStart(2, '0');
    const day = String(targetTime.getDate()).padStart(2, '0');
    const hoursStr = String(targetTime.getHours()).padStart(2, '0');
    const minutes = String(targetTime.getMinutes()).padStart(2, '0');

    const datetimeString = `${year}-${month}-${day}T${hoursStr}:${minutes}`;
    document.getElementById('expire-datetime').value = datetimeString;

    console.log(`🕐 当前本地时间: ${now.toLocaleString('zh-CN')}`);
    console.log(`🕐 快捷设置时间: ${targetTime.toLocaleString('zh-CN')} (+${hours}小时)`);
    console.log(`🕐 datetime-local值: ${datetimeString}`);
}

// ==================== 更新管理相关函数 ====================

// 加载更新管理页面
async function loadUpdateManager() {
    try {
        // 绑定表单提交事件
        bindUpdateFormEvents();

        // 加载版本信息
        await loadVersionInfo();

        // 加载版本历史
        await loadVersionHistory();

    } catch (error) {
        console.error('加载更新管理页面错误:', error);
        showError('加载更新管理页面失败');
    }
}

// 绑定更新表单事件
function bindUpdateFormEvents() {
    const updateForm = document.getElementById('update-form');
    if (updateForm) {
        updateForm.removeEventListener('submit', handleUpdateFormSubmit); // 移除旧的监听器
        updateForm.addEventListener('submit', handleUpdateFormSubmit);
    }
}

// 处理更新表单提交
async function handleUpdateFormSubmit(e) {
    e.preventDefault();

    try {
        const formData = new FormData();
        const version = document.getElementById('new-version').value;
        const description = document.getElementById('version-description').value;
        const features = document.getElementById('update-features').value;
        const mandatory = document.getElementById('mandatory-update').checked;
        const exeFile = document.getElementById('update-file').files[0];

        // 验证必填字段
        if (!version || !description || !features) {
            showError('请填写所有必填字段');
            return;
        }

        // 必须上传EXE文件
        if (!exeFile) {
            showError('请上传更新文件（EXE格式）');
            return;
        }

        // 添加表单数据
        formData.append('version', version);
        formData.append('description', description);
        formData.append('features', features);
        formData.append('mandatory', mandatory);

        // 添加文件
        // 上传EXE文件
        formData.append('updateFile', exeFile);

        // 显示上传进度
        showUploadProgress(['EXE文件']);

        const response = await fetch('/api/update/admin/publish', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        hideUploadProgress();

        if (result.success) {
            showSuccess('新版本发布成功！');

            // 重置表单
            document.getElementById('update-form').reset();

            // 重新加载版本信息
            await loadVersionInfo();
            await loadVersionHistory();

        } else {
            showError('发布失败: ' + result.message);
        }

    } catch (error) {
        hideUploadProgress();
        console.error('发布新版本错误:', error);
        showError('发布失败，请检查网络连接');
    }
}

// 加载版本信息
async function loadVersionInfo() {
    try {
        const response = await fetch('/api/update/info');
        const result = await response.json();

        if (result.success) {
            const data = result.data;

            document.getElementById('current-version').textContent = data.currentVersion;
            document.getElementById('latest-version').textContent = data.latestVersion;
            document.getElementById('release-date').textContent = data.updateInfo.releaseDate;

            // 更新状态
            const statusElement = document.getElementById('update-status');
            if (data.hasUpdate) {
                statusElement.textContent = '有新版本可用';
                statusElement.className = 'status-badge status-warning';
            } else {
                statusElement.textContent = '已是最新版本';
                statusElement.className = 'status-badge status-active';
            }

        } else {
            console.error('获取版本信息失败:', result.message);
        }

    } catch (error) {
        console.error('加载版本信息错误:', error);
    }
}

// 加载版本历史
async function loadVersionHistory() {
    try {
        const container = document.getElementById('version-history');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                正在加载版本历史...
            </div>
        `;

        const response = await fetch('/api/update/admin/history');
        const result = await response.json();

        if (result.success) {
            renderVersionHistory(result.data);
        } else {
            container.innerHTML = '<p style="color: #666;">加载版本历史失败</p>';
        }

    } catch (error) {
        console.error('加载版本历史错误:', error);
        document.getElementById('version-history').innerHTML = '<p style="color: #666;">加载版本历史失败</p>';
    }
}

// 渲染版本历史
function renderVersionHistory(versions) {
    const container = document.getElementById('version-history');

    if (versions.length === 0) {
        container.innerHTML = '<p style="color: #666;">暂无版本历史</p>';
        return;
    }

    let historyHTML = '';

    versions.forEach((version, index) => {
        const isLatest = index === 0;
        const releaseDate = formatDate(version.release_date);
        const features = version.features || [];

        historyHTML += `
            <div style="
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                background: ${isLatest ? '#f8f9fa' : 'white'};
                ${isLatest ? 'border-left: 4px solid #28a745;' : ''}
            ">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">
                    <div>
                        <h5 style="margin: 0; color: #333;">
                            版本 ${version.version}
                            ${isLatest ? '<span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">最新</span>' : ''}
                            ${version.is_current ? '<span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">当前</span>' : ''}
                        </h5>
                        <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">${version.description}</p>
                    </div>
                    <div style="text-align: right; color: #666; font-size: 14px;">
                        <div>发布时间: ${releaseDate}</div>
                        <div>文件大小: ${version.fileSize}</div>
                        ${version.mandatory ? '<div style="color: #dc3545;">强制更新</div>' : ''}
                    </div>
                </div>

                ${features.length > 0 ? `
                    <div>
                        <strong style="color: #333;">更新内容:</strong>
                        <ul style="margin: 8px 0 0 20px; color: #666;">
                            ${features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    });

    container.innerHTML = historyHTML;
}

// 显示上传进度
function showUploadProgress(uploadFiles = []) {
    const filesList = uploadFiles.length > 0 ? uploadFiles.join(' + ') : '更新文件';
    const progressHTML = `
        <div id="upload-progress" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                min-width: 300px;
            ">
                <div class="spinner" style="margin-bottom: 15px;"></div>
                <p style="margin: 0; color: #333;">正在上传并发布新版本...</p>
                <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">正在上传: ${filesList}</p>
                <p style="margin: 5px 0 0 0; color: #666; font-size: 12px;">包含文件上传、数据库更新等步骤，请稍候...</p>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', progressHTML);
}

// 隐藏上传进度
function hideUploadProgress() {
    const progress = document.getElementById('upload-progress');
    if (progress) {
        progress.remove();
    }
}

// 复制版本提醒文字
function copyVersionReminder() {
    const text = '记得修改更新版本要修改 Cargo.toml 和 tauri.conf.json';
    const element = document.getElementById('version-reminder');

    console.log('开始复制版本提醒:', text);

    // 尝试使用现代API复制
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess(element);
        }).catch(function() {
            fallbackCopy(text, element);
        });
    } else {
        fallbackCopy(text, element);
    }
}

// 显示复制成功
function showCopySuccess(element) {
    const originalText = element.textContent;
    element.textContent = '✅ 已复制到剪贴板';
    element.style.color = '#28a745';
    element.style.background = '#d4edda';
    setTimeout(function() {
        element.textContent = originalText;
        element.style.color = '#666';
        element.style.background = '#f8f9fa';
    }, 2000);
}

// 备用复制方法
function fallbackCopy(text, element) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const result = document.execCommand('copy');
        if (result) {
            showCopySuccess(element);
        } else {
            alert('复制失败，请手动复制：\n' + text);
        }
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制：\n' + text);
    }

    document.body.removeChild(textArea);
}

// ==================== 公告管理功能 ====================

// 加载公告列表
async function loadAnnouncements() {
    const container = document.getElementById('announcements-container');
    container.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            正在加载公告列表...
        </div>
    `;

    try {
        const response = await fetch('/api/announcement/admin/list');
        const result = await response.json();

        if (result.success) {
            displayAnnouncements(result.data);
        } else {
            container.innerHTML = `<div class="alert alert-error">加载失败: ${result.message}</div>`;
        }
    } catch (error) {
        console.error('加载公告失败:', error);
        container.innerHTML = `<div class="alert alert-error">网络错误，请稍后重试</div>`;
    }
}

// 显示公告列表
function displayAnnouncements(announcements) {
    const container = document.getElementById('announcements-container');

    if (announcements.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #666;">
                <h3>暂无公告</h3>
                <p>点击"新建公告"创建第一个公告</p>
            </div>
        `;
        return;
    }

    const html = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>内容</th>
                        <th>类型</th>
                        <th>优先级</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${announcements.map(announcement => `
                        <tr>
                            <td>${announcement.id}</td>
                            <td style="max-width: 300px; word-wrap: break-word;">${announcement.content}</td>
                            <td>
                                <span class="status-badge ${getAnnouncementTypeClass(announcement.type)}">
                                    ${getAnnouncementTypeText(announcement.type)}
                                </span>
                            </td>
                            <td>${announcement.priority}</td>
                            <td>
                                <span class="status-badge ${announcement.is_active ? 'status-active' : 'status-inactive'}">
                                    ${announcement.is_active ? '启用' : '禁用'}
                                </span>
                            </td>
                            <td>${formatDateTime(announcement.created_at)}</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px; margin-right: 5px;"
                                        onclick="editAnnouncement(${announcement.id})">编辑</button>
                                <button class="btn ${announcement.is_active ? 'btn-danger' : 'btn-success'}"
                                        style="padding: 4px 8px; font-size: 12px; margin-right: 5px;"
                                        onclick="toggleAnnouncementStatus(${announcement.id}, ${!announcement.is_active})">
                                    ${announcement.is_active ? '禁用' : '启用'}
                                </button>
                                <button class="btn btn-danger" style="padding: 4px 8px; font-size: 12px;"
                                        onclick="deleteAnnouncement(${announcement.id})">删除</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

// 获取公告类型样式类
function getAnnouncementTypeClass(type) {
    const classes = {
        'info': 'status-active',
        'warning': 'status-expired',
        'success': 'status-active',
        'error': 'status-disabled'
    };
    return classes[type] || 'status-active';
}

// 获取公告类型文本
function getAnnouncementTypeText(type) {
    const texts = {
        'info': '信息',
        'warning': '警告',
        'success': '成功',
        'error': '错误'
    };
    return texts[type] || '信息';
}

// 显示创建公告表单
function showCreateAnnouncementForm() {
    document.getElementById('form-title').textContent = '新建公告';
    document.getElementById('announcement-id').value = '';
    document.getElementById('announcement-form-element').reset();
    document.getElementById('announcement-form').classList.remove('hidden');
}

// 隐藏公告表单
function hideAnnouncementForm() {
    document.getElementById('announcement-form').classList.add('hidden');
}

// 编辑公告
async function editAnnouncement(id) {
    try {
        const response = await fetch('/api/announcement/admin/list');
        const result = await response.json();

        if (result.success) {
            const announcement = result.data.find(a => a.id === id);
            if (announcement) {
                document.getElementById('form-title').textContent = '编辑公告';
                document.getElementById('announcement-id').value = announcement.id;
                document.getElementById('announcement-content').value = announcement.content;
                document.getElementById('announcement-type').value = announcement.type;
                document.getElementById('announcement-priority').value = announcement.priority;
                document.getElementById('announcement-form').classList.remove('hidden');
            }
        }
    } catch (error) {
        console.error('加载公告详情失败:', error);
        showMessage('加载公告详情失败', 'error');
    }
}

// 切换公告状态
async function toggleAnnouncementStatus(id, isActive) {
    try {
        const response = await fetch(`/api/announcement/admin/update/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ is_active: isActive })
        });

        const result = await response.json();
        if (result.success) {
            showMessage(`公告已${isActive ? '启用' : '禁用'}`, 'success');
            loadAnnouncements();
        } else {
            showMessage('操作失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('切换公告状态失败:', error);
        showMessage('操作失败', 'error');
    }
}

// 删除公告
async function deleteAnnouncement(id) {
    if (!confirm('确定要删除这个公告吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/announcement/admin/delete/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('公告删除成功', 'success');
            loadAnnouncements();
        } else {
            showMessage('删除失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('删除公告失败:', error);
        showMessage('删除失败', 'error');
    }
}

// 公告表单提交处理
document.addEventListener('DOMContentLoaded', function() {
    const announcementForm = document.getElementById('announcement-form-element');
    if (announcementForm) {
        announcementForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const id = document.getElementById('announcement-id').value;
            const content = document.getElementById('announcement-content').value;
            const type = document.getElementById('announcement-type').value;
            const priority = parseInt(document.getElementById('announcement-priority').value);

            try {
                let response;
                if (id) {
                    // 编辑公告
                    response = await fetch(`/api/announcement/admin/update/${id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            content,
                            type,
                            priority,
                            is_active: true
                        })
                    });
                } else {
                    // 创建公告
                    response = await fetch('/api/announcement/admin/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            content,
                            type,
                            priority
                        })
                    });
                }

                const result = await response.json();
                if (result.success) {
                    showMessage(id ? '公告更新成功' : '公告创建成功', 'success');
                    hideAnnouncementForm();
                    loadAnnouncements();
                } else {
                    showMessage('操作失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('保存公告失败:', error);
                showMessage('保存失败', 'error');
            }
        });
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUpdateInfo();
    loadVersionHistory();
    loadAuthCodes();
    loadUsageLogs();

    console.log('管理页面初始化完成');
});

// ==================== Augment账号管理功能 ====================

// 全局变量
let augmentAccountsData = [];

// 加载Augment账号列表
async function loadAugmentAccounts() {
    try {
        const container = document.getElementById('augment-accounts-container');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                正在加载账号列表...
            </div>
        `;

        const response = await fetch('/api/augment/accounts', {
            credentials: 'include'
        });
        const result = await response.json();

        if (result.success) {
            augmentAccountsData = result.data;
            renderAugmentAccountsTable(result.data);
        } else {
            showError('加载账号列表失败: ' + result.message);
        }

    } catch (error) {
        console.error('加载账号列表错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 渲染Augment账号表格
function renderAugmentAccountsTable(accounts) {
    const container = document.getElementById('augment-accounts-container');

    if (accounts.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #666;">
                <p>暂无账号数据</p>
                <button class="btn btn-primary" onclick="showAddAccountForm()">
                    ➕ 添加第一个账号
                </button>
            </div>
        `;
        return;
    }

    let tableHTML = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>账号名称</th>
                        <th>邮箱</th>
                        <th>类型</th>
                        <th>额度情况</th>
                        <th>状态</th>
                        <th>最后使用</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    accounts.forEach(account => {
        const statusClass = `status-${account.status}`;
        const statusText = getAugmentStatusText(account.status);
        const accountTypeText = account.account_type === 'trial' ? '试用' : '正式';
        const accountTypeBadge = account.account_type === 'trial' ? 'style="background: #fff3cd; color: #856404;"' : 'style="background: #d4edda; color: #155724;"';
        const lastUsed = account.last_used_at ? formatDate(account.last_used_at) : '从未使用';
        const createDate = formatDate(account.created_at);

        // 额度进度条
        const quotaPercent = account.quota_total > 0 ? ((account.quota_total - account.quota_used) / account.quota_total * 100) : 0;
        const quotaColor = quotaPercent > 50 ? '#28a745' : quotaPercent > 20 ? '#ffc107' : '#dc3545';

        tableHTML += `
            <tr>
                <td>
                    <strong>${account.account_name}</strong>
                    ${account.notes ? `<br><small style="color: #666;">${account.notes}</small>` : ''}
                </td>
                <td>
                    <code style="font-size: 12px;">${account.email}</code>
                </td>
                <td>
                    <span class="status-badge" ${accountTypeBadge}>${accountTypeText}</span>
                </td>
                <td>
                    <div style="margin-bottom: 5px;">
                        <strong>${account.quota_remaining}</strong>/${account.quota_total}
                    </div>
                    <div style="background: #e9ecef; border-radius: 10px; height: 6px; overflow: hidden;">
                        <div style="background: ${quotaColor}; height: 100%; width: ${quotaPercent}%; transition: width 0.3s ease;"></div>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td style="font-size: 12px;">${lastUsed}</td>
                <td style="font-size: 12px;">${createDate}</td>
                <td>
                    <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px; margin-right: 5px;"
                            onclick="editAugmentAccount(${account.id})">编辑</button>
                    <button class="btn btn-success" style="padding: 4px 8px; font-size: 11px; margin-right: 5px;"
                            onclick="checkAccountQuota(${account.id})">检查额度</button>
                    <button class="btn btn-danger" style="padding: 4px 8px; font-size: 11px;"
                            onclick="deleteAugmentAccount(${account.id}, '${account.account_name}')">删除</button>
                </td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHTML;
}

// 显示添加账号表单
function showAddAccountForm() {
    document.getElementById('add-account-form').classList.remove('hidden');
    document.getElementById('account-form').reset();
    document.getElementById('account-id').value = '';

    // 设置默认值
    document.getElementById('account-type').value = 'regular';
    document.getElementById('account-quota').value = '50';
}

// 隐藏添加账号表单
function hideAddAccountForm() {
    document.getElementById('add-account-form').classList.add('hidden');
}

// 显示批量导入表单
function showBatchImportForm() {
    document.getElementById('batch-import-form').classList.remove('hidden');
}

// 隐藏批量导入表单
function hideBatchImportForm() {
    document.getElementById('batch-import-form').classList.add('hidden');
}

// 获取Augment账号状态文本
function getAugmentStatusText(status) {
    const statusMap = {
        'active': '可用',
        'exhausted': '已耗尽',
        'disabled': '已禁用',
        'error': '错误'
    };
    return statusMap[status] || '未知';
}

// 绑定账号表单事件
document.addEventListener('DOMContentLoaded', function() {
    // 账号表单提交事件
    const accountForm = document.getElementById('account-form');
    if (accountForm) {
        accountForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitAccountForm();
        });
    }

    // 账号类型变化时自动更新额度
    const accountTypeSelect = document.getElementById('account-type');
    if (accountTypeSelect) {
        accountTypeSelect.addEventListener('change', function() {
            const quotaInput = document.getElementById('account-quota');
            if (this.value === 'trial') {
                quotaInput.value = '300';
            } else {
                quotaInput.value = '50';
            }
        });
    }
});

// 提交账号表单
async function submitAccountForm() {
    try {
        const accountId = document.getElementById('account-id').value;
        const isEdit = accountId !== '';

        const formData = {
            account_name: document.getElementById('account-name').value,
            email: document.getElementById('account-email').value,
            user_id: document.getElementById('account-user-id').value,
            session_id: document.getElementById('account-session-id').value,
            account_type: document.getElementById('account-type').value,
            quota_total: parseInt(document.getElementById('account-quota').value),
            notes: document.getElementById('account-notes').value
        };

        // 验证必填字段
        if (!formData.account_name || !formData.email || !formData.user_id) {
            showError('请填写所有必填字段');
            return;
        }

        const url = isEdit ? `/api/augment/accounts/${accountId}` : '/api/augment/accounts';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(isEdit ? '账号更新成功！' : '账号添加成功！');
            hideAddAccountForm();
            loadAugmentAccounts(); // 重新加载列表
        } else {
            showError((isEdit ? '更新' : '添加') + '账号失败: ' + result.message);
        }

    } catch (error) {
        console.error('提交账号表单错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 编辑账号
function editAugmentAccount(accountId) {
    const account = augmentAccountsData.find(acc => acc.id === accountId);
    if (!account) {
        showError('账号不存在');
        return;
    }

    // 填充表单
    document.getElementById('account-id').value = account.id;
    document.getElementById('account-name').value = account.account_name;
    document.getElementById('account-email').value = account.email;
    document.getElementById('account-user-id').value = account.user_id;
    document.getElementById('account-session-id').value = account.session_id || '';
    document.getElementById('account-type').value = account.account_type;
    document.getElementById('account-quota').value = account.quota_total;
    document.getElementById('account-notes').value = account.notes || '';

    // 显示表单
    showAddAccountForm();
}

// 检查账号额度
async function checkAccountQuota(accountId) {
    try {
        showSuccess('正在检查账号额度...');

        const response = await fetch(`/api/augment/accounts/${accountId}/check-quota`, {
            method: 'POST',
            credentials: 'include'
        });

        const result = await response.json();

        if (result.success) {
            const data = result.data;
            showSuccess(`账号 ${data.account_name} 额度检查完成：剩余 ${data.quota_remaining}/${data.quota_total}`);
            loadAugmentAccounts(); // 重新加载列表以显示最新数据
        } else {
            showError('检查账号额度失败: ' + result.message);
        }

    } catch (error) {
        console.error('检查账号额度错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 删除账号
async function deleteAugmentAccount(accountId, accountName) {
    if (!confirm(`确定要删除账号 "${accountName}" 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/augment/accounts/${accountId}`, {
            method: 'DELETE',
            credentials: 'include'
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('账号删除成功！');
            loadAugmentAccounts(); // 重新加载列表
        } else {
            showError('删除账号失败: ' + result.message);
        }

    } catch (error) {
        console.error('删除账号错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 批量导入账号
async function submitBatchImport() {
    try {
        const accountsText = document.getElementById('batch-accounts').value;

        if (!accountsText.trim()) {
            showError('请输入账号数据');
            return;
        }

        let accounts;
        try {
            accounts = JSON.parse(accountsText);
        } catch (e) {
            showError('JSON格式错误，请检查数据格式');
            return;
        }

        if (!Array.isArray(accounts)) {
            showError('数据格式错误，请提供账号数组');
            return;
        }

        const response = await fetch('/api/augment/accounts/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ accounts })
        });

        const result = await response.json();

        if (result.success) {
            const data = result.data;
            showSuccess(`批量导入完成：成功 ${data.success} 个，失败 ${data.failed} 个`);
            if (data.errors.length > 0) {
                console.log('导入错误:', data.errors);
            }
            hideBatchImportForm();
            loadAugmentAccounts(); // 重新加载列表
        } else {
            showError('批量导入失败: ' + result.message);
        }

    } catch (error) {
        console.error('批量导入错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}

// 加载统计信息
async function loadAugmentStats() {
    try {
        const response = await fetch('/api/augment/stats', {
            credentials: 'include'
        });

        const result = await response.json();

        if (result.success) {
            const data = result.data;
            let statsHtml = `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4>📊 账号统计信息</h4>
                    <div class="row" style="margin-top: 15px;">
                        <div class="col">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <h5 style="color: #28a745;">可用账号</h5>
                                <p style="font-size: 24px; font-weight: bold; margin: 0;">${data.available_accounts}</p>
                            </div>
                        </div>
            `;

            // 状态统计
            if (data.status_stats) {
                data.status_stats.forEach(stat => {
                    const statusText = getAugmentStatusText(stat.status);
                    statsHtml += `
                        <div class="col">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <h5>${statusText}</h5>
                                <p style="font-size: 24px; font-weight: bold; margin: 0;">${stat.count}</p>
                            </div>
                        </div>
                    `;
                });
            }

            statsHtml += `
                    </div>
                </div>
            `;

            // 显示统计信息
            const container = document.getElementById('augment-accounts-container');
            container.innerHTML = statsHtml + container.innerHTML;

            showSuccess('统计信息已更新');
        } else {
            showError('加载统计信息失败: ' + result.message);
        }

    } catch (error) {
        console.error('加载统计信息错误:', error);
        showError('网络错误，请检查服务器连接');
    }
}
