
const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // 注入反检测脚本
    await page.addInitScript(() => {
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
    });
    
    await page.goto('https://login.augmentcode.com/u/signup/identifier');
    await page.fill('input[name="username"]', '<EMAIL>');
    
    // 等待用户手动完成人机验证
    console.log('请手动完成人机验证，然后按回车继续...');
    await page.waitForTimeout(30000);
    
    await page.click('button[name="action"][value="default"]');
    await page.waitForTimeout(5000);
    
    console.log('注册完成');
    await browser.close();
})();
