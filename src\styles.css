/* 🎨 AmpCode 超现代化样式 - GitHub深色主题风格 */

/* 本地字体定义 */
@font-face {
    font-family: 'ZQL';
    src: url('./assets/fonts/zql.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* GitHub深色主题配色 */
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --bg-card: #161b22;
    --bg-hover: #21262d;
    --bg-active: #30363d;
    
    /* 边框颜色 */
    --border-primary: #30363d;
    --border-secondary: #21262d;
    
    /* 文字颜色 */
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #656d76;
    
    /* 品牌色彩 */
    --color-primary: #238636;
    --color-primary-hover: #2ea043;
    --color-primary-rgb: 35, 134, 54;
    --color-secondary: #1f6feb;
    --color-danger: #da3633;
    --color-danger-hover: #f85149;
    --color-warning: #d29922;
    --color-warning-hover: #e3b341;
    --color-info: #1f6feb;
    --color-info-hover: #388bfd;
    --color-success: #238636;
    --color-success-dark: #1a6e2b;
    --color-success-light: #2ea043;
    --color-danger-light: rgba(218, 54, 51, 0.1);

    /* 表面颜色 */
    --surface-color: #161b22;
    --border-color: #30363d;

    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
    
    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* 字体 */
    --font-family: 'ZQL', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

body {
    font-family: var(--font-family), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
}

/* 应用容器 */
.app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 自定义标题栏 */
.custom-titlebar {
    height: 32px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-bottom: 1px solid var(--border-primary);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    user-select: none;
    -webkit-app-region: drag;
}

.titlebar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
}

.titlebar-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 6px;
    padding-left: 16px;
}

.titlebar-title i {
    color: var(--color-primary);
    font-size: 12px;
}

.titlebar-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

.titlebar-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

.titlebar-controls {
    display: flex;
    align-items: center;
    height: 100%;
    -webkit-app-region: no-drag;
}

.titlebar-btn {
    width: 46px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 12px;
}

.titlebar-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.close-btn {
    border-top-right-radius: 8px;
}

.close-btn:hover {
    background: #e81123;
    color: white;
}

.minimize-btn:hover {
    background: var(--bg-hover);
}

/* 顶部标题栏 */
.header {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
    padding: 16px 24px;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.version-display {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* SessionId显示区域样式 */
.session-id-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
}

.session-id-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}



.session-id-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.session-id-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group {
    display: flex;
    gap: 6px;
    align-items: center;
}

.session-id-input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 11px;
    min-height: 32px;
    transition: all 0.2s ease;
}

.session-id-input:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.1);
}

.session-id-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: var(--text-secondary);
}

.session-id-status.success {
    color: var(--color-success);
}

.session-id-status.error {
    color: var(--color-danger);
}

.session-id-status.loading {
    color: var(--color-info);
}

/* 合并后的数值容器样式 */
.session-id-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-primary);
}

.session-id-header h3 {
    font-size: 16px;
}



.session-id-item {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    transition: all 0.2s ease;
}

.session-id-item:last-child {
    margin-bottom: 0;
}

.session-id-item:hover {
    border-color: var(--color-primary);
    box-shadow: 0 2px 8px rgba(var(--color-primary-rgb), 0.1);
}

.session-id-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.label-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.label-content i {
    color: var(--color-primary);
    font-size: 16px;
}

.btn-small {
    padding: 4px 8px !important;
    font-size: 10px !important;
    min-height: 26px !important;
    font-weight: 500 !important;
}

/* API额度模式样式 */
.api-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.form-actions .btn {
    flex: 1;
}

/* 额度显示样式 */
.quota-section, .models-section {
    margin-top: 16px;
}

.quota-display, .models-display {
    padding: 16px;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
}

.quota-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--border-secondary);
    font-size: 15px;
}

.quota-item:last-child {
    border-bottom: none;
}

.quota-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 15px;
    min-width: 50px;
}

.quota-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 15px;
    text-align: right;
    word-break: break-all;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.quota-progress {
    margin: 16px 0;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
}

.quota-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-success), var(--color-warning), var(--color-danger));
    transition: width 0.3s ease;
}

.quota-footer {
    text-align: center;
    margin-top: 10px;
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.8;
}

/* 模型测试结果样式 */
.models-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
}

.stat-value.valid {
    color: var(--color-success);
}

.stat-value.invalid {
    color: var(--color-danger);
}

.stat-value.inconsistent {
    color: var(--color-warning);
}

.models-progress {
    margin: 16px 0;
}

.progress-bar {
    height: 6px;
    background: var(--bg-secondary);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: var(--color-primary);
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 12px;
    color: var(--text-secondary);
}

/* VSCode Augment 账号管理样式 */
.account-section {
    margin-bottom: 16px;
}

.account-controls {
    margin-bottom: 16px;
}

.current-account {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: 12px;
    margin-bottom: 12px;
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.account-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.account-name {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 600;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.account-quota {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quota-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.quota-value {
    font-size: 13px;
    color: var(--color-success);
    font-weight: 600;
}

.account-actions {
    display: flex;
    gap: 8px;
}

.account-actions .btn {
    flex: 1;
    font-size: 11px;
    padding: 6px 8px;
    min-height: 28px;
}

.accounts-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
}

.account-item {
    padding: 10px 12px;
    border-bottom: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-item:last-child {
    border-bottom: none;
}

.account-item:hover {
    background: var(--bg-hover);
}

.account-item.active {
    background: rgba(var(--color-primary-rgb), 0.1);
    border-left: 3px solid var(--color-primary);
}

.account-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.account-item.exhausted {
    background: rgba(218, 54, 51, 0.05);
    border-left: 3px solid var(--color-danger);
}

.account-details {
    flex: 1;
}

.account-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.account-type-badge {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
    text-transform: uppercase;
}

.account-type-badge.trial {
    background: rgba(209, 153, 34, 0.2);
    color: var(--color-warning);
}

.account-type-badge.regular {
    background: rgba(35, 134, 54, 0.2);
    color: var(--color-success);
}

.account-email {
    font-size: 10px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.account-quota-info {
    font-size: 10px;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
}

.account-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    margin-left: 8px;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.active {
    background: var(--color-success);
}

.status-dot.exhausted {
    background: var(--color-danger);
}

.status-dot.disabled {
    background: var(--text-muted);
}

.status-dot.error {
    background: var(--color-warning);
}

.accounts-list::-webkit-scrollbar {
    width: 4px;
}

.accounts-list::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.accounts-list::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: 2px;
}

.accounts-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 额度状态样式 */
.quota-high {
    color: var(--color-success) !important;
}

.quota-medium {
    color: var(--color-warning) !important;
}

.quota-low {
    color: var(--color-danger) !important;
}

.quota-exhausted {
    color: var(--text-muted) !important;
}

.models-list {
    max-height: 300px;
    overflow-y: auto;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-primary);
}

.model-name {
    font-family: var(--font-family);
    font-size: 12px;
    color: var(--text-primary);
}

.model-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.model-status.valid {
    background: var(--color-success-light);
    color: var(--color-success);
}

.model-status.invalid {
    background: var(--color-danger-light);
    color: var(--color-danger);
}

.model-status.inconsistent {
    background: var(--color-warning-light);
    color: var(--color-warning);
}

/* 右侧面板通用样式 */
.right-panel > .card {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 面板显示控制 */
.panel-hidden {
    display: none !important;
}

.panel-visible {
    display: flex !important;
}

/* 右侧模型测试面板样式 */
.models-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.models-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-primary);
    background: var(--bg-secondary);
}

.models-title-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.models-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.models-progress-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.progress-spinner {
    display: flex;
    align-items: center;
}

.progress-spinner i {
    color: var(--color-primary);
    font-size: 14px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pagination-controls .btn {
    min-width: 32px;
    padding: 4px 8px;
}

.models-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #1a1a1a;
    color: #e0e0e0;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
}

.models-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;
    padding: 12px;
    background: #2a2a2a;
    border-radius: var(--radius-sm);
    border: 1px solid #404040;
}

/* 模型表格样式 */
.models-table {
    max-height: none;
    overflow-y: auto;
}

.models-table-content {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family);
    font-size: 12px;
}

.models-table-content th {
    background: #333333;
    color: #ffffff;
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #404040;
    font-weight: 600;
    font-size: 11px;
    position: sticky;
    top: 0;
    z-index: 1;
}

.models-table-content td {
    padding: 8px 12px;
    border: 1px solid #404040;
    background: #2a2a2a;
    color: #e0e0e0;
    vertical-align: middle;
}

.models-table-content tbody tr:hover {
    background: #333333;
}

/* 列宽设置 */
.col-status {
    width: 120px;
    text-align: center;
    white-space: nowrap;
}

.col-model {
    width: 300px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.col-time {
    width: 80px;
    text-align: center;
}

.col-remark {
    width: 200px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.col-verify {
    width: 100px;
    text-align: center;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    white-space: nowrap;
    min-width: 60px;
    text-align: center;
}

.status-badge.valid {
    background: var(--color-success);
    color: white;
}

.status-badge.invalid {
    background: var(--color-danger);
    color: white;
}

.status-badge.inconsistent {
    background: var(--color-warning);
    color: white;
}

/* 验证按钮样式 */
.verify-btn {
    padding: 2px 8px;
    border: 1px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.verify-btn:hover {
    background: #667eea;
    color: white;
}

/* 验证结果弹窗样式 */
.verification-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.verification-modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-primary);
}

.verification-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.verification-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.verification-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.verification-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.verification-body {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.verification-body pre {
    background: #1a1a1a;
    color: #e0e0e0;
    padding: 16px;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.verification-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-primary);
    display: flex;
    justify-content: flex-end;
}

.verification-ok {
    min-width: 80px;
}

/* 模型选择弹窗样式 */
.model-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.model-selection-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 79%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
}

.model-selection-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.model-selection-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.model-selection-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.model-selection-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.model-selection-body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.model-selection-info {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.model-selection-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
}

.model-filter-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 13px;
}

.model-selection-buttons {
    display: flex;
    gap: 8px;
}

.model-selection-options {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
}

.model-option {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: var(--text-primary);
    cursor: pointer;
}

.model-option input[type="checkbox"] {
    margin: 0;
}

.model-selection-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: 12px;
    background: var(--bg-primary);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 8px;
    max-height: 300px;
}

.model-item-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 12px;
}

.model-item-checkbox:hover {
    background: var(--bg-hover);
}

.model-item-checkbox input[type="checkbox"] {
    margin: 0;
}

.model-item-checkbox label {
    cursor: pointer;
    color: var(--text-primary);
    flex: 1;
    margin: 0;
}

.model-selection-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-primary);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: #2a2a2a;
    border-radius: var(--radius-sm);
    border: 1px solid #404040;
    transition: all 0.2s ease;
}

.model-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    background: #333333;
}

.model-info {
    flex: 1;
}

.model-name {
    font-family: var(--font-family);
    font-size: 13px;
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 4px;
}

.model-details {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: #b0b0b0;
}

.response-time {
    color: var(--color-info);
}

.error-info {
    color: var(--color-danger);
    cursor: help;
}



.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

/* 公告区域样式 */
.announcements-section {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: 0;
    max-height: 200px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.announcements-section.hidden {
    display: none;
}

.announcements-container {
    padding: 0;
}

.announcement-item {
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background-color 0.2s ease;
}

.announcement-item:hover {
    background: var(--bg-hover);
}

.announcement-item:last-child {
    border-bottom: none;
}

.announcement-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.announcement-icon.info {
    background: #17a2b8;
}

.announcement-icon.warning {
    background: #ffc107;
    color: #333;
}

.announcement-icon.success {
    background: #28a745;
}

.announcement-icon.error {
    background: #dc3545;
}

.announcement-content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.announcement-label {
    font-size: 13px;
    color: #DAA520;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
}

.announcement-text {
    font-size: 13px;
    color: white;
    font-weight: 600;
    margin: 0;
    line-height: 1.4;
    word-wrap: break-word;
    flex: 1;
}

.title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.title i {
    color: var(--color-primary);
    font-size: 28px;
}

.subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.subtitle i {
    color: var(--color-warning);
}

/* 模式切换器 */
.mode-switcher {
    display: flex;
    gap: 8px;
    justify-content: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.mode-btn {
    padding: 8px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.mode-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: #ffffff;
    transform: translateY(-1px);
}

.mode-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mode-btn i {
    margin-right: 6px;
}

/* 头部按钮样式 */
.btn-header {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.btn-header:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: #ffffff;
    transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    gap: 12px;
    padding: 16px;
    overflow: hidden;
}

/* 左侧面板 */
.left-panel {
    width: 320px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
    max-height: 100%;
}

/* 模式内容切换 */
.mode-content {
    display: none;
    flex-direction: column;
    gap: 12px;
}

.mode-content.active {
    display: flex;
}





/* 中间教程面板 */
.middle-panel {
    width: 350px;
    display: flex;
    flex-direction: column;
}

/* 右侧日志面板 */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-width: 280px;
}

/* API额度模式时右侧面板扩展 */
.main-content.api-quota-mode .right-panel {
    flex: 2;
    max-width: none;
}

.main-content.api-quota-mode .middle-panel {
    display: none;
}

/* 卡片样式 */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 16px;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card:hover {
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
}

/* 区域标题 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.section-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: var(--color-info);
}

/* API Key 列表 */
.api-list-container {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 12px;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    min-height: 400px;
}

.api-list {
    list-style: none;
}

.api-list li {
    padding: 8px 50px 8px 12px; /* 右侧留出空间给复制按钮 */
    border-bottom: 1px solid var(--border-primary);
    font-family: var(--font-family);
    font-size: 11px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    word-break: break-all;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.api-list li:last-child {
    border-bottom: none;
}

.api-list li:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.api-list li.selected {
    background: var(--color-info);
    color: white;
}

/* API Key 复制按钮 */
.api-key-text {
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.copy-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--color-success);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.copy-btn:hover {
    opacity: 1;
    background: var(--color-success-dark);
    transform: translateY(-50%) scale(1.05);
}

.copy-btn:active {
    transform: translateY(-50%) scale(0.95);
}

.copy-btn.copied {
    background: var(--color-info);
    animation: copySuccess 0.3s ease;
}

@keyframes copySuccess {
    0% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.1); }
    100% { transform: translateY(-50%) scale(1); }
}

/* 表单样式 */
.form-group {
    margin-bottom: 12px;
}

.form-row {
    display: flex;
    gap: 10px;
}

.form-row .form-group {
    flex: 1;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.form-input, .form-control {
    width: 100%;
    padding: 10px 14px;
    background: var(--bg-primary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 13px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-input:focus, .form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.2), 0 4px 12px rgba(0, 0, 0, 0.15);
    background: var(--bg-card);
}

.form-input:hover, .form-control:hover {
    border-color: var(--color-primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.form-input:read-only {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* API额度表单特殊样式 */
.api-form .form-group {
    margin-bottom: 16px;
}

.api-form .form-actions {
    display: flex;
    gap: 8px;
    margin-top: 20px;
}

.api-form .form-actions .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    min-height: 36px;
    white-space: nowrap;
}

/* 按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    user-select: none;
    white-space: nowrap;
    min-height: 44px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 按钮变体 */
.btn-primary {
    background: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--color-primary-hover);
}

.btn-danger {
    background: var(--color-danger);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.btn-danger:hover:not(:disabled) {
    background: var(--color-danger-hover);
}

.btn-warning {
    background: var(--color-warning);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: var(--color-warning-hover);
}

.btn-info {
    background: #06b6d4;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #0891b2;
}

.btn-success {
    background: var(--color-success);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: var(--color-success-dark);
}

/* 内存级清理按钮特殊样式 */
#ideaMemoryCleanBtn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

#ideaMemoryCleanBtn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
    transform: translateY(-1px);
}

#ideaMemoryCleanBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#ideaMemoryCleanBtn:hover::before {
    left: 100%;
}

.btn-purple {
    background: #8b5cf6;
    color: white;
}

.btn-purple:hover:not(:disabled) {
    background: #7c3aed;
}

.btn-large {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    margin-bottom: 6px;
}

/* 按钮图标样式 */
.btn-icon {
    width: 22px;
    height: 22px;
    margin-right: 8px;
    vertical-align: middle;
}

/* 标签图标样式 */
.label-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: middle;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-height: auto;
    font-weight: 500;
}

/* 按钮组 */
.button-group {
    display: flex;
    gap: 8px;
}

.button-group .btn {
    flex: 1;
}

/* SessionId 显示区域 */
.session-info {
    margin-top: 16px;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.session-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 6px;
}

.session-title i {
    color: var(--color-warning);
}

.session-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.session-id {
    flex: 1;
    font-family: var(--font-family);
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-primary);
    padding: 6px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-primary);
    word-break: break-all;
    min-height: 20px;
    display: flex;
    align-items: center;
}

.btn-refresh {
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 4px 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
}

.btn-refresh:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
}

.btn-refresh:active {
    transform: translateY(0);
}

.btn-refresh i {
    font-size: 11px;
}



/* 底部区域 */
.bottom-section {
    margin-top: auto;
    display: flex;
    flex-direction: column;
}

/* 状态栏 */
.status-bar {
    padding: 12px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
}

.status-ready {
    color: var(--color-primary);
}

.status-running {
    color: var(--color-warning);
}

.status-error {
    color: var(--color-danger);
}

.status-indicator i {
    font-size: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 日志面板 */
.log-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.log-header {
    padding: 16px 20px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.log-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.log-title i {
    color: var(--color-info);
}

.log-content {
    flex: 1;
    overflow: hidden;
}

.log-container {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    font-family: var(--font-family);
    font-size: 13px;
    line-height: 1.5;
    background: #1a1a1a;
    color: #e0e0e0;
    border-radius: var(--radius-md);
}

/* 日志条目 */
.log-entry {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.log-entry:hover {
    background: var(--bg-hover);
}

.log-entry.log-info {
    border-left-color: var(--color-info);
    color: var(--text-secondary);
}

.log-entry.log-success {
    border-left-color: var(--color-primary);
    color: var(--color-primary);
    background: rgba(35, 134, 54, 0.1);
}

.log-entry.log-warning {
    border-left-color: var(--color-warning);
    color: var(--color-warning);
    background: rgba(210, 153, 34, 0.1);
}

.log-entry.log-error {
    border-left-color: var(--color-danger);
    color: var(--color-danger);
    background: rgba(218, 54, 51, 0.1);
}

.log-timestamp {
    color: var(--text-muted);
    margin-right: 8px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 2px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-secondary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .left-panel {
        width: 250px;
    }

    .middle-panel {
        width: 380px;
    }
}

/* 授权验证弹窗样式 */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.auth-modal-content {
    background: var(--surface-color);
    border-radius: 15px;
    padding: 40px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: authModalSlideIn 0.3s ease-out;
}

@keyframes authModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    color: var(--text-primary);
    font-size: 24px;
    margin-bottom: 10px;
    font-weight: 600;
}

.auth-header p {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.customer-service {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

.customer-service p {
    margin: 0;
    color: white !important;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
}

.auth-modal .form-group {
    margin-bottom: 25px;
}

.auth-modal .form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.auth-modal .form-group input {
    width: 100%;
    padding: 15px;
    border: 3px solid #d1d5db;
    border-radius: 10px;
    font-size: 16px;
    background: #ffffff;
    color: #1f2937;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auth-modal .form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2), 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #ffffff;
}

.auth-modal .form-group input:hover {
    border-color: #9ca3af;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.auth-modal .btn {
    padding: 15px 40px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.auth-modal .btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    color: white;
}

.auth-modal .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-modal .btn-primary:active {
    transform: translateY(0);
}

.auth-error {
    background: var(--color-danger-light);
    color: var(--color-danger);
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    border: 1px solid var(--color-danger);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

@media (max-width: 1000px) {
    .main-content {
        flex-direction: column;
        gap: 8px;
    }

    .left-panel,
    .middle-panel,
    .right-panel {
        width: 100%;
        height: auto;
    }

    .left-panel {
        flex-direction: row;
        overflow-x: auto;
        gap: 8px;
    }

    .left-panel .card {
        min-width: 250px;
        flex-shrink: 0;
    }

    .middle-panel .card {
        height: 300px;
    }
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--color-info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 授权错误样式 */
.auth-error {
    background: rgba(218, 54, 51, 0.1);
    color: var(--color-danger);
    padding: 12px;
    border-radius: var(--radius-md);
    margin: 10px 0;
    border: 1px solid rgba(218, 54, 51, 0.2);
    font-size: 14px;
    text-align: center;
}

/* 自定义确认对话框样式 */
.confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.confirm-modal.show {
    display: flex;
}

.confirm-modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 420px;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.confirm-header {
    padding: 24px 24px 16px;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
}

.confirm-header i {
    font-size: 48px;
    color: var(--color-warning);
    margin-bottom: 12px;
    display: block;
}

.confirm-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.confirm-body {
    padding: 24px;
    text-align: center;
}

.confirm-body p {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.confirm-actions {
    padding: 16px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: center;
}

.confirm-actions .btn {
    min-width: 100px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    border-radius: var(--radius-lg);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--border-secondary);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 教程面板样式 */
.tutorial-panel {
    height: 100%;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.tutorial-panel.active {
    display: flex;
}

.tutorial-header {
    padding: 8px 20px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.tutorial-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.tutorial-title i {
    color: var(--color-warning);
}

.tutorial-tabs {
    display: flex;
    justify-content: center;
    padding: 8px 4px;
}

.tab-slider {
    position: relative;
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 4px;
    gap: 2px;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 2px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.tab-btn {
    position: relative;
    z-index: 2;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 500;
    min-width: 85px;
}

.tab-btn:hover {
    color: var(--text-primary);
}

.tab-btn.active {
    color: white;
}

.tab-indicator {
    position: absolute;
    top: 4px;
    left: 4px;
    height: calc(100% - 8px);
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
    border-radius: var(--radius-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    width: 85px;
    box-shadow:
        0 2px 8px rgba(var(--color-primary-rgb), 0.3),
        0 1px 3px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tutorial-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: var(--bg-primary);
    display: none;
}

.tutorial-content.active {
    display: block;
}

/* 配置内容样式 */
.config-content {
    margin-top: 12px;
    margin-left: -40px;
    position: relative;
}

.config-actions {
    position: relative;
}

.config-text {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: 10px 12px 10px 12px;
    font-family: var(--font-family);
    font-size: 12px;
    line-height: 1.3;
    color: var(--text-primary);
    white-space: pre-line;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 12px;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
}

.copy-btn {
    position: absolute;
    top: -25px;
    right: 0;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.2s ease;
    z-index: 10;
}

.copy-btn:hover {
    background: var(--color-primary-hover);
}

.copy-btn:active {
    background: var(--color-primary);
}

.copy-btn i {
    font-size: 12px;
}

.tutorial-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 16px;
    margin-bottom: 16px;
}

.tutorial-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tutorial-card h4 i {
    color: var(--color-primary);
}

.tutorial-steps {
    margin-bottom: 16px;
}

.step {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: flex-start;
}

.step-number {
    background: var(--color-primary);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content strong {
    color: var(--text-primary);
    font-size: 13px;
    display: block;
    margin-bottom: 4px;
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
    overflow-wrap: break-word;
}

.step-content p {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    word-wrap: break-word;
    white-space: normal;
    overflow-wrap: break-word;
}

.tutorial-note {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-md);
    padding: 12px;
    font-size: 12px;
    line-height: 1.5;
}

.tutorial-note i {
    color: var(--color-primary);
    margin-right: 6px;
}

.tutorial-note strong {
    color: var(--text-primary);
    white-space: nowrap;
}

.important-warning {
    color: #ff4757;
    font-weight: bold;
    font-size: 13px;
    text-shadow: 0 1px 2px rgba(255, 71, 87, 0.3);
    animation: warningPulse 2s ease-in-out infinite;
    display: block;
    margin-top: 4px;
}

@keyframes warningPulse {
    0%, 100% {
        color: #ff4757;
    }
    50% {
        color: #ff6b7a;
    }
}

/* 邮箱推荐区域样式 */
.email-section {
    margin-bottom: 16px;
}

.email-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 8px 0;
    line-height: 1.4;
}

.email-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.email-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--color-primary);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    width: 100%;
    justify-content: center;
}

.email-link-btn:hover {
    background: var(--color-primary-hover);
    transform: translateY(-1px);
}

.email-link-btn i {
    font-size: 10px;
}

/* VPN推荐区域样式 */
.vpn-section {
    margin-bottom: 16px;
}

.vpn-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 8px 0;
    line-height: 1.4;
}

.vpn-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.vpn-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #10b981; /* 绿色，区别于邮箱的蓝色 */
    color: white;
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    width: 100%;
    justify-content: center;
}

.vpn-link-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.vpn-link-btn i {
    font-size: 10px;
}

/* 使用量显示区域样式 */
.usage-section {
    margin-bottom: 16px;
}

.usage-display {
    padding: 12px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
}

.usage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.usage-item:last-of-type {
    margin-bottom: 12px;
}

.usage-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.usage-value {
    color: var(--text-primary);
    font-weight: 600;
}

.usage-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 8px;
}

.usage-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #f59e0b 70%, #ef4444 90%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.usage-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--border-primary);
    font-size: 11px;
}

.last-update {
    color: var(--text-secondary);
    font-style: italic;
    font-size: 10px;
    opacity: 0.8;
}

.auto-refresh-info {
    color: var(--color-primary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.countdown-number {
    display: inline-block;
    min-width: 20px;
    text-align: center;
    font-weight: 700;
    font-size: 14px;
    color: var(--color-primary);
    background: rgba(76, 175, 80, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    transition: all 0.3s ease;
}

.countdown-number.pulse {
    transform: scale(1.1);
    background: rgba(76, 175, 80, 0.2);
}

.countdown-text {
    font-size: 12px;
    color: var(--text-secondary);
}

.email-recommendation {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 16px;
}

.email-recommendation h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.email-recommendation h4 i {
    color: var(--color-success);
}

.email-recommendation p {
    color: var(--text-secondary);
    font-size: 12px;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.email-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: var(--color-primary);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 12px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.email-link:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

.email-note {
    color: var(--text-muted);
    font-size: 11px;
    margin: 8px 0 0 0;
    font-style: italic;
}

/* 专业更新对话框样式 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: scale(0.9) translateY(20px); opacity: 0; }
    to { transform: scale(1) translateY(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.05); opacity: 1; }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.update-dialog-overlay {
    animation: fadeIn 0.3s ease-out;
}

.update-dialog-content {
    animation: slideIn 0.4s ease-out;
}

.update-progress-overlay {
    animation: fadeIn 0.3s ease-out;
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: var(--text-primary) !important;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-update:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50) !important;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-content {
        flex-direction: column;
    }

    .left-panel, .right-panel {
        width: 100%;
        margin-bottom: 20px;
    }

    .update-dialog-content {
        padding: 30px 20px !important;
        max-width: 95% !important;
    }

    .progress-content {
        padding: 30px 20px !important;
        max-width: 95% !important;
    }
}

/* Augment插件回退教程弹窗样式 */
.plugin-tutorial-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.plugin-tutorial-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
}

.plugin-tutorial-content {
    position: relative;
    background: var(--bg-card);
    border: 2px solid var(--color-primary);
    border-radius: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.4s ease-out;
}

.plugin-tutorial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--color-primary), rgba(76, 175, 80, 0.8));
}

.plugin-tutorial-header h3 {
    margin: 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
}

.plugin-tutorial-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
}

.plugin-tutorial-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.plugin-tutorial-body {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bg-primary);
}

.plugin-tutorial-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    display: block;
}

@keyframes slideIn {
    from {
        transform: scale(0.8) translateY(30px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* 插件回退教程容器显示控制 */
#pluginTutorialContainer {
    display: none; /* 默认隐藏 */
}

/* 只在VSCode标签页激活时显示插件回退教程 */
#vscodeTab.active + #pluginTutorialContainer {
    display: block;
}

/* 当IDEA或降智方法标签页激活时隐藏插件回退教程 */
#ideaTab.active ~ #pluginTutorialContainer,
#augmentTab.active ~ #pluginTutorialContainer {
    display: none;
}

/* 万能工具箱弹窗样式 */
.universal-toolbox-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.universal-toolbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
}

.universal-toolbox-content {
    position: relative;
    background: var(--bg-card);
    border: 2px solid var(--color-primary);
    border-radius: 20px;
    width: 95vw;
    max-width: 1000px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.4s ease-out;
}

.universal-toolbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--color-primary), rgba(76, 175, 80, 0.8));
}

.universal-toolbox-header h3 {
    margin: 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
}

.universal-toolbox-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
}

.universal-toolbox-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.universal-toolbox-body {
    padding: 25px;
    max-height: 75vh;
    overflow-y: auto;
    background: var(--bg-primary);
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 25px;
    align-items: start;
}

.universal-toolbox-body .session-id-section {
    grid-column: 1; /* 当前数值在第1列 */
}

.universal-toolbox-body .email-section {
    grid-column: 2; /* 临时邮箱在第2列 */
}

.universal-toolbox-body .vpn-section {
    grid-column: 3; /* 推荐梯子在第3列 */
}

.universal-toolbox-body .card {
    margin-bottom: 0; /* 移除底部边距，使用grid-gap */
}

/* 万能工具箱内的输入框样式修复 */
.universal-toolbox-body .session-id-input {
    font-family: var(--font-family);
}

.universal-toolbox-body .session-id-input:hover {
    border-color: var(--border-primary); /* 悬停时不改变边框颜色 */
    box-shadow: none; /* 悬停时不显示阴影 */
}

.universal-toolbox-body .session-id-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
}

/* 工具箱按钮样式 */
.toolbox-section {
    margin-bottom: 20px;
}

.toolbox-section .btn {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.toolbox-section .btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

/* Cursor万能工具箱样式 */
.cursor-toolbox-content {
    position: relative;
    background: var(--bg-card);
    border: 2px solid var(--color-primary);
    border-radius: 20px;
    width: 80vw;
    max-width: 600px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.4s ease-out;
}

.cursor-toolbox-body {
    padding: 25px;
    max-height: 75vh;
    overflow-y: auto;
    background: var(--bg-primary);
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 25px;
    align-items: start;
}

.cursor-toolbox-body .email-section {
    grid-column: 1; /* 临时邮箱在左边 */
}

.cursor-toolbox-body .vpn-section {
    grid-column: 2; /* 推荐梯子在右边 */
}

.cursor-toolbox-body .card {
    margin-bottom: 0; /* 移除底部边距，使用grid-gap */
}







/* 响应式设计 */
@media (max-width: 768px) {
    .plugin-tutorial-content {
        max-width: 95vw;
        max-height: 95vh;
    }

    .plugin-tutorial-header {
        padding: 15px 20px;
    }

    .plugin-tutorial-header h3 {
        font-size: 18px;
    }
}
