<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-hAolFNc71jnKc95TcryusQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <script type="module" crossorigin src="./assets/main-panel-CD6EXLpt.js" nonce="nonce-hAolFNc71jnKc95TcryusQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Cx9dt_ox.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-BqzdgpkK.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BjDqXmYl.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/arrow-up-right-from-square-D2UwhhNo.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BCZOObrS.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-BiWRcmeV.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-DTMpOwfF.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-B-te1sXh.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-8LwCBeyq.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-Drc0SN5U.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/github-7gPAsyj4.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-CX_GXeEO.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BxQII05L.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/utils-DJhaageo.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-C_TwPNdv.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/check-BrrMO4vE.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-RumqAz-v.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-CGnj6T3o.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BWVRxMGM.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DEYj-_0J.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-8X-F_Twk.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-C7XQLqYW.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-CEjIF7oG.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-DUiNNixO.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-BAEKlH2H.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BFrX0piu.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DhtPLzGu.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BbVpV4C-.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CZwCjcp0.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DdgjewTP.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-C3J8hU1V.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-H61A9-v_.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-D-BqE8vd.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-Cm0UKVWz.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-8-Z76Y2_.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-BjJSzToG.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-D3vAw6HE.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-B3px2_jp.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-GjgruWjX.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/arrow-up-right-from-square-Df_FYENN.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CrDNmo5Z.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-B6hSl5-9.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DTcQ2vsq.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/folder-ol95BxhL.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-Dvw-pMXw.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-BFFBoxX3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-B1-rUMk8.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-YmaBgMfd.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
