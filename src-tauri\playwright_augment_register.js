
// 真实的 Playwright 自动注册脚本
const { chromium } = require('playwright');

(async () => {
    console.log('🚀 启动 Playwright 浏览器...');

    try {
        // 启动浏览器，使用反检测配置
        const browser = await chromium.launch({
            headless: false,  // 显示浏览器，便于观察
            args: [
                '--disable-blink-features=AutomationControlled',
                '--exclude-switches=enable-automation',
                '--disable-extensions',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-translate',
                '--disable-infobars',  // 隐藏信息栏
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
        });

        const context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1200, height: 800 },
            extraHTTPHeaders: {
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br'
            }
        });

        const page = await context.newPage();

        // 注入反检测脚本
        await page.addInitScript(() => {
            // 核心：隐藏 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 隐藏其他自动化特征
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });

            console.log('✅ 反检测脚本已注入，webdriver:', navigator.webdriver);
        });

        // 步骤1: 获取临时邮箱
        console.log('📧 获取临时邮箱...');
        await page.goto('https://awamail.com/');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);

        // 获取邮箱地址
        const emailText = await page.textContent('body');
        const emailMatch = emailText.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        const tempEmail = emailMatch ? emailMatch[1] : '<EMAIL>';
        console.log('📧 获取到邮箱:', tempEmail);

        // 步骤2: 打开注册页面
        console.log('🌐 导航到注册页面...');
        await page.goto('https://login.augmentcode.com/u/signup/identifier');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);

        // 步骤3: 填入邮箱地址
        console.log('✍️ 填入邮箱地址:', tempEmail);
        await page.fill('input[name="username"]', tempEmail);
        await page.waitForTimeout(2000);

        // 步骤4: 处理人机验证
        console.log('🤖 处理人机验证...');
        try {
            // 等待人机验证框出现
            await page.waitForSelector('input[type="checkbox"]', { timeout: 10000 });

            // 尝试勾选人机验证
            const checkboxes = await page.locator('input[type="checkbox"]').all();
            for (const checkbox of checkboxes) {
                if (await checkbox.isVisible()) {
                    await checkbox.check();
                    console.log('✅ 人机验证已勾选');
                    break;
                }
            }

            await page.waitForTimeout(3000);
        } catch (error) {
            console.log('⚠️ 未找到人机验证或已自动通过');
        }

        // 步骤5: 提交表单
        console.log('🚀 提交表单...');
        try {
            await page.click('button[name="action"][value="default"]');
            await page.waitForLoadState('networkidle');
            console.log('✅ 注册请求已提交');
        } catch (error) {
            console.log('⚠️ 提交失败，尝试其他按钮');
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
        }

        console.log('🎉 SUCCESS: Playwright 注册流程完成');

        // 保持浏览器打开以便观察结果
        console.log('🔍 保持浏览器打开 30 秒以便观察...');
        await page.waitForTimeout(30000);

    } catch (error) {
        console.error('❌ 注册过程中出错:', error);
        console.log('ERROR:', error.message);
    } finally {
        try {
            await browser.close();
        } catch (e) {
            console.log('浏览器关闭时出错:', e.message);
        }
    }
})().catch(console.error);
