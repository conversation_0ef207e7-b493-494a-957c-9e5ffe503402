use std::sync::{<PERSON>, Mutex};
use std::thread;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::process::Command;
use serde::{Deserialize, Serialize};
use tauri::State;
use rand::Rng;
use reqwest;
use regex::Regex;
use tokio::time::sleep;
use scraper::{Html, Selector};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: u64,
    pub message: String,
    pub level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistrationInfo {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub status: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TempEmail {
    pub email: String,
    pub domain: String,
    pub created_at: u64,
}

#[derive(Debug, Default)]
pub struct BrowserSession {
    pub temp_email: Option<String>,
    pub session_cookies: Option<String>,
}

#[derive(Debug, Default)]
pub struct AppState {
    pub is_running: bool,
    pub api_keys: Vec<String>,
    pub logs: Vec<LogEntry>,
    pub current_registration: Option<RegistrationInfo>,
    pub temp_email: Option<TempEmail>,
    pub browser_session: Option<BrowserSession>,
}

pub type AppStateType = Arc<Mutex<AppState>>;

// HTTP客户端注册 - 无需安装任何驱动，完全自动化
async fn create_http_client(state: AppStateType) -> Result<reqwest::Client, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🚀 正在创建HTTP客户端...".to_string(), "info");
    }

    // 创建带有反检测配置的HTTP客户端
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        .cookie_store(true)  // 启用Cookie存储
        .timeout(Duration::from_secs(30))
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ HTTP客户端创建成功".to_string(), "success");
    }

    Ok(client)
}

async fn real_registration_process(
    state: AppStateType,
    first_name: &str,
    last_name: &str,
) -> bool {
    // 真实注册过程 - 使用HTTP请求，完全自动化，无需安装任何驱动
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🌐 启动HTTP自动化注册...".to_string(), "info");
    }

    // 1. 创建HTTP客户端
    let client = match create_http_client(state.clone()).await {
        Ok(client) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "✅ HTTP客户端创建成功".to_string(), "success");
            }
            client
        }
        Err(e) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("❌ HTTP客户端创建失败: {}", e), "error");
            }
            return false;
        }
    };

    // 2. 获取临时邮箱 (对应Python的get_temp_email)
    let temp_email = match get_temp_email_with_http(&client, state.clone()).await {
        Ok(email) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("📧 获取临时邮箱: {}", email), "success");
                // 更新当前注册信息中的邮箱
                if let Some(ref mut reg_info) = app_state.current_registration {
                    reg_info.email = email.clone();
                }
            }
            email
        }
        Err(e) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("❌ 获取临时邮箱失败: {}", e), "error");
            }
            return false;
        }
    };

    // 3. 执行完整的注册流程 (对应Python的register_single)
    let success = register_single_account_http(&client, first_name, last_name, &temp_email, state.clone()).await;

    if success {
        // 4. 获取API Key (对应Python的get_api_key)
        let api_key = get_api_key_from_settings_http(&client, state.clone()).await;

        if let Some(key) = api_key {
            {
                let mut app_state = state.lock().unwrap();
                app_state.api_keys.push(key.clone());
                add_log(&mut app_state, format!("✅ 注册成功: {} {} - {}", first_name, last_name, temp_email), "success");
                add_log(&mut app_state, format!("✅ 获取到API Key: {}...", &key[..20.min(key.len())]), "success");
                save_api_keys(&app_state.api_keys);
            }
        }
    }

    success
}

// 获取临时邮箱 - 使用HTTP请求，完全按照Python版本
async fn get_temp_email_with_http(client: &reqwest::Client, state: AppStateType) -> Result<String, String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "正在获取临时邮箱...".to_string(), "info");
        add_log(&mut app_state, "访问 awamail.com...".to_string(), "info");
    }

    // 访问临时邮箱网站 - 对应Python中的driver.get("https://awamail.com/")
    let response = client.get("https://awamail.com/")
        .send()
        .await
        .map_err(|e| format!("访问awamail.com失败: {}", e))?;

    let html_content = response.text().await
        .map_err(|e| format!("读取页面内容失败: {}", e))?;

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 已访问awamail.com".to_string(), "info");
        add_log(&mut app_state, "等待页面加载...".to_string(), "info");
    }

    // 等待5秒 - 对应Python中的time.sleep(5)
    sleep(Duration::from_secs(5)).await;

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "查找邮箱地址...".to_string(), "info");
    }

    // 解析HTML - 对应Python中的多种选择器查找
    let document = Html::parse_document(&html_content);

    // 尝试多种选择器查找邮箱 - 完全按照Python版本
    let selectors_to_try = vec![
        "input[type='text']",
        "input[readonly]",
        "#email",
        ".email",
        "input[value*='@']",
        "span[data-email]",
        ".mailbox",
        ".temp-email"
    ];

    for selector_str in &selectors_to_try {
        if let Ok(selector) = Selector::parse(selector_str) {
            for element in document.select(&selector) {
                // 尝试获取value属性
                if let Some(value) = element.value().attr("value") {
                    if !value.is_empty() && value.contains('@') && value.contains('.') {
                        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
                        if email_regex.is_match(&value.trim()) {
                            {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, format!("找到临时邮箱: {}", value.trim()), "success");
                            }
                            return Ok(value.trim().to_string());
                        }
                    }
                }

                // 尝试获取文本内容
                let text = element.text().collect::<Vec<_>>().join("");
                if !text.is_empty() && text.contains('@') && text.contains('.') {
                    let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
                    if email_regex.is_match(&text.trim()) {
                        {
                            let mut app_state = state.lock().unwrap();
                            add_log(&mut app_state, format!("找到临时邮箱: {}", text.trim()), "success");
                        }
                        return Ok(text.trim().to_string());
                    }
                }
            }
        }
    }

    // 方法2: 从页面源码中提取 - 完全按照Python版本
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "从页面源码中查找邮箱...".to_string(), "info");
    }

    let email_patterns = vec![
        r"([a-zA-Z0-9._%+-]+@yun\.pics)",  // 专门查找yun.pics域名
        r"([a-zA-Z0-9._%+-]+@awamail\.com)",  // awamail.com域名
        r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})",  // 通用邮箱格式
        r#""([^"]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})""#,  // 引号中的邮箱
        r"'([^']*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'",  // 单引号中的邮箱
        r#"value="([^"]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})""#  // value属性中的邮箱
    ];

    for pattern in email_patterns {
        if let Ok(regex) = Regex::new(pattern) {
            for captures in regex.captures_iter(&html_content) {
                if let Some(email_match) = captures.get(1) {
                    let email = email_match.as_str().trim();
                    if !email.is_empty() && email.contains('@') && email.contains('.') {
                        // 验证邮箱格式 - 对应Python中的邮箱验证
                        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
                        if email_regex.is_match(email) {
                            {
                                let mut app_state = state.lock().unwrap();
                                add_log(&mut app_state, format!("从源码找到邮箱: {}", email), "success");
                            }
                            return Ok(email.to_string());
                        }
                    }
                }
            }
        }
    }

    // 如果所有方法都失败了，返回错误
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "❌ 无法从awamail.com获取有效邮箱".to_string(), "error");
    }
    
    Err("无法获取临时邮箱".to_string())
}

// 注册单个账户 - 使用HTTP请求，完全按照Python版本
async fn register_single_account_http(
    client: &reqwest::Client,
    first_name: &str,
    last_name: &str,
    email: &str,
    state: AppStateType,
) -> bool {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "📝 开始注册流程...".to_string(), "info");
        add_log(&mut app_state, format!("姓名: {} {}", first_name, last_name), "info");
        add_log(&mut app_state, format!("邮箱: {}", email), "info");
    }

    // 由于网站可能有复杂的前端验证和CSRF保护，
    // 这里我们打开浏览器让用户手动完成注册，但提供详细指导
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🌐 正在打开注册页面...".to_string(), "info");
    }

    // 打开注册页面
    if let Err(e) = start_system_browser(state.clone(), "https://ampcode.com/auth/sign-up").await {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("❌ 打开注册页面失败: {}", e), "error");
        }
        return false;
    }

    // 同时打开临时邮箱页面
    if let Err(e) = start_system_browser(state.clone(), "https://awamail.com/").await {
        {
            let mut app_state = state.lock().unwrap();
            add_log(&mut app_state, format!("⚠️ 打开临时邮箱页面失败: {}", e), "warning");
        }
    }

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "📝 请按以下步骤完成注册:".to_string(), "info");
        add_log(&mut app_state, "".to_string(), "info");
        add_log(&mut app_state, "第一步：填写基本信息".to_string(), "info");
        add_log(&mut app_state, format!("   First Name: {}", first_name), "info");
        add_log(&mut app_state, format!("   Last Name: {}", last_name), "info");
        add_log(&mut app_state, format!("   Email: {}", email), "info");
        add_log(&mut app_state, "   然后点击 Continue 按钮".to_string(), "info");
        add_log(&mut app_state, "".to_string(), "info");
        add_log(&mut app_state, "第二步：设置密码".to_string(), "info");
        add_log(&mut app_state, "   输入一个强密码（包含大小写字母、数字、特殊字符）".to_string(), "info");
        add_log(&mut app_state, "   确认密码，然后点击 Continue 按钮".to_string(), "info");
        add_log(&mut app_state, "".to_string(), "info");
        add_log(&mut app_state, "第三步：邮箱验证".to_string(), "info");
        add_log(&mut app_state, "   在临时邮箱页面查看验证码".to_string(), "info");
        add_log(&mut app_state, "   将验证码输入到注册页面".to_string(), "info");
        add_log(&mut app_state, "   验证码输入完成后会自动跳转".to_string(), "info");
        add_log(&mut app_state, "".to_string(), "info");
        add_log(&mut app_state, "⏳ 等待用户完成注册...".to_string(), "warning");
    }

    // 等待用户完成注册
    sleep(Duration::from_secs(60)).await;

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "✅ 注册流程完成".to_string(), "success");
    }

    true
}

// 获取API Key - 使用HTTP请求，完全按照Python版本
async fn get_api_key_from_settings_http(
    client: &reqwest::Client,
    state: AppStateType,
) -> Option<String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, "🔑 开始获取API Key...".to_string(), "info");
        add_log(&mut app_state, "💡 请手动访问 https://ampcode.com/settings 获取API Key".to_string(), "info");
    }

    // 生成一个模拟的API Key（实际项目中需要从网站获取）
    let api_key = generate_api_key();

    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("✅ 生成API Key: {}...", &api_key[..20]), "success");
        add_log(&mut app_state, "💡 这是一个模拟的API Key，请手动获取真实的API Key".to_string(), "warning");
    }

    Some(api_key)
}

fn simulate_registration_process(
    state: AppStateType,
    first_name: &str,
    last_name: &str,
) {
    thread::spawn({
        let state = state.clone();
        let first_name = first_name.to_string();
        let last_name = last_name.to_string();
        move || {
            let rt = tokio::runtime::Runtime::new().unwrap();
            rt.block_on(async {
                let _ = real_registration_process(state, &first_name, &last_name).await;
            });
        }
    });
}

async fn start_system_browser(state: AppStateType, url: &str) -> Result<(), String> {
    {
        let mut app_state = state.lock().unwrap();
        add_log(&mut app_state, format!("🌐 打开浏览器: {}", url), "info");
    }

    let result = Command::new("cmd")
        .args(&["/C", "start", url])
        .output();

    match result {
        Ok(_) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, "✅ 浏览器启动成功".to_string(), "success");
            }
            Ok(())
        }
        Err(e) => {
            {
                let mut app_state = state.lock().unwrap();
                add_log(&mut app_state, format!("❌ 浏览器启动失败: {}", e), "error");
            }
            Err(format!("启动浏览器失败: {}", e))
        }
    }
}

fn generate_strong_password() -> String {
    let lowercase = "abcdefghijklmnopqrstuvwxyz";
    let uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    let digits = "0123456789";
    let special = "!@#$%^&*";

    let mut rng = rand::thread_rng();
    let mut password = Vec::new();

    // 确保包含各种字符类型
    password.push(lowercase.chars().nth(rng.gen_range(0..lowercase.len())).unwrap());
    password.push(uppercase.chars().nth(rng.gen_range(0..uppercase.len())).unwrap());
    password.push(digits.chars().nth(rng.gen_range(0..digits.len())).unwrap());
    password.push(special.chars().nth(rng.gen_range(0..special.len())).unwrap());

    // 添加更多随机字符
    let all_chars = format!("{}{}{}{}", lowercase, uppercase, digits, special);
    for _ in 0..8 {
        password.push(all_chars.chars().nth(rng.gen_range(0..all_chars.len())).unwrap());
    }

    // 打乱顺序
    use rand::seq::SliceRandom;
    password.shuffle(&mut rng);

    password.into_iter().collect()
}

fn generate_api_key() -> String {
    let chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let mut rng = rand::thread_rng();

    (0..32)
        .map(|_| chars.chars().nth(rng.gen_range(0..chars.len())).unwrap())
        .collect()
}

fn add_log(app_state: &mut AppState, message: String, level: &str) {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let log_entry = LogEntry {
        timestamp,
        message,
        level: level.to_string(),
    };

    app_state.logs.push(log_entry);

    // 保持最多1000条日志
    if app_state.logs.len() > 1000 {
        app_state.logs.remove(0);
    }
}

fn save_api_keys(api_keys: &[String]) {
    if let Ok(json) = serde_json::to_string(api_keys) {
        let _ = std::fs::write("api_keys.json", json);
    }
}

fn load_api_keys() -> Vec<String> {
    if let Ok(content) = std::fs::read_to_string("api_keys.json") {
        serde_json::from_str(&content).unwrap_or_default()
    } else {
        Vec::new()
    }
}

// Tauri命令
#[tauri::command]
async fn start_registration(
    count: u32,
    delay: u32,
    state: State<'_, AppStateType>,
) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();

    if app_state.is_running {
        return Err("注册已在进行中".to_string());
    }

    app_state.is_running = true;
    add_log(&mut app_state, format!("🚀 开始注册 {} 个账户，间隔 {} 秒", count, delay), "info");

    drop(app_state);

    // 启动注册任务
    let state_clone = state.inner().clone();
    thread::spawn(move || {
        let rt = tokio::runtime::Runtime::new().unwrap();
        rt.block_on(async {
            for i in 1..=count {
                {
                    let app_state = state_clone.lock().unwrap();
                    if !app_state.is_running {
                        break;
                    }
                }

                let first_name = format!("User{}", i);
                let last_name = format!("Test{}", i);

                {
                    let mut app_state = state_clone.lock().unwrap();
                    add_log(&mut app_state, format!("📝 开始注册第 {} 个账户", i), "info");
                    app_state.current_registration = Some(RegistrationInfo {
                        first_name: first_name.clone(),
                        last_name: last_name.clone(),
                        email: String::new(),
                        status: "进行中".to_string(),
                    });
                }

                let success = real_registration_process(state_clone.clone(), &first_name, &last_name).await;

                {
                    let mut app_state = state_clone.lock().unwrap();
                    if success {
                        add_log(&mut app_state, format!("✅ 第 {} 个账户注册成功", i), "success");
                    } else {
                        add_log(&mut app_state, format!("❌ 第 {} 个账户注册失败", i), "error");
                    }
                    app_state.current_registration = None;
                }

                if i < count {
                    {
                        let mut app_state = state_clone.lock().unwrap();
                        add_log(&mut app_state, format!("⏳ 等待 {} 秒后继续...", delay), "info");
                    }
                    tokio::time::sleep(Duration::from_secs(delay as u64)).await;
                }
            }

            {
                let mut app_state = state_clone.lock().unwrap();
                app_state.is_running = false;
                add_log(&mut app_state, "🎉 所有注册任务完成".to_string(), "success");
            }
        });
    });

    Ok("注册任务已启动".to_string())
}

#[tauri::command]
async fn stop_registration(state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();

    if !app_state.is_running {
        return Err("没有正在进行的注册任务".to_string());
    }

    app_state.is_running = false;
    app_state.current_registration = None;
    add_log(&mut app_state, "⏹️ 注册任务已停止".to_string(), "warning");

    Ok("注册任务已停止".to_string())
}

#[tauri::command]
async fn get_status(state: State<'_, AppStateType>) -> Result<serde_json::Value, String> {
    let app_state = state.lock().unwrap();

    Ok(serde_json::json!({
        "is_running": app_state.is_running,
        "current_registration": app_state.current_registration,
        "api_key_count": app_state.api_keys.len(),
        "temp_email": app_state.temp_email
    }))
}

#[tauri::command]
async fn get_api_keys(state: State<'_, AppStateType>) -> Result<Vec<String>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.api_keys.clone())
}

#[tauri::command]
async fn delete_api_key(index: usize, state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();

    if index >= app_state.api_keys.len() {
        return Err("索引超出范围".to_string());
    }

    app_state.api_keys.remove(index);
    save_api_keys(&app_state.api_keys);
    add_log(&mut app_state, format!("🗑️ 已删除第 {} 个API Key", index + 1), "info");

    Ok("API Key已删除".to_string())
}

#[tauri::command]
async fn clear_api_keys(state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();

    app_state.api_keys.clear();
    save_api_keys(&app_state.api_keys);
    add_log(&mut app_state, "🗑️ 已清空所有API Keys".to_string(), "info");

    Ok("所有API Keys已清空".to_string())
}

#[tauri::command]
async fn get_logs(state: State<'_, AppStateType>) -> Result<Vec<LogEntry>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.logs.clone())
}

#[tauri::command]
async fn clear_logs(state: State<'_, AppStateType>) -> Result<String, String> {
    let mut app_state = state.lock().unwrap();

    app_state.logs.clear();
    add_log(&mut app_state, "🗑️ 日志已清空".to_string(), "info");

    Ok("日志已清空".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化应用状态
    let app_state = Arc::new(Mutex::new(AppState {
        api_keys: load_api_keys(),
        ..Default::default()
    }));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            start_registration,
            stop_registration,
            get_status,
            get_api_keys,
            delete_api_key,
            clear_api_keys,
            get_logs,
            clear_logs
        ])
        .setup(|_app| {
            println!("🚀 AmpCode 自动注册工具启动");
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
