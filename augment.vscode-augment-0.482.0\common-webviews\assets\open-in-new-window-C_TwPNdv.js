var h=Object.defineProperty;var g=(r,e,a)=>e in r?h(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a;var l=(r,e,a)=>g(r,typeof e!="symbol"?e+"":e,a);import{W as i}from"./BaseButton-BqzdgpkK.js";import{a3 as p,S as u,i as y,s as C,b as c,c as n,e as f,f as F,n as d,h as _}from"./SpinnerAugment-Cx9dt_ox.js";class w{constructor(e){l(this,"_applyingFilePaths",p([]));l(this,"_appliedFilePaths",p([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(a=>{e=a})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(a=>{e=a})(),e}async getDiffExplanation(e,a,s=3e4){try{return(await this._asyncMsgSender.send({type:i.diffExplanationRequest,data:{changedFiles:e,apikey:a}},s)).data.explanation}catch(t){return console.error("Failed to get diff explanation:",t),[]}}async groupChanges(e,a=!1,s){try{return(await this._asyncMsgSender.send({type:i.diffGroupChangesRequest,data:{changedFiles:e,changesById:a,apikey:s}})).data.groupedChanges}catch(t){return console.error("Failed to group changes:",t),[]}}async getDescriptions(e,a){try{const s=await this._asyncMsgSender.send({type:i.diffDescriptionsRequest,data:{groupedChanges:e,apikey:a}},1e5);return{explanation:s.data.explanation,error:s.data.error}}catch(s){return console.error("Failed to get descriptions:",s),{explanation:[],error:`Failed to get descriptions: ${s instanceof Error?s.message:String(s)}`}}}async applyChanges(e,a,s){this._applyingFilePaths.update(t=>[...t.filter(o=>o!==e),e]);try{(await this._asyncMsgSender.send({type:i.applyChangesRequest,data:{path:e,originalCode:a,newCode:s}},3e4)).data.success&&this._appliedFilePaths.update(t=>[...t.filter(o=>o!==e),e])}catch(t){console.error("applyChanges error",t)}finally{this._applyingFilePaths.update(t=>t.filter(o=>o!==e))}}async openFile(e){try{const a=await this._asyncMsgSender.send({type:i.openFileRequest,data:{path:e}},1e4);return a.data.success||console.error("Failed to open file:",a.data.error),a.data.success}catch(a){console.error("openFile error",a)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:i.reportAgentChangesApplied})}}l(w,"key","remoteAgentsDiffOpsModel");function x(r){let e,a;return{c(){e=c("svg"),a=c("path"),n(a,"fill-rule","evenodd"),n(a,"clip-rule","evenodd"),n(a,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),n(a,"fill","currentColor"),n(e,"class",r[0]),n(e,"width","15"),n(e,"height","15"),n(e,"viewBox","0 0 15 15"),n(e,"fill","none"),n(e,"xmlns","http://www.w3.org/2000/svg")},m(s,t){f(s,e,t),F(e,a)},p(s,[t]){1&t&&n(e,"class",s[0])},i:d,o:d,d(s){s&&_(e)}}}function M(r,e,a){let{class:s=""}=e;return r.$$set=t=>{"class"in t&&a(0,s=t.class)},[s]}class V extends u{constructor(e){super(),y(this,e,M,x,C,{class:0})}}export{V as O,w as R};
