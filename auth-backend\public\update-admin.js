// 更新管理JavaScript

// DOM元素
const elements = {
    currentVersion: document.getElementById('currentVersion'),
    latestVersion: document.getElementById('latestVersion'),
    updateStatus: document.getElementById('updateStatus'),
    releaseDate: document.getElementById('releaseDate'),
    fileSize: document.getElementById('fileSize'),
    versionDescription: document.getElementById('versionDescription'),
    featuresList: document.getElementById('featuresList'),
    updateForm: document.getElementById('updateForm'),
    alertContainer: document.getElementById('alertContainer')
};

// 显示提示信息
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${type}`;
    alertDiv.textContent = message;
    
    elements.alertContainer.innerHTML = '';
    elements.alertContainer.appendChild(alertDiv);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 加载版本信息
async function loadVersionInfo() {
    try {
        const response = await fetch('/api/update/info');
        const data = await response.json();
        
        if (data.success) {
            const info = data.data;
            
            // 更新界面显示
            elements.currentVersion.textContent = info.currentVersion;
            elements.latestVersion.textContent = info.latestVersion;
            elements.releaseDate.textContent = info.updateInfo.releaseDate;
            elements.fileSize.textContent = info.updateInfo.fileSize;
            elements.versionDescription.textContent = info.updateInfo.description;
            
            // 更新状态
            const statusElement = elements.updateStatus;
            if (info.hasUpdate) {
                statusElement.textContent = '有新版本可用';
                statusElement.className = 'status available';
            } else {
                statusElement.textContent = '已是最新版本';
                statusElement.className = 'status latest';
            }
            
            // 更新功能列表
            elements.featuresList.innerHTML = '';
            info.updateInfo.features.forEach(feature => {
                const li = document.createElement('li');
                li.textContent = feature;
                elements.featuresList.appendChild(li);
            });
            
        } else {
            showAlert('加载版本信息失败: ' + data.message, 'error');
        }
        
    } catch (error) {
        console.error('加载版本信息失败:', error);
        showAlert('加载版本信息失败: ' + error.message, 'error');
    }
}

// 发布新版本
async function publishUpdate(formData) {
    try {
        const response = await fetch('/api/update/admin/version', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('新版本发布成功！', 'success');
            // 重新加载版本信息
            await loadVersionInfo();
            // 清空表单
            elements.updateForm.reset();
        } else {
            showAlert('发布失败: ' + data.message, 'error');
        }
        
    } catch (error) {
        console.error('发布新版本失败:', error);
        showAlert('发布失败: ' + error.message, 'error');
    }
}

// 表单提交处理
elements.updateForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    // 获取表单数据
    const formData = new FormData(e.target);
    const features = formData.get('features').split('\n').filter(f => f.trim());
    
    const updateData = {
        version: formData.get('version'),
        description: formData.get('description'),
        features: features,
        downloadUrl: formData.get('downloadUrl'),
        fileSize: formData.get('fileSize'),
        mandatory: formData.has('mandatory')
    };
    
    // 验证数据
    if (!updateData.version || !updateData.description || features.length === 0) {
        showAlert('请填写所有必填字段', 'error');
        return;
    }
    
    // 发布更新
    await publishUpdate(updateData);
});

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    loadVersionInfo();
    
    // 设置默认下载URL
    const versionInput = document.getElementById('version');
    const downloadUrlInput = document.getElementById('downloadUrl');
    
    versionInput.addEventListener('input', (e) => {
        const version = e.target.value;
        if (version) {
            downloadUrlInput.value = `${window.location.origin}/downloads/magic-box-${version}.exe`;
        }
    });
});

// 定期刷新版本信息（每30秒）
setInterval(loadVersionInfo, 30000);
