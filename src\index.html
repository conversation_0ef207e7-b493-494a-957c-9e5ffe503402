<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✨ Magic Box</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="config.js"></script>
</head>
<body>
    <!-- 授权验证弹窗 -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-modal-content">
            <div class="auth-header">
                <h2>🔐 授权验证</h2>
                <p>请输入授权码以使用 Magic Box</p>
                <div class="customer-service">
                    <p><strong>🎯 客服QQ：362856178</strong></p>
                </div>
            </div>
            <form id="auth-form">
                <div class="form-group">
                    <label for="auth-code">授权码</label>
                    <input type="text" id="auth-code" placeholder="请输入授权码" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">验证授权</button>
                </div>
            </form>
            <div id="auth-error" class="auth-error hidden"></div>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="confirm-modal" class="confirm-modal">
        <div class="confirm-modal-content">
            <div class="confirm-header">
                <i class="fas fa-exclamation-triangle"></i>
                <h3 id="confirm-title">确认操作</h3>
            </div>
            <div class="confirm-body">
                <p id="confirm-message">您确定要执行此操作吗？</p>
            </div>
            <div class="confirm-actions">
                <button id="confirm-cancel" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button id="confirm-ok" class="btn btn-danger">
                    <i class="fas fa-check"></i>
                    确定
                </button>
            </div>
        </div>
    </div>

    <div class="app">
        <!-- 自定义标题栏 -->
        <div class="custom-titlebar" data-tauri-drag-region>
            <div class="titlebar-content">
                <div class="titlebar-title">
                    <img src="assets/icon.png" alt="Magic Box" class="titlebar-icon">
                    Magic Box
                </div>
                <div class="titlebar-controls">
                    <button id="minimize-btn" class="titlebar-btn minimize-btn">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button id="close-btn" class="titlebar-btn close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-content">
                <!-- 模式切换按钮 -->
                <div class="mode-switcher">
                    <button id="augmentModeBtn" class="mode-btn active">
                        <i class="fas fa-rocket"></i>
                        Augment
                    </button>
                    <button id="cursorModeBtn" class="mode-btn">
                        <i class="fas fa-mouse-pointer"></i>
                        Cursor
                    </button>
                    <button id="apiQuotaModeBtn" class="mode-btn">
                        <i class="fas fa-chart-line"></i>
                        API额度
                    </button>
                </div>
                <!-- 检查更新按钮和版本号 -->
                <div class="header-right">
                    <button id="checkUpdateBtn" class="btn btn-header">
                        <i class="fas fa-download"></i>
                        检查更新
                    </button>
                    <span id="header-version" class="version-display">v-.-.-</span>
                </div>
            </div>
        </header>

        <!-- 公告区域 -->
        <div id="announcements-section" class="announcements-section hidden">
            <div id="announcements-container" class="announcements-container">
                <!-- 公告内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧控制面板 -->
            <aside class="left-panel">
                <!-- Augment模式界面 -->
                <div id="augmentMode" class="mode-content active">


                    <!-- 清理工具区域 -->
                    <section class="card control-section">
                        <h3 class="section-title">
                            <i class="fas fa-broom"></i>
                            清理工具
                        </h3>
                        <button id="vscodeCleanBtn" class="btn btn-primary btn-large">
                            <img src="assets/Vscode.png" alt="VSCode" class="btn-icon">
                            VSCode 注入
                        </button>
                        <button id="ideaMemoryCleanBtn" class="btn btn-success btn-large">
                            <img src="assets/idea.png" alt="IDEA" class="btn-icon">
                            IDEA 注入
                        </button>

                    </section>

                    <!-- VSCode Augment 账号管理 -->
                    <section class="card account-section">
                        <h3 class="section-title">
                            <i class="fas fa-users"></i>
                            账号管理
                        </h3>
                        <div class="account-controls">
                            <div class="current-account">
                                <div class="account-info">
                                    <span class="account-label">当前账号:</span>
                                    <span id="currentAccountName" class="account-name">未选择</span>
                                </div>
                                <div class="account-quota">
                                    <span class="quota-label">剩余额度:</span>
                                    <span id="currentAccountQuota" class="quota-value">-</span>
                                </div>
                            </div>
                            <div class="account-actions">
                                <button id="refreshAccountsBtn" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-sync-alt"></i>
                                    刷新
                                </button>
                                <button id="autoSwitchBtn" class="btn btn-success btn-sm">
                                    <i class="fas fa-magic"></i>
                                    智能切换
                                </button>
                            </div>
                        </div>
                        <div id="accountsList" class="accounts-list">
                            <!-- 账号列表将通过JavaScript动态加载 -->
                        </div>
                    </section>

                    <!-- 万能工具箱 -->
                    <section class="card toolbox-section">
                        <button id="universalToolboxBtn" class="btn btn-info btn-large">
                            <i class="fas fa-toolbox"></i>
                            万能工具箱
                        </button>
                    </section>




                </div>

                <!-- Cursor模式界面 -->
                <div id="cursorMode" class="mode-content">


                    <!-- Cursor工具区域 -->
                    <section class="card control-section">
                        <h3 class="section-title">
                            <i class="fas fa-tools"></i>
                            免费额度工具
                        </h3>
                        <button id="cursorCacheBtn" class="btn btn-success btn-large">
                            <i class="fas fa-broom"></i>
                            清理数据
                        </button>
                        <button id="cursorUsageBtn" class="btn btn-info btn-large">
                            <i class="fas fa-chart-bar"></i>
                            使用量查看
                        </button>
                    </section>

                    <!-- 万能工具箱 -->
                    <section class="card toolbox-section">
                        <button id="cursorUniversalToolboxBtn" class="btn btn-info btn-large">
                            <i class="fas fa-toolbox"></i>
                            万能工具箱
                        </button>
                    </section>

                    <!-- 使用量显示区域 -->
                    <section id="usageSection" class="card usage-section" style="display: none;">
                        <h3 class="section-title">
                            <i class="fas fa-chart-pie"></i>
                            当前使用量
                        </h3>
                        <div id="usageDisplay" class="usage-display">
                            <div class="usage-item">
                                <span class="usage-label">已使用:</span>
                                <span id="usageUsed" class="usage-value">-</span>
                            </div>
                            <div class="usage-item">
                                <span class="usage-label">总限额:</span>
                                <span id="usageTotal" class="usage-value">-</span>
                            </div>
                            <div class="usage-item">
                                <span class="usage-label">使用率:</span>
                                <span id="usagePercentage" class="usage-value">-</span>
                            </div>
                            <div class="usage-item">
                                <span class="usage-label">剩余:</span>
                                <span id="usageRemaining" class="usage-value">-</span>
                            </div>
                            <div class="usage-progress">
                                <div id="usageProgressBar" class="usage-progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="usage-footer">
                                <span id="lastUpdateTime" class="last-update">点击查看使用量</span>
                                <span class="auto-refresh-info">
                                    <span id="refreshCountdown" class="countdown-number">30</span>
                                    <span class="countdown-text">秒后刷新</span>
                                </span>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- API额度模式界面 -->
                <div id="apiQuotaMode" class="mode-content">
                    <!-- API配置区域 -->
                    <section class="card control-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-cog"></i>
                                API配置
                            </h3>
                            <button id="stopTestingBtn" class="btn btn-danger btn-sm" style="display: none;">
                                <i class="fas fa-stop"></i>
                                停止测试
                            </button>
                        </div>
                        <div class="api-form">
                            <div class="form-group">
                                <label for="apiUrl">API地址:</label>
                                <input type="text" id="apiUrl" class="form-control"
                                       placeholder="https://api.openai.com">
                            </div>
                            <div class="form-group">
                                <label for="apiKey">API密钥:</label>
                                <input type="password" id="apiKey" class="form-control"
                                       placeholder="sk-...">
                            </div>
                            <div class="form-actions">
                                <button id="checkQuotaBtn" class="btn btn-primary btn-large">
                                    <i class="fas fa-search"></i>
                                    检查额度
                                </button>
                                <button id="testModelsBtn" class="btn btn-success btn-large">
                                    <i class="fas fa-flask"></i>
                                    测试模型
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- 额度显示区域 -->
                    <section id="quotaSection" class="card quota-section" style="display: none;">
                        <h3 class="section-title">
                            <i class="fas fa-chart-pie"></i>
                            额度信息
                        </h3>
                        <div id="quotaDisplay" class="quota-display">
                            <div class="quota-item">
                                <span class="quota-label">总额度:</span>
                                <span id="quotaTotal" class="quota-value">-</span>
                            </div>
                            <div class="quota-item">
                                <span class="quota-label">已使用:</span>
                                <span id="quotaUsed" class="quota-value">-</span>
                            </div>
                            <div class="quota-item">
                                <span class="quota-label">剩余:</span>
                                <span id="quotaRemaining" class="quota-value">-</span>
                            </div>
                            <div class="quota-item">
                                <span class="quota-label">使用率:</span>
                                <span id="quotaPercentage" class="quota-value">-</span>
                            </div>
                            <div class="quota-progress">
                                <div id="quotaProgressBar" class="quota-progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="quota-footer">
                                <span id="quotaLastUpdate" class="last-update">点击检查额度</span>
                            </div>
                        </div>
                    </section>


                </div>

                <!-- 底部区域：状态栏 -->
                <div class="bottom-section">
                    <!-- 状态显示 -->
                    <div class="status-bar">
                        <div id="statusIndicator" class="status-indicator status-ready">
                            <i class="fas fa-circle"></i>
                            <span id="statusText">就绪</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 中间教程面板 -->
            <section class="middle-panel">
                <!-- Augment模式教程 -->
                <div id="augmentTutorial" class="card tutorial-panel active">
                    <div class="tutorial-header">
                        <div class="tutorial-tabs">
                            <div class="tab-slider">
                                <button id="vscodeTabBtn" class="tab-btn active">VSCode</button>
                                <button id="ideaTabBtn" class="tab-btn">IDEA</button>
                                <button id="augmentTabBtn" class="tab-btn">降智方法</button>
                                <div class="tab-indicator"></div>
                            </div>
                        </div>
                    </div>

                    <!-- VSCode教程 -->
                    <div id="vscodeTab" class="tutorial-content active">
                        <div class="tutorial-card">
                            <h4><i class="fas fa-code"></i> VS教程<span style="color: red;">（插件退回到0.453.1版本！）</span></h4>
                            <div class="tutorial-steps">
                                <div class="step">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <strong>自动化清理作业</strong>
                                        <p>点击"VSCode 注入"按钮即可自动完成所有操作</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <strong>智能进程管理</strong>
                                        <p>自动检测并关闭运行中的VSCode</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">3</span>
                                    <div class="step-content">
                                        <strong>精准数据清理和用户设置</strong>
                                        <p>更新设备标识，清理遥测数据，保留聊天记录</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">4</span>
                                    <div class="step-content">
                                        <strong>自动重启</strong>
                                        <p>清理完成后自动重新启动之前关闭的编辑器</p>
                                    </div>
                                </div>
                            </div>
                            <div class="tutorial-note">
                                <i class="fas fa-info-circle"></i>
                                <strong>注意：</strong><span class="important-warning">一定要插件退回到0.453.1版本！</span>
                            </div>
                        </div>
                    </div>

                    <!-- Augment插件回退教程 -->
                    <div id="pluginTutorialContainer" class="tutorial-card" style="margin-top: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 16px;">
                            <div style="flex: 1; margin-right: 20px;">
                                <h4 style="margin: 0; color: var(--text-primary); font-size: 13px;">
                                    Augment插件回退教程
                                </h4>
                                <p style="margin: 2px 0 0 0; color: var(--text-secondary); font-size: 11px; line-height: 1.3;">
                                    详细的插件版本回退操作指南<span style="color: var(--color-primary); font-weight: 600;">（仅限vscode）</span>
                                </p>
                            </div>
                            <button id="showPluginTutorialBtn" class="btn btn-primary" style="min-width: 100px; flex-shrink: 0;">
                                <i class="fas fa-eye" style="margin-right: 4px;"></i>
                                点我查看
                            </button>
                        </div>
                    </div>

                    <!-- IDEA教程 -->
                    <div id="ideaTab" class="tutorial-content">
                        <div class="tutorial-card">
                            <h4><i class="fas fa-lightbulb"></i> IDEA 劫持教程<span style="color: red;">（请手动退出旧账号！）</span></h4>
                            <div class="tutorial-steps">
                                <div class="step">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <strong>自动化操作</strong>
                                        <p>点击"IDEA 劫持"按钮即可自动完成所有操作</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <strong>支持所有idea系列</strong>
                                        <p>自动反射注入，等待即可，支持（IDEA、PyCharm、WebStorm等）</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">3</span>
                                    <div class="step-content">
                                        <strong>设备更新</strong>
                                        <p>保留项目数据和个人设置</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">4</span>
                                    <div class="step-content">
                                        <strong>手动重启</strong>
                                        <p>劫持完成后重新启动之前关闭的IDE</p>
                                    </div>
                                </div>
                            </div>
                            <div class="tutorial-note">
                                <i class="fas fa-info-circle"></i>
                                <strong>注意：</strong>Your account 你的邮箱 has been suspended because we believe you may be using multiple free tral Please contact support here for more information.如果遇到这个提示就是邮箱有问题，换别的后缀邮箱就行！<br>
                                <span class="important-warning">次数用完一定要退出账号，旧的账号不要使用！！！</span>
                            </div>
                        </div>
                    </div>

                    <!-- 降智方法教程 -->
                    <div id="augmentTab" class="tutorial-content">
                        <div class="tutorial-card">
                            <h4><i class="fas fa-brain"></i> Augment插件设置</h4>
                            <div class="tutorial-steps">
                                <div class="step">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <strong>打开User Guidelines设置</strong>
                                        <p>点击User Guidelines看见这个User Guidelines在输入框输入：</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <strong>复制配置内容</strong>
                                        <div class="config-content">
                                            <button class="copy-btn" onclick="copyAugmentConfig()">
                                                <i class="fas fa-copy"></i> 复制配置
                                            </button>
                                            <div class="config-text" id="augmentConfig">Communication Language: Simplified Chinese
Basic Requirements: Master the augmentContextEngine
Used Model: Claude Sonnet 4
Please keep in mind that you are the Claude Sonnet 4 model;
The user's question is a complex one. Please take it seriously and use ACE (Continue after collecting sufficient information through augmentContextEngine);</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tutorial-note">
                                <i class="fas fa-info-circle"></i>
                                <strong>注意：</strong>复制上述配置内容到User Guidelines输入框中即可
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Cursor模式教程 -->
                <div id="cursorTutorial" class="card tutorial-panel">
                    <div class="tutorial-header">
                        <h3 class="tutorial-title">
                            <i class="fas fa-mouse-pointer"></i>
                            Cursor 使用教程
                        </h3>
                    </div>

                    <div class="tutorial-content active">
                        <div class="tutorial-card">
                            <h4><i class="fas fa-mouse-pointer"></i> Cursor 免费额度获取教程</h4>
                            <div class="tutorial-steps">
                                <div class="step">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <strong>确保Cursor安装在C盘</strong>
                                        <p>请注册Cursor时安装在C盘的默认位置</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <strong>突破claude-3.7的限制</strong>
                                        <p>（提示词先备份好）</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">3</span>
                                    <div class="step-content">
                                        <strong>如果还是不行，一定要换IP</strong>
                                        <p style="color: red; font-weight: bold;">市面上的那些claude-3.7max和claude-4那些免费用的，都是骗子，自慰的！！！</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <span class="step-number">4</span>
                                    <div class="step-content">
                                        <strong>本软件靠手艺吃饭，不弄虚作假</strong>
                                        <p>额度用完退出账号，重新注册账号，这个自行操作</p>
                                    </div>
                                </div>
                            </div>
                            <div class="tutorial-note">
                                <i class="fas fa-info-circle"></i>
                                <strong>注意：</strong>点击"清理数据"按钮可自动获取新的免费额度<br>
                                <span class="important-warning">次数用完一定要退出账号，旧的账号不要使用！！！</span>
                            </div>
                        </div>
                    </div>
                </div>


            </section>

            <!-- 右侧面板 -->
            <section class="right-panel">
                <!-- 运行日志面板 (Augment和Cursor模式) -->
                <div id="logPanel" class="card log-panel panel-visible">
                    <div class="log-header">
                        <h3 class="log-title">
                            <i class="fas fa-terminal"></i>
                            运行日志
                        </h3>
                        <button id="clearLogBtn" class="btn btn-sm">
                            <i class="fas fa-broom"></i>
                            清空
                        </button>
                    </div>
                    <div class="log-content">
                        <div id="logContainer" class="log-container">
                            <!-- 日志将通过 JavaScript 动态添加 -->
                        </div>
                    </div>
                </div>

                <!-- 模型测试结果面板 (API额度模式) -->
                <div id="modelsPanel" class="card models-panel panel-hidden">
                    <div class="models-header">
                        <div class="models-title-section">
                            <h3 class="models-title">
                                <i class="fas fa-list"></i>
                                模型测试结果
                            </h3>
                            <div id="modelsProgress" class="models-progress-inline" style="display: none;">
                                <div class="progress-spinner">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                                <span id="modelsProgressText">准备测试...</span>
                            </div>
                        </div>
                        <div class="header-controls">
                            <div class="pagination-controls" style="display: none;">
                                <button id="prevPageBtn" class="btn btn-sm">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button id="nextPageBtn" class="btn btn-sm">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <button id="clearModelsBtn" class="btn btn-sm">
                                <i class="fas fa-broom"></i>
                                清空
                            </button>
                        </div>
                    </div>
                    <div class="models-content">
                        <div class="models-stats">
                            <div class="stat-item">
                                <span class="stat-label">可用:</span>
                                <span id="validModelsCount" class="stat-value valid">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">不可用:</span>
                                <span id="invalidModelsCount" class="stat-value invalid">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">不一致:</span>
                                <span id="inconsistentModelsCount" class="stat-value inconsistent">0</span>
                            </div>
                        </div>

                        <div id="modelsTable" class="models-table">
                            <table class="models-table-content">
                                <thead>
                                    <tr>
                                        <th class="col-status">模型状态</th>
                                        <th class="col-model">模型名称</th>
                                        <th class="col-time">用时</th>
                                        <th class="col-remark">备注</th>
                                        <th class="col-verify">验证</th>
                                    </tr>
                                </thead>
                                <tbody id="modelsTableBody">
                                    <!-- 模型列表将通过JavaScript动态添加 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 模型选择弹窗 -->
    <div id="modelSelectionModal" class="model-selection-modal" style="display: none;">
        <div class="model-selection-content">
            <div class="model-selection-header">
                <h3>选择模型</h3>
                <button class="model-selection-close">&times;</button>
            </div>
            <div class="model-selection-body">
                <div class="model-selection-info">
                    已选择 <span id="selectedModelCount">0</span> 个模型
                </div>
                <div class="model-selection-controls">
                    <input type="text" id="modelFilterInput" placeholder="输入模型关键词进行筛选" class="model-filter-input">
                    <div class="model-selection-buttons">
                        <button id="selectAllModelsBtn" class="btn btn-sm">筛选</button>
                        <button id="clearSelectionBtn" class="btn btn-sm">清空</button>
                    </div>
                </div>
                <div class="model-selection-options">
                    <label class="model-option">
                        <input type="checkbox" id="selectAllCheckbox"> 全选
                    </label>
                    <label class="model-option">
                        <input type="checkbox" id="selectChatModelsCheckbox"> 全选聊天模型
                    </label>
                </div>
                <div id="modelSelectionList" class="model-selection-list">
                    <!-- 模型列表将通过JavaScript动态添加 -->
                </div>
            </div>
            <div class="model-selection-footer">
                <button id="cancelModelSelectionBtn" class="btn btn-secondary">取消</button>
                <button id="confirmModelSelectionBtn" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

    <!-- Augment插件回退教程弹窗 -->
    <div id="pluginTutorialModal" class="plugin-tutorial-modal" style="display: none;">
        <div class="plugin-tutorial-overlay"></div>
        <div class="plugin-tutorial-content">
            <div class="plugin-tutorial-header">
                <h3>Augment插件回退教程</h3>
                <button class="plugin-tutorial-close" id="closePluginTutorialBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="plugin-tutorial-body">
                <img src="assets/chajianhuitui.png" alt="插件回退教程" class="plugin-tutorial-image">
            </div>
        </div>
    </div>

    <!-- 万能工具箱弹窗 -->
    <div id="universalToolboxModal" class="universal-toolbox-modal" style="display: none;">
        <div class="universal-toolbox-overlay"></div>
        <div class="universal-toolbox-content">
            <div class="universal-toolbox-header">
                <h3>万能工具箱</h3>
                <button class="universal-toolbox-close" id="closeUniversalToolboxBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="universal-toolbox-body">
                <!-- 数值显示区域 -->
                <section class="card session-id-section">
                    <div class="session-id-header">
                        <h3>当前数值</h3>
                    </div>

                    <!-- VSCode 数值 -->
                    <div class="session-id-item">
                        <div class="session-id-label">
                            <div class="label-content">
                                <img src="assets/Vscode.png" alt="VSCode" class="label-icon">
                                <span>VSCode 数值</span>
                            </div>
                            <button id="refreshVscodeSessionIdBtn" class="btn btn-secondary btn-small">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                        <div class="session-id-content">
                            <div class="input-group">
                                <input type="text" id="currentVscodeSessionId" class="form-control session-id-input"
                                       placeholder="点击刷新按钮获取数值..." readonly>
                            </div>
                            <div class="session-id-status" id="vscodeSessionIdStatus">
                                <i class="fas fa-info-circle"></i>
                                <span>未获取</span>
                            </div>
                        </div>
                    </div>

                    <!-- IDEA 数值 -->
                    <div class="session-id-item">
                        <div class="session-id-label">
                            <div class="label-content">
                                <img src="assets/idea.png" alt="IDEA" class="label-icon">
                                <span>IDEA 数值</span>
                            </div>
                            <button id="refreshSessionIdBtn" class="btn btn-secondary btn-small">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                        <div class="session-id-content">
                            <div class="input-group">
                                <input type="text" id="currentSessionId" class="form-control session-id-input"
                                       placeholder="点击刷新按钮获取数值..." readonly>
                            </div>
                            <div class="session-id-status" id="sessionIdStatus">
                                <i class="fas fa-info-circle"></i>
                                <span>未获取</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 临时邮箱推荐 -->
                <section class="card email-section">
                    <h3 class="section-title">
                        <i class="fas fa-envelope"></i>
                        推荐临时邮箱
                    </h3>
                    <p class="email-description">需要注册新账号时推荐使用：</p>
                    <div class="email-links">
                        <a href="https://awamail.com/" target="_blank" class="email-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            awa临时邮箱
                        </a>
                        <a href="https://ducktempmail.netlify.app/" target="_blank" class="email-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            duck临时邮箱
                        </a>
                    </div>
                </section>

                <!-- 推荐梯子 -->
                <section class="card vpn-section">
                    <h3 class="section-title">
                        <i class="fas fa-globe"></i>
                        推荐梯子
                    </h3>
                    <p class="vpn-description">建议购买IEPL套餐</p>
                    <div class="vpn-links">
                        <a href="https://api.fwcloud.life/auth/register?code=lNs4KW" target="_blank" class="vpn-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            蜂窝梯子
                        </a>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- Cursor万能工具箱弹窗 -->
    <div id="cursorUniversalToolboxModal" class="universal-toolbox-modal" style="display: none;">
        <div class="universal-toolbox-overlay"></div>
        <div class="cursor-toolbox-content">
            <div class="universal-toolbox-header">
                <h3>万能工具箱</h3>
                <button class="universal-toolbox-close" id="closeCursorUniversalToolboxBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="cursor-toolbox-body">
                <!-- 临时邮箱推荐 -->
                <section class="card email-section">
                    <h3 class="section-title">
                        <i class="fas fa-envelope"></i>
                        推荐临时邮箱
                    </h3>
                    <p class="email-description">需要注册新账号时推荐使用：</p>
                    <div class="email-links">
                        <a href="https://awamail.com/" target="_blank" class="email-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            awa临时邮箱
                        </a>
                        <a href="https://ducktempmail.netlify.app/" target="_blank" class="email-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            duck临时邮箱
                        </a>
                    </div>
                </section>

                <!-- 推荐梯子 -->
                <section class="card vpn-section">
                    <h3 class="section-title">
                        <i class="fas fa-globe"></i>
                        推荐梯子
                    </h3>
                    <p class="vpn-description">建议购买IEPL套餐</p>
                    <div class="vpn-links">
                        <a href="https://api.fwcloud.life/auth/register?code=lNs4KW" target="_blank" class="vpn-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            蜂窝梯子
                        </a>
                    </div>
                </section>
            </div>
        </div>
    </div>



    <script type="module" src="main.js"></script>
</body>
</html>
