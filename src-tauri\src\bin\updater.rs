use std::env;
use std::fs;
use std::path::Path;
use std::process::Command;
use std::thread;
use std::time::Duration;

#[cfg(windows)]
use std::os::windows::process::CommandExt;

fn main() {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 5 {
        eprintln!("用法: updater.exe --update-target <目标文件> --source-file <源文件>");
        std::process::exit(1);
    }

    let mut target_file = String::new();
    let mut source_file = String::new();
    
    // 解析命令行参数
    let mut i = 1;
    while i < args.len() {
        match args[i].as_str() {
            "--update-target" => {
                if i + 1 < args.len() {
                    target_file = args[i + 1].clone();
                    i += 2;
                } else {
                    eprintln!("错误: --update-target 需要一个参数");
                    std::process::exit(1);
                }
            }
            "--source-file" => {
                if i + 1 < args.len() {
                    source_file = args[i + 1].clone();
                    i += 2;
                } else {
                    eprintln!("错误: --source-file 需要一个参数");
                    std::process::exit(1);
                }
            }
            _ => {
                i += 1;
            }
        }
    }

    if target_file.is_empty() || source_file.is_empty() {
        eprintln!("错误: 缺少必要参数");
        std::process::exit(1);
    }

    println!("🔄 Magic Box 更新器启动");
    println!("📁 目标文件: {}", target_file);
    println!("📁 源文件: {}", source_file);

    // 等待原程序退出
    println!("⏱️ 等待原程序退出...");
    thread::sleep(Duration::from_secs(3));

    // 执行更新
    match update_file(&source_file, &target_file) {
        Ok(_) => {
            println!("✅ 更新成功");
            
            // 启动新版本
            if let Err(e) = start_updated_program(&target_file) {
                eprintln!("❌ 启动新版本失败: {}", e);
            } else {
                println!("🚀 新版本已启动");
            }
        }
        Err(e) => {
            eprintln!("❌ 更新失败: {}", e);
            std::process::exit(1);
        }
    }

    // 清理临时文件
    let _ = fs::remove_file(&source_file);
    println!("🧹 清理完成");
    
    // 等待一下再自删除
    thread::sleep(Duration::from_secs(2));
    
    // 自删除（Windows特有）
    #[cfg(windows)]
    {
        let current_exe = env::current_exe().unwrap_or_default();
        let _ = Command::new("cmd")
            .args(&["/c", "timeout", "/t", "1", ">nul", "&", "del", &current_exe.to_string_lossy()])
            .creation_flags(0x08000000) // CREATE_NO_WINDOW
            .spawn();
    }
}

fn update_file(source: &str, target: &str) -> Result<(), String> {
    let source_path = Path::new(source);
    let target_path = Path::new(target);
    
    if !source_path.exists() {
        return Err(format!("源文件不存在: {}", source));
    }

    println!("📋 创建备份...");
    // 创建备份
    let backup_path = target_path.with_extension("exe.bak");
    if target_path.exists() {
        if let Err(e) = fs::copy(target_path, &backup_path) {
            eprintln!("⚠️ 警告: 无法创建备份: {}", e);
        } else {
            println!("✅ 备份创建成功");
        }
    }

    println!("🔄 替换文件...");
    // 尝试替换文件
    match fs::copy(source_path, target_path) {
        Ok(_) => {
            println!("✅ 文件替换成功");
            // 删除备份文件
            let _ = fs::remove_file(&backup_path);
            Ok(())
        }
        Err(e) => {
            println!("❌ 文件替换失败，正在恢复备份...");
            // 恢复备份
            if backup_path.exists() {
                let _ = fs::copy(&backup_path, target_path);
                let _ = fs::remove_file(&backup_path);
            }
            Err(format!("文件替换失败: {}", e))
        }
    }
}

fn start_updated_program(program_path: &str) -> Result<(), String> {
    let path = Path::new(program_path);
    
    if !path.exists() {
        return Err("程序文件不存在".to_string());
    }

    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;
        Command::new(path)
            .creation_flags(0x00000008) // DETACHED_PROCESS
            .spawn()
            .map_err(|e| format!("启动程序失败: {}", e))?;
    }

    #[cfg(not(windows))]
    {
        Command::new(path)
            .spawn()
            .map_err(|e| format!("启动程序失败: {}", e))?;
    }

    Ok(())
}
