use std::path::{Path, PathBuf};
use std::fs;
use uuid::Uuid;
use regex::Regex;

#[cfg(windows)]
use winapi::um::{
    handleapi::CloseHandle,
    tlhelp32::{CreateToolhelp32Snapshot, Process32First, Process32Next, PROCESSENTRY32, TH32CS_SNAPPROCESS},
};

/// IDEA配置文件SessionId替换器（基于你的工作项目原理）
#[derive(Debug, Clone)]
pub struct IdeaConfigReplacer {
}

impl IdeaConfigReplacer {
    pub fn new() -> Self {
        Self {
        }
    }

    /// 生成新的SessionId
    pub fn generate_new_session_id(&self) -> String {
        Uuid::new_v4().to_string()
    }

    /// 替换所有IDEA进程的SessionId（基于配置文件方式）
    pub fn replace_all_processes(&self, new_session_id: Option<String>) -> Result<String, String> {
        let session_id = new_session_id.unwrap_or_else(|| self.generate_new_session_id());
        
        println!("🎯 开始IDEA配置文件SessionId替换策略...");
        println!("📝 新SessionId: {}", session_id);

        // 1. 检查IDEA进程
        let processes = self.find_idea_processes()?;
        if processes.is_empty() {
            return Err("未找到运行中的IDEA进程".to_string());
        }

        println!("🔍 找到 {} 个IDEA进程", processes.len());

        // 2. 查找IDEA配置目录
        let config_dirs = self.find_idea_config_directories()?;
        if config_dirs.is_empty() {
            return Err("未找到IDEA配置目录".to_string());
        }

        println!("📁 找到 {} 个IDEA配置目录", config_dirs.len());

        // 3. 替换配置文件中的SessionId
        let mut success_count = 0;
        let mut _total_replaced = 0;

        for config_dir in &config_dirs {
            println!("🔄 正在处理配置目录: {}", config_dir.display());
            
            match self.replace_session_id_in_config_dir(config_dir, &session_id) {
                Ok(replaced_count) => {
                    if replaced_count > 0 {
                        success_count += 1;
                        _total_replaced += replaced_count;
                        println!("✅ 配置目录 {} 处理成功，替换了 {} 个劫持",
                            config_dir.display(), replaced_count);
                    } else {
                        println!("⚠️ 配置目录 {} 中未找到可替换的劫持", config_dir.display());
                    }
                }
                Err(e) => {
                    println!("❌ 配置目录 {} 处理失败: {}", config_dir.display(), e);
                }
            }
        }

        if success_count > 0 {
            Ok(format!("劫持成功"))
        } else {
            Err("未能成功替换任何配置目录中的劫持".to_string())
        }
    }

    /// 查找IDEA进程
    #[cfg(windows)]
    fn find_idea_processes(&self) -> Result<Vec<u32>, String> {
        let mut processes = Vec::new();
        
        unsafe {
            let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if snapshot == winapi::um::handleapi::INVALID_HANDLE_VALUE {
                return Err("无法创建进程快照".to_string());
            }

            let mut entry: PROCESSENTRY32 = std::mem::zeroed();
            entry.dwSize = std::mem::size_of::<PROCESSENTRY32>() as u32;

            if Process32First(snapshot, &mut entry) != 0 {
                loop {
                    let process_name = std::ffi::CStr::from_ptr(entry.szExeFile.as_ptr())
                        .to_string_lossy()
                        .to_lowercase();

                    if process_name.contains("idea64.exe") || 
                       process_name.contains("idea.exe") ||
                       process_name.contains("goland64.exe") ||
                       process_name.contains("goland.exe") ||
                       process_name.contains("pycharm64.exe") ||
                       process_name.contains("pycharm.exe") ||
                       process_name.contains("webstorm64.exe") ||
                       process_name.contains("webstorm.exe") {
                        processes.push(entry.th32ProcessID);
                        println!("🔍 找到IDEA进程: {} (PID: {})", process_name, entry.th32ProcessID);
                    }

                    if Process32Next(snapshot, &mut entry) == 0 {
                        break;
                    }
                }
            }

            CloseHandle(snapshot);
        }

        Ok(processes)
    }

    #[cfg(not(windows))]
    fn find_idea_processes(&self) -> Result<Vec<u32>, String> {
        Err("目前仅支持Windows系统".to_string())
    }

    /// 查找IDEA配置目录
    fn find_idea_config_directories(&self) -> Result<Vec<PathBuf>, String> {
        let mut config_dirs = Vec::new();

        // Windows下的IDEA配置目录路径
        let user_home = std::env::var("USERPROFILE")
            .or_else(|_| std::env::var("HOME"))
            .map_err(|_| "无法获取用户主目录")?;

        println!("🔍 开始搜索 IDEA 配置目录，用户目录: {}", user_home);

        // 首先尝试从 Toolbox 配置文件读取实际安装路径
        if let Ok(toolbox_paths) = self.get_toolbox_app_paths(&user_home) {
            config_dirs.extend(toolbox_paths);
        }

        let possible_paths = vec![
            // JetBrains产品配置目录
            format!("{}\\AppData\\Roaming\\JetBrains", user_home),
            format!("{}\\AppData\\Local\\JetBrains", user_home),
            // JetBrains Toolbox 默认路径
            format!("{}\\AppData\\Local\\JetBrains\\Toolbox\\apps", user_home),
            format!("{}\\AppData\\Roaming\\JetBrains\\Toolbox\\apps", user_home),
            // 旧版本路径
            format!("{}\\.IntelliJIdea*", user_home),
            format!("{}\\.GoLand*", user_home),
            format!("{}\\.PyCharm*", user_home),
            format!("{}\\.WebStorm*", user_home),
        ];

        for path_pattern in possible_paths {
            println!("🔍 检查路径: {}", path_pattern);

            if path_pattern.contains('*') {
                // 处理通配符路径
                let parent = Path::new(&path_pattern).parent().unwrap_or(Path::new("."));
                println!("  📁 扫描父目录: {}", parent.display());

                if let Ok(entries) = fs::read_dir(parent) {
                    for entry in entries.flatten() {
                        let path = entry.path();
                        if path.is_dir() {
                            let name = path.file_name().unwrap_or_default().to_string_lossy();
                            println!("    📂 发现目录: {}", name);

                            if name.starts_with(".IntelliJIdea") ||
                               name.starts_with(".GoLand") ||
                               name.starts_with(".PyCharm") ||
                               name.starts_with(".WebStorm") {
                                println!("    ✅ 匹配的配置目录: {}", path.display());
                                config_dirs.push(path);
                            }
                        }
                    }
                } else {
                    println!("  ❌ 无法读取父目录: {}", parent.display());
                }
            } else {
                let path = PathBuf::from(&path_pattern);
                if path.exists() && path.is_dir() {
                    // 检查是否是 Toolbox apps 目录
                    if path_pattern.contains("Toolbox\\apps") {
                        // 处理 Toolbox 特殊结构: Toolbox/apps/IDEA-U/ch-0/xxx.xx.x/
                        if let Ok(entries) = fs::read_dir(&path) {
                            for entry in entries.flatten() {
                                let app_path = entry.path();
                                if app_path.is_dir() {
                                    let app_name = app_path.file_name().unwrap_or_default().to_string_lossy();
                                    println!("🔍 检查 Toolbox 应用: {}", app_name);

                                    // 查找 IDEA 相关应用
                                    if app_name.contains("IDEA") || app_name.contains("PyCharm") ||
                                       app_name.contains("GoLand") || app_name.contains("WebStorm") ||
                                       app_name.contains("PhpStorm") || app_name.contains("CLion") {

                                        // 进入 ch-0 目录
                                        if let Ok(ch_entries) = fs::read_dir(&app_path) {
                                            for ch_entry in ch_entries.flatten() {
                                                let ch_path = ch_entry.path();
                                                if ch_path.is_dir() && ch_path.file_name().unwrap_or_default().to_string_lossy().starts_with("ch-") {
                                                    // 进入版本目录
                                                    if let Ok(version_entries) = fs::read_dir(&ch_path) {
                                                        for version_entry in version_entries.flatten() {
                                                            let version_path = version_entry.path();
                                                            if version_path.is_dir() {
                                                                // 查找配置目录
                                                                let config_path = version_path.join("config");
                                                                if config_path.exists() {
                                                                    println!("✅ 找到 Toolbox IDEA 配置目录: {}", config_path.display());
                                                                    config_dirs.push(config_path);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // 查找JetBrains产品子目录（常规安装）
                        if path.exists() {
                            println!("  📁 扫描 JetBrains 目录: {}", path.display());

                            if let Ok(entries) = fs::read_dir(&path) {
                                let mut found_any = false;
                                for entry in entries.flatten() {
                                    let sub_path = entry.path();
                                    if sub_path.is_dir() {
                                        let name = sub_path.file_name().unwrap_or_default().to_string_lossy();
                                        println!("    📂 发现子目录: {}", name);

                                        // 扩展支持更多 JetBrains 产品和版本格式
                                        if name.contains("IntelliJIdea") || name.contains("Idea") ||
                                           name.contains("GoLand") ||
                                           name.contains("PyCharm") ||
                                           name.contains("WebStorm") ||
                                           name.contains("PhpStorm") ||
                                           name.contains("RubyMine") ||
                                           name.contains("CLion") ||
                                           name.contains("DataGrip") ||
                                           name.contains("Rider") ||
                                           name.contains("AndroidStudio") {
                                            println!("    ✅ 匹配的产品目录: {}", name);
                                            config_dirs.push(sub_path);
                                            found_any = true;
                                        }
                                    }
                                }

                                if !found_any {
                                    println!("    ❌ 未找到匹配的 JetBrains 产品目录");
                                }
                            } else {
                                println!("  ❌ 无法读取 JetBrains 目录: {}", path.display());
                            }
                        } else {
                            println!("  ❌ JetBrains 目录不存在: {}", path.display());
                        }
                    }
                }
            }
        }

        println!("📊 搜索完成，共找到 {} 个配置目录:", config_dirs.len());
        for (i, dir) in config_dirs.iter().enumerate() {
            println!("  {}. {}", i + 1, dir.display());
        }

        if config_dirs.is_empty() {
            println!("❌ 未找到任何 IDEA 配置目录！");
            println!("💡 请检查:");
            println!("   1. IDEA 是否已安装");
            println!("   2. IDEA 是否至少运行过一次");
            println!("   3. 配置目录是否在非标准位置");
        }

        Ok(config_dirs)
    }

    /// 从 Toolbox 配置文件读取应用安装路径
    fn get_toolbox_app_paths(&self, user_home: &str) -> Result<Vec<PathBuf>, String> {
        let mut app_paths = Vec::new();

        // Toolbox 配置文件路径
        let toolbox_settings = PathBuf::from(format!("{}\\AppData\\Local\\JetBrains\\Toolbox\\.settings.json", user_home));
        let toolbox_state = PathBuf::from(format!("{}\\AppData\\Local\\JetBrains\\Toolbox\\state.json", user_home));

        println!("🔍 检查 Toolbox 配置文件...");

        // 尝试读取 .settings.json
        if toolbox_settings.exists() {
            if let Ok(content) = fs::read_to_string(&toolbox_settings) {
                if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
                    // 查找自定义安装路径
                    if let Some(install_location) = json.get("install_location").and_then(|v| v.as_str()) {
                        println!("✅ 找到 Toolbox 自定义安装路径: {}", install_location);
                        let custom_apps_path = PathBuf::from(install_location).join("apps");
                        if custom_apps_path.exists() {
                            app_paths.extend(self.scan_toolbox_apps(&custom_apps_path)?);
                        }
                    }
                }
            }
        }

        // 尝试读取 state.json
        if toolbox_state.exists() {
            if let Ok(content) = fs::read_to_string(&toolbox_state) {
                if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
                    // 查找已安装的应用
                    if let Some(tools) = json.get("tools").and_then(|v| v.as_object()) {
                        for (tool_name, tool_info) in tools {
                            if tool_name.contains("IDEA") || tool_name.contains("PyCharm") ||
                               tool_name.contains("GoLand") || tool_name.contains("WebStorm") {
                                println!("🔍 找到已安装的工具: {}", tool_name);

                                if let Some(installs) = tool_info.get("installs").and_then(|v| v.as_object()) {
                                    for install_info in installs.values() {
                                        if let Some(install_path) = install_info.get("installLocation").and_then(|v| v.as_str()) {
                                            let config_path = PathBuf::from(install_path).join("config");
                                            if config_path.exists() {
                                                println!("✅ 找到工具配置目录: {}", config_path.display());
                                                app_paths.push(config_path);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 如果没有找到配置文件中的路径，尝试默认路径
        if app_paths.is_empty() {
            let default_apps_path = PathBuf::from(format!("{}\\AppData\\Local\\JetBrains\\Toolbox\\apps", user_home));
            if default_apps_path.exists() {
                app_paths.extend(self.scan_toolbox_apps(&default_apps_path)?);
            }
        }

        Ok(app_paths)
    }

    /// 扫描 Toolbox apps 目录
    fn scan_toolbox_apps(&self, apps_path: &PathBuf) -> Result<Vec<PathBuf>, String> {
        let mut config_dirs = Vec::new();

        if let Ok(entries) = fs::read_dir(apps_path) {
            for entry in entries.flatten() {
                let app_path = entry.path();
                if app_path.is_dir() {
                    let app_name = app_path.file_name().unwrap_or_default().to_string_lossy();

                    // 查找 IDEA 相关应用
                    if app_name.contains("IDEA") || app_name.contains("PyCharm") ||
                       app_name.contains("GoLand") || app_name.contains("WebStorm") ||
                       app_name.contains("PhpStorm") || app_name.contains("CLion") {

                        println!("🔍 扫描 Toolbox 应用: {}", app_name);

                        // 进入 ch-0 目录
                        if let Ok(ch_entries) = fs::read_dir(&app_path) {
                            for ch_entry in ch_entries.flatten() {
                                let ch_path = ch_entry.path();
                                if ch_path.is_dir() && ch_path.file_name().unwrap_or_default().to_string_lossy().starts_with("ch-") {
                                    // 进入版本目录
                                    if let Ok(version_entries) = fs::read_dir(&ch_path) {
                                        for version_entry in version_entries.flatten() {
                                            let version_path = version_entry.path();
                                            if version_path.is_dir() {
                                                // 查找配置目录
                                                let config_path = version_path.join("config");
                                                if config_path.exists() {
                                                    println!("✅ 找到 Toolbox 应用配置目录: {}", config_path.display());
                                                    config_dirs.push(config_path);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(config_dirs)
    }

    /// 在配置目录中替换SessionId
    fn replace_session_id_in_config_dir(&self, config_dir: &Path, new_session_id: &str) -> Result<usize, String> {
        let mut total_replaced = 0;

        // 1. 查找并替换properties文件
        total_replaced += self.replace_in_properties_files(config_dir, new_session_id)?;

        // 2. 查找并替换XML配置文件
        total_replaced += self.replace_in_xml_files(config_dir, new_session_id)?;

        // 3. 查找并替换其他配置文件
        total_replaced += self.replace_in_other_config_files(config_dir, new_session_id)?;

        Ok(total_replaced)
    }

    /// 替换properties文件中的SessionId
    fn replace_in_properties_files(&self, config_dir: &Path, new_session_id: &str) -> Result<usize, String> {
        let mut replaced_count = 0;

        // 查找properties文件
        let properties_patterns = vec![
            "options/ide.general.xml",
            "options/other.xml", 
            "options/project.default.xml",
            "config/options/ide.general.xml",
            "config/options/other.xml",
        ];

        for pattern in properties_patterns {
            let file_path = config_dir.join(pattern);
            if file_path.exists() {
                match self.replace_session_id_in_file(&file_path, new_session_id) {
                    Ok(count) => {
                        replaced_count += count;
                        if count > 0 {
                            println!("📝 在文件 {} 中替换了 {} 个SessionId", file_path.display(), count);
                        }
                    }
                    Err(e) => {
                        println!("⚠️ 处理文件 {} 失败: {}", file_path.display(), e);
                    }
                }
            }
        }

        Ok(replaced_count)
    }

    /// 替换XML文件中的SessionId
    fn replace_in_xml_files(&self, config_dir: &Path, new_session_id: &str) -> Result<usize, String> {
        let mut replaced_count = 0;

        // 递归查找XML文件
        if let Ok(entries) = fs::read_dir(config_dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_file() && path.extension().map_or(false, |ext| ext == "xml") {
                    match self.replace_session_id_in_file(&path, new_session_id) {
                        Ok(count) => {
                            replaced_count += count;
                            if count > 0 {
                                println!("📝 在XML文件 {} 中替换了 {} 个SessionId", path.display(), count);
                            }
                        }
                        Err(e) => {
                            println!("⚠️ 处理XML文件 {} 失败: {}", path.display(), e);
                        }
                    }
                } else if path.is_dir() {
                    // 递归处理子目录
                    match self.replace_in_xml_files(&path, new_session_id) {
                        Ok(count) => replaced_count += count,
                        Err(_) => {} // 忽略子目录错误
                    }
                }
            }
        }

        Ok(replaced_count)
    }

    /// 替换其他配置文件中的SessionId
    fn replace_in_other_config_files(&self, _config_dir: &Path, _new_session_id: &str) -> Result<usize, String> {
        let replaced_count = 0;

        // 查找其他可能包含SessionId的文件
        let _other_patterns = vec![
            "*.properties",
            "*.conf",
            "*.config",
            "*.yml",
            "*.yaml",
        ];

        // 这里可以添加更多文件类型的处理逻辑
        // 暂时返回0，表示没有处理其他类型的文件

        Ok(replaced_count)
    }

    /// 在单个文件中替换SessionId
    fn replace_session_id_in_file(&self, file_path: &Path, new_session_id: &str) -> Result<usize, String> {
        let content = fs::read_to_string(file_path)
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut replaced_count = 0;
        let mut new_content = content.clone();

        // 1. 替换augment.session.id配置项
        let augment_patterns = vec![
            // XML格式
            r#"<option name="augment\.session\.id" value="[^"]*""#,
            r#"<property name="augment\.session\.id" value="[^"]*""#,
            // Properties格式
            r"augment\.session\.id\s*=\s*[^\s\n\r]*",
            r"augment_session_id\s*=\s*[^\s\n\r]*",
            // YAML格式
            r"augment\.session\.id\s*:\s*[^\s\n\r]*",
            r"augment_session_id\s*:\s*[^\s\n\r]*",
        ];

        for pattern_str in &augment_patterns {
            if let Ok(regex) = Regex::new(pattern_str) {
                // 收集所有匹配项的信息，避免借用冲突
                let matches: Vec<(usize, usize, String)> = regex.find_iter(&new_content)
                    .map(|mat| {
                        let original = mat.as_str();
                        let replacement = if original.contains("value=\"") {
                            // XML格式
                            if original.contains("option name") {
                                format!(r#"<option name="augment.session.id" value="{}""#, new_session_id)
                            } else {
                                format!(r#"<property name="augment.session.id" value="{}""#, new_session_id)
                            }
                        } else if original.contains("=") {
                            // Properties格式
                            if original.contains("augment.session.id") {
                                format!("augment.session.id={}", new_session_id)
                            } else {
                                format!("augment_session_id={}", new_session_id)
                            }
                        } else {
                            // YAML格式
                            if original.contains("augment.session.id") {
                                format!("augment.session.id: {}", new_session_id)
                            } else {
                                format!("augment_session_id: {}", new_session_id)
                            }
                        };
                        (mat.start(), mat.end(), replacement)
                    })
                    .collect();

                // 从后往前替换，避免索引变化
                for (start, end, replacement) in matches.iter().rev() {
                    new_content.replace_range(*start..*end, replacement);
                    replaced_count += 1;
                    println!("🔄 替换配置项为: {}", replacement);
                }
            }
        }

        // 2. 替换直接的UUID值（在augment相关上下文中）
        let uuid_pattern = Regex::new(r"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")
            .map_err(|e| format!("UUID正则表达式编译失败: {}", e))?;

        // 查找包含augment关键字的行中的UUID
        for line in content.lines() {
            if line.to_lowercase().contains("augment") && line.to_lowercase().contains("session") {
                if let Some(uuid_match) = uuid_pattern.find(line) {
                    let old_uuid = uuid_match.as_str();
                    new_content = new_content.replace(old_uuid, new_session_id);
                    replaced_count += 1;
                    println!("🔄 在augment上下文中替换UUID: {} -> {}", old_uuid, new_session_id);
                }
            }
        }

        // 3. 如果有替换，写回文件
        if replaced_count > 0 {
            // 备份原文件
            let backup_path = format!("{}.backup", file_path.display());
            fs::copy(file_path, &backup_path)
                .map_err(|e| format!("备份文件失败: {}", e))?;

            // 写入新内容
            fs::write(file_path, new_content)
                .map_err(|e| format!("写入文件失败: {}", e))?;

            println!("✅ 文件已更新: {} (备份: {})", file_path.display(), backup_path);
        }

        Ok(replaced_count)
    }



    /// 验证SessionId格式
    pub fn is_valid_session_id(&self, session_id: &str) -> bool {
        if session_id.trim().is_empty() {
            return false;
        }

        // 尝试解析为UUID
        Uuid::parse_str(session_id).is_ok()
    }

    /// 获取当前系统中的SessionId（从配置文件读取）
    pub fn get_current_session_id(&self) -> Result<Option<String>, String> {
        let config_dirs = self.find_idea_config_directories()?;

        for config_dir in &config_dirs {
            if let Ok(session_id) = self.read_session_id_from_config_dir(config_dir) {
                if let Some(id) = session_id {
                    return Ok(Some(id));
                }
            }
        }

        Ok(None)
    }

    /// 从配置目录读取SessionId
    fn read_session_id_from_config_dir(&self, config_dir: &Path) -> Result<Option<String>, String> {
        // 查找可能包含SessionId的文件
        let possible_files = vec![
            "options/ide.general.xml",
            "options/other.xml",
            "config/options/ide.general.xml",
            "config/options/other.xml",
        ];

        for file_pattern in possible_files {
            let file_path = config_dir.join(file_pattern);
            if file_path.exists() {
                if let Ok(content) = fs::read_to_string(&file_path) {
                    // 查找augment.session.id（支持多种格式）
                    let patterns = vec![
                        // XML格式
                        r#"<option name="augment\.session\.id" value="([^"]*)"#,
                        r#"<property name="augment\.session\.id" value="([^"]*)"#,
                        // Properties格式
                        r"augment\.session\.id\s*=\s*([^\s\n\r]*)",
                        r"augment_session_id\s*=\s*([^\s\n\r]*)",
                        // JSON格式（在other.xml中常见）
                        r#""augment\.session\.id"\s*:\s*"([^"]*)""#,
                        r#"'augment\.session\.id'\s*:\s*'([^']*)'"#,
                        // YAML格式
                        r"augment\.session\.id\s*:\s*([^\s\n\r]*)",
                    ];

                    for pattern_str in &patterns {
                        if let Ok(regex) = Regex::new(pattern_str) {
                            if let Some(captures) = regex.captures(&content) {
                                if let Some(session_id) = captures.get(1) {
                                    let id = session_id.as_str().to_string();
                                    if self.is_valid_session_id(&id) {
                                        return Ok(Some(id));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(None)
    }
}
