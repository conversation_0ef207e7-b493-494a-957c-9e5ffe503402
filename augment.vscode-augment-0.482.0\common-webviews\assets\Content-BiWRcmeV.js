var Un=Object.defineProperty;var Fn=(e,t,n)=>t in e?Un(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var j=(e,t,n)=>Fn(e,typeof t!="symbol"?t+"":t,n);import{N as te,O as Yn,an as ln,a3 as dn,ak as Kn,S as _e,i as Ce,s as De,_ as Ae,$ as He,a0 as Le,a1 as je,u as ke,t as Me,V as vn,c as nt,a2 as We,e as hn,a8 as bt,a9 as mn,ad as Pt,h as gn,aa as yn,ag as bn,ab as me,X as Ne,ax as qe,ah as Ie,aj as Xn,a5 as zn}from"./SpinnerAugment-Cx9dt_ox.js";import{g as Jn}from"./globals-D0QH3NT1.js";var F="top",tt="bottom",et="right",Y="left",ge="auto",Kt=[F,tt,et,Y],Ct="start",Ut="end",Gn="clippingParents",En="viewport",Rt="popper",Qn="reference",Ue=Kt.reduce(function(e,t){return e.concat([t+"-"+Ct,t+"-"+Ut])},[]),On=[].concat(Kt,[ge]).reduce(function(e,t){return e.concat([t,t+"-"+Ct,t+"-"+Ut])},[]),Zn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function ut(e){return e?(e.nodeName||"").toLowerCase():null}function z(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function xt(e){return e instanceof z(e).Element||e instanceof Element}function Z(e){return e instanceof z(e).HTMLElement||e instanceof HTMLElement}function Se(e){return typeof ShadowRoot<"u"&&(e instanceof z(e).ShadowRoot||e instanceof ShadowRoot)}const xn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},a=t.attributes[n]||{},i=t.elements[n];Z(i)&&ut(i)&&(Object.assign(i.style,o),Object.keys(a).forEach(function(u){var s=a[u];s===!1?i.removeAttribute(u):i.setAttribute(u,s===!0?"":s)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var a=t.elements[o],i=t.attributes[o]||{},u=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(s,c){return s[c]="",s},{});Z(a)&&ut(a)&&(Object.assign(a.style,u),Object.keys(i).forEach(function(s){a.removeAttribute(s)}))})}},requires:["computeStyles"]};function ct(e){return e.split("-")[0]}var Ot=Math.max,re=Math.min,Dt=Math.round;function ye(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function wn(){return!/^((?!chrome|android).)*safari/i.test(ye())}function At(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),a=1,i=1;t&&Z(e)&&(a=e.offsetWidth>0&&Dt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Dt(o.height)/e.offsetHeight||1);var u=(xt(e)?z(e):window).visualViewport,s=!wn()&&n,c=(o.left+(s&&u?u.offsetLeft:0))/a,f=(o.top+(s&&u?u.offsetTop:0))/i,d=o.width/a,v=o.height/i;return{width:d,height:v,top:f,right:c+d,bottom:f+v,left:c,x:c,y:f}}function $e(e){var t=At(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Tn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Se(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function lt(e){return z(e).getComputedStyle(e)}function to(e){return["table","td","th"].indexOf(ut(e))>=0}function vt(e){return((xt(e)?e.ownerDocument:e.document)||window.document).documentElement}function ce(e){return ut(e)==="html"?e:e.assignedSlot||e.parentNode||(Se(e)?e.host:null)||vt(e)}function Fe(e){return Z(e)&&lt(e).position!=="fixed"?e.offsetParent:null}function Xt(e){for(var t=z(e),n=Fe(e);n&&to(n)&&lt(n).position==="static";)n=Fe(n);return n&&(ut(n)==="html"||ut(n)==="body"&&lt(n).position==="static")?t:n||function(o){var a=/firefox/i.test(ye());if(/Trident/i.test(ye())&&Z(o)&&lt(o).position==="fixed")return null;var i=ce(o);for(Se(i)&&(i=i.host);Z(i)&&["html","body"].indexOf(ut(i))<0;){var u=lt(i);if(u.transform!=="none"||u.perspective!=="none"||u.contain==="paint"||["transform","perspective"].indexOf(u.willChange)!==-1||a&&u.willChange==="filter"||a&&u.filter&&u.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function Re(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Vt(e,t,n){return Ot(e,re(t,n))}function _n(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Cn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const eo={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,a=e.options,i=n.elements.arrow,u=n.modifiersData.popperOffsets,s=ct(n.placement),c=Re(s),f=[Y,et].indexOf(s)>=0?"height":"width";if(i&&u){var d=function(L,A){return _n(typeof(L=typeof L=="function"?L(Object.assign({},A.rects,{placement:A.placement})):L)!="number"?L:Cn(L,Kt))}(a.padding,n),v=$e(i),m=c==="y"?F:Y,b=c==="y"?tt:et,O=n.rects.reference[f]+n.rects.reference[c]-u[c]-n.rects.popper[f],E=u[c]-n.rects.reference[c],l=Xt(i),g=l?c==="y"?l.clientHeight||0:l.clientWidth||0:0,T=O/2-E/2,r=d[m],_=g-v[f]-d[b],h=g/2-v[f]/2+T,C=Vt(r,h,_),H=c;n.modifiersData[o]=((t={})[H]=C,t.centerOffset=C-h,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&Tn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ht(e){return e.split("-")[1]}var no={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ye(e){var t,n=e.popper,o=e.popperRect,a=e.placement,i=e.variation,u=e.offsets,s=e.position,c=e.gpuAcceleration,f=e.adaptive,d=e.roundOffsets,v=e.isFixed,m=u.x,b=m===void 0?0:m,O=u.y,E=O===void 0?0:O,l=typeof d=="function"?d({x:b,y:E}):{x:b,y:E};b=l.x,E=l.y;var g=u.hasOwnProperty("x"),T=u.hasOwnProperty("y"),r=Y,_=F,h=window;if(f){var C=Xt(n),H="clientHeight",L="clientWidth";C===z(n)&&lt(C=vt(n)).position!=="static"&&s==="absolute"&&(H="scrollHeight",L="scrollWidth"),(a===F||(a===Y||a===et)&&i===Ut)&&(_=tt,E-=(v&&C===h&&h.visualViewport?h.visualViewport.height:C[H])-o.height,E*=c?1:-1),(a===Y||(a===F||a===tt)&&i===Ut)&&(r=et,b-=(v&&C===h&&h.visualViewport?h.visualViewport.width:C[L])-o.width,b*=c?1:-1)}var A,M=Object.assign({position:s},f&&no),k=d===!0?function(R,B){var I=R.x,K=R.y,S=B.devicePixelRatio||1;return{x:Dt(I*S)/S||0,y:Dt(K*S)/S||0}}({x:b,y:E},z(n)):{x:b,y:E};return b=k.x,E=k.y,c?Object.assign({},M,((A={})[_]=T?"0":"",A[r]=g?"0":"",A.transform=(h.devicePixelRatio||1)<=1?"translate("+b+"px, "+E+"px)":"translate3d("+b+"px, "+E+"px, 0)",A)):Object.assign({},M,((t={})[_]=T?E+"px":"",t[r]=g?b+"px":"",t.transform="",t))}var ee={passive:!0};const oo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,a=o.scroll,i=a===void 0||a,u=o.resize,s=u===void 0||u,c=z(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&f.forEach(function(d){d.addEventListener("scroll",n.update,ee)}),s&&c.addEventListener("resize",n.update,ee),function(){i&&f.forEach(function(d){d.removeEventListener("scroll",n.update,ee)}),s&&c.removeEventListener("resize",n.update,ee)}},data:{}};var ro={left:"right",right:"left",bottom:"top",top:"bottom"};function ne(e){return e.replace(/left|right|bottom|top/g,function(t){return ro[t]})}var io={start:"end",end:"start"};function Ke(e){return e.replace(/start|end/g,function(t){return io[t]})}function Pe(e){var t=z(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function be(e){return At(vt(e)).left+Pe(e).scrollLeft}function Ve(e){var t=lt(e),n=t.overflow,o=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+o)}function Dn(e){return["html","body","#document"].indexOf(ut(e))>=0?e.ownerDocument.body:Z(e)&&Ve(e)?e:Dn(ce(e))}function Wt(e,t){var n;t===void 0&&(t=[]);var o=Dn(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),i=z(o),u=a?[i].concat(i.visualViewport||[],Ve(o)?o:[]):o,s=t.concat(u);return a?s:s.concat(Wt(ce(u)))}function Ee(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Xe(e,t,n){return t===En?Ee(function(o,a){var i=z(o),u=vt(o),s=i.visualViewport,c=u.clientWidth,f=u.clientHeight,d=0,v=0;if(s){c=s.width,f=s.height;var m=wn();(m||!m&&a==="fixed")&&(d=s.offsetLeft,v=s.offsetTop)}return{width:c,height:f,x:d+be(o),y:v}}(e,n)):xt(t)?function(o,a){var i=At(o,!1,a==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):Ee(function(o){var a,i=vt(o),u=Pe(o),s=(a=o.ownerDocument)==null?void 0:a.body,c=Ot(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),f=Ot(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),d=-u.scrollLeft+be(o),v=-u.scrollTop;return lt(s||i).direction==="rtl"&&(d+=Ot(i.clientWidth,s?s.clientWidth:0)-c),{width:c,height:f,x:d,y:v}}(vt(e)))}function so(e,t,n,o){var a=t==="clippingParents"?function(c){var f=Wt(ce(c)),d=["absolute","fixed"].indexOf(lt(c).position)>=0&&Z(c)?Xt(c):c;return xt(d)?f.filter(function(v){return xt(v)&&Tn(v,d)&&ut(v)!=="body"}):[]}(e):[].concat(t),i=[].concat(a,[n]),u=i[0],s=i.reduce(function(c,f){var d=Xe(e,f,o);return c.top=Ot(d.top,c.top),c.right=re(d.right,c.right),c.bottom=re(d.bottom,c.bottom),c.left=Ot(d.left,c.left),c},Xe(e,u,o));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function An(e){var t,n=e.reference,o=e.element,a=e.placement,i=a?ct(a):null,u=a?Ht(a):null,s=n.x+n.width/2-o.width/2,c=n.y+n.height/2-o.height/2;switch(i){case F:t={x:s,y:n.y-o.height};break;case tt:t={x:s,y:n.y+n.height};break;case et:t={x:n.x+n.width,y:c};break;case Y:t={x:n.x-o.width,y:c};break;default:t={x:n.x,y:n.y}}var f=i?Re(i):null;if(f!=null){var d=f==="y"?"height":"width";switch(u){case Ct:t[f]=t[f]-(n[d]/2-o[d]/2);break;case Ut:t[f]=t[f]+(n[d]/2-o[d]/2)}}return t}function Ft(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=o===void 0?e.placement:o,i=n.strategy,u=i===void 0?e.strategy:i,s=n.boundary,c=s===void 0?Gn:s,f=n.rootBoundary,d=f===void 0?En:f,v=n.elementContext,m=v===void 0?Rt:v,b=n.altBoundary,O=b!==void 0&&b,E=n.padding,l=E===void 0?0:E,g=_n(typeof l!="number"?l:Cn(l,Kt)),T=m===Rt?Qn:Rt,r=e.rects.popper,_=e.elements[O?T:m],h=so(xt(_)?_:_.contextElement||vt(e.elements.popper),c,d,u),C=At(e.elements.reference),H=An({reference:C,element:r,strategy:"absolute",placement:a}),L=Ee(Object.assign({},r,H)),A=m===Rt?L:C,M={top:h.top-A.top+g.top,bottom:A.bottom-h.bottom+g.bottom,left:h.left-A.left+g.left,right:A.right-h.right+g.right},k=e.modifiersData.offset;if(m===Rt&&k){var R=k[a];Object.keys(M).forEach(function(B){var I=[et,tt].indexOf(B)>=0?1:-1,K=[F,tt].indexOf(B)>=0?"y":"x";M[B]+=R[K]*I})}return M}function ao(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=n.boundary,i=n.rootBoundary,u=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,f=c===void 0?On:c,d=Ht(o),v=d?s?Ue:Ue.filter(function(O){return Ht(O)===d}):Kt,m=v.filter(function(O){return f.indexOf(O)>=0});m.length===0&&(m=v);var b=m.reduce(function(O,E){return O[E]=Ft(e,{placement:E,boundary:a,rootBoundary:i,padding:u})[ct(E)],O},{});return Object.keys(b).sort(function(O,E){return b[O]-b[E]})}const co={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u===void 0||u,c=n.fallbackPlacements,f=n.padding,d=n.boundary,v=n.rootBoundary,m=n.altBoundary,b=n.flipVariations,O=b===void 0||b,E=n.allowedAutoPlacements,l=t.options.placement,g=ct(l),T=c||(g===l||!O?[ne(l)]:function(N){if(ct(N)===ge)return[];var U=ne(N);return[Ke(N),U,Ke(U)]}(l)),r=[l].concat(T).reduce(function(N,U){return N.concat(ct(U)===ge?ao(t,{placement:U,boundary:d,rootBoundary:v,padding:f,flipVariations:O,allowedAutoPlacements:E}):U)},[]),_=t.rects.reference,h=t.rects.popper,C=new Map,H=!0,L=r[0],A=0;A<r.length;A++){var M=r[A],k=ct(M),R=Ht(M)===Ct,B=[F,tt].indexOf(k)>=0,I=B?"width":"height",K=Ft(t,{placement:M,boundary:d,rootBoundary:v,altBoundary:m,padding:f}),S=B?R?et:Y:R?tt:F;_[I]>h[I]&&(S=ne(S));var $=ne(S),rt=[];if(i&&rt.push(K[k]<=0),s&&rt.push(K[S]<=0,K[$]<=0),rt.every(function(N){return N})){L=M,H=!1;break}C.set(M,rt)}if(H)for(var it=function(N){var U=r.find(function(ht){var mt=C.get(ht);if(mt)return mt.slice(0,N).every(function(dt){return dt})});if(U)return L=U,"break"},st=O?3:1;st>0&&it(st)!=="break";st--);t.placement!==L&&(t.modifiersData[o]._skip=!0,t.placement=L,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function ze(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Je(e){return[F,et,tt,Y].some(function(t){return e[t]>=0})}const uo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.offset,i=a===void 0?[0,0]:a,u=On.reduce(function(d,v){return d[v]=function(m,b,O){var E=ct(m),l=[Y,F].indexOf(E)>=0?-1:1,g=typeof O=="function"?O(Object.assign({},b,{placement:m})):O,T=g[0],r=g[1];return T=T||0,r=(r||0)*l,[Y,et].indexOf(E)>=0?{x:r,y:T}:{x:T,y:r}}(v,t.rects,i),d},{}),s=u[t.placement],c=s.x,f=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=f),t.modifiersData[o]=u}},po={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u!==void 0&&u,c=n.boundary,f=n.rootBoundary,d=n.altBoundary,v=n.padding,m=n.tether,b=m===void 0||m,O=n.tetherOffset,E=O===void 0?0:O,l=Ft(t,{boundary:c,rootBoundary:f,padding:v,altBoundary:d}),g=ct(t.placement),T=Ht(t.placement),r=!T,_=Re(g),h=_==="x"?"y":"x",C=t.modifiersData.popperOffsets,H=t.rects.reference,L=t.rects.popper,A=typeof E=="function"?E(Object.assign({},t.rects,{placement:t.placement})):E,M=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(C){if(i){var B,I=_==="y"?F:Y,K=_==="y"?tt:et,S=_==="y"?"height":"width",$=C[_],rt=$+l[I],it=$-l[K],st=b?-L[S]/2:0,N=T===Ct?H[S]:L[S],U=T===Ct?-L[S]:-H[S],ht=t.elements.arrow,mt=b&&ht?$e(ht):{width:0,height:0},dt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},jt=dt[I],pt=dt[K],gt=Vt(0,H[S],mt[S]),zt=r?H[S]/2-st-gt-jt-M.mainAxis:N-gt-jt-M.mainAxis,Jt=r?-H[S]/2+st+gt+pt+M.mainAxis:U+gt+pt+M.mainAxis,wt=t.elements.arrow&&Xt(t.elements.arrow),Gt=wt?_==="y"?wt.clientTop||0:wt.clientLeft||0:0,kt=(B=k==null?void 0:k[_])!=null?B:0,Qt=$+Jt-kt,Mt=Vt(b?re(rt,$+zt-kt-Gt):rt,$,b?Ot(it,Qt):it);C[_]=Mt,R[_]=Mt-$}if(s){var St,$t=_==="x"?F:Y,Zt=_==="x"?tt:et,J=C[h],p=h==="y"?"height":"width",y=J+l[$t],x=J-l[Zt],w=[F,Y].indexOf(g)!==-1,D=(St=k==null?void 0:k[h])!=null?St:0,P=w?y:J-H[p]-L[p]-D+M.altAxis,V=w?J+H[p]+L[p]-D-M.altAxis:x,W=b&&w?function(q,G,Q){var X=Vt(q,G,Q);return X>Q?Q:X}(P,J,V):Vt(b?P:y,J,b?V:x);C[h]=W,R[h]=W-J}t.modifiersData[o]=R}},requiresIfExists:["offset"]};function fo(e,t,n){n===void 0&&(n=!1);var o,a=Z(t),i=Z(t)&&function(d){var v=d.getBoundingClientRect(),m=Dt(v.width)/d.offsetWidth||1,b=Dt(v.height)/d.offsetHeight||1;return m!==1||b!==1}(t),u=vt(t),s=At(e,i,n),c={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(a||!a&&!n)&&((ut(t)!=="body"||Ve(u))&&(c=(o=t)!==z(o)&&Z(o)?function(d){return{scrollLeft:d.scrollLeft,scrollTop:d.scrollTop}}(o):Pe(o)),Z(t)?((f=At(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):u&&(f.x=be(u))),{x:s.left+c.scrollLeft-f.x,y:s.top+c.scrollTop-f.y,width:s.width,height:s.height}}function lo(e){var t=new Map,n=new Set,o=[];function a(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(u){if(!n.has(u)){var s=t.get(u);s&&a(s)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||a(i)}),o}var Ge={placement:"bottom",modifiers:[],strategy:"absolute"};function Qe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function vo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,a=t.defaultOptions,i=a===void 0?Ge:a;return function(u,s,c){c===void 0&&(c=i);var f,d,v={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ge,i),modifiersData:{},elements:{reference:u,popper:s},attributes:{},styles:{}},m=[],b=!1,O={state:v,setOptions:function(l){var g=typeof l=="function"?l(v.options):l;E(),v.options=Object.assign({},i,v.options,g),v.scrollParents={reference:xt(u)?Wt(u):u.contextElement?Wt(u.contextElement):[],popper:Wt(s)};var T,r,_=function(h){var C=lo(h);return Zn.reduce(function(H,L){return H.concat(C.filter(function(A){return A.phase===L}))},[])}((T=[].concat(o,v.options.modifiers),r=T.reduce(function(h,C){var H=h[C.name];return h[C.name]=H?Object.assign({},H,C,{options:Object.assign({},H.options,C.options),data:Object.assign({},H.data,C.data)}):C,h},{}),Object.keys(r).map(function(h){return r[h]})));return v.orderedModifiers=_.filter(function(h){return h.enabled}),v.orderedModifiers.forEach(function(h){var C=h.name,H=h.options,L=H===void 0?{}:H,A=h.effect;if(typeof A=="function"){var M=A({state:v,name:C,instance:O,options:L}),k=function(){};m.push(M||k)}}),O.update()},forceUpdate:function(){if(!b){var l=v.elements,g=l.reference,T=l.popper;if(Qe(g,T)){v.rects={reference:fo(g,Xt(T),v.options.strategy==="fixed"),popper:$e(T)},v.reset=!1,v.placement=v.options.placement,v.orderedModifiers.forEach(function(A){return v.modifiersData[A.name]=Object.assign({},A.data)});for(var r=0;r<v.orderedModifiers.length;r++)if(v.reset!==!0){var _=v.orderedModifiers[r],h=_.fn,C=_.options,H=C===void 0?{}:C,L=_.name;typeof h=="function"&&(v=h({state:v,options:H,name:L,instance:O})||v)}else v.reset=!1,r=-1}}},update:(f=function(){return new Promise(function(l){O.forceUpdate(),l(v)})},function(){return d||(d=new Promise(function(l){Promise.resolve().then(function(){d=void 0,l(f())})})),d}),destroy:function(){E(),b=!0}};if(!Qe(u,s))return O;function E(){m.forEach(function(l){return l()}),m=[]}return O.setOptions(c).then(function(l){!b&&c.onFirstUpdate&&c.onFirstUpdate(l)}),O}}var ho=vo({defaultModifiers:[oo,{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=An({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,a=o===void 0||o,i=n.adaptive,u=i===void 0||i,s=n.roundOffsets,c=s===void 0||s,f={placement:ct(t.placement),variation:Ht(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ye(Object.assign({},f,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:u,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ye(Object.assign({},f,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},xn,uo,co,po,eo,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,a=t.rects.popper,i=t.modifiersData.preventOverflow,u=Ft(t,{elementContext:"reference"}),s=Ft(t,{altBoundary:!0}),c=ze(u,o),f=ze(s,a,i),d=Je(c),v=Je(f);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:f,isReferenceHidden:d,hasPopperEscaped:v},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":v})}}]}),Hn="tippy-content",mo="tippy-backdrop",Ln="tippy-arrow",jn="tippy-svg-arrow",yt={passive:!0,capture:!0},kn=function(){return document.body};function pe(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function Be(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function Mn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function Ze(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function _t(e){return[].concat(e)}function tn(e,t){e.indexOf(t)===-1&&e.push(t)}function ie(e){return[].slice.call(e)}function en(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function Nt(){return document.createElement("div")}function ue(e){return["Element","Fragment"].some(function(t){return Be(e,t)})}function go(e){return ue(e)?[e]:function(t){return Be(t,"NodeList")}(e)?ie(e):Array.isArray(e)?e:ie(document.querySelectorAll(e))}function fe(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function nn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function le(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(a){e[o](a,n)})}function on(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var at={isTouch:!1},rn=0;function yo(){at.isTouch||(at.isTouch=!0,window.performance&&document.addEventListener("mousemove",Sn))}function Sn(){var e=performance.now();e-rn<20&&(at.isTouch=!1,document.removeEventListener("mousemove",Sn)),rn=e}function bo(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Eo=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,ot=Object.assign({appendTo:kn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Oo=Object.keys(ot);function $n(e){var t=(e.plugins||[]).reduce(function(n,o){var a,i=o.name,u=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(a=ot[i])!=null?a:u),n},{});return Object.assign({},e,t)}function sn(e,t){var n=Object.assign({},t,{content:Mn(t.content,[e])},t.ignoreAttributes?{}:function(o,a){return(a?Object.keys($n(Object.assign({},ot,{plugins:a}))):Oo).reduce(function(i,u){var s=(o.getAttribute("data-tippy-"+u)||"").trim();if(!s)return i;if(u==="content")i[u]=s;else try{i[u]=JSON.parse(s)}catch{i[u]=s}return i},{})}(e,t.plugins));return n.aria=Object.assign({},ot.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var xo=function(){return"innerHTML"};function Oe(e,t){e[xo()]=t}function an(e){var t=Nt();return e===!0?t.className=Ln:(t.className=jn,ue(e)?t.appendChild(e):Oe(t,e)),t}function cn(e,t){ue(t.content)?(Oe(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?Oe(e,t.content):e.textContent=t.content)}function xe(e){var t=e.firstElementChild,n=ie(t.children);return{box:t,content:n.find(function(o){return o.classList.contains(Hn)}),arrow:n.find(function(o){return o.classList.contains(Ln)||o.classList.contains(jn)}),backdrop:n.find(function(o){return o.classList.contains(mo)})}}function Rn(e){var t=Nt(),n=Nt();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=Nt();function a(i,u){var s=xe(t),c=s.box,f=s.content,d=s.arrow;u.theme?c.setAttribute("data-theme",u.theme):c.removeAttribute("data-theme"),typeof u.animation=="string"?c.setAttribute("data-animation",u.animation):c.removeAttribute("data-animation"),u.inertia?c.setAttribute("data-inertia",""):c.removeAttribute("data-inertia"),c.style.maxWidth=typeof u.maxWidth=="number"?u.maxWidth+"px":u.maxWidth,u.role?c.setAttribute("role",u.role):c.removeAttribute("role"),i.content===u.content&&i.allowHTML===u.allowHTML||cn(f,e.props),u.arrow?d?i.arrow!==u.arrow&&(c.removeChild(d),c.appendChild(an(u.arrow))):c.appendChild(an(u.arrow)):d&&c.removeChild(d)}return o.className=Hn,o.setAttribute("data-state","hidden"),cn(o,e.props),t.appendChild(n),n.appendChild(o),a(e.props,e.props),{popper:t,onUpdate:a}}Rn.$$tippy=!0;var wo=1,oe=[],de=[];function To(e,t){var n,o,a,i,u,s,c,f,d=sn(e,Object.assign({},ot,$n(en(t)))),v=!1,m=!1,b=!1,O=!1,E=[],l=Ze(wt,d.interactiveDebounce),g=wo++,T=(f=d.plugins).filter(function(p,y){return f.indexOf(p)===y}),r={id:g,reference:e,popper:Nt(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:T,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(a)},setProps:function(p){if(!r.state.isDestroyed){$("onBeforeUpdate",[r,p]),zt();var y=r.props,x=sn(e,Object.assign({},y,en(p),{ignoreAttributes:!0}));r.props=x,gt(),y.interactiveDebounce!==x.interactiveDebounce&&(st(),l=Ze(wt,x.interactiveDebounce)),y.triggerTarget&&!x.triggerTarget?_t(y.triggerTarget).forEach(function(w){w.removeAttribute("aria-expanded")}):x.triggerTarget&&e.removeAttribute("aria-expanded"),it(),S(),C&&C(y,x),r.popperInstance&&(Mt(),$t().forEach(function(w){requestAnimationFrame(w._tippy.popperInstance.forceUpdate)})),$("onAfterUpdate",[r,p])}},setContent:function(p){r.setProps({content:p})},show:function(){var p=r.state.isVisible,y=r.state.isDestroyed,x=!r.state.isEnabled,w=at.isTouch&&!r.props.touch,D=pe(r.props.duration,0,ot.duration);if(!(p||y||x||w)&&!R().hasAttribute("disabled")&&($("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,k()&&(h.style.visibility="visible"),S(),mt(),r.state.isMounted||(h.style.transition="none"),k()){var P=I();fe([P.box,P.content],0)}s=function(){var V;if(r.state.isVisible&&!O){if(O=!0,h.offsetHeight,h.style.transition=r.props.moveTransition,k()&&r.props.animation){var W=I(),q=W.box,G=W.content;fe([q,G],D),nn([q,G],"visible")}rt(),it(),tn(de,r),(V=r.popperInstance)==null||V.forceUpdate(),$("onMount",[r]),r.props.animation&&k()&&function(Q,X){jt(Q,X)}(D,function(){r.state.isShown=!0,$("onShown",[r])})}},function(){var V,W=r.props.appendTo,q=R();V=r.props.interactive&&W===kn||W==="parent"?q.parentNode:Mn(W,[q]),V.contains(h)||V.appendChild(h),r.state.isMounted=!0,Mt()}()}},hide:function(){var p=!r.state.isVisible,y=r.state.isDestroyed,x=!r.state.isEnabled,w=pe(r.props.duration,1,ot.duration);if(!(p||y||x)&&($("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,O=!1,v=!1,k()&&(h.style.visibility="hidden"),st(),dt(),S(!0),k()){var D=I(),P=D.box,V=D.content;r.props.animation&&(fe([P,V],w),nn([P,V],"hidden"))}rt(),it(),r.props.animation?k()&&function(W,q){jt(W,function(){!r.state.isVisible&&h.parentNode&&h.parentNode.contains(h)&&q()})}(w,r.unmount):r.unmount()}},hideWithInteractivity:function(p){B().addEventListener("mousemove",l),tn(oe,l),l(p)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(St(),$t().forEach(function(p){p._tippy.unmount()}),h.parentNode&&h.parentNode.removeChild(h),de=de.filter(function(p){return p!==r}),r.state.isMounted=!1,$("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),zt(),delete e._tippy,r.state.isDestroyed=!0,$("onDestroy",[r]))}};if(!d.render)return r;var _=d.render(r),h=_.popper,C=_.onUpdate;h.setAttribute("data-tippy-root",""),h.id="tippy-"+r.id,r.popper=h,e._tippy=r,h._tippy=r;var H=T.map(function(p){return p.fn(r)}),L=e.hasAttribute("aria-expanded");return gt(),it(),S(),$("onCreate",[r]),d.showOnCreate&&Zt(),h.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),h.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&B().addEventListener("mousemove",l)}),r;function A(){var p=r.props.touch;return Array.isArray(p)?p:[p,0]}function M(){return A()[0]==="hold"}function k(){var p;return!((p=r.props.render)==null||!p.$$tippy)}function R(){return c||e}function B(){var p=R().parentNode;return p?function(y){var x,w=_t(y)[0];return w!=null&&(x=w.ownerDocument)!=null&&x.body?w.ownerDocument:document}(p):document}function I(){return xe(h)}function K(p){return r.state.isMounted&&!r.state.isVisible||at.isTouch||i&&i.type==="focus"?0:pe(r.props.delay,p?0:1,ot.delay)}function S(p){p===void 0&&(p=!1),h.style.pointerEvents=r.props.interactive&&!p?"":"none",h.style.zIndex=""+r.props.zIndex}function $(p,y,x){var w;x===void 0&&(x=!0),H.forEach(function(D){D[p]&&D[p].apply(D,y)}),x&&(w=r.props)[p].apply(w,y)}function rt(){var p=r.props.aria;if(p.content){var y="aria-"+p.content,x=h.id;_t(r.props.triggerTarget||e).forEach(function(w){var D=w.getAttribute(y);if(r.state.isVisible)w.setAttribute(y,D?D+" "+x:x);else{var P=D&&D.replace(x,"").trim();P?w.setAttribute(y,P):w.removeAttribute(y)}})}}function it(){!L&&r.props.aria.expanded&&_t(r.props.triggerTarget||e).forEach(function(p){r.props.interactive?p.setAttribute("aria-expanded",r.state.isVisible&&p===R()?"true":"false"):p.removeAttribute("aria-expanded")})}function st(){B().removeEventListener("mousemove",l),oe=oe.filter(function(p){return p!==l})}function N(p){if(!at.isTouch||!b&&p.type!=="mousedown"){var y=p.composedPath&&p.composedPath()[0]||p.target;if(!r.props.interactive||!on(h,y)){if(_t(r.props.triggerTarget||e).some(function(x){return on(x,y)})){if(at.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else $("onClickOutside",[r,p]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),m=!0,setTimeout(function(){m=!1}),r.state.isMounted||dt())}}}function U(){b=!0}function ht(){b=!1}function mt(){var p=B();p.addEventListener("mousedown",N,!0),p.addEventListener("touchend",N,yt),p.addEventListener("touchstart",ht,yt),p.addEventListener("touchmove",U,yt)}function dt(){var p=B();p.removeEventListener("mousedown",N,!0),p.removeEventListener("touchend",N,yt),p.removeEventListener("touchstart",ht,yt),p.removeEventListener("touchmove",U,yt)}function jt(p,y){var x=I().box;function w(D){D.target===x&&(le(x,"remove",w),y())}if(p===0)return y();le(x,"remove",u),le(x,"add",w),u=w}function pt(p,y,x){x===void 0&&(x=!1),_t(r.props.triggerTarget||e).forEach(function(w){w.addEventListener(p,y,x),E.push({node:w,eventType:p,handler:y,options:x})})}function gt(){var p;M()&&(pt("touchstart",Jt,{passive:!0}),pt("touchend",Gt,{passive:!0})),(p=r.props.trigger,p.split(/\s+/).filter(Boolean)).forEach(function(y){if(y!=="manual")switch(pt(y,Jt),y){case"mouseenter":pt("mouseleave",Gt);break;case"focus":pt(Eo?"focusout":"blur",kt);break;case"focusin":pt("focusout",kt)}})}function zt(){E.forEach(function(p){var y=p.node,x=p.eventType,w=p.handler,D=p.options;y.removeEventListener(x,w,D)}),E=[]}function Jt(p){var y,x=!1;if(r.state.isEnabled&&!Qt(p)&&!m){var w=((y=i)==null?void 0:y.type)==="focus";i=p,c=p.currentTarget,it(),!r.state.isVisible&&Be(p,"MouseEvent")&&oe.forEach(function(D){return D(p)}),p.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||v)&&r.props.hideOnClick!==!1&&r.state.isVisible?x=!0:Zt(p),p.type==="click"&&(v=!x),x&&!w&&J(p)}}function wt(p){var y=p.target,x=R().contains(y)||h.contains(y);p.type==="mousemove"&&x||function(w,D){var P=D.clientX,V=D.clientY;return w.every(function(W){var q=W.popperRect,G=W.popperState,Q=W.props.interactiveBorder,X=G.placement.split("-")[0],ft=G.modifiersData.offset;if(!ft)return!0;var Tt=X==="bottom"?ft.top.y:0,Pn=X==="top"?ft.bottom.y:0,Vn=X==="right"?ft.left.x:0,Bn=X==="left"?ft.right.x:0,Wn=q.top-V+Tt>Q,Nn=V-q.bottom-Pn>Q,qn=q.left-P+Vn>Q,In=P-q.right-Bn>Q;return Wn||Nn||qn||In})}($t().concat(h).map(function(w){var D,P=(D=w._tippy.popperInstance)==null?void 0:D.state;return P?{popperRect:w.getBoundingClientRect(),popperState:P,props:d}:null}).filter(Boolean),p)&&(st(),J(p))}function Gt(p){Qt(p)||r.props.trigger.indexOf("click")>=0&&v||(r.props.interactive?r.hideWithInteractivity(p):J(p))}function kt(p){r.props.trigger.indexOf("focusin")<0&&p.target!==R()||r.props.interactive&&p.relatedTarget&&h.contains(p.relatedTarget)||J(p)}function Qt(p){return!!at.isTouch&&M()!==p.type.indexOf("touch")>=0}function Mt(){St();var p=r.props,y=p.popperOptions,x=p.placement,w=p.offset,D=p.getReferenceClientRect,P=p.moveTransition,V=k()?xe(h).arrow:null,W=D?{getBoundingClientRect:D,contextElement:D.contextElement||R()}:e,q={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(Q){var X=Q.state;if(k()){var ft=I().box;["placement","reference-hidden","escaped"].forEach(function(Tt){Tt==="placement"?ft.setAttribute("data-placement",X.placement):X.attributes.popper["data-popper-"+Tt]?ft.setAttribute("data-"+Tt,""):ft.removeAttribute("data-"+Tt)}),X.attributes.popper={}}}},G=[{name:"offset",options:{offset:w}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!P}},q];k()&&V&&G.push({name:"arrow",options:{element:V,padding:3}}),G.push.apply(G,(y==null?void 0:y.modifiers)||[]),r.popperInstance=ho(W,h,Object.assign({},y,{placement:x,onFirstUpdate:s,modifiers:G}))}function St(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function $t(){return ie(h.querySelectorAll("[data-tippy-root]"))}function Zt(p){r.clearDelayTimeouts(),p&&$("onTrigger",[r,p]),mt();var y=K(!0),x=A(),w=x[0],D=x[1];at.isTouch&&w==="hold"&&D&&(y=D),y?n=setTimeout(function(){r.show()},y):r.show()}function J(p){if(r.clearDelayTimeouts(),$("onUntrigger",[r,p]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(p.type)>=0&&v)){var y=K(!1);y?o=setTimeout(function(){r.state.isVisible&&r.hide()},y):a=requestAnimationFrame(function(){r.hide()})}}else dt()}}function Bt(e,t){t===void 0&&(t={});var n=ot.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",yo,yt),window.addEventListener("blur",bo);var o=Object.assign({},t,{plugins:n}),a=go(e).reduce(function(i,u){var s=u&&To(u,o);return s&&i.push(s),i},[]);return ue(e)?a[0]:a}Bt.defaultProps=ot,Bt.setDefaultProps=function(e){Object.keys(e).forEach(function(t){ot[t]=e[t]})},Bt.currentInput=at,Object.assign({},xn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Bt.setDefaultProps({render:Rn});var Yt=(e=>(e.Hover="hover",e.Click="click",e))(Yt||{});const qt=class qt extends Event{constructor(){super(qt.eventType,{bubbles:!0})}static isEvent(t){return t.type===qt.eventType}};j(qt,"eventType","augment-ds-event__close-tooltip-request");let Et=qt;var un=NaN,_o="[object Symbol]",Co=/^\s+|\s+$/g,Do=/^[-+]0x[0-9a-f]+$/i,Ao=/^0b[01]+$/i,Ho=/^0o[0-7]+$/i,Lo=parseInt,jo=typeof te=="object"&&te&&te.Object===Object&&te,ko=typeof self=="object"&&self&&self.Object===Object&&self,Mo=jo||ko||Function("return this")(),So=Object.prototype.toString,$o=Math.max,Ro=Math.min,ve=function(){return Mo.Date.now()};function we(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function pn(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(a){return!!a&&typeof a=="object"}(o)&&So.call(o)==_o}(e))return un;if(we(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=we(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(Co,"");var n=Ao.test(e);return n||Ho.test(e)?Lo(e.slice(2),n?2:8):Do.test(e)?un:+e}const fn=Yn(function(e,t,n){var o,a,i,u,s,c,f=0,d=!1,v=!1,m=!0;if(typeof e!="function")throw new TypeError("Expected a function");function b(T){var r=o,_=a;return o=a=void 0,f=T,u=e.apply(_,r)}function O(T){var r=T-c;return c===void 0||r>=t||r<0||v&&T-f>=i}function E(){var T=ve();if(O(T))return l(T);s=setTimeout(E,function(r){var _=t-(r-c);return v?Ro(_,i-(r-f)):_}(T))}function l(T){return s=void 0,m&&o?b(T):(o=a=void 0,u)}function g(){var T=ve(),r=O(T);if(o=arguments,a=this,c=T,r){if(s===void 0)return function(_){return f=_,s=setTimeout(E,t),d?b(_):u}(c);if(v)return s=setTimeout(E,t),b(c)}return s===void 0&&(s=setTimeout(E,t)),u}return t=pn(t)||0,we(n)&&(d=!!n.leading,i=(v="maxWait"in n)?$o(pn(n.maxWait)||0,t):i,m="trailing"in n?!!n.trailing:m),g.cancel=function(){s!==void 0&&clearTimeout(s),f=0,o=c=a=s=void 0},g.flush=function(){return s===void 0?u:l(ve())},g}),ae=class ae{constructor(t){j(this,"debouncedHoverStart");j(this,"debouncedHoverEnd");j(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});j(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=fn(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=fn(t.onHoverEnd,ae.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};j(ae,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let se=ae;function Te(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const Po=Symbol("hover-action-context");function Xo(e=100){const t=dn(!1);ln(Po,t);const n=new se({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return Te(o,n)}}const It=class It{constructor(t){j(this,"_state");j(this,"_tippy");j(this,"_triggerElement");j(this,"_contentElement");j(this,"_contentProps");j(this,"_hoverContext");j(this,"_referenceClientRect");j(this,"_hasPointerEvents",!0);j(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(a=>({...a,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});j(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});j(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});j(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});j(this,"externalControlSetOpen",t=>{this._opts.open=t,t!==void 0&&this._setOpen(t)});j(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});j(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:this._opts.offset??[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed",modifiers:[{name:"preventOverflow",options:{padding:this._opts.nested?12:0}}]},duration:0,delay:0,placement:Vo(this._contentProps),hideOnClick:!1,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(a=>{var i,u;a.open?(i=this._tippy)==null||i.show():(u=this._tippy)==null||u.hide()});this._tippy=Bt(this._triggerElement,{...t,onDestroy:o})}});j(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});j(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&Te(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:a=>{this._referenceClientRect=a,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});j(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&Te(this._contentElement,this._hoverContext);this._updateTippy();const a=function(i,u){const s=new ResizeObserver(()=>u());return s.observe(i),()=>s.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),a()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});j(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new Et)});this._opts=t,this._state=dn({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new se({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(Yt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(Yt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??It.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return Kn(this._state).open}};j(It,"CONTEXT_KEY","augment-tooltip-context"),j(It,"DEFAULT_DELAY_DURATION_MS",160);let Lt=It;function Vo(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function Bo(e){let t;const n=e[14].default,o=Ae(n,e,e[13],null);return{c(){o&&o.c()},m(a,i){o&&o.m(a,i),t=!0},p(a,[i]){o&&o.p&&(!t||8192&i)&&He(o,n,a,a[13],t?je(n,a[13],i,null):Le(a[13]),null)},i(a){t||(ke(o,a),t=!0)},o(a){Me(o,a),t=!1},d(a){o&&o.d(a)}}}function Wo(e,t,n){let{$$slots:o={},$$scope:a}=t,{defaultOpen:i}=t,{open:u}=t,{onOpenChange:s}=t,{delayDurationMs:c}=t,{nested:f=!0}=t,{hasPointerEvents:d=!0}=t,{offset:v}=t,{onHoverStart:m=()=>{}}=t,{onHoverEnd:b=()=>{}}=t,{triggerOn:O=[Yt.Hover,Yt.Click]}=t,{tippyTheme:E}=t;const l=new Lt({defaultOpen:i,open:u,onOpenChange:s,delayDurationMs:c,nested:f,onHoverStart:m,onHoverEnd:b,triggerOn:O,tippyTheme:E,hasPointerEvents:d,offset:v});return ln(Lt.CONTEXT_KEY,l),e.$$set=g=>{"defaultOpen"in g&&n(0,i=g.defaultOpen),"open"in g&&n(1,u=g.open),"onOpenChange"in g&&n(2,s=g.onOpenChange),"delayDurationMs"in g&&n(3,c=g.delayDurationMs),"nested"in g&&n(4,f=g.nested),"hasPointerEvents"in g&&n(5,d=g.hasPointerEvents),"offset"in g&&n(6,v=g.offset),"onHoverStart"in g&&n(7,m=g.onHoverStart),"onHoverEnd"in g&&n(8,b=g.onHoverEnd),"triggerOn"in g&&n(9,O=g.triggerOn),"tippyTheme"in g&&n(12,E=g.tippyTheme),"$$scope"in g&&n(13,a=g.$$scope)},e.$$.update=()=>{2&e.$$.dirty&&l.externalControlSetOpen(u)},[i,u,s,c,f,d,v,m,b,O,()=>l.openTooltip(),()=>l.closeTooltip(),E,a,o]}class zo extends _e{constructor(t){super(),Ce(this,t,Wo,Bo,De,{defaultOpen:0,open:1,onOpenChange:2,delayDurationMs:3,nested:4,hasPointerEvents:5,offset:6,onHoverStart:7,onHoverEnd:8,triggerOn:9,requestOpen:10,requestClose:11,tippyTheme:12})}get requestOpen(){return this.$$.ctx[10]}get requestClose(){return this.$$.ctx[11]}}function No(e){let t,n,o,a,i,u;const s=e[5].default,c=Ae(s,e,e[4],null);return{c(){t=vn("div"),c&&c.c(),nt(t,"class",n=We(`l-tooltip-trigger ${e[1]}`)+" svelte-2ap4ct"),nt(t,"role","button"),nt(t,"tabindex","-1")},m(f,d){hn(f,t,d),c&&c.m(t,null),a=!0,i||(u=[bt(t,"click",e[3]),bt(t,"keydown",e[6]),mn(o=e[2].registerTrigger(t,e[0]))],i=!0)},p(f,[d]){c&&c.p&&(!a||16&d)&&He(c,s,f,f[4],a?je(s,f[4],d,null):Le(f[4]),null),(!a||2&d&&n!==(n=We(`l-tooltip-trigger ${f[1]}`)+" svelte-2ap4ct"))&&nt(t,"class",n),o&&Pt(o.update)&&1&d&&o.update.call(null,f[0])},i(f){a||(ke(c,f),a=!0)},o(f){Me(c,f),a=!1},d(f){f&&gn(t),c&&c.d(f),i=!1,yn(u)}}}function qo(e,t,n){let{$$slots:o={},$$scope:a}=t,{referenceClientRect:i}=t,{class:u=""}=t;const s=bn(Lt.CONTEXT_KEY);return e.$$set=c=>{"referenceClientRect"in c&&n(0,i=c.referenceClientRect),"class"in c&&n(1,u=c.class),"$$scope"in c&&n(4,a=c.$$scope)},[i,u,s,c=>{s.supportsClick&&(s.toggleTooltip(),c.stopPropagation())},a,o,function(c){me.call(this,e,c)}]}class Jo extends _e{constructor(t){super(),Ce(this,t,qo,No,De,{referenceClientRect:0,class:1})}}const{window:he}=Jn;function Io(e){let t,n,o,a,i;const u=e[14].default,s=Ae(u,e,e[13],null);return{c(){t=vn("div"),s&&s.c(),nt(t,"class","l-tooltip-contents svelte-1mcoenu"),nt(t,"role","button"),nt(t,"tabindex","-1"),nt(t,"data-position-side",e[0]),nt(t,"data-position-align",e[1]),Ne(t,"l-tooltip-contents--open",e[2].open)},m(c,f){hn(c,t,f),s&&s.m(t,null),o=!0,a||(i=[bt(he,"click",function(){Pt(e[2].open?e[5]:void 0)&&(e[2].open?e[5]:void 0).apply(this,arguments)},!0),bt(he,"keydown",function(){Pt(e[2].open?e[6]:void 0)&&(e[2].open?e[6]:void 0).apply(this,arguments)},!0),bt(he,"blur",function(){Pt(e[2].open?e[7]:void 0)&&(e[2].open?e[7]:void 0).apply(this,arguments)},!0),mn(n=e[3].registerContents(t,{side:e[0],align:e[1]})),bt(t,"click",qe(e[15])),bt(t,"keydown",qe(e[16]))],a=!0)},p(c,[f]){e=c,s&&s.p&&(!o||8192&f)&&He(s,u,e,e[13],o?je(u,e[13],f,null):Le(e[13]),null),(!o||1&f)&&nt(t,"data-position-side",e[0]),(!o||2&f)&&nt(t,"data-position-align",e[1]),n&&Pt(n.update)&&3&f&&n.update.call(null,{side:e[0],align:e[1]}),(!o||4&f)&&Ne(t,"l-tooltip-contents--open",e[2].open)},i(c){o||(ke(s,c),o=!0)},o(c){Me(s,c),o=!1},d(c){c&&gn(t),s&&s.d(c),a=!1,yn(i)}}}function Uo(e,t,n){let o,a,{$$slots:i={},$$scope:u}=t,{onEscapeKeyDown:s=()=>{}}=t,{onClickOutside:c=()=>{}}=t,{onRequestClose:f=()=>{}}=t,{side:d="top"}=t,{align:v="center"}=t;const m=bn(Lt.CONTEXT_KEY),b=m.state;Ie(e,b,l=>n(2,a=l));const O=l=>{var g;if(Et.isEvent(l)&&l.target&&((g=m.contentElement)!=null&&g.contains(l.target)))return m.closeTooltip(),f(l),void l.stopPropagation()},E=Xn(b,l=>l.open);return Ie(e,E,l=>n(12,o=l)),zn(()=>{var l;(l=m.contentElement)==null||l.removeEventListener(Et.eventType,O)}),e.$$set=l=>{"onEscapeKeyDown"in l&&n(9,s=l.onEscapeKeyDown),"onClickOutside"in l&&n(10,c=l.onClickOutside),"onRequestClose"in l&&n(11,f=l.onRequestClose),"side"in l&&n(0,d=l.side),"align"in l&&n(1,v=l.align),"$$scope"in l&&n(13,u=l.$$scope)},e.$$.update=()=>{4096&e.$$.dirty&&m.contentElement&&(o?m.contentElement.addEventListener(Et.eventType,O):m.contentElement.removeEventListener(Et.eventType,O))},[d,v,a,m,b,l=>{l.target!==null&&l.target instanceof Node&&m.contentElement&&m.triggerElement&&a.open&&(l.composedPath().includes(m.contentElement)||l.composedPath().includes(m.triggerElement)||(m.closeTooltip(),c(l)))},l=>{l.target!==null&&l.target instanceof Node&&m.contentElement&&a.open&&l.key==="Escape"&&(m.closeTooltip(),s(l))},l=>{l.target===window&&m.requestClose()},E,s,c,f,o,u,i,function(l){me.call(this,e,l)},function(l){me.call(this,e,l)}]}class Go extends _e{constructor(t){super(),Ce(this,t,Uo,Io,De,{onEscapeKeyDown:9,onClickOutside:10,onRequestClose:11,side:0,align:1})}}export{Go as C,se as H,zo as R,Yt as T,Jo as a,Et as b,Lt as c,fn as d,Xo as e,Te as o,Bt as t};
