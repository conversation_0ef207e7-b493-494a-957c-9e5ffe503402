var ot=Object.defineProperty;var rt=(i,t,e)=>t in i?ot(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var _=(i,t,e)=>rt(i,typeof t!="symbol"?t+"":t,e);import{S as T,i as q,s as E,V as I,C as S,I as O,c as D,ae as lt,e as v,f as y,D as F,av as J,a8 as B,u as $,t as m,h as w,F as C,K as nt,aa as ct,L as R,a7 as K,M as st,aw as ut,n as N,q as k,r as P,am as dt,ao as it,ah as ft,a5 as $t,A as U,B as V,J as pt,E as W}from"./SpinnerAugment-Cx9dt_ox.js";import{A as gt}from"./autofix-state-d-ymFdyn.js";import{e as z,h as mt,W as L}from"./BaseButton-BqzdgpkK.js";import{B as j}from"./ButtonAugment-DhtPLzGu.js";import{s as ht,n as xt}from"./IconFilePath-B4JAagx1.js";import{C as yt,f as It}from"./file-reader-BdhEMA38.js";import{A as bt}from"./IconButtonAugment-BjDqXmYl.js";import{M as vt}from"./index-8X-F_Twk.js";import"./LanguageIcon-FVMxq7uD.js";import"./next-edit-types-904A5ehg.js";import"./VSCodeCodicon-B3px2_jp.js";import"./keypress-DD1aQVr0.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./resize-observer-DdAtcrRr.js";import"./index-BxQII05L.js";import"./isObjectLike-BWVRxMGM.js";var b=(i=>(i.testLog="testLog",i.solution="solution",i))(b||{});function Y(i,t,e){const a=i.slice();return a[12]=t[e],a}function wt(i){let t;return{c(){t=R("Test log")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function At(i){let t;return{c(){t=R("Solution")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function G(i){let t,e,a,o,s,n=i[2].indexOf(i[12])+1+"";return{c(){t=I("option"),e=R("Test Attempt "),a=R(n),o=O(),t.__value=s=i[12].id,K(t,t.__value)},m(l,r){v(l,t,r),y(t,e),y(t,a),y(t,o)},p(l,r){4&r&&n!==(n=l[2].indexOf(l[12])+1+"")&&st(a,n),4&r&&s!==(s=l[12].id)&&(t.__value=s,K(t,t.__value))},d(l){l&&w(t)}}}function Dt(i){let t,e,a,o,s,n,l,r,c,d,g;a=new j({props:{variant:i[1]===b.testLog?"classic":"solid",$$slots:{default:[wt]},$$scope:{ctx:i}}}),a.$on("click",i[8]),s=new j({props:{variant:i[1]===b.solution?"classic":"solid",disabled:!i[3],$$slots:{default:[At]},$$scope:{ctx:i}}}),s.$on("click",i[9]);let A=z(i[2]),p=[];for(let f=0;f<A.length;f+=1)p[f]=G(Y(i,A,f));return{c(){t=I("div"),e=I("div"),S(a.$$.fragment),o=O(),S(s.$$.fragment),n=O(),l=I("div"),r=I("select");for(let f=0;f<p.length;f+=1)p[f].c();D(e,"class","tab-buttons svelte-174sacf"),D(r,"class","iteration-selector svelte-174sacf"),i[0]===void 0&&lt(()=>i[10].call(r)),D(l,"class","iteration-selector-container svelte-174sacf"),D(t,"class","autofix-details-header svelte-174sacf")},m(f,u){v(f,t,u),y(t,e),F(a,e,null),y(e,o),F(s,e,null),y(t,n),y(t,l),y(l,r);for(let h=0;h<p.length;h+=1)p[h]&&p[h].m(r,null);J(r,i[0],!0),c=!0,d||(g=[B(r,"change",i[10]),B(r,"change",i[11])],d=!0)},p(f,[u]){const h={};2&u&&(h.variant=f[1]===b.testLog?"classic":"solid"),32768&u&&(h.$$scope={dirty:u,ctx:f}),a.$set(h);const M={};if(2&u&&(M.variant=f[1]===b.solution?"classic":"solid"),8&u&&(M.disabled=!f[3]),32768&u&&(M.$$scope={dirty:u,ctx:f}),s.$set(M),4&u){let x;for(A=z(f[2]),x=0;x<A.length;x+=1){const H=Y(f,A,x);p[x]?p[x].p(H,u):(p[x]=G(H),p[x].c(),p[x].m(r,null))}for(;x<p.length;x+=1)p[x].d(1);p.length=A.length}5&u&&J(r,f[0])},i(f){c||($(a.$$.fragment,f),$(s.$$.fragment,f),c=!0)},o(f){m(a.$$.fragment,f),m(s.$$.fragment,f),c=!1},d(f){f&&w(t),C(a),C(s),nt(p,f),d=!1,ct(g)}}}function _t(i,t,e){let a,o,s,{autofixData:n}=t,{currentTab:l=b.testLog}=t,{currentIterationId:r}=t;function c(g){e(1,l=g)}function d(g){e(1,l=b.testLog),e(0,r=g)}return i.$$set=g=>{"autofixData"in g&&e(6,n=g.autofixData),"currentTab"in g&&e(1,l=g.currentTab),"currentIterationId"in g&&e(0,r=g.currentIterationId)},i.$$.update=()=>{64&i.$$.dirty&&e(2,a=n.autofixIterations||[]),5&i.$$.dirty&&a.length>0&&!r&&e(0,r=a[a.length-1].id),5&i.$$.dirty&&e(7,o=a.find(g=>g.id===r)),128&i.$$.dirty&&e(3,s=(o==null?void 0:o.suggestedSolutions)!==void 0)},[r,l,a,s,c,d,n,o,()=>c(b.testLog),()=>c(b.solution),function(){r=ut(this),e(0,r),e(2,a),e(6,n),e(2,a),e(6,n)},()=>d(r)]}class St extends T{constructor(t){super(),q(this,t,_t,Dt,E,{autofixData:6,currentTab:1,currentIterationId:0})}}function Ft(i){var o;let t,e,a=((o=i[0])==null?void 0:o.commandOutput)+"";return{c(){t=I("div"),e=R(a),D(t,"class","c-autofix-details-log svelte-18pm6ob")},m(s,n){v(s,t,n),y(t,e)},p(s,[n]){var l;1&n&&a!==(a=((l=s[0])==null?void 0:l.commandOutput)+"")&&st(e,a)},i:N,o:N,d(s){s&&w(t)}}}function Ct(i,t,e){let a,{iterationId:o}=t,{autofixData:s}=t;return i.$$set=n=>{"iterationId"in n&&e(1,o=n.iterationId),"autofixData"in n&&e(2,s=n.autofixData)},i.$$.update=()=>{var n;6&i.$$.dirty&&e(0,a=(n=s.autofixIterations)==null?void 0:n.filter(l=>l.id===o)[0])},[a,o,s]}class Mt extends T{constructor(t){super(),q(this,t,Ct,Ft,E,{iterationId:1,autofixData:2})}}function Q(i,t,e){const a=i.slice();return a[6]=t[e][0],a[7]=t[e][1],a}function X(i){let t,e,a;return e=new dt({}),{c(){t=I("li"),S(e.$$.fragment),D(t,"class","c-code-roll__item svelte-1iyponq")},m(o,s){v(o,t,s),F(e,t,null),a=!0},i(o){a||($(e.$$.fragment,o),a=!0)},o(o){m(e.$$.fragment,o),a=!1},d(o){o&&w(t),C(e)}}}function Z(i){var s;let t,e,a,o;return e=new yt({props:{codeActions:i[1],filepath:(s=i[7][0])==null?void 0:s.qualifiedPathName,readFile:i[3],onCodeAction:i[0],suggestions:i[7]}}),{c(){t=I("li"),S(e.$$.fragment),a=O(),D(t,"class","c-code-roll__item svelte-1iyponq")},m(n,l){v(n,t,l),F(e,t,null),y(t,a),o=!0},p(n,l){var c;const r={};2&l&&(r.codeActions=n[1]),16&l&&(r.filepath=(c=n[7][0])==null?void 0:c.qualifiedPathName),8&l&&(r.readFile=n[3]),1&l&&(r.onCodeAction=n[0]),16&l&&(r.suggestions=n[7]),e.$set(r)},i(n){o||($(e.$$.fragment,n),o=!0)},o(n){m(e.$$.fragment,n),o=!1},d(n){n&&w(t),C(e)}}}function Ot(i){let t,e,a,o=i[2]&&X(),s=z(i[4]),n=[];for(let r=0;r<s.length;r+=1)n[r]=Z(Q(i,s,r));const l=r=>m(n[r],1,1,()=>{n[r]=null});return{c(){t=I("ul"),o&&o.c(),e=O();for(let r=0;r<n.length;r+=1)n[r].c();D(t,"class","c-code-roll svelte-1iyponq")},m(r,c){v(r,t,c),o&&o.m(t,null),y(t,e);for(let d=0;d<n.length;d+=1)n[d]&&n[d].m(t,null);a=!0},p(r,[c]){if(r[2]?o?4&c&&$(o,1):(o=X(),o.c(),$(o,1),o.m(t,e)):o&&(k(),m(o,1,1,()=>{o=null}),P()),27&c){let d;for(s=z(r[4]),d=0;d<s.length;d+=1){const g=Q(r,s,d);n[d]?(n[d].p(g,c),$(n[d],1)):(n[d]=Z(g),n[d].c(),$(n[d],1),n[d].m(t,null))}for(k(),d=s.length;d<n.length;d+=1)l(d);P()}},i(r){if(!a){$(o);for(let c=0;c<s.length;c+=1)$(n[c]);a=!0}},o(r){m(o),n=n.filter(Boolean);for(let c=0;c<n.length;c+=1)m(n[c]);a=!1},d(r){r&&w(t),o&&o.d(),nt(n,r)}}}function Lt(i,t,e){let a,{suggestions:o=[]}=t,{onCodeAction:s=xt}=t,{codeActions:n=[]}=t,{loading:l=!0}=t,{readFile:r}=t;return i.$$set=c=>{"suggestions"in c&&e(5,o=c.suggestions),"onCodeAction"in c&&e(0,s=c.onCodeAction),"codeActions"in c&&e(1,n=c.codeActions),"loading"in c&&e(2,l=c.loading),"readFile"in c&&e(3,r=c.readFile)},i.$$.update=()=>{32&i.$$.dirty&&e(4,a=new Map(ht(o)))},[s,n,l,r,a,o]}class Rt extends T{constructor(t){super(),q(this,t,Lt,Ot,E,{suggestions:5,onCodeAction:0,codeActions:1,loading:2,readFile:3})}}function kt(i){let t;return{c(){t=R("Apply & Retest")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function Pt(i){let t,e,a,o,s;return o=new j({props:{$$slots:{default:[kt]},$$scope:{ctx:i}}}),o.$on("click",i[1]),{c(){t=I("div"),e=I("span"),e.textContent=`Here are the suggestions for how to fix this failure. You must review each change before
    continuing.`,a=O(),S(o.$$.fragment),D(t,"class","autofix-details-footer svelte-112w9tk")},m(n,l){v(n,t,l),y(t,e),y(t,a),F(o,t,null),s=!0},p(n,[l]){const r={};4&l&&(r.$$scope={dirty:l,ctx:n}),o.$set(r)},i(n){s||($(o.$$.fragment,n),s=!0)},o(n){m(o.$$.fragment,n),s=!1},d(n){n&&w(t),C(o)}}}function Tt(i){const t=it();return[t,()=>t("applyAndRetestClick")]}class qt extends T{constructor(t){super(),q(this,t,Tt,Pt,E,{})}}function tt(i){let t,e;return t=new Rt({props:{suggestions:i[2],readFile:i[0],loading:!1}}),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const s={};4&o&&(s.suggestions=a[2]),1&o&&(s.readFile=a[0]),t.$set(s)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){m(t.$$.fragment,a),e=!1},d(a){C(t,a)}}}function et(i){let t,e;return t=new qt({}),t.$on("applyAndRetestClick",i[7]),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p:N,i(a){e||($(t.$$.fragment,a),e=!0)},o(a){m(t.$$.fragment,a),e=!1},d(a){C(t,a)}}}function Et(i){let t,e,a,o,s=i[2]&&tt(i),n=i[1]&&et(i);return{c(){t=I("div"),e=I("div"),s&&s.c(),a=O(),n&&n.c(),D(e,"class","c-autofix-details-solution-coderoll-container svelte-1uzxw9z"),D(t,"class","c-autofix-details-solution svelte-1uzxw9z")},m(l,r){v(l,t,r),y(t,e),s&&s.m(e,null),y(t,a),n&&n.m(t,null),o=!0},p(l,[r]){l[2]?s?(s.p(l,r),4&r&&$(s,1)):(s=tt(l),s.c(),$(s,1),s.m(e,null)):s&&(k(),m(s,1,1,()=>{s=null}),P()),l[1]?n?(n.p(l,r),2&r&&$(n,1)):(n=et(l),n.c(),$(n,1),n.m(t,null)):n&&(k(),m(n,1,1,()=>{n=null}),P())},i(l){o||($(s),$(n),o=!0)},o(l){m(s),m(n),o=!1},d(l){l&&w(t),s&&s.d(),n&&n.d()}}}function zt(i,t,e){let a,o,{iterationId:s}=t,{autofixData:n}=t,{readFile:l}=t;const r=it();let c;return i.$$set=d=>{"iterationId"in d&&e(4,s=d.iterationId),"autofixData"in d&&e(5,n=d.autofixData),"readFile"in d&&e(0,l=d.readFile)},i.$$.update=()=>{var d,g,A,p,f;48&i.$$.dirty&&e(6,c=(d=n.autofixIterations)==null?void 0:d.filter(u=>u.id===s)[0]),64&i.$$.dirty&&e(2,a=(A=(g=c==null?void 0:c.suggestedSolutions)==null?void 0:g.at(-1))==null?void 0:A.replacements),48&i.$$.dirty&&e(1,o=s===((f=(p=n.autofixIterations)==null?void 0:p.at(-1))==null?void 0:f.id))},[l,o,a,r,s,n,c,()=>a&&r("applyAndRetest",a)]}class Bt extends T{constructor(t){super(),q(this,t,zt,Et,E,{iterationId:4,autofixData:5,readFile:0})}}class Nt{constructor(t){_(this,"_asyncMsgSender");_(this,"_latestData");_(this,"subscribers",new Set);_(this,"_readFile");_(this,"dispose",()=>{this.subscribers.clear()});_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"initialize",async()=>{const t=await this._asyncMsgSender.send({type:L.autofixPanelDetailsInitRequest},2e3);this.onOpenSpecificStage(t.data.iterationId,t.data.stage)});_(this,"applyAndRetest",async t=>{await this._asyncMsgSender.send({type:L.autofixPanelApplyAndRetestRequest,data:{selectedSolutions:t}},2e3)});_(this,"handleMessageFromExtension",async t=>{const e=t.data;switch(e.type){case L.autofixPanelStateUpdate:this._latestData=e.data,this.notifySubscribers();break;case L.autofixPanelExecuteCommandPartialOutput:this.handlePartialOutput(e.data.iterationId,e.data.output);break;case L.autofixPanelOpenSpecificStage:this.onOpenSpecificStage(e.data.iterationId,e.data.stage);break;case L.empty:break;default:console.warn("AutofixModel got unexpected message: ",e)}});this.onOpenSpecificStage=t,this._asyncMsgSender=new bt(e=>mt.postMessage(e)),this._readFile=It(this._asyncMsgSender),this.initialize()}get latestData(){return this._latestData}get readFile(){return this._readFile}handlePartialOutput(t,e){var a,o;(o=(a=this._latestData)==null?void 0:a.autofixIterations)==null||o.forEach(s=>{s.id===t&&(s.commandOutput=e)}),this.notifySubscribers()}}function at(i){let t,e,a,o,s,n,l,r;function c(u){i[5](u)}function d(u){i[6](u)}let g={autofixData:i[0]};i[1]!==void 0&&(g.currentIterationId=i[1]),i[2]!==void 0&&(g.currentTab=i[2]),t=new St({props:g}),U.push(()=>V(t,"currentIterationId",c)),U.push(()=>V(t,"currentTab",d));const A=[Ht,jt],p=[];function f(u,h){return u[2]===b.testLog?0:u[2]===b.solution?1:-1}return~(s=f(i))&&(n=p[s]=A[s](i)),{c(){S(t.$$.fragment),o=O(),n&&n.c(),l=pt()},m(u,h){F(t,u,h),v(u,o,h),~s&&p[s].m(u,h),v(u,l,h),r=!0},p(u,h){const M={};1&h&&(M.autofixData=u[0]),!e&&2&h&&(e=!0,M.currentIterationId=u[1],W(()=>e=!1)),!a&&4&h&&(a=!0,M.currentTab=u[2],W(()=>a=!1)),t.$set(M);let x=s;s=f(u),s===x?~s&&p[s].p(u,h):(n&&(k(),m(p[x],1,1,()=>{p[x]=null}),P()),~s?(n=p[s],n?n.p(u,h):(n=p[s]=A[s](u),n.c()),$(n,1),n.m(l.parentNode,l)):n=null)},i(u){r||($(t.$$.fragment,u),$(n),r=!0)},o(u){m(t.$$.fragment,u),m(n),r=!1},d(u){u&&(w(o),w(l)),C(t,u),~s&&p[s].d(u)}}}function jt(i){let t,e;return t=new Bt({props:{autofixData:i[0],iterationId:i[1],readFile:i[3].readFile}}),t.$on("applyAndRetest",i[7]),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const s={};1&o&&(s.autofixData=a[0]),2&o&&(s.iterationId=a[1]),t.$set(s)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){m(t.$$.fragment,a),e=!1},d(a){C(t,a)}}}function Ht(i){let t,e;return t=new Mt({props:{iterationId:i[1],autofixData:i[0]}}),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const s={};2&o&&(s.iterationId=a[1]),1&o&&(s.autofixData=a[0]),t.$set(s)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){m(t.$$.fragment,a),e=!1},d(a){C(t,a)}}}function Jt(i){let t,e,a=i[0]&&at(i);return{c(){t=I("div"),a&&a.c(),D(t,"class","autofix-container svelte-107lb5t")},m(o,s){v(o,t,s),a&&a.m(t,null),e=!0},p(o,s){o[0]?a?(a.p(o,s),1&s&&$(a,1)):(a=at(o),a.c(),$(a,1),a.m(t,null)):a&&(k(),m(a,1,1,()=>{a=null}),P())},i(o){e||($(a),e=!0)},o(o){m(a),e=!1},d(o){o&&w(t),a&&a.d()}}}function Kt(i){let t,e,a,o;return t=new vt.Root({props:{$$slots:{default:[Jt]},$$scope:{ctx:i}}}),{c(){S(t.$$.fragment)},m(s,n){var l;F(t,s,n),e=!0,a||(o=B(window,"message",(l=i[3])==null?void 0:l.handleMessageFromExtension),a=!0)},p(s,[n]){const l={};519&n&&(l.$$scope={dirty:n,ctx:s}),t.$set(l)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){m(t.$$.fragment,s),e=!1},d(s){C(t,s),a=!1,o()}}}function Ut(i,t,e){let a,o,s,n=b.testLog,l=new Nt((r,c)=>{e(1,s=r),e(2,n=c===gt.runTest?b.testLog:b.solution)});return ft(i,l,r=>e(4,a=r)),$t(()=>{l.dispose()}),i.$$.update=()=>{16&i.$$.dirty&&e(0,o=a.latestData)},[o,s,n,l,a,function(r){s=r,e(1,s)},function(r){n=r,e(2,n)},r=>l==null?void 0:l.applyAndRetest(r.detail)]}new class extends T{constructor(i){super(),q(this,i,Ut,Kt,E,{})}}({target:document.getElementById("app")});
