<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Box 授权管理系统</title>
    <style>
        /* 本地字体定义 */
        @font-face {
            font-family: 'ZQL';
            src: url('./fonts/zql.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'ZQL', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏 */
        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 18px;
            font-weight: bold;
        }
        
        .menu {
            list-style: none;
        }
        
        .menu-item {
            padding: 15px 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
        }
        
        .menu-item.active {
            background: rgba(102, 126, 234, 0.15);
            border-left-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        
        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .content-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .content-header h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .content-header p {
            color: #666;
            font-size: 14px;
        }
        
        .content-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        
        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #e2e3e5;
            color: #495057;
        }

        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .status-expired {
            background: #fff3cd;
            color: #856404;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .main-content {
                padding: 15px;
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        
        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 消息提示 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-auto {
            flex: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h1>✨ Magic Box 管理</h1>
            </div>

            <!-- 用户信息 -->
            <div style="padding: 15px 20px; border-bottom: 1px solid #eee; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <div style="font-size: 14px; color: #333; font-weight: 500;" id="username-display">加载中...</div>
                        <div style="font-size: 12px; color: #666;">管理员</div>
                    </div>
                    <button onclick="logout()" style="background: #ff4757; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                        登出
                    </button>
                </div>
            </div>

            <ul class="menu">
                <li class="menu-item active" data-page="auth-codes">
                    📋 授权码
                </li>
                <li class="menu-item" data-page="generate">
                    ➕ 生成授权码
                </li>
                <li class="menu-item" data-page="usage-logs">
                    📊 使用记录
                </li>
                <li class="menu-item" data-page="augment-accounts">
                    👥 Augment账号
                </li>
                <li class="menu-item" data-page="announcements">
                    📢 通知公告
                </li>
                <li class="menu-item" data-page="update-manager">
                    🚀 更新管理
                </li>
                <li class="menu-item" data-page="settings">
                    ⚙️ 系统设置
                </li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 授权码管理页面 -->
            <div id="auth-codes-page" class="page">
                <div class="content-header">
                    <h2>授权码管理</h2>
                    <p>管理所有授权码，查看使用状态和统计信息</p>
                </div>
                <div class="content-body">
                    <div class="row">
                        <div class="col">
                            <input type="text" id="search-input" class="form-control" placeholder="搜索授权码、名称或备注...">
                        </div>
                        <div class="col-auto">
                            <select id="status-filter" class="form-control">
                                <option value="">全部状态</option>
                                <option value="active">激活</option>
                                <option value="disabled">禁用</option>
                                <option value="expired">过期</option>
                            </select>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="loadAuthCodes()">🔍 搜索</button>
                        </div>
                    </div>
                    
                    <div id="auth-codes-container">
                        <div class="loading">
                            <div class="spinner"></div>
                            正在加载授权码列表...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 生成授权码页面 -->
            <div id="generate-page" class="page hidden">
                <div class="content-header">
                    <h2>生成授权码</h2>
                    <p>创建新的授权码，设置使用次数和过期时间</p>
                </div>
                <div class="content-body">
                    <form id="generate-form">
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="auth-name">授权码名称 *</label>
                                    <input type="text" id="auth-name" class="form-control" placeholder="请输入授权码名称" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="max-uses">最大使用次数</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="number" id="max-uses" class="form-control" value="1" min="1" placeholder="1" style="flex: 1;">
                                        <label style="margin: 0; white-space: nowrap;">
                                            <input type="checkbox" id="unlimited-uses" onchange="toggleUnlimitedUses()"> 永久使用
                                        </label>
                                    </div>
                                    <small style="color: #666;">勾选"永久使用"表示无限制使用次数</small>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="max-devices">最大设备数量</label>
                                    <input type="number" id="max-devices" class="form-control" value="1" min="1" placeholder="1">
                                    <small style="color: #666;">限制可以使用此授权码的设备数量</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="expire-type">有效期类型</label>
                                    <select id="expire-type" class="form-control" onchange="toggleExpireMode()">
                                        <option value="days">按天数设置</option>
                                        <option value="datetime">按具体时间设置</option>
                                        <option value="never">永不过期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="custom-code">自定义授权码（可选）</label>
                                    <input type="text" id="custom-code" class="form-control" placeholder="留空自动生成">
                                </div>
                            </div>
                        </div>

                        <!-- 按天数设置 -->
                        <div id="expire-days-group" class="form-group">
                            <label for="expire-days">有效期（天）</label>
                            <div class="row">
                                <div class="col">
                                    <input type="number" id="expire-days" class="form-control" value="30" min="1" placeholder="30">
                                </div>
                                <div class="col-auto" style="display: flex; align-items: end;">
                                    <span style="padding: 12px 0; color: #666;">天后过期</span>
                                </div>
                            </div>
                        </div>

                        <!-- 按具体时间设置 -->
                        <div id="expire-datetime-group" class="form-group hidden">
                            <label for="expire-datetime">过期时间</label>
                            <div class="row">
                                <div class="col">
                                    <input type="datetime-local" id="expire-datetime" class="form-control">
                                </div>
                                <div class="col-auto" style="display: flex; align-items: end;">
                                    <button type="button" class="btn btn-primary" style="padding: 8px 15px; font-size: 12px;" onclick="setQuickTime(1)">+1小时</button>
                                </div>
                                <div class="col-auto" style="display: flex; align-items: end;">
                                    <button type="button" class="btn btn-primary" style="padding: 8px 15px; font-size: 12px;" onclick="setQuickTime(24)">+1天</button>
                                </div>
                                <div class="col-auto" style="display: flex; align-items: end;">
                                    <button type="button" class="btn btn-primary" style="padding: 8px 15px; font-size: 12px;" onclick="setQuickTime(168)">+1周</button>
                                </div>
                            </div>
                            <small style="color: #666; margin-top: 5px; display: block;">精确到秒，支持快捷设置</small>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="notes">备注信息</label>
                                    <textarea id="notes" class="form-control" rows="3" placeholder="请输入备注信息（可选）"></textarea>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>安全设置</label>
                                    <div style="margin-top: 10px;">
                                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                            <input type="checkbox" id="device-binding" checked style="margin-right: 8px;">
                                            <span>🔒 启用设备绑定</span>
                                        </label>
                                        <small style="color: #666; margin-left: 20px;">防止授权码被多人共享使用</small>
                                    </div>
                                    <div style="margin-top: 15px;">
                                        <label style="display: flex; align-items: center;">
                                            <input type="checkbox" id="custom-code-enable" style="margin-right: 8px;">
                                            <span>🎯 自定义授权码</span>
                                        </label>
                                        <input type="text" id="custom-code" class="form-control" placeholder="留空自动生成" style="margin-top: 8px; margin-left: 20px;" disabled>
                                        <small style="color: #666; margin-left: 20px;">16位字母数字组合</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">🎯 生成授权码</button>
                    </form>
                    
                    <div id="generate-result" class="hidden" style="margin-top: 30px;">
                        <h3>生成成功！</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 15px;">
                            <p><strong>授权码：</strong> <span id="generated-code" style="font-family: monospace; font-size: 16px; color: #667eea;"></span></p>
                            <p><strong>名称：</strong> <span id="generated-name"></span></p>
                            <p><strong>最大使用次数：</strong> <span id="generated-uses"></span></p>
                            <p><strong>最大设备数量：</strong> <span id="generated-max-devices"></span></p>
                            <p><strong>设备绑定：</strong> <span id="generated-device-binding"></span></p>
                            <p><strong>过期时间：</strong> <span id="generated-expire"></span></p>
                            <button class="btn btn-primary" onclick="copyToClipboard()">📋 复制授权码</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 公告管理页面 -->
            <div id="announcements-page" class="page hidden">
                <div class="content-body">
                    <div class="row">
                        <div class="col-auto">
                            <button class="btn btn-success" onclick="showCreateAnnouncementForm()">➕ 新建公告</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="loadAnnouncements()">🔄 刷新</button>
                        </div>
                    </div>

                    <!-- 创建/编辑公告表单 -->
                    <div id="announcement-form" class="hidden" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h4 id="form-title">新建公告</h4>
                        <form id="announcement-form-element">
                            <input type="hidden" id="announcement-id">
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="announcement-type">公告类型</label>
                                        <select id="announcement-type" class="form-control">
                                            <option value="info">信息</option>
                                            <option value="warning">警告</option>
                                            <option value="success">成功</option>
                                            <option value="error">错误</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label for="announcement-priority">优先级</label>
                                        <input type="number" id="announcement-priority" class="form-control" value="0" min="0" max="100">
                                        <small style="color: #666;">数字越大越靠前显示</small>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="announcement-content">公告内容 *</label>
                                <textarea id="announcement-content" class="form-control" rows="4" placeholder="请输入公告内容" required></textarea>
                            </div>
                            <div class="row">
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-success">💾 保存公告</button>
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-danger" onclick="hideAnnouncementForm()">❌ 取消</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 公告列表 -->
                    <div id="announcements-container" style="margin-top: 20px;">
                        <div class="loading">
                            <div class="spinner"></div>
                            正在加载公告列表...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用记录页面 -->
            <div id="usage-logs-page" class="page hidden">
                <div class="content-header">
                    <h2>使用记录</h2>
                    <p>查看授权码的使用历史和统计信息</p>
                </div>
                <div class="content-body">
                    <!-- 统计信息 -->
                    <div id="usage-statistics" class="row" style="margin-bottom: 25px;">
                        <div class="col">
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #1976d2;">总请求数</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #1976d2;" id="stat-total">-</p>
                            </div>
                        </div>
                        <div class="col">
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #388e3c;">成功验证</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #388e3c;" id="stat-success">-</p>
                            </div>
                        </div>
                        <div class="col">
                            <div style="background: #ffebee; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #d32f2f;">失败验证</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #d32f2f;" id="stat-failed">-</p>
                            </div>
                        </div>
                        <div class="col">
                            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #f57c00;">唯一授权码</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #f57c00;" id="stat-codes">-</p>
                            </div>
                        </div>
                        <div class="col">
                            <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #7b1fa2;">唯一IP</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #7b1fa2;" id="stat-ips">-</p>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col">
                            <input type="text" id="filter-auth-code" class="form-control" placeholder="授权码筛选...">
                        </div>
                        <div class="col">
                            <select id="filter-success" class="form-control">
                                <option value="">全部状态</option>
                                <option value="true">成功</option>
                                <option value="false">失败</option>
                            </select>
                        </div>
                        <div class="col">
                            <input type="date" id="filter-start-date" class="form-control" placeholder="开始日期">
                        </div>
                        <div class="col">
                            <input type="date" id="filter-end-date" class="form-control" placeholder="结束日期">
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="loadUsageLogs()">🔍 筛选</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="clearFilters()">🔄 重置</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-warning" onclick="showClearLogsModal()">🗑️ 清空记录</button>
                        </div>
                    </div>

                    <!-- 使用记录列表 -->
                    <div id="usage-logs-container">
                        <div class="loading">
                            <div class="spinner"></div>
                            正在加载使用记录...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 更新管理页面 -->
            <div id="update-manager-page" class="page hidden">
                <div class="content-header">
                    <h2>更新管理</h2>
                    <p>管理软件版本更新，发布新版本给用户</p>
                </div>
                <div class="content-body">
                    <!-- 当前版本信息 -->
                    <div class="row" style="margin-bottom: 30px;">
                        <div class="col">
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;">
                                <h4 style="margin: 0 0 15px 0; color: #333;">📊 版本状态</h4>
                                <div class="row">
                                    <div class="col">
                                        <p><strong>当前版本：</strong> <span id="current-version">加载中...</span></p>
                                        <p><strong>最新版本：</strong> <span id="latest-version">加载中...</span></p>
                                    </div>
                                    <div class="col">
                                        <p><strong>更新状态：</strong> <span id="update-status" class="status-badge">加载中...</span></p>
                                        <p><strong>发布日期：</strong> <span id="release-date">加载中...</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 发布新版本 -->
                    <div style="background: white; padding: 25px; border-radius: 8px; border: 1px solid #e1e5e9; margin-bottom: 25px;">
                        <h4 style="margin: 0 0 20px 0; color: #333;">🚀 发布新版本</h4>
                        <form id="update-form">
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="new-version">版本号 *</label>
                                        <input type="text" id="new-version" class="form-control" placeholder="例如: 1.0.2" required>
                                        <div id="version-reminder" style="
                                            color: #666;
                                            font-size: 12px;
                                            margin-top: 8px;
                                            padding: 8px 12px;
                                            background: #f8f9fa;
                                            border: 1px solid #e9ecef;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            user-select: none;
                                            transition: all 0.2s ease;
                                        " onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='#f8f9fa'" onclick="copyVersionReminder()">
                                            💡 记得修改更新版本要修改 Cargo.toml 和 tauri.conf.json（点击复制）
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label for="update-file">更新文件 (EXE) *</label>
                                        <input type="file" id="update-file" class="form-control" accept=".exe" required>
                                        <small style="color: #666;">统一使用EXE格式，支持所有安装方式</small>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info" style="margin-bottom: 20px;">
                                <strong>📝 更新说明：</strong><br>
                                • 统一使用EXE文件格式，兼容所有安装方式<br>
                                • 更新机制已简化，避免MSI安装的复杂性<br>
                                • 用户更新时会直接启动新版本EXE文件
                            </div>

                            <div class="form-group">
                                <label for="version-description">版本描述 *</label>
                                <input type="text" id="version-description" class="form-control" placeholder="例如: 修复了一些已知问题，提升了用户体验" required>
                            </div>

                            <div class="form-group">
                                <label for="update-features">更新内容 *</label>
                                <textarea id="update-features" class="form-control" rows="4" placeholder="每行一个更新内容，例如:&#10;优化了清理算法，提升清理效率&#10;改进了用户界面，更加美观易用&#10;修复了网络连接问题" required></textarea>
                                <small style="color: #666;">每行一个更新内容</small>
                            </div>

                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" id="mandatory-update" style="margin-right: 8px;">
                                            强制更新
                                        </label>
                                        <small style="color: #666; display: block; margin-top: 5px;">勾选后用户必须更新才能继续使用</small>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-success" style="width: 100%;">
                                            📤 发布更新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 版本历史 -->
                    <div style="background: white; padding: 25px; border-radius: 8px; border: 1px solid #e1e5e9;">
                        <h4 style="margin: 0 0 20px 0; color: #333;">📋 版本历史</h4>
                        <div id="version-history">
                            <div class="loading">
                                <div class="spinner"></div>
                                正在加载版本历史...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Augment账号管理页面 -->
            <div id="augment-accounts-page" class="page hidden">
                <div class="content-header">
                    <h2>Augment账号管理</h2>
                    <p>管理VSCode Augment账号池，支持一键切换和自动轮换</p>
                </div>
                <div class="content-body">
                    <!-- 操作按钮 -->
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-auto">
                            <button class="btn btn-success" onclick="showAddAccountForm()">➕ 添加账号</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="loadAugmentAccounts()">🔄 刷新</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="showBatchImportForm()">📥 批量导入</button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" onclick="loadAugmentStats()">📊 统计信息</button>
                        </div>
                    </div>

                    <!-- 添加账号表单 -->
                    <div id="add-account-form" class="hidden" style="margin-bottom: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h4>添加新账号</h4>
                        <form id="account-form">
                            <input type="hidden" id="account-id">
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-name">账号名称 *</label>
                                        <input type="text" id="account-name" class="form-control" placeholder="例如: 账号001" required>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-email">邮箱 *</label>
                                        <input type="email" id="account-email" class="form-control" placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-user-id">浏览器用户ID *</label>
                                        <input type="text" id="account-user-id" class="form-control" placeholder="8be0bb5e-53d5-46fb-aee5-e2044713f778" required>
                                        <small style="color: #666;">从浏览器localStorage中的ajs_user_id获取</small>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-session-id">VSCode Session ID</label>
                                        <input type="text" id="account-session-id" class="form-control" placeholder="df9909e0-dfb0-4ee4-8273-f6e41e3d6da5">
                                        <small style="color: #666;">从VSCode中复制Session ID获取</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-type">账号类型</label>
                                        <select id="account-type" class="form-control">
                                            <option value="regular">正式账号 (50额度)</option>
                                            <option value="trial">试用账号 (300额度)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label for="account-quota">总额度</label>
                                        <input type="number" id="account-quota" class="form-control" value="50" min="1">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="account-notes">备注</label>
                                <textarea id="account-notes" class="form-control" rows="2" placeholder="账号备注信息（可选）"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-success">💾 保存账号</button>
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-danger" onclick="hideAddAccountForm()">❌ 取消</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 批量导入表单 -->
                    <div id="batch-import-form" class="hidden" style="margin-bottom: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h4>批量导入账号</h4>
                        <div class="form-group">
                            <label for="batch-accounts">账号数据 (JSON格式)</label>
                            <textarea id="batch-accounts" class="form-control" rows="8" placeholder='[
  {
    "account_name": "账号001",
    "email": "<EMAIL>",
    "user_id": "uuid1",
    "session_id": "session1",
    "account_type": "regular",
    "quota_total": 50
  },
  {
    "account_name": "账号002",
    "email": "<EMAIL>",
    "user_id": "uuid2",
    "session_id": "session2",
    "account_type": "regular",
    "quota_total": 50
  }
]'></textarea>
                        </div>
                        <div class="row">
                            <div class="col-auto">
                                <button class="btn btn-success" onclick="submitBatchImport()">📥 导入账号</button>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-danger" onclick="hideBatchImportForm()">❌ 取消</button>
                            </div>
                        </div>
                    </div>

                    <!-- 账号列表 -->
                    <div id="augment-accounts-container">
                        <div class="loading">
                            <div class="spinner"></div>
                            正在加载账号列表...
                        </div>
                    </div>
                </div>
            </div>

            <div id="settings-page" class="page hidden">
                <div class="content-header">
                    <h2>系统设置</h2>
                    <p>配置系统参数和默认值</p>
                </div>
                <div class="content-body">
                    <p>系统设置功能开发中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="admin.js"></script>
</body>
</html>
