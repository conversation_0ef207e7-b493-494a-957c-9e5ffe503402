/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.openapi.diagnostic.Logger
 *  com.intellij.openapi.project.Project
 *  com.intellij.openapi.startup.ProjectActivity
 *  kotlin.Unit
 *  kotlin.coroutines.Continuation
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.wangzai;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.wangzai.SessionIdReplacer;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class AugmentStartupActivity
implements ProjectActivity {
    private static final Logger LOG = Logger.getInstance(AugmentStartupActivity.class);

    @Nullable
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        if (project == null) {
            AugmentStartupActivity.$$$reportNull$$$0(0);
        }
        if (continuation == null) {
            AugmentStartupActivity.$$$reportNull$$$0(1);
        }
        LOG.info("\u5f00\u59cb\u66ff\u6362\u76ee\u6807\u63d2\u4ef6\u7c7b...");
        try {
            SessionIdReplacer replacer = new SessionIdReplacer();
            if (replacer.replaceSessionIdClass()) {
                LOG.info("\u6210\u529f\u66ff\u6362SessionId\u7c7b");
            } else {
                LOG.warn("\u66ff\u6362SessionId\u7c7b\u5931\u8d25");
            }
        }
        catch (Exception e) {
            LOG.error("\u66ff\u6362\u8fc7\u7a0b\u4e2d\u53d1\u751f\u9519\u8bef", (Throwable)e);
        }
        return Unit.INSTANCE;
    }

    private static /* synthetic */ void $$$reportNull$$$0(int n) {
        Object[] objectArray;
        Object[] objectArray2 = new Object[3];
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[0] = "project";
                break;
            }
            case 1: {
                objectArray = objectArray2;
                objectArray2[0] = "continuation";
                break;
            }
        }
        objectArray[1] = "com/wangzai/AugmentStartupActivity";
        objectArray[2] = "execute";
        throw new IllegalArgumentException(String.format("Argument for @NotNull parameter '%s' of %s.%s must not be null", objectArray));
    }
}

