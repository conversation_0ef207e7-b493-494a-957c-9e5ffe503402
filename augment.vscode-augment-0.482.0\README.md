# Augment Code for Visual Studio Code

[Augment Code](https://augmentcode.com) is the AI-powered coding platform built for professional software engineers and large codebases. Powered by a cutting-edge context engine that understands your entire codebase, use Agent, Completions, Chat, and Next Edit to accelerate the way you code.

[![Augment Code](https://augment-assets.com/augment-hero-sm.png)](https://www.augmentcode.com)

---

**Install Augment and sign in for a free trial of Augment Professional and get unlimited Agent, Chat, Next Edit, Instructions and Completions.**

---

### Get up to speed

Find out how a system works, investigate a bug, or learn to use a new API. Whether you’ve been in a codebase from the first commit or are onboarding to a new team, Augment orients you in minutes even if you’re new to a project or working in an unfamiliar part of the codebase.

### Make updates with confidence

There are no simple changes in production-grade software, but Augment manages the challenges for you. Agent, Next Edit, and code completions automatically understand your code, APIs, schemas, and dependencies so you have everything you need at your fingertips.

## Powerful AI for the way you code

#### Agent powered engineering

Complete tasks, build features, and solve production problems with an agent that knows you and your codebase best. Expand Agent's powers when you use Augment's native integrations or access 100+ tools with MCP.

#### Intelligent Chat with deep integrations

Get instant answers, plan a project, and define changes through Chat. Then use _Smart Apply_ to intelligently update your code in one click.

#### Next Edit

Even simple changes ripple throughout a codebase. Next Edit gives turn-by-turn directions that makes edits across your code, tests, and docs.

#### Instructions using natural language

Instructions let you add or modify code with natural language prompts directly in your open editor–from simple edits to tendious refactors.

#### Code completions

Lightning fast, codebase aware suggestions as you type, tailored to your project's structure, dependencies, and style.

## Get coding with Augment

Get Augment Professional free for 14 days, including unlimited Chat, Next Edit, Completions, and Instructions.

-   Install the Augment extension
-   Click _Start using Augment_ to sign up for a 14-day free trial
-   Get coding with Chat, Next Edit, and Completions

Head over to our [documentation](https://docs.augmentcode.com/) to learn more. If you need any support, please reach out to us at [<EMAIL>](mailto:<EMAIL>). Want to use Augment across your organization? [Contact Sales](https://www.augmentcode.com/contact).
