<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Box 更新中...</title>
    <style>
        @font-face {
            font-family: 'ZQL';
            src: url('assets/fonts/zql.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'ZQL', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .update-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            width: 400px;
            max-width: 90vw;
        }

        .update-icon {
            font-size: 60px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .update-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .update-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .progress-container {
            background: #f0f0f0;
            border-radius: 10px;
            height: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .status-text {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
            min-height: 24px;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-icon {
            color: #4CAF50;
            font-size: 24px;
            margin-right: 10px;
        }

        .error-icon {
            color: #f44336;
            font-size: 24px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="update-container">
        <div class="update-icon">🔄</div>
        <div class="update-title">Magic Box 更新中</div>
        <div class="update-subtitle">请稍候，正在为您更新到最新版本...</div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="progress-text" id="progressText">0%</div>
        <div class="status-text" id="statusText">
            <span class="spinner"></span>准备更新...
        </div>
    </div>

    <script>
        let currentStep = 0;
        const steps = [
            { text: '准备更新...', progress: 10 },
            { text: '停止应用程序...', progress: 25 },
            { text: '备份当前版本...', progress: 40 },
            { text: '安装新版本...', progress: 70 },
            { text: '清理临时文件...', progress: 85 },
            { text: '启动新版本...', progress: 95 },
            { text: '更新完成！', progress: 100 }
        ];

        function updateProgress() {
            if (currentStep < steps.length) {
                const step = steps[currentStep];
                const progressBar = document.getElementById('progressBar');
                const progressText = document.getElementById('progressText');
                const statusText = document.getElementById('statusText');

                // 更新进度条
                progressBar.style.width = step.progress + '%';
                progressText.textContent = step.progress + '%';

                // 更新状态文本
                if (currentStep === steps.length - 1) {
                    // 最后一步 - 完成
                    statusText.innerHTML = '<span class="success-icon">✅</span>' + step.text;

                    // 15秒后自动关闭窗口（给新应用足够时间启动）
                    setTimeout(() => {
                        if (window.__TAURI__) {
                            window.__TAURI__.window.getCurrent().close();
                        } else {
                            window.close();
                        }
                    }, 15000);
                } else {
                    // 进行中的步骤
                    statusText.innerHTML = '<span class="spinner"></span>' + step.text;
                }

                currentStep++;
                
                // 如果不是最后一步，继续下一步
                if (currentStep < steps.length) {
                    setTimeout(updateProgress, 2000 + Math.random() * 1000); // 2-3秒随机间隔
                }
            }
        }

        // 检测新软件是否启动
        function checkForNewApp() {
            // 检测是否有新的Magic Box进程启动
            // 这里使用一个简单的方法：检测窗口焦点变化
            let checkCount = 0;
            const maxChecks = 30; // 最多检查30次（60秒）

            const checkInterval = setInterval(() => {
                checkCount++;

                // 如果检查次数超过最大值，自动关闭
                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    setTimeout(() => {
                        if (window.__TAURI__) {
                            window.__TAURI__.window.getCurrent().close();
                        } else {
                            window.close();
                        }
                    }, 2000);
                    return;
                }

                // 检测是否有其他Magic Box窗口
                if (document.hidden || !document.hasFocus()) {
                    // 窗口失去焦点，可能是新应用启动了
                    setTimeout(() => {
                        clearInterval(checkInterval);
                        if (window.__TAURI__) {
                            window.__TAURI__.window.getCurrent().close();
                        } else {
                            window.close();
                        }
                    }, 3000); // 3秒后关闭
                }
            }, 2000); // 每2秒检查一次
        }

        // 页面加载后开始更新进度
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateProgress, 1000); // 1秒后开始
            setTimeout(checkForNewApp, 10000); // 10秒后开始检测新应用
        });

        // 监听来自主进程的更新事件（如果有的话）
        if (window.__TAURI__) {
            window.__TAURI__.event.listen('update-progress', (event) => {
                const { step, progress, message } = event.payload;
                
                const progressBar = document.getElementById('progressBar');
                const progressText = document.getElementById('progressText');
                const statusText = document.getElementById('statusText');

                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '%';
                statusText.innerHTML = '<span class="spinner"></span>' + message;
            });

            window.__TAURI__.event.listen('update-complete', (event) => {
                const statusText = document.getElementById('statusText');
                statusText.innerHTML = '<span class="success-icon">✅</span>更新完成！';
                
                setTimeout(() => {
                    window.__TAURI__.window.getCurrent().close();
                }, 2000);
            });

            window.__TAURI__.event.listen('update-error', (event) => {
                const statusText = document.getElementById('statusText');
                statusText.innerHTML = '<span class="error-icon">❌</span>更新失败：' + event.payload.message;
            });
        }
    </script>
</body>
</html>
