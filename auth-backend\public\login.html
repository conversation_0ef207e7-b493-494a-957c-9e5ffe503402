<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>管理员登录 - Magic Box</title>
    <style>
        @font-face {
            font-family: 'ZQL';
            src: url('./fonts/zql.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'ZQL', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 50px;
            width: 420px;
            text-align: center;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-title {
            font-size: 32px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 40px;
        }

        .form-table {
            width: 100%;
            border-collapse: collapse;
        }

        .form-table td {
            padding: 12px 0;
            text-align: left;
        }

        .form-label {
            color: #333;
            font-weight: 600;
            font-size: 16px;
            padding-right: 20px;
            width: 80px;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            display: none;
        }

        .footer {
            margin-top: 30px;
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-title">🔐 管理员登录</div>
        <div class="login-subtitle">Magic Box 授权管理系统</div>

        <table class="form-table">
            <tr>
                <td class="form-label">用户名</td>
                <td><input type="text" id="username" class="form-input" placeholder="请输入用户名" autocomplete="username"></td>
            </tr>
            <tr>
                <td class="form-label">密码</td>
                <td><input type="password" id="password" class="form-input" placeholder="请输入密码" autocomplete="current-password"></td>
            </tr>
            <tr>
                <td colspan="2">
                    <button type="button" class="login-button" onclick="doLogin()">登录</button>
                </td>
            </tr>
        </table>

        <div id="message" class="message"></div>

        <div class="footer">
            &copy; 2024 Magic Box. All rights reserved.
        </div>
    </div>

    <script>
        function doLogin() {
            var username = document.getElementById('username').value;
            var password = document.getElementById('password').value;
            var button = document.querySelector('.login-button');

            if (!username || !password) {
                showMessage('请输入用户名和密码', 'error');
                return;
            }

            button.disabled = true;
            button.textContent = '登录中...';
            showMessage('正在验证身份...', 'info');

            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/admin/login');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.withCredentials = true;

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    button.disabled = false;
                    button.textContent = '登录';

                    if (xhr.status === 200) {
                        try {
                            var result = JSON.parse(xhr.responseText);
                            if (result.success) {
                                showMessage('登录成功！正在跳转...', 'success');
                                setTimeout(function() {
                                    window.location.href = '/admin';
                                }, 1000);
                            } else {
                                showMessage('登录失败: ' + result.message, 'error');
                            }
                        } catch (e) {
                            showMessage('响应解析错误', 'error');
                        }
                    } else {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                }
            };

            xhr.send(JSON.stringify({
                username: username,
                password: password
            }));
        }

        function showMessage(text, type) {
            var message = document.getElementById('message');
            message.textContent = text;
            message.style.display = 'block';

            if (type === 'error') {
                message.style.background = '#fee';
                message.style.color = '#c33';
                message.style.border = '1px solid #fcc';
            } else if (type === 'success') {
                message.style.background = '#efe';
                message.style.color = '#363';
                message.style.border = '1px solid #cfc';
            } else if (type === 'info') {
                message.style.background = '#eef';
                message.style.color = '#336';
                message.style.border = '1px solid #ccf';
            }
        }

        // 回车键登录
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                doLogin();
            }
        });

        // 检查是否已登录
        window.onload = function() {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/admin/check');
            xhr.withCredentials = true;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        var result = JSON.parse(xhr.responseText);
                        if (result.success && result.data.isLoggedIn) {
                            window.location.href = '/admin';
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }
            };
            xhr.send();
        };
    </script>
</body>
</html>
