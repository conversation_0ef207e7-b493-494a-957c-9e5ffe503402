import{S as R,i as V,s as W,J as X,e as x,q as Y,t as $,r as _,u as p,h as B,Y as y,a as h,j as A,R as D,ab as f,_ as w,V as j,W as m,X as g,$ as q,a0 as S,a1 as J,g as N,a8 as v,aa as E}from"./SpinnerAugment-Cx9dt_ox.js";import"./BaseButton-BqzdgpkK.js";function F(e){let n,a;const u=e[9].default,t=w(u,e,e[8],null);let r=[e[1]],c={};for(let i=0;i<r.length;i+=1)c=h(c,r[i]);return{c(){n=j("div"),t&&t.c(),m(n,c),g(n,"svelte-149ttoo",!0)},m(i,l){x(i,n,l),t&&t.m(n,null),a=!0},p(i,l){t&&t.p&&(!a||256&l)&&q(t,u,i,i[8],a?J(u,i[8],l,null):S(i[8]),null),m(n,c=N(r,[2&l&&i[1]])),g(n,"svelte-149ttoo",!0)},i(i){a||(p(t,i),a=!0)},o(i){$(t,i),a=!1},d(i){i&&B(n),t&&t.d(i)}}}function G(e){let n,a,u,t;const r=e[9].default,c=w(r,e,e[8],null);let i=[e[1],{role:"button"},{tabindex:"0"}],l={};for(let o=0;o<i.length;o+=1)l=h(l,i[o]);return{c(){n=j("div"),c&&c.c(),m(n,l),g(n,"svelte-149ttoo",!0)},m(o,d){x(o,n,d),c&&c.m(n,null),a=!0,u||(t=[v(n,"click",e[10]),v(n,"keyup",e[11]),v(n,"keydown",e[12]),v(n,"mousedown",e[13]),v(n,"mouseover",e[14]),v(n,"focus",e[15]),v(n,"mouseleave",e[16]),v(n,"blur",e[17]),v(n,"contextmenu",e[18])],u=!0)},p(o,d){c&&c.p&&(!a||256&d)&&q(c,r,o,o[8],a?J(r,o[8],d,null):S(o[8]),null),m(n,l=N(i,[2&d&&o[1],{role:"button"},{tabindex:"0"}])),g(n,"svelte-149ttoo",!0)},i(o){a||(p(c,o),a=!0)},o(o){$(c,o),a=!1},d(o){o&&B(n),c&&c.d(o),u=!1,E(t)}}}function H(e){let n,a,u,t;const r=[G,F],c=[];function i(l,o){return l[0]?0:1}return n=i(e),a=c[n]=r[n](e),{c(){a.c(),u=X()},m(l,o){c[n].m(l,o),x(l,u,o),t=!0},p(l,[o]){let d=n;n=i(l),n===d?c[n].p(l,o):(Y(),$(c[d],1,1,()=>{c[d]=null}),_(),a=c[n],a?a.p(l,o):(a=c[n]=r[n](l),a.c()),p(a,1),a.m(u.parentNode,u))},i(l){t||(p(a),t=!0)},o(l){$(a),t=!1},d(l){l&&B(u),c[n].d(l)}}}function I(e,n,a){let u,t,r;const c=["size","insetContent","variant","interactive","includeBackground"];let i=y(n,c),{$$slots:l={},$$scope:o}=n,{size:d=1}=n,{insetContent:k=!1}=n,{variant:b="surface"}=n,{interactive:C=!1}=n,{includeBackground:z=!0}=n;return e.$$set=s=>{n=h(h({},n),A(s)),a(19,i=y(n,c)),"size"in s&&a(2,d=s.size),"insetContent"in s&&a(3,k=s.insetContent),"variant"in s&&a(4,b=s.variant),"interactive"in s&&a(0,C=s.interactive),"includeBackground"in s&&a(5,z=s.includeBackground),"$$scope"in s&&a(8,o=s.$$scope)},e.$$.update=()=>{a(7,{class:u}=i,u),189&e.$$.dirty&&a(6,t=["c-card",`c-card--size-${d}`,`c-card--${b}`,k?"c-card--insetContent":"",C?"c-card--interactive":"",u,z?"c-card--with-background":""]),64&e.$$.dirty&&a(1,r={...D("accent"),class:t.join(" ")})},[C,r,d,k,b,z,t,u,o,l,function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)}]}class M extends R{constructor(n){super(),V(this,n,I,H,W,{size:2,insetContent:3,variant:4,interactive:0,includeBackground:5})}}export{M as C};
