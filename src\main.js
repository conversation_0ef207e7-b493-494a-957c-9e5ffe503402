// 🚀 VSCode + IDEA 清理工具 - 前端逻辑
const { invoke } = window.__TAURI__.core;

// 全局状态
let isRunning = false;
let logUpdateInterval = null;
let isAuthorized = false;
let deviceId = null;
let progressCountdownInterval = null;

// DOM 元素
let elements = {};

// 禁用开发者工具
function disableDevTools() {
    // 禁用F12键和其他开发者工具快捷键
    document.addEventListener('keydown', function(e) {
        // F12
        if (e.key === 'F12') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // Ctrl+Shift+I (开发者工具)
        if (e.ctrlKey && e.shiftKey && e.key === 'I') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // Ctrl+Shift+J (控制台)
        if (e.ctrlKey && e.shiftKey && e.key === 'J') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // Ctrl+U (查看源代码)
        if (e.ctrlKey && e.key === 'u') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // Ctrl+Shift+C (元素选择器)
        if (e.ctrlKey && e.shiftKey && e.key === 'C') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // 禁用右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // 禁用选择文本
    document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
    });

    // 禁用拖拽
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
    });

    // 检测开发者工具是否打开（通过控制台检测）
    let devtools = {
        open: false,
        orientation: null
    };

    const threshold = 160;

    setInterval(() => {
        if (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold) {
            if (!devtools.open) {
                devtools.open = true;
                console.clear();
                console.log('%c🚫 开发者工具已被禁用', 'color: red; font-size: 20px; font-weight: bold;');
            }
        } else {
            devtools.open = false;
        }
    }, 500);

    console.log('🔒 开发者工具限制已启用');
}

// ==================== 公告功能 ====================

// 公告自动刷新相关变量
let announcementRefreshInterval = null;

// 加载公告
async function loadAnnouncements() {
    try {
        const config = window.CONFIG;
        const response = await fetch(`${config.API_BASE_URL}/api/announcement/list`);
        const result = await response.json();

        if (result.success && result.data.length > 0) {
            displayAnnouncements(result.data);
        } else {
            hideAnnouncementsSection();
        }
    } catch (error) {
        console.error('加载公告失败:', error);
        hideAnnouncementsSection();
    }
}

// 启动公告自动刷新
function startAnnouncementAutoRefresh() {
    // 清除之前的定时器
    if (announcementRefreshInterval) {
        clearInterval(announcementRefreshInterval);
    }

    // 每30秒自动刷新一次
    announcementRefreshInterval = setInterval(() => {
        console.log('🔄 自动刷新公告...');
        loadAnnouncements();
    }, 30000); // 30秒

    console.log('✅ 公告自动刷新已启动（30秒间隔）');
}

// 停止公告自动刷新
function stopAnnouncementAutoRefresh() {
    if (announcementRefreshInterval) {
        clearInterval(announcementRefreshInterval);
        announcementRefreshInterval = null;
        console.log('⏹️ 公告自动刷新已停止');
    }
}

// 显示公告
function displayAnnouncements(announcements) {
    const section = document.getElementById('announcements-section');
    const container = document.getElementById('announcements-container');

    if (!section || !container) return;

    const html = announcements.map(announcement => {
        const icon = getAnnouncementIcon(announcement.type);

        return `
            <div class="announcement-item">
                <div class="announcement-icon ${announcement.type}">
                    ${icon}
                </div>
                <div class="announcement-content">
                    <span class="announcement-label">通知公告：</span>
                    <div class="announcement-text">${announcement.content}</div>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;
    section.classList.remove('hidden');
}

// 隐藏公告区域
function hideAnnouncementsSection() {
    const section = document.getElementById('announcements-section');
    if (section) {
        section.classList.add('hidden');
    }
}

// 获取公告图标（纯色圆圈）
function getAnnouncementIcon(type) {
    // 返回空字符串，只显示纯色圆圈背景
    return '';
}

// 格式化公告时间
function formatAnnouncementTime(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) {
            return '刚刚';
        } else if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        return '';
    }
}

// 初始化自定义标题栏
function initializeCustomTitlebar() {
    console.log('🔧 初始化自定义标题栏...');

    // 最小化按钮
    const minimizeBtn = document.getElementById('minimize-btn');
    if (minimizeBtn) {
        console.log('✅ 找到最小化按钮');
        minimizeBtn.addEventListener('click', async () => {
            console.log('🔽 点击最小化按钮');
            try {
                if (window.__TAURI__ && window.__TAURI__.window) {
                    const currentWindow = window.__TAURI__.window.getCurrentWindow();
                    await currentWindow.minimize();
                    console.log('✅ 窗口最小化成功');
                } else {
                    console.error('❌ Tauri API 不可用');
                }
            } catch (error) {
                console.error('❌ 最小化窗口失败:', error);
            }
        });
    } else {
        console.error('❌ 未找到最小化按钮');
    }

    // 关闭按钮
    const closeBtn = document.getElementById('close-btn');
    if (closeBtn) {
        console.log('✅ 找到关闭按钮');
        closeBtn.addEventListener('click', async () => {
            console.log('❌ 点击关闭按钮');
            try {
                if (window.__TAURI__ && window.__TAURI__.window) {
                    const currentWindow = window.__TAURI__.window.getCurrentWindow();
                    await currentWindow.close();
                    console.log('✅ 窗口关闭成功');
                } else {
                    console.error('❌ Tauri API 不可用');
                }
            } catch (error) {
                console.error('❌ 关闭窗口失败:', error);
            }
        });
    } else {
        console.error('❌ 未找到关闭按钮');
    }
}

// 初始化应用
window.addEventListener("DOMContentLoaded", async () => {
    console.log('🚀 Magic Box 应用启动...');

    // 禁用开发者工具
    disableDevTools();

    // 初始化自定义标题栏
    initializeCustomTitlebar();

    // 监听热更新事件
    if (window.__TAURI__) {
        try {
            await window.__TAURI__.event.listen('hot-update-applied', (event) => {
                console.log('🔥 收到热更新事件:', event.payload);
                showNotification('🔄 正在应用热更新...', 'info');

                // 可以在这里重新加载特定的前端资源
                setTimeout(() => {
                    showNotification('✨ 热更新已完成！新功能已生效', 'success');
                }, 1000);
            });
            console.log('✅ 热更新事件监听器已注册');
        } catch (error) {
            console.error('❌ 注册热更新事件监听器失败:', error);
        }

        // 监听版本更新事件
        try {
            await window.__TAURI__.event.listen('version-updated', (event) => {
                console.log('📦 收到版本更新事件:', event.payload);
                const newVersion = event.payload.version;

                // 更新标题栏版本号
                updateVersionDisplay(newVersion);

                showNotification(`🎉 版本已更新为 ${newVersion}`, 'success');
            });
            console.log('✅ 版本更新事件监听器已注册');
        } catch (error) {
            console.error('❌ 注册版本更新事件监听器失败:', error);
        }
    }

    // 启动时进行智能授权检查
    await checkAuthOnStartup();
});

// 应用启动时的智能授权检查
async function checkAuthOnStartup() {
    console.log('🔍 检查授权状态...');

    // 检查本地授权码是否存在
    const authCode = localStorage.getItem('magic_box_auth_code');

    if (!authCode) {
        console.log('❌ 本地未找到授权码，显示授权验证界面');
        showAuthModal();
        return;
    }

    console.log('✅ 本地找到授权码，进行后台验证...');

    // 显示验证中的状态
    showVerifyingStatus();

    try {
        // 后台验证授权码（检查模式，不消耗使用次数）
        const result = await verifyAuthCodeWithDetails(authCode, true);

        if (result.valid) {
            console.log('✅ 后台验证成功，直接进入主界面');
            isAuthorized = true;

            // 隐藏授权弹窗
            const authModal = document.getElementById('auth-modal');
            if (authModal) {
                authModal.style.display = 'none';
            }

            initializeMainApp();
        } else {
            console.log('❌ 后台验证失败，需要重新输入授权码');
            console.log('❌ 失败原因:', result.reason);

            // 清除无效的授权码
            localStorage.removeItem('magic_box_auth_code');

            // 显示授权验证界面，并提示错误信息
            showAuthModal(result.reason);
        }
    } catch (error) {
        console.error('❌ 后台验证出错:', error);

        // 网络错误时，给用户选择：重试或重新输入
        showNetworkErrorDialog();
    }
}

// 初始化主应用界面
function initializeMainApp() {
    console.log('🎉 初始化主应用界面...');

    // 设置动态窗口标题
    setDynamicTitle();

    // 加载公告并启动自动刷新
    loadAnnouncements();
    startAnnouncementAutoRefresh();

    initializeElements();
    setupEventListeners();
    initializeModeSwitch(); // 初始化模式切换
    initializePluginTutorial(); // 初始化插件教程弹窗
    initializeUniversalToolbox(); // 初始化万能工具箱弹窗
    initializeCursorUniversalToolbox(); // 初始化Cursor万能工具箱弹窗
    initializeAugmentAccountManager(); // 初始化Augment账号管理

    loadInitialData();
    startLogUpdates();

    // 启动定期授权检查（每60秒检查一次）
    startPeriodicAuthCheck();

    // 启动时自动检查更新（延迟3秒，避免影响启动速度）
    setTimeout(() => {
        autoCheckForUpdates();
    }, 3000);

    // 启动定期更新检查（每6小时检查一次，减少频率）
    startPeriodicUpdateCheck();

    console.log('✅ 应用初始化完成');
}

// 启动定期更新检查
function startPeriodicUpdateCheck() {
    // 每6小时检查一次更新，减少频率
    setInterval(() => {
        console.log('🔄 定期检查更新...');
        autoCheckForUpdates();
    }, 6 * 60 * 60 * 1000); // 6小时 = 6 * 60 * 60 * 1000 毫秒
}

// 显示验证中状态
function showVerifyingStatus() {
    const authModal = document.getElementById('auth-modal');
    if (authModal) {
        // 修改标题和描述
        const header = authModal.querySelector('.auth-header h2');
        const description = authModal.querySelector('.auth-header p');
        if (header) header.textContent = '🔐 正在验证授权码...';
        if (description) description.textContent = '请稍候，正在验证您的授权...';

        // 隐藏表单，显示加载动画
        const form = document.getElementById('auth-form');
        const errorDiv = document.getElementById('auth-error');

        if (form) {
            form.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 15px; color: #666;">验证中...</p>
                </div>
            `;
        }

        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }

        // 显示弹窗
        authModal.style.display = 'flex';
    }
}

// 显示网络错误对话框
function showNetworkErrorDialog() {
    const authModal = document.getElementById('auth-modal');
    if (authModal) {
        // 修改标题和描述
        const header = authModal.querySelector('.auth-header h2');
        const description = authModal.querySelector('.auth-header p');
        if (header) header.textContent = '🔐 网络连接错误';
        if (description) description.textContent = '无法连接到授权服务器';

        // 显示错误信息和重试按钮
        const form = document.getElementById('auth-form');
        const errorDiv = document.getElementById('auth-error');

        if (form) {
            form.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <p style="color: #dc3545; margin-bottom: 20px;">
                        ❌ 请检查网络连接或稍后重试
                    </p>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="retryAuth()" class="btn btn-primary" style="white-space: nowrap; min-width: 120px;">
                            🔄 重试验证
                        </button>
                        <button onclick="showAuthModal()" class="btn btn-secondary" style="white-space: nowrap; min-width: 140px;">
                            🔑 重新输入授权码
                        </button>
                    </div>
                </div>
            `;
        }

        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }

        // 显示弹窗
        authModal.style.display = 'flex';
    }
}

// 重试授权验证
async function retryAuth() {
    await checkAuthOnStartup();
}

// 设置动态窗口标题
async function setDynamicTitle() {
    try {
        // 从Tauri获取应用版本号
        const version = await invoke('get_app_version');
        const title = `Magic Box v${version}`;

        // 设置窗口标题
        await invoke('set_window_title', { title });

        // 设置header中的版本号显示
        const headerVersion = document.getElementById('header-version');
        if (headerVersion) {
            headerVersion.textContent = `v${version}`;
        }

        console.log(`✅ 窗口标题已设置为: ${title}`);
    } catch (error) {
        console.error('❌ 设置窗口标题失败:', error);
        // 如果失败，使用默认标题
        try {
            await invoke('set_window_title', { title: 'Magic Box' });
            const headerVersion = document.getElementById('header-version');
            if (headerVersion) {
                headerVersion.textContent = 'v1.0.0'; // 默认版本，会被动态更新覆盖
            }
        } catch (fallbackError) {
            console.error('❌ 设置默认标题也失败:', fallbackError);
        }
    }
}

// 初始化DOM元素引用
function initializeElements() {
    elements = {
        // 清理按钮
        vscodeCleanBtn: document.getElementById('vscodeCleanBtn'),
        checkUpdateBtn: document.getElementById('checkUpdateBtn'),

        // 教程标签页
        vscodeTabBtn: document.getElementById('vscodeTabBtn'),
        ideaTabBtn: document.getElementById('ideaTabBtn'),
        augmentTabBtn: document.getElementById('augmentTabBtn'),
        vscodeTab: document.getElementById('vscodeTab'),
        ideaTab: document.getElementById('ideaTab'),
        augmentTab: document.getElementById('augmentTab'),
        tabIndicator: document.querySelector('.tab-indicator'),



        // 状态和日志
        statusIndicator: document.getElementById('statusIndicator'),
        statusText: document.getElementById('statusText'),
        logContainer: document.getElementById('logContainer'),
        clearLogBtn: document.getElementById('clearLogBtn')
    };
}

// 设置事件监听器
function setupEventListeners() {
    // VSCode清理
    elements.vscodeCleanBtn.addEventListener('click', cleanVSCode);

    // IDEA清理
    if (document.getElementById('ideaMemoryCleanBtn')) {
        document.getElementById('ideaMemoryCleanBtn').addEventListener('click', cleanIDEAMemory);
    }



    // SessionId相关按钮
    if (document.getElementById('refreshSessionIdBtn')) {
        document.getElementById('refreshSessionIdBtn').addEventListener('click', refreshSessionId);
    }
    if (document.getElementById('refreshVscodeSessionIdBtn')) {
        document.getElementById('refreshVscodeSessionIdBtn').addEventListener('click', refreshVscodeSessionId);
    }

    // 检查更新
    elements.checkUpdateBtn.addEventListener('click', checkForUpdates);

    // 教程标签页切换
    elements.vscodeTabBtn.addEventListener('click', () => switchTab('vscode'));
    elements.ideaTabBtn.addEventListener('click', () => switchTab('idea'));
    elements.augmentTabBtn.addEventListener('click', () => switchTab('augment'));

    // 清空日志
    elements.clearLogBtn.addEventListener('click', clearLogs);
}

// 加载初始数据
async function loadInitialData() {
    await updateStatus();
    await updateLogs();
}

// 开始日志更新
function startLogUpdates() {
    // 每秒更新一次日志和状态
    logUpdateInterval = setInterval(async () => {
        await updateLogs();
        await updateStatus();
    }, 1000);
}

// VSCode清理
async function cleanVSCode() {
    try {
        // 在开始操作前检查授权状态
        if (!isAuthorized) {
            showNotification('请先进行授权验证', 'warning');
            showAuthModal();
            return;
        }

        // 检查本地授权码是否存在
        const authCode = localStorage.getItem('magic_box_auth_code');
        if (!authCode) {
            handleAuthExpired('本地授权码丢失');
            return;
        }

        if (isRunning) {
            showNotification('清理正在进行中，请稍候...', 'warning');
            return;
        }

        // 🚨 显示醒目的进度提示（VSCode清理预计30秒）
        showProgressAlert('正在执行VSCode清理，请耐心等待日志运行结束...', 'info', 30);

        // 禁用按钮防止重复点击
        const vscodeBtn = document.getElementById('vscodeCleanBtn');
        const originalText = vscodeBtn.innerHTML;
        vscodeBtn.disabled = true;
        vscodeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清理中...';

        const result = await invoke('clean_vscode');

        // 强制刷新日志显示
        await forceUpdateLogs();

        // 🚨 隐藏进度提示，显示完成消息
        hideProgressAlert();
        showNotification('✅ VSCode清理已完成！', 'success');

        // 恢复按钮状态
        vscodeBtn.disabled = false;
        vscodeBtn.innerHTML = originalText;

    } catch (error) {
        console.error('VSCode清理失败:', error);

        // 隐藏进度提示
        hideProgressAlert();
        showNotification(`VSCode清理失败: ${error}`, 'error');

        // 恢复按钮状态
        const vscodeBtn = document.getElementById('vscodeCleanBtn');
        vscodeBtn.disabled = false;
        vscodeBtn.innerHTML = '<img src="assets/Vscode.png" alt="VSCode" class="btn-icon">VSCode 注入';
    }
}



// IDEA内存级清理（推荐）
async function cleanIDEAMemory() {
    try {
        // 在开始操作前检查授权状态
        if (!isAuthorized) {
            showNotification('请先进行授权验证', 'warning');
            showAuthModal();
            return;
        }

        // 检查本地授权码是否存在
        const authCode = localStorage.getItem('magic_box_auth_code');
        if (!authCode) {
            handleAuthExpired('本地授权码丢失');
            return;
        }

        if (isRunning) {
            showNotification('清理正在进行中，请稍候...', 'warning');
            return;
        }

        // 🚨 显示醒目的进度提示（IDEA清理预计30秒）
        showProgressAlert('正在执行IDEA清理，请保持IDEA运行并耐心等待日志结束...', 'info', 30);

        // 禁用按钮防止重复点击
        const ideaBtn = document.getElementById('ideaMemoryCleanBtn');
        const originalText = ideaBtn.innerHTML;
        ideaBtn.disabled = true;
        ideaBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 劫持中...';

        const result = await invoke('clean_idea_memory');

        // 强制刷新日志显示
        await forceUpdateLogs();

        // 🚨 隐藏进度提示，显示完成消息
        hideProgressAlert();
        showNotification('✅ IDEA清理已完成！', 'success');

        // 恢复按钮状态
        ideaBtn.disabled = false;
        ideaBtn.innerHTML = originalText;

    } catch (error) {
        console.error('IDEA内存级清理失败:', error);

        // 隐藏进度提示
        hideProgressAlert();
        showNotification(`IDEA内存级清理失败: ${error}`, 'error');

        // 恢复按钮状态
        const ideaBtn = document.getElementById('ideaMemoryCleanBtn');
        ideaBtn.disabled = false;
        ideaBtn.innerHTML = '<img src="assets/idea.png" alt="IDEA" class="btn-icon">IDEA 注入';
    }
}





// 清空日志
async function clearLogs() {
    try {
        // 根据当前模式清空对应的日志
        const currentMode = getCurrentMode();
        const result = currentMode === 'cursor'
            ? await invoke('clear_cursor_logs')
            : await invoke('clear_augment_logs');

        await updateLogs();
        showNotification(result, 'success');

    } catch (error) {
        console.error('清空日志失败:', error);
        showNotification(`清空日志失败: ${error}`, 'error');
    }
}

// 切换教程标签页
function switchTab(tabName) {
    // 更新按钮状态
    elements.vscodeTabBtn.classList.remove('active');
    elements.ideaTabBtn.classList.remove('active');
    elements.augmentTabBtn.classList.remove('active');

    // 更新内容显示
    elements.vscodeTab.classList.remove('active');
    elements.ideaTab.classList.remove('active');
    elements.augmentTab.classList.remove('active');

    // 计算滑动指示器位置
    let translateX = 0;
    if (tabName === 'vscode') {
        elements.vscodeTabBtn.classList.add('active');
        elements.vscodeTab.classList.add('active');
        translateX = 0;
    } else if (tabName === 'idea') {
        elements.ideaTabBtn.classList.add('active');
        elements.ideaTab.classList.add('active');
        translateX = 87; // 85px width + 2px gap
    } else if (tabName === 'augment') {
        elements.augmentTabBtn.classList.add('active');
        elements.augmentTab.classList.add('active');
        translateX = 174; // 2 * (85px width + 2px gap)
    }

    // 更新滑动指示器位置
    if (elements.tabIndicator) {
        elements.tabIndicator.style.transform = `translateX(${translateX}px)`;
    }
}

// 复制Augment配置（全局函数）
window.copyAugmentConfig = function() {
    const configText = `Communication Language: Simplified Chinese
Basic Requirements: Master the augmentContextEngine
Used Model: Claude Sonnet 4
Please keep in mind that you are the Claude Sonnet 4 model;
The user's question is a complex one. Please take it seriously and use ACE (Continue after collecting sufficient information through augmentContextEngine);`;

    navigator.clipboard.writeText(configText).then(() => {
        showNotification('✅ 配置已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showNotification('❌ 复制失败，请手动复制', 'error');
    });
};

// 自动检查更新（静默检查，不影响UI）
async function autoCheckForUpdates() {
    try {
        console.log('🔍 自动检查更新...');

        // 使用专业更新检查
        const result = await invoke('check_for_updates_custom');

        if (result === '当前已是最新版本') {
            console.log('✅ 当前已是最新版本');
            return;
        }

        // 解析更新信息
        const updateInfo = JSON.parse(result);

        if (updateInfo.hasUpdate) {
            console.log('✅ 发现新版本，显示更新提示');

            // 有新版本，显示专业更新对话框
            showUpdateDialog(updateInfo);
        } else {
            console.log('✅ 当前已是最新版本');
        }

    } catch (error) {
        console.error('自动检查更新失败:', error);
        // 自动检查失败时不显示错误提示，避免打扰用户
    }
}

// 专业更新功能（类似QQ、腾讯视频）
async function checkForUpdates() {
    try {
        // 更新按钮状态
        elements.checkUpdateBtn.disabled = true;
        elements.checkUpdateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';

        // 使用专业更新检查
        const result = await invoke('check_for_updates_custom');

        if (result === '当前已是最新版本') {
            showNotification('当前已是最新版本', 'success');
            return;
        }

        // 解析更新信息
        const updateInfo = JSON.parse(result);

        if (updateInfo.hasUpdate) {
            // 显示专业更新对话框
            showUpdateDialog(updateInfo);
        } else {
            showNotification('当前已是最新版本', 'success');
        }

    } catch (error) {
        console.error('检查更新失败:', error);
        showNotification(`检查更新失败: ${error}`, 'error');
    } finally {
        // 恢复按钮状态
        elements.checkUpdateBtn.disabled = false;
        elements.checkUpdateBtn.innerHTML = '<i class="fas fa-download"></i> 检查更新';
    }
}

// 显示专业更新对话框
function showUpdateDialog(updateInfo) {
    const dialog = document.createElement('div');
    dialog.className = 'update-dialog-overlay';
    dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 3000;
        animation: fadeIn 0.3s ease-out;
    `;

    dialog.innerHTML = `
        <div class="update-dialog-content" style="
            background: linear-gradient(135deg, var(--bg-card), rgba(42, 42, 42, 0.95));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 20px;
            padding: 40px;
            max-width: 480px;
            width: 90%;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            text-align: center;
            backdrop-filter: blur(20px);
            animation: slideIn 0.4s ease-out;
        ">
            <div class="update-icon" style="
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 24px auto;
                box-shadow: 0 8px 24px rgba(76, 175, 80, 0.4);
            ">
                <i class="fas fa-download" style="color: white; font-size: 32px;"></i>
            </div>

            <h3 style="
                margin: 0 0 8px 0;
                color: var(--text-primary);
                font-size: 24px;
                font-weight: 700;
            ">🎉 发现新版本</h3>

            <div class="version-badge" style="
                display: inline-block;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 6px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 24px;
            ">v${updateInfo.version}</div>

            <div class="update-notes" style="
                background: rgba(76, 175, 80, 0.1);
                border: 1px solid rgba(76, 175, 80, 0.2);
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                text-align: left;
            ">
                <h4 style="margin: 0 0 12px 0; color: var(--text-primary); font-size: 16px;">更新内容：</h4>
                <p style="margin: 0; color: var(--text-secondary); line-height: 1.6;">${updateInfo.notes || '性能优化和bug修复'}</p>
            </div>

            <div class="update-date" style="
                color: var(--text-muted);
                font-size: 13px;
                margin-bottom: 32px;
            ">发布时间：${updateInfo.date || '刚刚'}</div>

            <div class="update-actions" style="
                display: flex;
                gap: 16px;
                justify-content: center;
            ">
                <button class="btn-cancel" id="update-cancel-btn" style="
                    padding: 14px 28px;
                    border: 2px solid rgba(255, 255, 255, 0.2);
                    background: rgba(255, 255, 255, 0.05);
                    color: var(--text-secondary);
                    border-radius: 12px;
                    cursor: pointer;
                    font-size: 15px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    min-width: 110px;
                ">稍后更新</button>

                <button class="btn-update" id="update-confirm-btn" style="
                    padding: 14px 28px;
                    border: none;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    color: white;
                    border-radius: 12px;
                    cursor: pointer;
                    font-size: 15px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    min-width: 110px;
                    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
                ">立即更新</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);

    // 添加事件监听器
    const cancelBtn = document.getElementById('update-cancel-btn');
    const confirmBtn = document.getElementById('update-confirm-btn');

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeUpdateDialog();
        });
    }

    if (confirmBtn) {
        confirmBtn.addEventListener('click', () => {
            startProfessionalUpdate();
        });
    }
}

// 开始专业更新
async function startProfessionalUpdate() {
    try {
        // 关闭更新对话框
        closeUpdateDialog();

        // 显示更新进度窗口
        showUpdateProgress();

        // 监听更新进度事件
        await window.__TAURI__.event.listen('update-progress', (event) => {
            updateProgressDisplay(event.payload);
        });

        // 调用专业更新函数
        const result = await invoke('professional_update');
        showNotification(result, 'success');

    } catch (error) {
        console.error('更新失败:', error);
        showNotification(`更新失败: ${error}`, 'error');
        closeUpdateProgress();
    }
}

// 显示更新进度窗口
function showUpdateProgress() {
    const progressDialog = document.createElement('div');
    progressDialog.id = 'update-progress-dialog';
    progressDialog.className = 'update-progress-overlay';
    progressDialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(15px);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 4000;
        animation: fadeIn 0.3s ease-out;
    `;

    progressDialog.innerHTML = `
        <div class="progress-content" style="
            background: linear-gradient(135deg, var(--bg-card), rgba(42, 42, 42, 0.95));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 20px;
            padding: 40px;
            max-width: 420px;
            width: 90%;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
            text-align: center;
            backdrop-filter: blur(20px);
        ">
            <div class="progress-icon" style="
                width: 64px;
                height: 64px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 24px auto;
                animation: pulse 2s ease-in-out infinite;
            ">
                <i class="fas fa-download" style="color: white; font-size: 24px;"></i>
            </div>

            <h3 style="
                margin: 0 0 24px 0;
                color: var(--text-primary);
                font-size: 20px;
                font-weight: 700;
            ">🔄 正在更新 Magic Box</h3>

            <div class="progress-bar-container" style="
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                height: 8px;
                margin: 20px 0;
                overflow: hidden;
                position: relative;
            ">
                <div class="progress-bar" id="update-progress-bar" style="
                    background: linear-gradient(90deg, #4CAF50, #45a049);
                    height: 100%;
                    width: 0%;
                    border-radius: 10px;
                    transition: width 0.3s ease;
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                        animation: shimmer 2s ease-in-out infinite;
                    "></div>
                </div>
            </div>

            <div class="progress-text" id="update-progress-text" style="
                color: var(--text-primary);
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 8px;
            ">准备下载...</div>

            <div class="progress-details" id="update-progress-details" style="
                color: var(--text-muted);
                font-size: 13px;
            "></div>
        </div>
    `;

    document.body.appendChild(progressDialog);
}

// 更新进度显示
function updateProgressDisplay(progressData) {
    const progressBar = document.getElementById('update-progress-bar');
    const progressText = document.getElementById('update-progress-text');
    const progressDetails = document.getElementById('update-progress-details');

    if (progressBar && progressText && progressDetails) {
        progressBar.style.width = `${progressData.progress}%`;

        if (progressData.status === 'downloading') {
            progressText.textContent = `下载中... ${progressData.progress}%`;
            if (progressData.total) {
                const downloaded = (progressData.downloaded / 1024 / 1024).toFixed(1);
                const total = (progressData.total / 1024 / 1024).toFixed(1);
                progressDetails.textContent = `${downloaded} MB / ${total} MB`;
            }
        } else if (progressData.status === 'installing') {
            progressText.textContent = '正在安装更新...';
            progressDetails.textContent = '即将完成，请稍候';
        }
    }
}

// 关闭更新对话框
function closeUpdateDialog() {
    const dialog = document.querySelector('.update-dialog-overlay');
    if (dialog) {
        dialog.remove();
    }
}

// 将函数添加到全局作用域
window.closeUpdateDialog = closeUpdateDialog;
window.startProfessionalUpdate = startProfessionalUpdate;

// 关闭更新进度窗口
function closeUpdateProgress() {
    const dialog = document.getElementById('update-progress-dialog');
    if (dialog) {
        dialog.remove();
    }
}





// 更新状态显示
async function updateStatus() {
    try {
        // 简化状态显示，只显示是否正在运行
        if (isRunning) {
            elements.statusIndicator.className = 'status-indicator status-running';
            elements.statusText.textContent = '清理中...';
        } else {
            elements.statusIndicator.className = 'status-indicator status-ready';
            elements.statusText.textContent = '就绪';
        }

    } catch (error) {
        console.error('获取状态失败:', error);
        elements.statusIndicator.className = 'status-indicator status-error';
        elements.statusText.textContent = '状态获取失败';
    }
}

// 获取当前模式
function getCurrentMode() {
    const augmentBtn = document.getElementById('augmentModeBtn');
    const cursorBtn = document.getElementById('cursorModeBtn');

    if (augmentBtn && augmentBtn.classList.contains('active')) {
        return 'augment';
    } else if (cursorBtn && cursorBtn.classList.contains('active')) {
        return 'cursor';
    }

    // 默认返回augment模式
    return 'augment';
}

// 更新日志显示
async function updateLogs() {
    try {
        // 根据当前模式获取对应的日志
        const currentMode = getCurrentMode();
        const logs = currentMode === 'cursor'
            ? await invoke('get_cursor_logs')
            : await invoke('get_augment_logs');

        // 只有当日志有变化时才更新DOM
        const currentLogCount = elements.logContainer.children.length;
        if (logs.length === currentLogCount) return;

        elements.logContainer.innerHTML = '';

        if (logs.length === 0) {
            elements.logContainer.innerHTML = '<div style="text-align: center; color: var(--text-muted); font-style: italic; padding: 20px;">暂无日志</div>';
        } else {
            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level}`;
                logEntry.innerHTML = `
                    <span class="log-message">${escapeHtml(log.message)}</span>
                `;
                elements.logContainer.appendChild(logEntry);
            });

            // 自动滚动到底部
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        }

    } catch (error) {
        console.error('获取日志失败:', error);
        elements.logContainer.innerHTML = '<div style="color: var(--color-danger); padding: 20px;">获取日志失败</div>';
    }
}

// 强制更新日志显示（不检查数量变化）
async function forceUpdateLogs() {
    try {
        // 根据当前模式获取对应的日志
        const currentMode = getCurrentMode();
        const logs = currentMode === 'cursor'
            ? await invoke('get_cursor_logs')
            : await invoke('get_augment_logs');

        elements.logContainer.innerHTML = '';

        if (logs.length === 0) {
            elements.logContainer.innerHTML = '<div style="text-align: center; color: var(--text-muted); font-style: italic; padding: 20px;">暂无日志</div>';
        } else {
            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level}`;
                logEntry.innerHTML = `
                    <span class="log-message">${escapeHtml(log.message)}</span>
                `;
                elements.logContainer.appendChild(logEntry);
            });

            // 自动滚动到底部
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        }

        // 强制触发重新渲染
        elements.logContainer.style.display = 'none';
        elements.logContainer.offsetHeight; // 触发重排
        elements.logContainer.style.display = '';

    } catch (error) {
        console.error('强制获取日志失败:', error);
        elements.logContainer.innerHTML = '<div style="color: var(--color-danger); padding: 20px;">获取日志失败</div>';
    }
}



// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 45px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 15000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;

    // 设置背景色
    switch (type) {
        case 'success':
            notification.style.background = 'var(--color-primary)';
            break;
        case 'error':
            notification.style.background = 'var(--color-danger)';
            break;
        case 'warning':
            notification.style.background = 'var(--color-warning)';
            break;
        default:
            notification.style.background = 'var(--color-info)';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 🚨 新增：显示醒目的进度提示
function showProgressAlert(message, type = 'info', estimatedSeconds = 35) {
    // 移除已存在的进度提示
    hideProgressAlert();

    const progressAlert = document.createElement('div');
    progressAlert.id = 'progressAlert';
    progressAlert.className = `progress-alert ${type}`;
    progressAlert.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        animation: fadeIn 0.3s ease-out;
    `;

    progressAlert.innerHTML = `
        <div class="progress-alert-content" style="
            background: linear-gradient(135deg, var(--bg-card), rgba(42, 42, 42, 0.95));
            border: 2px solid var(--color-primary);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            animation: slideIn 0.4s ease-out;
        ">
            <div class="progress-spinner" style="
                margin-bottom: 24px;
            ">
                <i class="fas fa-spinner fa-spin" style="
                    font-size: 48px;
                    color: var(--color-primary);
                    animation: spin 1s linear infinite;
                "></i>
            </div>
            <div class="progress-message">
                <h4 style="
                    margin: 0 0 16px 0;
                    color: var(--text-primary);
                    font-size: 24px;
                    font-weight: 700;
                ">正在处理中...</h4>
                <p style="
                    margin: 0 0 16px 0;
                    color: var(--text-secondary);
                    font-size: 16px;
                    line-height: 1.6;
                ">${message}</p>
                <div class="progress-countdown" style="
                    background: rgba(76, 175, 80, 0.1);
                    border: 1px solid rgba(76, 175, 80, 0.3);
                    border-radius: 12px;
                    padding: 12px 16px;
                    margin-bottom: 16px;
                    color: var(--color-primary);
                    font-size: 16px;
                    font-weight: 600;
                ">
                    <i class="fas fa-clock" style="margin-right: 8px;"></i>
                    预计还需 <span id="countdownTimer">${estimatedSeconds}</span> 秒
                </div>
                <div class="progress-tips" style="
                    background: rgba(255, 193, 7, 0.1);
                    border: 1px solid rgba(255, 193, 7, 0.3);
                    border-radius: 12px;
                    padding: 16px;
                    color: #ffc107;
                    font-size: 14px;
                    font-weight: 600;
                ">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                    请不要关闭程序，等待日志运行结束
                </div>
            </div>
        </div>
    `;

    // 添加CSS动画
    const style = document.createElement('style');
    style.id = 'progressAlertStyle';
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideIn {
            from { transform: scale(0.8) translateY(30px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(progressAlert);

    // 触发动画
    setTimeout(() => progressAlert.classList.add('show'), 100);

    // 启动倒计时
    startProgressCountdown(estimatedSeconds);
}

// 🚨 新增：启动进度倒计时
function startProgressCountdown(seconds) {
    // 清除已存在的倒计时
    if (progressCountdownInterval) {
        clearInterval(progressCountdownInterval);
    }

    let remainingSeconds = seconds;
    const timerElement = document.getElementById('countdownTimer');

    if (!timerElement) return;

    progressCountdownInterval = setInterval(() => {
        remainingSeconds--;

        if (remainingSeconds <= 0) {
            // 倒计时结束，显示"即将完成"
            timerElement.textContent = '即将完成';
            timerElement.parentElement.innerHTML = `
                <i class="fas fa-check-circle" style="margin-right: 8px; color: var(--color-primary);"></i>
                <span style="color: var(--color-primary);">即将完成，请稍候...</span>
            `;
            clearInterval(progressCountdownInterval);
            progressCountdownInterval = null;
        } else {
            // 更新倒计时显示
            timerElement.textContent = remainingSeconds;

            // 当剩余时间少于10秒时，改变颜色提醒
            if (remainingSeconds <= 10) {
                timerElement.style.color = '#ffc107';
                timerElement.parentElement.style.borderColor = 'rgba(255, 193, 7, 0.5)';
                timerElement.parentElement.style.background = 'rgba(255, 193, 7, 0.1)';
            }
        }
    }, 1000);
}

// 🚨 新增：隐藏进度提示
function hideProgressAlert() {
    // 清除倒计时
    if (progressCountdownInterval) {
        clearInterval(progressCountdownInterval);
        progressCountdownInterval = null;
    }

    const progressAlert = document.getElementById('progressAlert');
    const style = document.getElementById('progressAlertStyle');

    if (progressAlert) {
        progressAlert.style.animation = 'fadeOut 0.3s ease-out forwards';
        setTimeout(() => {
            if (progressAlert.parentNode) {
                progressAlert.parentNode.removeChild(progressAlert);
            }
        }, 300);
    }

    if (style) {
        document.head.removeChild(style);
    }
}

// 显示确认对话框
function showConfirm(title, message) {
    return new Promise((resolve) => {
        // 创建确认对话框元素
        const confirmDialog = document.createElement('div');
        confirmDialog.className = 'confirm-dialog';
        confirmDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            animation: fadeIn 0.3s ease-out;
        `;

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: scale(0.9) translateY(20px); opacity: 0; }
                to { transform: scale(1) translateY(0); opacity: 1; }
            }
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.8; }
                50% { transform: scale(1.1); opacity: 1; }
            }
            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `;
        document.head.appendChild(style);

        const dialogContent = document.createElement('div');
        dialogContent.className = 'confirm-dialog-content';
        dialogContent.style.cssText = `
            background: linear-gradient(135deg, var(--bg-card), rgba(42, 42, 42, 0.95));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 20px;
            padding: 36px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
            text-align: center;
            backdrop-filter: blur(20px);
            transform: scale(0.9);
            animation: slideIn 0.4s ease-out forwards;
            position: relative;
            overflow: hidden;
        `;

        // 添加装饰性背景
        const bgDecor = document.createElement('div');
        bgDecor.style.cssText = `
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(76, 175, 80, 0.05) 0%, transparent 70%);
            pointer-events: none;
            z-index: 0;
        `;
        dialogContent.appendChild(bgDecor);

        dialogContent.innerHTML = `
            <div style="margin-bottom: 28px;">
                <div style="
                    width: 72px;
                    height: 72px;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 20px auto;
                    box-shadow: 0 8px 24px rgba(76, 175, 80, 0.4);
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
                        animation: pulse 2s ease-in-out infinite;
                    "></div>
                    <i class="fas fa-download" style="color: white; font-size: 28px; z-index: 1; position: relative;"></i>
                </div>
                <h3 style="
                    margin: 0 0 16px 0;
                    color: var(--text-primary);
                    font-size: 22px;
                    font-weight: 700;
                    font-family: var(--font-family);
                    letter-spacing: 0.5px;
                ">${title}</h3>
                <div style="
                    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
                    border: 1px solid rgba(76, 175, 80, 0.2);
                    border-radius: 12px;
                    padding: 20px;
                    margin: 16px 0;
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 2px;
                        background: linear-gradient(90deg, #4CAF50, #45a049);
                    "></div>
                    <p style="
                        margin: 0;
                        color: var(--text-primary);
                        line-height: 1.7;
                        font-size: 15px;
                        font-family: var(--font-family);
                        white-space: pre-line;
                    ">${message}</p>
                </div>
            </div>
            <div style="display: flex; gap: 16px; justify-content: center; margin-top: 8px;">
                <button id="confirm-cancel" class="btn btn-cancel" style="
                    padding: 14px 28px;
                    border: 2px solid rgba(255, 255, 255, 0.2);
                    background: rgba(255, 255, 255, 0.05);
                    color: var(--text-secondary);
                    border-radius: 12px;
                    cursor: pointer;
                    font-size: 15px;
                    font-weight: 600;
                    font-family: var(--font-family);
                    transition: all 0.3s ease;
                    min-width: 110px;
                    position: relative;
                    overflow: hidden;
                ">
                    <span style="position: relative; z-index: 1;">取消</span>
                </button>
                <button id="confirm-ok" class="btn btn-primary" style="
                    padding: 14px 28px;
                    border: none;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    color: white;
                    border-radius: 12px;
                    cursor: pointer;
                    font-size: 15px;
                    font-weight: 600;
                    font-family: var(--font-family);
                    transition: all 0.3s ease;
                    min-width: 110px;
                    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
                    position: relative;
                    overflow: hidden;
                ">
                    <span style="position: relative; z-index: 1;">确定</span>
                    <div style="
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                        transition: left 0.6s ease;
                    "></div>
                </button>
            </div>
        `;

        confirmDialog.appendChild(dialogContent);
        document.body.appendChild(confirmDialog);

        // 绑定事件
        const cancelBtn = dialogContent.querySelector('#confirm-cancel');
        const okBtn = dialogContent.querySelector('#confirm-ok');

        // 添加悬停效果
        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.background = 'rgba(255, 255, 255, 0.1)';
            cancelBtn.style.borderColor = 'rgba(255, 255, 255, 0.4)';
            cancelBtn.style.color = 'var(--text-primary)';
            cancelBtn.style.transform = 'translateY(-2px) scale(1.02)';
            cancelBtn.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.background = 'rgba(255, 255, 255, 0.05)';
            cancelBtn.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            cancelBtn.style.color = 'var(--text-secondary)';
            cancelBtn.style.transform = 'translateY(0) scale(1)';
            cancelBtn.style.boxShadow = 'none';
        });

        okBtn.addEventListener('mouseenter', () => {
            okBtn.style.background = 'linear-gradient(135deg, #45a049, #4CAF50)';
            okBtn.style.transform = 'translateY(-2px) scale(1.02)';
            okBtn.style.boxShadow = '0 10px 30px rgba(76, 175, 80, 0.5)';
            // 触发光效动画
            const shimmer = okBtn.querySelector('div');
            if (shimmer) {
                shimmer.style.left = '100%';
                setTimeout(() => {
                    shimmer.style.left = '-100%';
                }, 600);
            }
        });

        okBtn.addEventListener('mouseleave', () => {
            okBtn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            okBtn.style.transform = 'translateY(0) scale(1)';
            okBtn.style.boxShadow = '0 6px 20px rgba(76, 175, 80, 0.4)';
        });

        cancelBtn.addEventListener('click', () => {
            confirmDialog.style.animation = 'fadeOut 0.3s ease-out forwards';
            setTimeout(() => {
                document.body.removeChild(confirmDialog);
                document.head.removeChild(style);
            }, 300);
            resolve(false);
        });

        okBtn.addEventListener('click', () => {
            confirmDialog.style.animation = 'fadeOut 0.3s ease-out forwards';
            setTimeout(() => {
                document.body.removeChild(confirmDialog);
                document.head.removeChild(style);
            }, 300);
            resolve(true);
        });

        // 点击背景关闭
        confirmDialog.addEventListener('click', (e) => {
            if (e.target === confirmDialog) {
                confirmDialog.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(confirmDialog);
                    document.head.removeChild(style);
                }, 300);
                resolve(false);
            }
        });

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                confirmDialog.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(confirmDialog);
                    document.head.removeChild(style);
                    document.removeEventListener('keydown', handleKeyDown);
                }, 300);
                resolve(false);
            }
        };
        document.addEventListener('keydown', handleKeyDown);

        // 添加淡出动画到样式表
        style.textContent += `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
    });
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 更新版本号显示
function updateVersionDisplay(newVersion) {
    // 更新页面标题
    const titleElement = document.querySelector('title');
    if (titleElement) {
        titleElement.textContent = `Magic Box v${newVersion}`;
    }

    // 更新header中的版本号显示
    const headerVersion = document.getElementById('header-version');
    if (headerVersion) {
        headerVersion.textContent = `v${newVersion}`;
    }

    // 更新窗口标题
    if (window.__TAURI__) {
        invoke('set_window_title', { title: `Magic Box v${newVersion}` });
    }

    console.log(`✅ 版本号已更新为: ${newVersion}`);
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (logUpdateInterval) {
        clearInterval(logUpdateInterval);
    }
});

// 初始化模式切换功能
function initializeModeSwitch() {
    const augmentBtn = document.getElementById('augmentModeBtn');
    const cursorBtn = document.getElementById('cursorModeBtn');
    const apiQuotaBtn = document.getElementById('apiQuotaModeBtn');
    const augmentMode = document.getElementById('augmentMode');
    const cursorMode = document.getElementById('cursorMode');
    const apiQuotaMode = document.getElementById('apiQuotaMode');
    const augmentTutorial = document.getElementById('augmentTutorial');
    const cursorTutorial = document.getElementById('cursorTutorial');
    const logPanel = document.getElementById('logPanel');
    const modelsPanel = document.getElementById('modelsPanel');

    if (!augmentBtn || !cursorBtn || !apiQuotaBtn || !augmentMode || !cursorMode || !apiQuotaMode ||
        !augmentTutorial || !cursorTutorial || !logPanel || !modelsPanel) {
        console.error('❌ 模式切换元素未找到');
        return;
    }

    // Augment模式按钮点击
    augmentBtn.addEventListener('click', function() {
        // 切换按钮状态
        augmentBtn.classList.add('active');
        cursorBtn.classList.remove('active');
        apiQuotaBtn.classList.remove('active');

        // 切换左侧内容显示
        augmentMode.classList.add('active');
        cursorMode.classList.remove('active');
        apiQuotaMode.classList.remove('active');

        // 切换中间教程面板
        augmentTutorial.classList.add('active');
        cursorTutorial.classList.remove('active');

        // 切换右侧面板显示
        logPanel.classList.remove('panel-hidden');
        logPanel.classList.add('panel-visible');
        modelsPanel.classList.remove('panel-visible');
        modelsPanel.classList.add('panel-hidden');

        // 移除API额度模式类
        document.querySelector('.main-content').classList.remove('api-quota-mode');

        // 停止Cursor使用量自动刷新
        stopUsageAutoRefresh();

        // 模式切换不需要添加日志，只需要更新显示
        updateLogs();
    });

    // Cursor模式按钮点击
    cursorBtn.addEventListener('click', function() {
        // 切换按钮状态
        cursorBtn.classList.add('active');
        augmentBtn.classList.remove('active');
        apiQuotaBtn.classList.remove('active');

        // 切换左侧内容显示
        cursorMode.classList.add('active');
        augmentMode.classList.remove('active');
        apiQuotaMode.classList.remove('active');

        // 切换中间教程面板
        cursorTutorial.classList.add('active');
        augmentTutorial.classList.remove('active');

        // 切换右侧面板显示
        logPanel.classList.remove('panel-hidden');
        logPanel.classList.add('panel-visible');
        modelsPanel.classList.remove('panel-visible');
        modelsPanel.classList.add('panel-hidden');

        // 移除API额度模式类
        document.querySelector('.main-content').classList.remove('api-quota-mode');

        // 如果使用量区域已经显示，重新启动自动刷新
        if (isUsageSectionVisible) {
            startUsageAutoRefresh();
        }

        // 模式切换不需要添加日志，只需要更新显示
        updateLogs();
    });

    // API额度模式按钮点击
    apiQuotaBtn.addEventListener('click', function() {
        // 切换按钮状态
        apiQuotaBtn.classList.add('active');
        augmentBtn.classList.remove('active');
        cursorBtn.classList.remove('active');

        // 切换左侧内容显示
        apiQuotaMode.classList.add('active');
        augmentMode.classList.remove('active');
        cursorMode.classList.remove('active');

        // 切换中间教程显示（API额度模式隐藏教程面板）
        augmentTutorial.classList.remove('active');
        cursorTutorial.classList.remove('active');

        // 切换右侧面板显示
        logPanel.classList.remove('panel-visible');
        logPanel.classList.add('panel-hidden');
        modelsPanel.classList.remove('panel-hidden');
        modelsPanel.classList.add('panel-visible');

        // 为主容器添加API额度模式类，扩展右侧面板宽度
        document.querySelector('.main-content').classList.add('api-quota-mode');

        // 停止Cursor使用量自动刷新
        stopUsageAutoRefresh();

        // 模式切换不需要添加日志，只需要更新显示
        updateLogs();
    });

    // 绑定Cursor模式的按钮事件
    const cursorCacheBtn = document.getElementById('cursorCacheBtn');
    const cursorUsageBtn = document.getElementById('cursorUsageBtn');

    if (cursorCacheBtn) {
        cursorCacheBtn.addEventListener('click', cleanCursor);
    }

    if (cursorUsageBtn) {
        cursorUsageBtn.addEventListener('click', checkCursorUsage);
    }

    // 绑定API额度模式的按钮事件
    const checkQuotaBtn = document.getElementById('checkQuotaBtn');
    const testModelsBtn = document.getElementById('testModelsBtn');

    if (checkQuotaBtn) {
        checkQuotaBtn.addEventListener('click', checkApiQuota);
    }

    if (testModelsBtn) {
        testModelsBtn.addEventListener('click', testApiModels);
    }

    // 绑定停止测试按钮
    const stopTestingBtn = document.getElementById('stopTestingBtn');
    if (stopTestingBtn) {
        stopTestingBtn.addEventListener('click', stopModelTesting);
    }

    // 绑定模型选择弹窗事件
    const modelSelectionModal = document.getElementById('modelSelectionModal');
    const closeModalBtn = document.querySelector('.model-selection-close');
    const cancelBtn = document.getElementById('cancelModelSelectionBtn');
    const confirmBtn = document.getElementById('confirmModelSelectionBtn');
    const selectAllBtn = document.getElementById('selectAllModelsBtn');
    const clearBtn = document.getElementById('clearSelectionBtn');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const selectChatCheckbox = document.getElementById('selectChatModelsCheckbox');
    const filterInput = document.getElementById('modelFilterInput');

    if (closeModalBtn) closeModalBtn.addEventListener('click', closeModelSelectionModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModelSelectionModal);
    if (confirmBtn) confirmBtn.addEventListener('click', confirmModelSelection);
    if (selectAllBtn) selectAllBtn.addEventListener('click', filterModels);
    if (clearBtn) clearBtn.addEventListener('click', clearSelection);
    if (selectAllCheckbox) selectAllCheckbox.addEventListener('change', toggleSelectAll);
    if (selectChatCheckbox) selectChatCheckbox.addEventListener('change', selectChatModels);
    if (filterInput) {
        filterInput.addEventListener('input', filterModels);
        filterInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') filterModels();
        });
    }

    // 点击弹窗背景关闭
    if (modelSelectionModal) {
        modelSelectionModal.addEventListener('click', (e) => {
            if (e.target === modelSelectionModal) {
                closeModelSelectionModal();
            }
        });
    }

    // 绑定清空模型结果按钮
    const clearModelsBtn = document.getElementById('clearModelsBtn');
    if (clearModelsBtn) {
        clearModelsBtn.addEventListener('click', clearModelResults);
    }

    // 绑定分页控制按钮
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');

    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderCurrentPage();
            }
        });
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
            const totalPages = Math.ceil(allModelResults.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                renderCurrentPage();
            }
        });
    }

    console.log('✅ 模式切换功能初始化完成');
}

// ==================== SessionId 功能 ====================

// 刷新IDEA SessionId
async function refreshSessionId() {
    try {
        const statusElement = document.getElementById('sessionIdStatus');
        const inputElement = document.getElementById('currentSessionId');
        const refreshBtn = document.getElementById('refreshSessionIdBtn');

        // 更新状态为加载中
        statusElement.className = 'session-id-status loading';
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>正在获取...</span>';

        // 禁用刷新按钮
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取中';

        // 清空输入框
        inputElement.value = '';

        // 调用后端获取SessionId
        const sessionId = await invoke('get_current_session_id');

        // 更新输入框
        inputElement.value = sessionId;

        // 更新状态为成功
        statusElement.className = 'session-id-status success';
        statusElement.innerHTML = '<i class="fas fa-check-circle"></i><span>获取成功</span>';

        showNotification('劫持获取成功', 'success');

    } catch (error) {
        console.error('获取劫持失败:', error);

        const statusElement = document.getElementById('sessionIdStatus');
        const inputElement = document.getElementById('currentSessionId');

        // 更新状态为错误
        statusElement.className = 'session-id-status error';
        statusElement.innerHTML = '<i class="fas fa-exclamation-circle"></i><span>获取失败</span>';

        // 清空输入框
        inputElement.value = '';

        showNotification(`获取劫持失败: ${error}`, 'error');
    } finally {
        // 恢复刷新按钮
        const refreshBtn = document.getElementById('refreshSessionIdBtn');
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新';
    }
}

// 刷新VSCode SessionId
async function refreshVscodeSessionId() {
    try {
        const statusElement = document.getElementById('vscodeSessionIdStatus');
        const inputElement = document.getElementById('currentVscodeSessionId');
        const refreshBtn = document.getElementById('refreshVscodeSessionIdBtn');

        // 更新状态为加载中
        statusElement.className = 'session-id-status loading';
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>正在获取...</span>';

        // 禁用刷新按钮
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取中';

        // 清空输入框
        inputElement.value = '';

        // 调用后端获取VSCode SessionId
        const sessionId = await invoke('get_current_vscode_session_id');

        // 更新输入框
        inputElement.value = sessionId;

        // 更新状态为成功
        statusElement.className = 'session-id-status success';
        statusElement.innerHTML = '<i class="fas fa-check-circle"></i><span>获取成功</span>';

        showNotification('VSCode 劫持获取成功', 'success');

    } catch (error) {
        console.error('获取VSCode 劫持失败:', error);

        const statusElement = document.getElementById('vscodeSessionIdStatus');
        const inputElement = document.getElementById('currentVscodeSessionId');

        // 更新状态为错误
        statusElement.className = 'session-id-status error';
        statusElement.innerHTML = '<i class="fas fa-exclamation-circle"></i><span>获取失败</span>';

        // 清空输入框
        inputElement.value = '';

        showNotification(`获取VSCode 劫持失败: ${error}`, 'error');
    } finally {
        // 恢复刷新按钮
        const refreshBtn = document.getElementById('refreshVscodeSessionIdBtn');
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新';
    }
}



// 页面卸载时清理定时器
window.addEventListener('beforeunload', () => {
    stopAnnouncementAutoRefresh();
    stopUsageAutoRefresh();
});

// 使用量自动刷新相关变量
let usageRefreshInterval = null;
let countdownInterval = null;
let isUsageSectionVisible = false;
let countdownSeconds = 30;

// 检查Cursor使用量
async function checkCursorUsage(showNotificationFlag = true) {
    const usageSection = document.getElementById('usageSection');
    const usageUsed = document.getElementById('usageUsed');
    const usageTotal = document.getElementById('usageTotal');
    const usagePercentage = document.getElementById('usagePercentage');
    const usageRemaining = document.getElementById('usageRemaining');
    const usageProgressBar = document.getElementById('usageProgressBar');

    try {
        // 如果是第一次显示，显示加载状态
        if (!isUsageSectionVisible) {
            usageUsed.textContent = '加载中...';
            usageTotal.textContent = '加载中...';
            usagePercentage.textContent = '加载中...';
            usageRemaining.textContent = '加载中...';
            usageSection.style.display = 'block';
            isUsageSectionVisible = true;

            // 启动自动刷新
            startUsageAutoRefresh();
        }

        // 调用后端API获取使用量
        const usage = await invoke('get_cursor_usage');

        // 更新显示
        usageUsed.textContent = `${usage.used}次`;
        usageTotal.textContent = `${usage.total}次`;
        usagePercentage.textContent = `${usage.percentage}%`;
        usageRemaining.textContent = `${usage.remaining}次`;

        // 更新进度条
        usageProgressBar.style.width = `${usage.percentage}%`;

        // 根据使用率设置进度条颜色
        if (usage.percentage >= 90) {
            usageProgressBar.style.background = '#ef4444';
        } else if (usage.percentage >= 70) {
            usageProgressBar.style.background = '#f59e0b';
        } else {
            usageProgressBar.style.background = '#10b981';
        }

        // 更新最后更新时间
        const lastUpdateTime = document.getElementById('lastUpdateTime');
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        lastUpdateTime.textContent = `最后更新: ${timeString}`;

        if (showNotificationFlag) {
            showNotification('使用量查询成功', 'success');
        }

    } catch (error) {
        console.error('获取使用量失败:', error);

        // 显示错误信息
        usageUsed.textContent = '获取失败';
        usageTotal.textContent = '获取失败';
        usagePercentage.textContent = '获取失败';
        usageRemaining.textContent = '获取失败';

        if (showNotificationFlag) {
            showNotification(`获取使用量失败: ${error}`, 'error');
        }
    }
}

// 启动使用量自动刷新
function startUsageAutoRefresh() {
    // 清除之前的定时器
    if (usageRefreshInterval) {
        clearInterval(usageRefreshInterval);
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    // 重置倒计时
    countdownSeconds = 30;
    updateCountdownDisplay();

    // 启动倒计时显示
    countdownInterval = setInterval(() => {
        countdownSeconds--;
        updateCountdownDisplay();

        if (countdownSeconds <= 0) {
            // 倒计时结束，重置并刷新
            countdownSeconds = 30;
            if (isUsageSectionVisible && getCurrentMode() === 'cursor') {
                checkCursorUsage(false); // 自动刷新时不显示通知
            }
        }
    }, 1000); // 每秒更新倒计时
}

// 停止使用量自动刷新
function stopUsageAutoRefresh() {
    if (usageRefreshInterval) {
        clearInterval(usageRefreshInterval);
        usageRefreshInterval = null;
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }
    isUsageSectionVisible = false;
}

// 更新倒计时显示
function updateCountdownDisplay() {
    const countdownElement = document.getElementById('refreshCountdown');
    if (countdownElement) {
        countdownElement.textContent = countdownSeconds;

        // 添加脉冲效果
        countdownElement.classList.add('pulse');
        setTimeout(() => {
            countdownElement.classList.remove('pulse');
        }, 200);
    }
}

// 前端直接添加日志的功能已移除，所有日志现在通过后端系统管理

// Cursor清理
async function cleanCursor() {
    try {
        // 在开始操作前检查授权状态
        if (!isAuthorized) {
            showNotification('请先进行授权验证', 'warning');
            showAuthModal();
            return;
        }

        // 检查本地授权码是否存在
        const authCode = localStorage.getItem('magic_box_auth_code');
        if (!authCode) {
            handleAuthExpired('本地授权码丢失');
            return;
        }

        if (isRunning) {
            showNotification('清理正在进行中，请稍候...', 'warning');
            return;
        }

        const result = await invoke('clean_cursor');

        // 强制刷新日志显示
        await forceUpdateLogs();

        showNotification(result, 'success');

    } catch (error) {
        console.error('Cursor清理失败:', error);
        showNotification(`Cursor清理失败: ${error}`, 'error');
    }
}

// ==================== 设备指纹相关函数 ====================

// 生成设备指纹（基于硬件特征，重装系统后保持不变）
function generateDeviceId() {
    if (deviceId) {
        return deviceId;
    }

    // 收集稳定的硬件特征（重装系统后不变）
    const hardwareInfo = {
        // 显示器硬件特征
        screenWidth: screen.width,
        screenHeight: screen.height,
        colorDepth: screen.colorDepth,
        pixelRatio: Math.round((window.devicePixelRatio || 1) * 100), // 四舍五入避免小数差异

        // 系统硬件特征
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone, // 地理位置相对稳定
        cpuCores: navigator.hardwareConcurrency || 4, // CPU核心数

        // 浏览器引擎特征（Tauri使用WebView）
        webglVendor: getWebGLVendor(), // 显卡厂商
        webglRenderer: getWebGLRenderer(), // 显卡型号

        // 操作系统特征
        platform: navigator.platform,

        // Canvas硬件指纹（显卡渲染特征）
        canvasFingerprint: getCanvasFingerprint()
    };

    // 生成稳定的设备ID
    const hardwareString = JSON.stringify(hardwareInfo);

    // 使用更稳定的哈希算法
    let hash = 0;
    for (let i = 0; i < hardwareString.length; i++) {
        const char = hardwareString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }

    // 转换为正数并生成16位字符串
    const positiveHash = Math.abs(hash);
    deviceId = `HW${positiveHash.toString(36).toUpperCase().padStart(14, '0')}`;

    // 保存到本地存储（作为缓存，提高性能）
    localStorage.setItem('magic_box_device_id', deviceId);

    console.log(`🔍 硬件设备指纹生成: ${deviceId}`);
    console.log(`🔍 硬件特征: ${JSON.stringify(hardwareInfo, null, 2)}`);
    return deviceId;
}

// 获取WebGL显卡厂商信息
function getWebGLVendor() {
    try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (debugInfo) {
                return gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || 'Unknown';
            }
        }
        return 'Unknown';
    } catch (e) {
        return 'Unknown';
    }
}

// 获取WebGL显卡渲染器信息
function getWebGLRenderer() {
    try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (debugInfo) {
                return gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'Unknown';
            }
        }
        return 'Unknown';
    } catch (e) {
        return 'Unknown';
    }
}

// 获取Canvas硬件指纹（显卡渲染特征）
function getCanvasFingerprint() {
    try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 绘制复杂图形来获取显卡渲染特征
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#f60';
        ctx.fillRect(125, 1, 62, 20);
        ctx.fillStyle = '#069';
        ctx.fillText('Hardware fingerprint 🔐', 2, 15);
        ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
        ctx.fillText('Hardware fingerprint 🔐', 4, 17);

        // 绘制几何图形
        ctx.globalCompositeOperation = 'multiply';
        ctx.fillStyle = 'rgb(255,0,255)';
        ctx.beginPath();
        ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
        ctx.closePath();
        ctx.fill();

        // 返回前32个字符作为指纹
        return canvas.toDataURL().substring(0, 32);
    } catch (e) {
        return 'Unknown';
    }
}

// 获取设备指纹
function getDeviceId() {
    if (deviceId) {
        return deviceId;
    }

    // 尝试从本地存储获取（作为缓存）
    const storedDeviceId = localStorage.getItem('magic_box_device_id');
    if (storedDeviceId && storedDeviceId.startsWith('HW')) {
        // 验证缓存的设备ID是否与当前硬件匹配
        const currentDeviceId = generateDeviceId();
        if (storedDeviceId === currentDeviceId) {
            deviceId = storedDeviceId;
            console.log(`🔍 从缓存获取设备指纹: ${deviceId}`);
            return deviceId;
        } else {
            console.log(`⚠️ 缓存的设备ID与当前硬件不匹配，重新生成`);
            localStorage.removeItem('magic_box_device_id');
        }
    }

    // 生成基于硬件特征的设备指纹
    return generateDeviceId();
}

// 获取操作系统名称
function getOSName() {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;

    if (userAgent.includes('Windows NT')) {
        return 'Windows';
    } else if (userAgent.includes('Mac OS X') || platform.includes('Mac')) {
        return 'macOS';
    } else if (userAgent.includes('Linux') || platform.includes('Linux')) {
        return 'Linux';
    } else {
        return platform || 'Unknown';
    }
}

// 获取操作系统版本
function getOSVersion() {
    const userAgent = navigator.userAgent;

    // Windows版本检测
    if (userAgent.includes('Windows NT')) {
        if (userAgent.includes('Windows NT 10.0')) {
            return 'Windows 10/11';
        } else if (userAgent.includes('Windows NT 6.3')) {
            return 'Windows 8.1';
        } else if (userAgent.includes('Windows NT 6.2')) {
            return 'Windows 8';
        } else if (userAgent.includes('Windows NT 6.1')) {
            return 'Windows 7';
        } else {
            return 'Windows';
        }
    }

    // macOS版本检测
    if (userAgent.includes('Mac OS X')) {
        const match = userAgent.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);
        if (match) {
            return `macOS ${match[1].replace(/_/g, '.')}`;
        }
        return 'macOS';
    }

    // Linux版本检测
    if (userAgent.includes('Linux')) {
        return 'Linux';
    }

    return 'Unknown';
}

// ==================== 授权验证相关函数 ====================

// 检查授权状态
async function checkAuthorization() {
    try {
        // 从本地存储检查授权状态
        const authCode = localStorage.getItem('magic_box_auth_code');
        if (!authCode) {
            console.log('🔍 本地未找到授权码');
            isAuthorized = false;
            return;
        }

        // 向后端数据库验证授权码（不依赖本地时间判断）
        console.log('🔍 向后端数据库验证授权码...');
        const result = await verifyAuthCodeWithDetails(authCode);
        isAuthorized = result.valid;

        if (!result.valid) {
            console.log(`❌ 后端数据库验证失败: ${result.reason}`);

            // 检查是否是网络错误
            const isNetworkError = result.reason.includes('网络') ||
                                  result.reason.includes('超时') ||
                                  result.reason.includes('连接') ||
                                  result.reason.includes('服务器错误');

            if (!isNetworkError) {
                // 后端数据库明确返回授权无效（过期、不存在等）
                localStorage.removeItem('magic_box_auth_code');
                console.log('🗑️ 后端数据库确认授权无效，已清除本地授权码');
            } else {
                // 网络错误，暂时允许使用，等待定期检查
                console.log('⚠️ 网络错误，暂时允许使用，等待定期检查');
                isAuthorized = true;
            }
        } else {
            console.log('✅ 后端数据库验证通过，授权码有效');
        }

    } catch (error) {
        console.error('❌ 检查授权状态失败:', error);
        isAuthorized = false;
    }
}

// 显示授权弹窗
function showAuthModal(errorMessage = null) {
    const modal = document.getElementById('auth-modal');
    if (!modal) {
        console.error('❌ 找不到授权弹窗元素');
        return;
    }

    // 重置弹窗内容为原始状态
    const modalContent = modal.querySelector('.auth-modal-content');
    modalContent.innerHTML = `
        <div class="auth-header">
            <h2>🔐 授权验证</h2>
            <p>请输入授权码以使用 Magic Box</p>
            <div class="customer-service">
                <p><strong>🎯 客服QQ：362856178</strong></p>
            </div>
        </div>
        <form id="auth-form">
            <div class="form-group">
                <label for="auth-code">授权码</label>
                <input type="text" id="auth-code" placeholder="请输入授权码" required>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">验证授权</button>
            </div>
        </form>
        <div id="auth-error" class="auth-error hidden"></div>
    `;

    // 显示错误信息（如果有）
    const errorDiv = document.getElementById('auth-error');
    if (errorMessage) {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
    }

    // 自动聚焦到输入框
    const authCodeInput = document.getElementById('auth-code');
    if (authCodeInput) {
        authCodeInput.focus();
    }

    // 显示弹窗
    modal.style.display = 'flex';

    // 绑定表单提交事件
    const form = document.getElementById('auth-form');

    // 绑定表单提交事件
    form.onsubmit = async (e) => {
        e.preventDefault();

        const authCode = document.getElementById('auth-code').value.trim();
        if (!authCode) {
            showAuthError('请输入授权码');
            return;
        }

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '验证中...';
        submitBtn.disabled = true;

        try {
            const result = await verifyAuthCodeWithDetails(authCode);

            if (result.valid) {
                // 保存授权码
                localStorage.setItem('magic_box_auth_code', authCode);
                isAuthorized = true;

                // 隐藏弹窗
                modal.style.display = 'none';

                // 如果是重新授权，不需要重新初始化
                if (!elements.vscodeCleanBtn) {
                    // 首次授权，需要初始化应用
                    initializeMainApp();
                }

                showNotification('授权验证成功！', 'success');

            } else {
                showAuthError(`授权失败: ${result.reason}`);
            }

        } catch (error) {
            console.error('授权验证失败:', error);
            showAuthError('验证失败，请检查网络连接');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    };
}

// 验证授权码
async function verifyAuthCode(authCode) {
    try {
        const response = await fetch(`${window.CONFIG.API_BASE_URL}/api/auth/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: authCode,
                clientInfo: {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    timestamp: new Date().toISOString()
                }
            })
        });

        const result = await response.json();
        return result.success;

    } catch (error) {
        console.error('授权验证请求失败:', error);
        return false;
    }
}

// 显示授权错误
function showAuthError(message) {
    const errorDiv = document.getElementById('auth-error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');

    // 3秒后隐藏错误信息
    setTimeout(() => {
        errorDiv.classList.add('hidden');
    }, 3000);
}

// 启动定期授权检查
function startPeriodicAuthCheck() {
    // 每60秒向后端数据库验证授权状态（减少频率，避免产生太多记录）
    setInterval(async () => {
        console.log('🔍 定期向后端数据库验证授权状态...');

        const authCode = localStorage.getItem('magic_box_auth_code');
        if (!authCode) {
            console.log('❌ 本地未找到授权码');
            handleAuthExpired('授权码丢失');
            return;
        }

        console.log('📡 向后端数据库发送授权检查请求（不消耗使用次数）...');
        const result = await verifyAuthCodeWithDetails(authCode, true); // checkOnly = true

        if (!result.valid) {
            // 检查是否是网络相关错误
            const isNetworkError = result.reason.includes('网络') ||
                                  result.reason.includes('超时') ||
                                  result.reason.includes('连接') ||
                                  result.reason.includes('服务器错误');

            if (isNetworkError) {
                // 网络错误，跳过此次检查，不做任何处理
                console.log(`⚠️ 网络连接问题，跳过此次检查: ${result.reason}`);
            } else {
                // 后端数据库明确返回授权无效（过期、不存在等）
                console.log(`🚨 后端数据库验证失败: ${result.reason}`);
                handleAuthExpired(result.reason);
            }
        } else {
            console.log('✅ 后端数据库验证通过，授权码有效');
        }
    }, 60 * 1000); // 60秒检查一次
}

// 处理授权过期
function handleAuthExpired(reason) {
    console.log(`🚨 授权过期: ${reason}`);

    // 清除本地授权码
    localStorage.removeItem('magic_box_auth_code');
    isAuthorized = false;

    // 停止所有正在运行的操作
    if (isRunning) {
        try {
            stopRegistration();
        } catch (error) {
            console.error('停止注册操作失败:', error);
        }
    }

    // 显示授权过期通知
    showNotification(`🚨 授权已过期: ${reason}`, 'error');
    showNotification('请重新输入有效的授权码', 'warning');

    // 立即显示授权弹窗
    setTimeout(() => {
        showAuthModal();
    }, 500);
}

// 修改验证授权码函数，添加更详细的错误处理
async function verifyAuthCodeWithDetails(authCode, checkOnly = false) {
    try {
        const mode = checkOnly ? '检查' : '验证';
        console.log(`🔍 开始${mode}授权码: ${authCode}`);
        console.log(`🕐 当前时间: ${new Date().toLocaleString('zh-CN')}`);
        console.log(`🔧 模式: ${checkOnly ? '仅检查状态（不消耗使用次数）' : '正式验证（消耗使用次数）'}`);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        console.log('📡 发送验证请求到后端...');
        const response = await fetch(`${window.CONFIG.API_BASE_URL}/api/auth/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: authCode,
                checkOnly: checkOnly,
                deviceId: getDeviceId(),
                clientInfo: {
                    appType: 'desktop',
                    osName: getOSName(),
                    osVersion: getOSVersion(),
                    appVersion: '1.0.0',
                    timestamp: new Date().toISOString(),
                    deviceName: `${getOSName()} Desktop`
                }
            }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        console.log(`📡 收到响应: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            console.log(`❌ HTTP响应错误: ${response.status} ${response.statusText}`);
            // 特殊处理401错误（授权失败）
            if (response.status === 401) {
                try {
                    const errorResult = await response.json();
                    console.log(`❌ 401错误详情: ${JSON.stringify(errorResult)}`);
                    return { valid: false, reason: errorResult.message || '授权码已过期或无效' };
                } catch (e) {
                    console.log(`❌ 401错误，无法解析响应体`);
                    return { valid: false, reason: '授权码已过期或无效' };
                }
            } else {
                console.log(`❌ 其他HTTP错误: ${response.status}`);
                return { valid: false, reason: `服务器错误 (${response.status})` };
            }
        }

        console.log('📡 开始解析响应JSON...');
        const result = await response.json();
        console.log(`📡 响应内容: ${JSON.stringify(result)}`);

        if (!result.success) {
            console.log(`❌ 后端返回验证失败: ${result.message}`);
            return { valid: false, reason: result.message };
        }

        console.log('✅ 授权验证成功');
        return { valid: true, data: result.data };

    } catch (error) {
        console.error('❌ 授权验证请求异常:', error);
        console.error('❌ 错误类型:', error.name);
        console.error('❌ 错误消息:', error.message);

        if (error.name === 'AbortError') {
            console.log('❌ 请求超时（5秒）');
            return { valid: false, reason: '网络请求超时' };
        } else if (error.message.includes('fetch')) {
            console.log('❌ 网络连接失败');
            return { valid: false, reason: '网络连接失败' };
        } else {
            console.log('❌ 其他网络异常');
            return { valid: false, reason: '网络异常' };
        }
    }
}

// ==================== API额度检查相关函数 ====================

// 检查API额度
async function checkApiQuota() {
    const apiUrl = document.getElementById('apiUrl').value.trim();
    const apiKey = document.getElementById('apiKey').value.trim();
    const checkBtn = document.getElementById('checkQuotaBtn');
    const quotaSection = document.getElementById('quotaSection');

    if (!apiUrl || !apiKey) {
        showNotification('请输入API地址和密钥', 'error');
        return;
    }

    // 显示加载状态
    checkBtn.disabled = true;
    checkBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';

    try {
        const result = await invoke('check_api_quota', { apiUrl, apiKey });

        // 显示额度信息
        document.getElementById('quotaTotal').textContent = `$${result.total.toFixed(2)}`;
        document.getElementById('quotaUsed').textContent = `$${result.used.toFixed(2)}`;
        document.getElementById('quotaRemaining').textContent = `$${result.remaining.toFixed(2)}`;
        document.getElementById('quotaPercentage').textContent = `${result.percentage.toFixed(1)}%`;

        // 更新进度条
        const progressBar = document.getElementById('quotaProgressBar');
        progressBar.style.width = `${result.percentage}%`;

        // 根据使用率设置颜色
        if (result.percentage < 50) {
            progressBar.style.background = 'var(--color-success)';
        } else if (result.percentage < 80) {
            progressBar.style.background = 'var(--color-warning)';
        } else {
            progressBar.style.background = 'var(--color-danger)';
        }

        // 更新最后更新时间
        const now = new Date();
        document.getElementById('quotaLastUpdate').textContent =
            `最后更新: ${now.toLocaleString('zh-CN')}`;

        // 显示额度区域
        quotaSection.style.display = 'block';

        showNotification('额度信息获取成功', 'success');

    } catch (error) {
        console.error('检查API额度失败:', error);
        showNotification(`检查失败: ${error}`, 'error');
    } finally {
        // 恢复按钮状态
        checkBtn.disabled = false;
        checkBtn.innerHTML = '<i class="fas fa-search"></i> 检查额度';
    }
}

// 测试API模型
async function testApiModels() {
    const apiUrl = document.getElementById('apiUrl').value.trim();
    const apiKey = document.getElementById('apiKey').value.trim();

    if (!apiUrl || !apiKey) {
        showNotification('请输入API地址和密钥', 'error');
        return;
    }

    try {
        // 首先获取模型列表
        showNotification('正在获取模型列表...', 'info');
        const modelListResult = await invoke('get_model_list', { apiUrl, apiKey });
        const models = modelListResult.models;

        if (!models || models.length === 0) {
            showNotification('未找到可用模型', 'error');
            return;
        }

        // 显示模型选择弹窗
        showModelSelectionModal(models, apiUrl, apiKey);

    } catch (error) {
        console.error('获取模型列表失败:', error);
        showNotification(`获取模型列表失败: ${error}`, 'error');
    }
}

// 实际执行模型测试
async function executeModelTesting(selectedModels, apiUrl, apiKey) {
    const testBtn = document.getElementById('testModelsBtn');

    // 设置测试状态
    isTestingModels = true;
    shouldStopTesting = false;

    // 显示停止按钮，禁用测试按钮
    const stopBtn = document.getElementById('stopTestingBtn');
    stopBtn.style.display = 'inline-flex';
    testBtn.disabled = true;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';

    // 确保模型面板可见（在API额度模式下应该已经可见）
    const progressSection = document.getElementById('modelsProgress');
    const progressText = document.getElementById('modelsProgressText');
    const modelsTableBody = document.getElementById('modelsTableBody');

    progressSection.style.display = 'flex';
    progressText.textContent = `开始测试 ${selectedModels.length} 个模型...`;
    modelsTableBody.innerHTML = '';

    try {
        console.log(`准备测试 ${selectedModels.length} 个模型:`, selectedModels);

        // 初始化结果数组和统计
        let allResults = [];
        let validCount = 0;
        let invalidCount = 0;
        let inconsistentCount = 0;

        // 清空表格
        modelsTableBody.innerHTML = '';

        // 逐个测试模型
        for (let i = 0; i < selectedModels.length; i++) {
            // 检查是否需要停止测试
            if (shouldStopTesting) {
                progressText.textContent = `测试已停止 (已测试 ${i}/${selectedModels.length} 个模型)`;
                showNotification(`测试已停止，已完成 ${i} 个模型的测试`, 'warning');
                break;
            }

            const model = selectedModels[i];
            progressText.textContent = `测试模型 ${i + 1}/${selectedModels.length}: ${model}`;

            try {
                console.log(`测试模型 ${i + 1}/${selectedModels.length}: ${model}`);
                const result = await invoke('test_single_model', { apiUrl, apiKey, model });

                // 添加到结果数组
                allResults.push(result);

                // 更新统计
                if (result.status === 'valid') validCount++;
                else if (result.status === 'invalid') invalidCount++;
                else if (result.status === 'inconsistent') inconsistentCount++;

                // 实时更新统计显示
                document.getElementById('validModelsCount').textContent = validCount;
                document.getElementById('invalidModelsCount').textContent = invalidCount;
                document.getElementById('inconsistentModelsCount').textContent = inconsistentCount;

                // 实时添加到表格
                addModelResultToTable(result);

                // 检查是否需要分页
                checkPagination();

            } catch (error) {
                console.error(`测试模型 ${model} 失败:`, error);
                // 添加失败结果
                const failedResult = {
                    model: model,
                    status: 'invalid',
                    response_time: null,
                    error: error.toString()
                };
                allResults.push(failedResult);
                invalidCount++;
                document.getElementById('invalidModelsCount').textContent = invalidCount;
                addModelResultToTable(failedResult);
                checkPagination();
            }
        }

        // 隐藏进度条
        progressSection.style.display = 'none';

        showNotification('模型测试完成', 'success');

    } catch (error) {
        console.error('测试API模型失败:', error);
        showNotification(`测试失败: ${error}`, 'error');
        progressSection.style.display = 'none';
    } finally {
        // 重置测试状态
        isTestingModels = false;
        shouldStopTesting = false;

        // 恢复按钮状态
        const stopBtn = document.getElementById('stopTestingBtn');
        stopBtn.style.display = 'none';
        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-flask"></i> 测试模型';
    }
}

// 分页相关变量
let currentPage = 1;
const itemsPerPage = 9;
let allModelResults = [];

// 测试控制变量
let isTestingModels = false;
let shouldStopTesting = false;

// 显示模型测试结果（批量）
function displayModelResults(results) {
    allModelResults = results;
    currentPage = 1;
    renderCurrentPage();
}

// 添加单个模型结果到表格
function addModelResultToTable(result) {
    allModelResults.push(result);

    // 如果当前页面还有空间，直接添加
    const currentPageItems = (currentPage - 1) * itemsPerPage;
    if (allModelResults.length <= currentPageItems + itemsPerPage) {
        const modelsTableBody = document.getElementById('modelsTableBody');
        const row = createModelRow(result);
        modelsTableBody.appendChild(row);
    }
}

// 创建模型行元素
function createModelRow(result) {
    const statusClass = result.status;
    const statusText = {
        'valid': '可用',
        'invalid': '不可用',
        'inconsistent': '不一致'
    }[result.status] || result.status;

    const responseTime = result.response_time ?
        `${(result.response_time * 1000).toFixed(0)}ms` : '-';

    const remark = result.error ?
        `错误: ${result.error.substring(0, 50)}${result.error.length > 50 ? '...' : ''}` :
        '正常';

    const row = document.createElement('tr');
    row.innerHTML = `
        <td class="col-status">
            <span class="status-badge ${statusClass}">${statusText}</span>
        </td>
        <td class="col-model" title="${result.model}">${result.model}</td>
        <td class="col-time">${responseTime}</td>
        <td class="col-remark" title="${remark}">${remark}</td>
        <td class="col-verify">
            <button class="verify-btn" onclick="verifyModel('${result.model}')">验证</button>
        </td>
    `;

    return row;
}

// 检查分页
function checkPagination() {
    const totalItems = allModelResults.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // 如果当前页已满，自动翻到下一页
    if (totalItems > currentPage * itemsPerPage) {
        currentPage++;
        renderCurrentPage();
        updatePaginationInfo();
    }
}

// 渲染当前页
function renderCurrentPage() {
    const modelsTableBody = document.getElementById('modelsTableBody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageResults = allModelResults.slice(startIndex, endIndex);

    modelsTableBody.innerHTML = '';
    pageResults.forEach(result => {
        const row = createModelRow(result);
        modelsTableBody.appendChild(row);
    });

    updatePaginationInfo();
}

// 更新分页信息
function updatePaginationInfo() {
    const totalItems = allModelResults.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // 在标题旁边显示分页信息
    const progressText = document.getElementById('modelsProgressText');
    if (totalPages > 1) {
        progressText.textContent = `第 ${currentPage}/${totalPages} 页 (共 ${totalItems} 个模型)`;
    } else if (totalItems > 0) {
        progressText.textContent = `共 ${totalItems} 个模型`;
    }

    // 显示/隐藏分页控制
    const paginationControls = document.querySelector('.pagination-controls');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');

    if (totalPages > 1) {
        paginationControls.style.display = 'flex';
        prevBtn.disabled = currentPage <= 1;
        nextBtn.disabled = currentPage >= totalPages;
    } else {
        paginationControls.style.display = 'none';
    }
}

// 验证模型函数
async function verifyModel(modelName) {
    const apiUrl = document.getElementById('apiUrl').value;
    const apiKey = document.getElementById('apiKey').value;

    if (!apiUrl || !apiKey) {
        showNotification('请先输入API地址和密钥', 'error');
        return;
    }

    try {
        showNotification(`正在验证模型: ${modelName}...`, 'info');

        // 发送验证请求
        const result = await invoke('verify_model', {
            apiUrl,
            apiKey,
            model: modelName
        });

        // 显示验证结果
        showVerificationResult(modelName, result);

    } catch (error) {
        console.error('验证模型失败:', error);
        showNotification(`验证模型 ${modelName} 失败: ${error}`, 'error');
    }
}

// 显示验证结果
function showVerificationResult(modelName, result) {
    const resultText = `
模型: ${modelName}
响应: ${result.response || '无响应'}
状态: ${result.success ? '验证成功' : '验证失败'}
响应时间: ${result.response_time ? (result.response_time * 1000).toFixed(0) + 'ms' : '未知'}
    `.trim();

    // 创建弹窗显示结果
    const modal = document.createElement('div');
    modal.className = 'verification-modal';
    modal.innerHTML = `
        <div class="verification-modal-content">
            <div class="verification-header">
                <h3>模型验证结果</h3>
                <button class="verification-close">&times;</button>
            </div>
            <div class="verification-body">
                <pre>${resultText}</pre>
            </div>
            <div class="verification-footer">
                <button class="btn btn-primary verification-ok">确定</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 绑定关闭事件
    const closeBtn = modal.querySelector('.verification-close');
    const okBtn = modal.querySelector('.verification-ok');

    const closeModal = () => {
        document.body.removeChild(modal);
    };

    closeBtn.addEventListener('click', closeModal);
    okBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

// 测试表格显示功能
function testTableDisplay() {
    console.log('测试表格显示功能...');
    const testResults = [
        { model: 'gpt-4o-turbo', status: 'valid', response_time: 1.234, error: null },
        { model: 'claude-3-opus-20240229', status: 'invalid', response_time: null, error: 'Authentication failed' },
        { model: 'gemini-pro-1.5', status: 'inconsistent', response_time: 2.567, error: null },
        { model: 'gpt-3.5-turbo', status: 'valid', response_time: 0.856, error: null }
    ];

    displayModelResults(testResults);

    // 更新统计
    document.getElementById('validModelsCount').textContent = '2';
    document.getElementById('invalidModelsCount').textContent = '1';
    document.getElementById('inconsistentModelsCount').textContent = '1';

    showNotification('测试数据已显示', 'info');
}

// 在控制台暴露测试函数
window.testTableDisplay = testTableDisplay;

// 暴露验证模型函数到全局作用域
window.verifyModel = verifyModel;

// 暴露测试API调用的函数
window.testApiCall = async function() {
    const testModels = ['gpt-4o', 'claude-3-opus'];
    const apiUrl = document.getElementById('apiUrl').value;
    const apiKey = document.getElementById('apiKey').value;

    if (!apiUrl || !apiKey) {
        console.error('请先输入API地址和密钥');
        return;
    }

    try {
        console.log('测试API调用...', { apiUrl, models: testModels });
        const results = await invoke('test_api_models', { apiUrl, apiKey, models: testModels });
        console.log('API调用结果:', results);
        return results;
    } catch (error) {
        console.error('API调用失败:', error);
        return null;
    }
};

// 清空模型测试结果
function clearModelResults() {
    // 重置统计数据
    document.getElementById('validModelsCount').textContent = '0';
    document.getElementById('invalidModelsCount').textContent = '0';
    document.getElementById('inconsistentModelsCount').textContent = '0';

    // 清空模型表格和分页数据
    document.getElementById('modelsTableBody').innerHTML = '';
    allModelResults = [];
    currentPage = 1;

    // 隐藏进度条和分页控制
    document.getElementById('modelsProgress').style.display = 'none';
    document.querySelector('.pagination-controls').style.display = 'none';

    showNotification('模型测试结果已清空', 'info');
}

// 停止测试函数
function stopModelTesting() {
    if (isTestingModels) {
        shouldStopTesting = true;
        showNotification('正在停止测试...', 'info');
    }
}

// 显示模型选择弹窗
function showModelSelectionModal(models, apiUrl, apiKey) {
    const modal = document.getElementById('modelSelectionModal');
    const modelList = document.getElementById('modelSelectionList');
    const selectedCountSpan = document.getElementById('selectedModelCount');
    const filterInput = document.getElementById('modelFilterInput');

    // 存储原始模型列表和API信息
    modal.dataset.apiUrl = apiUrl;
    modal.dataset.apiKey = apiKey;
    modal.dataset.allModels = JSON.stringify(models);

    // 渲染模型列表
    renderModelList(models);

    // 显示弹窗
    modal.style.display = 'flex';

    // 重置选择状态
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const chatModelsCheckbox = document.getElementById('selectChatModelsCheckbox');
    selectAllCheckbox.checked = false;
    chatModelsCheckbox.checked = false;

    // 更新选中数量
    updateSelectedCount();
}

// 渲染模型列表
function renderModelList(models, filter = '') {
    const modelList = document.getElementById('modelSelectionList');

    // 过滤模型
    const filteredModels = filter ?
        models.filter(model => model.toLowerCase().includes(filter.toLowerCase())) :
        models;

    modelList.innerHTML = '';

    filteredModels.forEach(model => {
        const item = document.createElement('div');
        item.className = 'model-item-checkbox';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `model_${model.replace(/[^a-zA-Z0-9]/g, '_')}`;
        checkbox.value = model;
        checkbox.addEventListener('change', updateSelectedCount);

        const label = document.createElement('label');
        label.htmlFor = checkbox.id;
        label.textContent = model;

        item.appendChild(checkbox);
        item.appendChild(label);
        modelList.appendChild(item);
    });
}

// 更新选中数量
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('#modelSelectionList input[type="checkbox"]:checked');
    const count = checkboxes.length;
    document.getElementById('selectedModelCount').textContent = count;

    // 更新确定按钮状态
    const confirmBtn = document.getElementById('confirmModelSelectionBtn');
    confirmBtn.disabled = count === 0;
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const modelCheckboxes = document.querySelectorAll('#modelSelectionList input[type="checkbox"]');

    modelCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
}

// 选择聊天模型
function selectChatModels() {
    const chatModelsCheckbox = document.getElementById('selectChatModelsCheckbox');
    const modelCheckboxes = document.querySelectorAll('#modelSelectionList input[type="checkbox"]');

    modelCheckboxes.forEach(checkbox => {
        const modelName = checkbox.value.toLowerCase();
        // 排除图像生成模型
        const isImageModel = modelName.includes('dall-e') ||
                           modelName.includes('flux') ||
                           modelName.includes('image');

        if (chatModelsCheckbox.checked && !isImageModel) {
            checkbox.checked = true;
        } else if (!chatModelsCheckbox.checked) {
            checkbox.checked = false;
        }
    });

    updateSelectedCount();
}

// 筛选模型
function filterModels() {
    const modal = document.getElementById('modelSelectionModal');
    const allModels = JSON.parse(modal.dataset.allModels);
    const filterText = document.getElementById('modelFilterInput').value;

    renderModelList(allModels, filterText);
    updateSelectedCount();
}

// 清空选择
function clearSelection() {
    const modelCheckboxes = document.querySelectorAll('#modelSelectionList input[type="checkbox"]');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const chatModelsCheckbox = document.getElementById('selectChatModelsCheckbox');

    modelCheckboxes.forEach(checkbox => checkbox.checked = false);
    selectAllCheckbox.checked = false;
    chatModelsCheckbox.checked = false;

    updateSelectedCount();
}

// 确认选择并开始测试
function confirmModelSelection() {
    const modal = document.getElementById('modelSelectionModal');
    const selectedCheckboxes = document.querySelectorAll('#modelSelectionList input[type="checkbox"]:checked');

    if (selectedCheckboxes.length === 0) {
        showNotification('请至少选择一个模型', 'warning');
        return;
    }

    const selectedModels = Array.from(selectedCheckboxes).map(cb => cb.value);
    const apiUrl = modal.dataset.apiUrl;
    const apiKey = modal.dataset.apiKey;

    // 关闭弹窗
    closeModelSelectionModal();

    // 开始测试选中的模型
    executeModelTesting(selectedModels, apiUrl, apiKey);
}

// 关闭模型选择弹窗
function closeModelSelectionModal() {
    const modal = document.getElementById('modelSelectionModal');
    modal.style.display = 'none';

    // 清理数据
    delete modal.dataset.apiUrl;
    delete modal.dataset.apiKey;
    delete modal.dataset.allModels;
}

// 🚨 新增：初始化插件教程弹窗
function initializePluginTutorial() {
    const showBtn = document.getElementById('showPluginTutorialBtn');
    const modal = document.getElementById('pluginTutorialModal');
    const closeBtn = document.getElementById('closePluginTutorialBtn');
    const overlay = modal?.querySelector('.plugin-tutorial-overlay');

    if (showBtn && modal && closeBtn) {
        // 显示弹窗
        showBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });

        // 关闭弹窗
        const closeModal = () => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        };

        closeBtn.addEventListener('click', closeModal);

        // 点击遮罩层关闭
        if (overlay) {
            overlay.addEventListener('click', closeModal);
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeModal();
            }
        });
    }
}

// 🚨 新增：初始化万能工具箱弹窗
function initializeUniversalToolbox() {
    const showBtn = document.getElementById('universalToolboxBtn');
    const modal = document.getElementById('universalToolboxModal');
    const closeBtn = document.getElementById('closeUniversalToolboxBtn');
    const overlay = modal?.querySelector('.universal-toolbox-overlay');

    if (showBtn && modal && closeBtn) {
        // 显示弹窗
        showBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });

        // 关闭弹窗
        const closeModal = () => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        };

        closeBtn.addEventListener('click', closeModal);

        // 点击遮罩层关闭
        if (overlay) {
            overlay.addEventListener('click', closeModal);
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeModal();
            }
        });
    }
}

// 🚨 新增：初始化Cursor万能工具箱弹窗
function initializeCursorUniversalToolbox() {
    const showBtn = document.getElementById('cursorUniversalToolboxBtn');
    const modal = document.getElementById('cursorUniversalToolboxModal');
    const closeBtn = document.getElementById('closeCursorUniversalToolboxBtn');
    const overlay = modal?.querySelector('.universal-toolbox-overlay');

    if (showBtn && modal && closeBtn) {
        // 显示弹窗
        showBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });

        // 关闭弹窗
        const closeModal = () => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        };

        closeBtn.addEventListener('click', closeModal);

        // 点击遮罩层关闭
        if (overlay) {
            overlay.addEventListener('click', closeModal);
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeModal();
            }
        });
    }
}





// 工具函数：等待
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// ==================== VSCode Augment 账号管理 ====================

// 全局变量
let currentAugmentAccount = null;
let augmentAccounts = [];
let autoSwitchEnabled = false;

// 初始化Augment账号管理
function initializeAugmentAccountManager() {
    console.log('🔧 初始化Augment账号管理...');

    // 绑定事件监听器
    const refreshBtn = document.getElementById('refreshAccountsBtn');
    const autoSwitchBtn = document.getElementById('autoSwitchBtn');

    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshAugmentAccounts);
    }

    if (autoSwitchBtn) {
        autoSwitchBtn.addEventListener('click', toggleAutoSwitch);
    }

    // 初始加载账号列表
    refreshAugmentAccounts();

    // 启动定期刷新（每30秒）
    setInterval(refreshAugmentAccounts, 30000);

    console.log('✅ Augment账号管理初始化完成');
}

// 刷新账号列表
async function refreshAugmentAccounts() {
    try {
        console.log('🔄 刷新Augment账号列表...');

        const result = await invoke('get_augment_accounts');

        if (result.success) {
            augmentAccounts = result.data;
            updateAccountsDisplay();
            console.log(`✅ 成功加载 ${augmentAccounts.length} 个账号`);
        } else {
            console.error('❌ 获取账号列表失败:', result.message);
            showNotification('获取账号列表失败', 'error');
        }
    } catch (error) {
        console.error('❌ 刷新账号列表出错:', error);
        showNotification('刷新账号列表失败', 'error');
    }
}

// 更新账号显示
function updateAccountsDisplay() {
    updateCurrentAccountInfo();
    updateAccountsList();
}

// 更新当前账号信息
function updateCurrentAccountInfo() {
    const accountNameEl = document.getElementById('currentAccountName');
    const accountQuotaEl = document.getElementById('currentAccountQuota');

    if (currentAugmentAccount) {
        if (accountNameEl) {
            accountNameEl.textContent = currentAugmentAccount.account_name;
            accountNameEl.title = currentAugmentAccount.email;
        }
        if (accountQuotaEl) {
            accountQuotaEl.textContent = currentAugmentAccount.quota_remaining;
            accountQuotaEl.className = `quota-value ${getQuotaClass(currentAugmentAccount.quota_remaining)}`;
        }
    } else {
        if (accountNameEl) {
            accountNameEl.textContent = '未选择';
            accountNameEl.title = '';
        }
        if (accountQuotaEl) {
            accountQuotaEl.textContent = '-';
            accountQuotaEl.className = 'quota-value';
        }
    }
}

// 更新账号列表
function updateAccountsList() {
    const accountsList = document.getElementById('accountsList');
    if (!accountsList) return;

    if (augmentAccounts.length === 0) {
        accountsList.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                暂无可用账号
            </div>
        `;
        return;
    }

    const html = augmentAccounts.map(account => {
        const isActive = currentAugmentAccount && currentAugmentAccount.id === account.id;
        const statusClass = getAccountStatusClass(account);
        const quotaClass = getQuotaClass(account.quota_remaining);

        return `
            <div class="account-item ${statusClass} ${isActive ? 'active' : ''}"
                 onclick="switchToAccount(${account.id})"
                 data-account-id="${account.id}">
                <div class="account-details">
                    <div class="account-title">
                        ${account.account_name}
                        <span class="account-type-badge ${account.account_type}">
                            ${account.account_type === 'trial' ? '试用' : '正式'}
                        </span>
                    </div>
                    <div class="account-email" title="${account.email}">
                        ${account.email}
                    </div>
                    <div class="account-quota-info">
                        <span>剩余: <span class="${quotaClass}">${account.quota_remaining}</span>/${account.quota_total}</span>
                        <span>${formatLastUsed(account.last_used_at)}</span>
                    </div>
                </div>
                <div class="account-status">
                    <span class="status-dot ${account.status}"></span>
                    <span>${getStatusText(account.status)}</span>
                </div>
            </div>
        `;
    }).join('');

    accountsList.innerHTML = html;
}

// 切换到指定账号
async function switchToAccount(accountId) {
    try {
        console.log(`🔄 切换到账号ID: ${accountId}`);

        const account = augmentAccounts.find(acc => acc.id === accountId);
        if (!account) {
            showNotification('账号不存在', 'error');
            return;
        }

        if (account.status !== 'active' || account.quota_remaining <= 0) {
            showNotification('该账号不可用或额度已用完', 'warning');
            return;
        }

        // 显示切换中状态
        showNotification(`正在切换到账号: ${account.account_name}`, 'info');

        const result = await invoke('switch_augment_account', { accountId });

        if (result) {
            currentAugmentAccount = account;
            updateAccountsDisplay();
            showNotification(`✅ ${result}`, 'success');

            // 刷新账号列表以获取最新状态
            setTimeout(refreshAugmentAccounts, 2000);
        }
    } catch (error) {
        console.error('❌ 切换账号失败:', error);
        showNotification(`切换账号失败: ${error}`, 'error');
    }
}

// 切换自动切换模式
function toggleAutoSwitch() {
    autoSwitchEnabled = !autoSwitchEnabled;

    const btn = document.getElementById('autoSwitchBtn');
    if (btn) {
        if (autoSwitchEnabled) {
            btn.innerHTML = '<i class="fas fa-magic"></i> 智能切换中';
            btn.classList.remove('btn-success');
            btn.classList.add('btn-warning');
        } else {
            btn.innerHTML = '<i class="fas fa-magic"></i> 智能切换';
            btn.classList.remove('btn-warning');
            btn.classList.add('btn-success');
        }
    }

    showNotification(
        autoSwitchEnabled ? '✅ 智能切换已启用' : '⏹️ 智能切换已关闭',
        autoSwitchEnabled ? 'success' : 'info'
    );

    console.log(`🔄 自动切换模式: ${autoSwitchEnabled ? '启用' : '关闭'}`);
}

// 获取账号状态样式类
function getAccountStatusClass(account) {
    if (account.status === 'exhausted' || account.quota_remaining <= 0) {
        return 'exhausted';
    }
    if (account.status === 'disabled') {
        return 'disabled';
    }
    if (account.status === 'error') {
        return 'error';
    }
    return 'active';
}

// 获取额度样式类
function getQuotaClass(quota) {
    if (quota <= 0) return 'quota-exhausted';
    if (quota <= 5) return 'quota-low';
    if (quota <= 20) return 'quota-medium';
    return 'quota-high';
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'active': '可用',
        'exhausted': '已耗尽',
        'disabled': '已禁用',
        'error': '错误'
    };
    return statusMap[status] || '未知';
}

// 格式化最后使用时间
function formatLastUsed(dateString) {
    if (!dateString) return '未使用';

    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) {
            return '刚刚';
        } else if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        return '未知';
    }
}

// 自动切换账号（当前账号额度不足时）
async function autoSwitchAccount() {
    if (!autoSwitchEnabled) return;

    try {
        console.log('🔄 检查是否需要自动切换账号...');

        // 获取可用账号
        const result = await invoke('get_available_augment_accounts');

        if (result.success && result.data.length > 0) {
            const availableAccounts = result.data;

            // 如果当前账号额度不足，切换到第一个可用账号
            if (!currentAugmentAccount || currentAugmentAccount.quota_remaining <= 0) {
                const nextAccount = availableAccounts[0];
                console.log(`🔄 自动切换到账号: ${nextAccount.account_name}`);
                await switchToAccount(nextAccount.id);
            }
        } else {
            console.log('⚠️ 没有可用的账号进行自动切换');
            showNotification('没有可用的账号进行自动切换', 'warning');

            // 关闭自动切换
            autoSwitchEnabled = false;
            const btn = document.getElementById('autoSwitchBtn');
            if (btn) {
                btn.innerHTML = '<i class="fas fa-magic"></i> 智能切换';
                btn.classList.remove('btn-warning');
                btn.classList.add('btn-success');
            }
        }
    } catch (error) {
        console.error('❌ 自动切换账号失败:', error);
    }
}

// 启动自动切换检查（每分钟检查一次）
setInterval(() => {
    if (autoSwitchEnabled) {
        autoSwitchAccount();
    }
}, 60000); // 60秒

// 工具函数保留
// (其他模拟函数已删除，现在使用真实的Playwright自动化)
