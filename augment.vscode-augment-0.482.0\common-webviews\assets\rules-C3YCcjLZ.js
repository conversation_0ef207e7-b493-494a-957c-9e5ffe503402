import{S as I,i as S,s as T,a as q,b as H,H as V,w as W,x as J,y as Q,h as y,d as P,z as U,g as X,n as x,j,A as Y,B as Z,C as g,D as w,E as _,u as f,t as d,F as h,T as D,V as z,I as k,c as L,e as v,f as F,L as K,a8 as tt,r as et,ah as E,a6 as st,q as nt,a3 as R}from"./SpinnerAugment-Cx9dt_ox.js";import"./design-system-init-BCZOObrS.js";import{h as b,W as N}from"./BaseButton-BqzdgpkK.js";import{B as ot}from"./ButtonAugment-DhtPLzGu.js";import{O as at}from"./OpenFileButton-DgvbNVLn.js";import{C as rt,E as ct}from"./chat-flags-model-GjgruWjX.js";import{M as G,T as lt}from"./TextTooltipAugment-DTMpOwfF.js";import{M as it}from"./MarkdownEditor-ChD76zyi.js";import{R as O}from"./types-8LwCBeyq.js";import{R as pt}from"./RulesDropdown-Df3D6er-.js";import"./check-BrrMO4vE.js";import"./types-DDm27S8B.js";import"./chat-types-B-te1sXh.js";import"./index-BxQII05L.js";import"./lodash-Drc0SN5U.js";import"./file-paths-BcSg4gks.js";import"./utils-DJhaageo.js";import"./open-in-new-window-C_TwPNdv.js";import"./types-CGlLNakm.js";import"./Content-BiWRcmeV.js";import"./globals-D0QH3NT1.js";import"./IconButtonAugment-BjDqXmYl.js";import"./index-CGnj6T3o.js";import"./CardAugment-RumqAz-v.js";import"./TextAreaAugment-DEYj-_0J.js";import"./chevron-down-DYf4hfS2.js";function $t(n){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},n[0]],a={};for(let o=0;o<e.length;o+=1)a=q(a,e[o]);return{c(){t=H("svg"),s=new V(!0),this.h()},l(o){t=W(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=J(t);s=Q(r,!0),r.forEach(y),this.h()},h(){s.a=null,P(t,a)},m(o,r){U(o,t,r),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M15 239c-9.4 9.4-9.4 24.6 0 33.9L207 465c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9L65.9 256 241 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0z"/>',t)},p(o,[r]){P(t,a=X(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&r&&o[0]]))},i:x,o:x,d(o){o&&y(t)}}}function mt(n,t,s){return n.$$set=e=>{s(0,t=q(q({},t),j(e)))},[t=j(t)]}class ut extends I{constructor(t){super(),S(this,t,mt,$t,T,{})}}function ft(n){let t;return{c(){t=K("Back")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function dt(n){let t,s;return t=new ut({props:{slot:"iconLeft"}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p:x,i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function gt(n){let t,s;return t=new ot({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$slots:{iconLeft:[dt],default:[ft]},$$scope:{ctx:n}}}),t.$on("click",n[5]),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};2048&a&&(o.$$scope={dirty:a,ctx:e}),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function wt(n){let t;return{c(){t=K("Rules Trigger Mode:")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function ht(n){let t;return{c(){t=K("Open file")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function yt(n){let t,s;return t=new D({props:{slot:"text",size:1,$$slots:{default:[ht]},$$scope:{ctx:n}}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};2048&a&&(o.$$scope={dirty:a,ctx:e}),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function xt(n){let t,s,e,a,o,r,p,i,u,c,$;return e=new lt({props:{content:"Navigate back to all Rules & Guidelines",$$slots:{default:[gt]},$$scope:{ctx:n}}}),r=new D({props:{size:1,$$slots:{default:[wt]},$$scope:{ctx:n}}}),i=new pt({props:{onSave:n[4],alwaysApply:n[2]}}),c=new at({props:{size:1,path:n[1],onOpenLocalFile:n[6],$$slots:{text:[yt]},$$scope:{ctx:n}}}),{c(){t=z("div"),s=z("div"),g(e.$$.fragment),a=k(),o=z("div"),g(r.$$.fragment),p=k(),g(i.$$.fragment),u=k(),g(c.$$.fragment),L(o,"class","c-dropdown-with-label svelte-13rq98s"),L(s,"class","l-file-controls-left svelte-13rq98s"),L(t,"class","l-file-controls svelte-13rq98s"),L(t,"slot","header")},m(l,m){v(l,t,m),F(t,s),w(e,s,null),F(s,a),F(s,o),w(r,o,null),F(o,p),w(i,o,null),F(t,u),w(c,t,null),$=!0},p(l,m){const A={};2048&m&&(A.$$scope={dirty:m,ctx:l}),e.$set(A);const M={};2048&m&&(M.$$scope={dirty:m,ctx:l}),r.$set(M);const B={};4&m&&(B.alwaysApply=l[2]),i.$set(B);const C={};2&m&&(C.path=l[1]),2&m&&(C.onOpenLocalFile=l[6]),2048&m&&(C.$$scope={dirty:m,ctx:l}),c.$set(C)},i(l){$||(f(e.$$.fragment,l),f(r.$$.fragment,l),f(i.$$.fragment,l),f(c.$$.fragment,l),$=!0)},o(l){d(e.$$.fragment,l),d(r.$$.fragment,l),d(i.$$.fragment,l),d(c.$$.fragment,l),$=!1},d(l){l&&y(t),h(e),h(r),h(i),h(c)}}}function vt(n){let t,s,e;function a(r){n[8](r)}let o={saveFunction:n[7],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[xt]},$$scope:{ctx:n}};return n[0]!==void 0&&(o.value=n[0]),t=new it({props:o}),Y.push(()=>Z(t,"value",a)),{c(){g(t.$$.fragment)},m(r,p){w(t,r,p),e=!0},p(r,[p]){const i={};4&p&&(i.saveFunction=r[7]),2054&p&&(i.$$scope={dirty:p,ctx:r}),!s&&1&p&&(s=!0,i.value=r[0],_(()=>s=!1)),t.$set(i)},i(r){e||(f(t.$$.fragment,r),e=!0)},o(r){d(t.$$.fragment,r),e=!1},d(r){h(t,r)}}}function At(n,t,s){let{text:e}=t,{path:a}=t;const o=new G(b),r=new rt,p=new ct(b,o,r);let{alwaysApply:i}=t;const u=async c=>{const $=O.updateAlwaysApplyFrontmatterKey(e,c);p.saveFile({repoRoot:"",pathName:a,content:$})};return n.$$set=c=>{"text"in c&&s(0,e=c.text),"path"in c&&s(1,a=c.path),"alwaysApply"in c&&s(2,i=c.alwaysApply)},[e,a,i,p,u,()=>{b.postMessage({type:N.openSettingsPage,data:"guidelines"})},async()=>(p.openFile({repoRoot:"",pathName:a}),"success"),()=>u(i),function(c){e=c,s(0,e)}]}class Ft extends I{constructor(t){super(),S(this,t,At,vt,T,{text:0,path:1,alwaysApply:2})}}function Lt(n){let t;return{c(){t=z("div"),t.textContent="Loading..."},m(s,e){v(s,t,e)},p:x,i:x,o:x,d(s){s&&y(t)}}}function zt(n){let t,s;return t=new Ft({props:{text:n[0],path:n[1],alwaysApply:n[2]}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};1&a&&(o.text=e[0]),2&a&&(o.path=e[1]),4&a&&(o.alwaysApply=e[2]),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function bt(n){let t,s,e,a,o,r;const p=[zt,Lt],i=[];function u(c,$){return c[0]!==null&&c[1]!==null?0:1}return s=u(n),e=i[s]=p[s](n),{c(){t=z("div"),e.c(),L(t,"class","c-rules-container svelte-1vbu0zh")},m(c,$){v(c,t,$),i[s].m(t,null),a=!0,o||(r=tt(window,"message",n[3].onMessageFromExtension),o=!0)},p(c,[$]){let l=s;s=u(c),s===l?i[s].p(c,$):(nt(),d(i[l],1,1,()=>{i[l]=null}),et(),e=i[s],e?e.p(c,$):(e=i[s]=p[s](c),e.c()),f(e,1),e.m(t,null))},i(c){a||(f(e),a=!0)},o(c){d(e),a=!1},d(c){c&&y(t),i[s].d(),o=!1,r()}}}function Mt(n,t,s){let e,a,o;const r=new G(b),p=R(null);E(n,p,$=>s(0,e=$));const i=R("");E(n,i,$=>s(1,a=$));const u=R(!0);E(n,u,$=>s(2,o=$));const c={handleMessageFromExtension($){const l=$.data;if(l&&l.type===N.loadFile&&l){const m=l.data.content;if(m!==void 0){const A=m.replace(/^\n+/,""),M=O.getAlwaysApplyFrontmatterKey(A),B=O.extractContent(A);u.set(M),p.set(B)}i.set(l.data.pathName)}return!0}};return st(()=>{r.registerConsumer(c),b.postMessage({type:N.rulesLoaded})}),[e,a,o,r,p,i,u]}new class extends I{constructor(n){super(),S(this,n,Mt,bt,T,{})}}({target:document.getElementById("app")});
