/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.ide.util.PropertiesComponent
 *  com.intellij.openapi.application.PermanentInstallationID
 *  com.intellij.openapi.diagnostic.Logger
 *  org.jetbrains.annotations.NotNull
 */
package com.wangzai;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.application.PermanentInstallationID;
import com.intellij.openapi.diagnostic.Logger;
import com.wangzai.TrialSessionManager;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;

public class SessionId {
    private static final Logger LOG = Logger.getInstance(SessionId.class);
    @NotNull
    public static final SessionId INSTANCE = new SessionId();
    @NotNull
    private static final String SESSION_ID_KEY = "augment.session.id";

    private SessionId() {
    }

    @NotNull
    public String getSessionId() {
        String trialSessionId;
        LOG.info("\u83b7\u53d6SessionId");
        TrialSessionManager trialManager = TrialSessionManager.getInstance();
        if (trialManager.hasValidTrialSession() && (trialSessionId = trialManager.getTrialSessionId()) != null && !this.isBlank(trialSessionId)) {
            LOG.info("\u4f7f\u7528\u8bd5\u7528SessionId: " + trialSessionId);
            String string = trialSessionId;
            if (string == null) {
                SessionId.$$$reportNull$$$0(0);
            }
            return string;
        }
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String storedSessionID = properties.getValue(SESSION_ID_KEY);
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            String string = storedSessionID;
            if (string == null) {
                SessionId.$$$reportNull$$$0(1);
            }
            return string;
        }
        String installationID = PermanentInstallationID.get();
        if (installationID != null && !this.isBlank(installationID)) {
            String string = installationID;
            if (string == null) {
                SessionId.$$$reportNull$$$0(2);
            }
            return string;
        }
        String newSessionID = UUID.randomUUID().toString();
        properties.setValue(SESSION_ID_KEY, newSessionID);
        String string = newSessionID;
        if (string == null) {
            SessionId.$$$reportNull$$$0(3);
        }
        return string;
    }

    @NotNull
    public String resetSessionId() {
        String newSessionId = UUID.randomUUID().toString();
        PropertiesComponent.getInstance().setValue(SESSION_ID_KEY, newSessionId);
        String string = newSessionId;
        if (string == null) {
            SessionId.$$$reportNull$$$0(4);
        }
        return string;
    }

    private boolean isBlank(@NotNull String str) {
        if (str == null) {
            SessionId.$$$reportNull$$$0(5);
        }
        return str.trim().isEmpty();
    }

    @NotNull
    public String getStoredSessionId() {
        String storedSessionID = PropertiesComponent.getInstance().getValue(SESSION_ID_KEY);
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            String string = storedSessionID;
            if (string == null) {
                SessionId.$$$reportNull$$$0(6);
            }
            return string;
        }
        String installationID = PermanentInstallationID.get();
        if (installationID != null && !this.isBlank(installationID)) {
            String string = installationID;
            if (string == null) {
                SessionId.$$$reportNull$$$0(7);
            }
            return string;
        }
        return "";
    }

    public boolean hasValidSessionId() {
        String storedSessionID = PropertiesComponent.getInstance().getValue(SESSION_ID_KEY);
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return true;
        }
        String installationID = PermanentInstallationID.get();
        return installationID != null && !this.isBlank(installationID);
    }

    public void clearStoredSessionId() {
        PropertiesComponent.getInstance().unsetValue(SESSION_ID_KEY);
    }

    @NotNull
    public String getSessionIdSource() {
        TrialSessionManager trialManager = TrialSessionManager.getInstance();
        if (trialManager.hasValidTrialSession()) {
            return "TrialSession";
        }
        String storedSessionID = PropertiesComponent.getInstance().getValue(SESSION_ID_KEY);
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return "PropertiesComponent";
        }
        String installationID = PermanentInstallationID.get();
        if (installationID != null && !this.isBlank(installationID)) {
            return "PermanentInstallationID";
        }
        return "Generated";
    }

    @NotNull
    public String getSessionIdInfo() {
        String sessionId = this.getSessionId();
        String source = this.getSessionIdSource();
        String string = String.format("SessionID: %s (Source: %s)", sessionId, source);
        if (string == null) {
            SessionId.$$$reportNull$$$0(8);
        }
        return string;
    }

    private static /* synthetic */ void $$$reportNull$$$0(int n) {
        Object[] objectArray;
        Object[] objectArray2;
        Object[] objectArray3 = new Object[switch (n) {
            default -> 2;
            case 5 -> 3;
        }];
        switch (n) {
            default: {
                objectArray2 = objectArray3;
                objectArray3[0] = "com/wangzai/SessionId";
                break;
            }
            case 5: {
                objectArray2 = objectArray3;
                objectArray3[0] = "str";
                break;
            }
        }
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[1] = "getSessionId";
                break;
            }
            case 4: {
                objectArray = objectArray2;
                objectArray2[1] = "resetSessionId";
                break;
            }
            case 5: {
                objectArray = objectArray2;
                objectArray2[1] = "com/wangzai/SessionId";
                break;
            }
            case 6: 
            case 7: {
                objectArray = objectArray2;
                objectArray2[1] = "getStoredSessionId";
                break;
            }
            case 8: {
                objectArray = objectArray2;
                objectArray2[1] = "getSessionIdInfo";
                break;
            }
        }
        switch (n) {
            default: {
                break;
            }
            case 5: {
                objectArray = objectArray;
                objectArray[2] = "isBlank";
                break;
            }
        }
        String string = String.format(v0, objectArray);
        throw switch (n) {
            default -> new IllegalStateException(string);
            case 5 -> new IllegalArgumentException(string);
        };
    }
}

