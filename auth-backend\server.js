require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const session = require('express-session');
const path = require('path');
const { initDatabase, getConnection, closeDatabase, safeQuery } = require('./database');

const app = express();
const PORT = parseInt(process.env.SERVER_PORT) || 7761;
const SERVER_URL = process.env.SERVER_URL;

// 中间件配置
app.use(cors({
    origin: true,
    credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Session配置
app.use(session({
    secret: process.env.SESSION_SECRET || 'magic-box-admin-secret-key-2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // 强制使用HTTP，即使在生产环境
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
}));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 下载文件服务
app.use('/downloads', express.static(path.join(__dirname, 'downloads')));

// 导入路由
const authRoutes = require('./routes/auth');
const webRoutes = require('./routes/web');
const updateRoutes = require('./routes/update');
const { router: adminRoutes } = require('./routes/admin');
const announcementRoutes = require('./routes/announcement');
const augmentRoutes = require('./routes/augment');

// 使用路由
app.use('/api/auth', authRoutes);
app.use('/api/update', updateRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/announcement', announcementRoutes);
app.use('/api/augment', augmentRoutes);
app.use('/', webRoutes);

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? err.message : '服务器错误'
    });
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 启动服务器
async function startServer() {
    try {
        // 初始化数据库
        await initDatabase();

        // 移除定时任务，改为实时检查
        console.log('📋 过期检查策略: 用户验证时实时检查，无需定时任务');

        // 启动HTTP服务器
        app.listen(PORT, () => {
            console.log('🚀 ========================================');
            console.log('✨ Magic Box 授权管理系统后台启动成功！');
            console.log('🚀 ========================================');
            console.log(`🌐 服务器地址: ${SERVER_URL}`);
            console.log(`🌐 管理界面: ${SERVER_URL}/admin`);
            console.log(`🔑 API接口: ${SERVER_URL}/api/auth`);
            console.log('🚀 ========================================');
            console.log('📊 系统状态: 运行中');
            console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
            console.log('⏰ 过期检查: 用户验证时实时检查');
            console.log('🚀 ========================================');
        });

    } catch (error) {
        console.error('❌ 服务器启动失败:', error.message);
        process.exit(1);
    }
}

// 移除了定时检查任务
// 现在过期检查在用户验证时实时进行，避免频繁的数据库连接

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n🔄 正在关闭服务器...');
    const { closeDatabase } = require('./database');
    await closeDatabase();
    console.log('✅ 服务器已关闭');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🔄 正在关闭服务器...');
    const { closeDatabase } = require('./database');
    await closeDatabase();
    console.log('✅ 服务器已关闭');
    process.exit(0);
});

// 启动服务器
startServer();
