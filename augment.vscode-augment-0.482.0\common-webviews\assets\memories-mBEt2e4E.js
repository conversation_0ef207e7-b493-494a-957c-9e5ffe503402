import{S as W,i as j,s as P,a as K,b as ht,H as vt,w as xt,x as wt,y as yt,h as b,d as Z,z as bt,g as Rt,n as M,j as tt,J as ct,e as R,u as p,q as k,t as m,r as q,ah as O,a6 as lt,G as Ct,a3 as N,A as E,C as h,D as v,F as x,B as I,E as B,L as D,I as H,V as z,c as S,f as T,K as Mt,T as ut,M as $t,a8 as Lt,a5 as St}from"./SpinnerAugment-Cx9dt_ox.js";import"./design-system-init-BCZOObrS.js";import{h as _,W as V,e as et}from"./BaseButton-BqzdgpkK.js";import{S as Ft,O as Gt}from"./OpenFileButton-DgvbNVLn.js";import{C as Et,E as zt}from"./chat-flags-model-GjgruWjX.js";import{T as dt,M as pt}from"./TextTooltipAugment-DTMpOwfF.js";import{M as Tt}from"./MarkdownEditor-ChD76zyi.js";import{M as J,A as It}from"./types-8LwCBeyq.js";import{D as A}from"./index-CGnj6T3o.js";import{B as mt}from"./ButtonAugment-DhtPLzGu.js";import{C as ft}from"./chevron-down-DYf4hfS2.js";import{F as Bt}from"./Filespan-D-BqE8vd.js";import{T as gt}from"./Content-BiWRcmeV.js";import"./check-BrrMO4vE.js";import"./types-DDm27S8B.js";import"./chat-types-B-te1sXh.js";import"./index-BxQII05L.js";import"./lodash-Drc0SN5U.js";import"./file-paths-BcSg4gks.js";import"./utils-DJhaageo.js";import"./open-in-new-window-C_TwPNdv.js";import"./types-CGlLNakm.js";import"./IconButtonAugment-BjDqXmYl.js";import"./TextAreaAugment-DEYj-_0J.js";import"./CardAugment-RumqAz-v.js";import"./globals-D0QH3NT1.js";function Ot(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=K(s,e[o]);return{c(){t=ht("svg"),n=new vt(!0),this.h()},l(o){t=xt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=wt(t);n=yt(c,!0),c.forEach(b),this.h()},h(){n.a=null,Z(t,s)},m(o,c){bt(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',t)},p(o,[c]){Z(t,s=Rt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function kt(r,t,n){return r.$$set=e=>{n(0,t=K(K({},t),tt(e)))},[t=tt(t)]}class Q extends W{constructor(t){super(),j(this,t,kt,Ot,P,{})}}function nt(r,t,n){const e=r.slice();return e[23]=t[n],e[25]=n,e}function st(r){let t,n,e,s;const o=[At,qt],c=[];function u(a,l){return a[0]?0:1}return t=u(r),n=c[t]=o[t](r),{c(){n.c(),e=ct()},m(a,l){c[t].m(a,l),R(a,e,l),s=!0},p(a,l){let i=t;t=u(a),t===i?c[t].p(a,l):(k(),m(c[i],1,1,()=>{c[i]=null}),q(),n=c[t],n?n.p(a,l):(n=c[t]=o[t](a),n.c()),p(n,1),n.m(e.parentNode,e))},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&b(e),c[t].d(a)}}}function qt(r){let t,n,e={content:r[5],triggerOn:[gt.Hover],side:"top",$$slots:{default:[_t]},$$scope:{ctx:r}};return t=new dt({props:e}),r[21](t),{c(){h(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){const c={};32&o&&(c.content=s[5]),67108864&o&&(c.$$scope={dirty:o,ctx:s}),t.$set(c)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){m(t.$$.fragment,s),n=!1},d(s){r[21](null),x(t,s)}}}function At(r){let t,n,e,s;function o(a){r[19](a)}function c(a){r[20](a)}let u={$$slots:{default:[Yt]},$$scope:{ctx:r}};return r[3]!==void 0&&(u.requestClose=r[3]),r[2]!==void 0&&(u.focusedIndex=r[2]),t=new A.Root({props:u}),E.push(()=>I(t,"requestClose",o)),E.push(()=>I(t,"focusedIndex",c)),{c(){h(t.$$.fragment)},m(a,l){v(t,a,l),s=!0},p(a,l){const i={};67109746&l&&(i.$$scope={dirty:l,ctx:a}),!n&&8&l&&(n=!0,i.requestClose=a[3],B(()=>n=!1)),!e&&4&l&&(e=!0,i.focusedIndex=a[2],B(()=>e=!1)),t.$set(i)},i(a){s||(p(t.$$.fragment,a),s=!0)},o(a){m(t.$$.fragment,a),s=!1},d(a){x(t,a)}}}function Dt(r){let t;return{c(){t=D("Rules")},m(n,e){R(n,t,e)},d(n){n&&b(t)}}}function Nt(r){let t,n;return t=new Q({props:{slot:"iconLeft"}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:M,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Ht(r){let t,n;return t=new ft({props:{slot:"iconRight"}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:M,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function _t(r){let t,n;return t=new mt({props:{color:"neutral",variant:"soft",size:1,disabled:!0,$$slots:{iconRight:[Ht],iconLeft:[Nt],default:[Dt]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};67108864&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Ut(r){let t,n=(r[8]?r[8].path:"Rules")+"";return{c(){t=D(n)},m(e,s){R(e,t,s)},p(e,s){256&s&&n!==(n=(e[8]?e[8].path:"Rules")+"")&&$t(t,n)},d(e){e&&b(t)}}}function Vt(r){let t,n;return t=new Q({props:{slot:"iconLeft"}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:M,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Wt(r){let t,n;return t=new ft({props:{slot:"iconRight"}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:M,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function jt(r){let t,n;return t=new mt({props:{color:"neutral",variant:"soft",size:1,disabled:r[6],$$slots:{iconRight:[Wt],iconLeft:[Vt],default:[Ut]},$$scope:{ctx:r}}}),t.$on("click",r[14]),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};64&s&&(o.disabled=e[6]),67109120&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Pt(r){let t,n,e={content:r[5],triggerOn:[gt.Hover],side:"top",$$slots:{default:[jt]},$$scope:{ctx:r}};return t=new dt({props:e}),r[17](t),{c(){h(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){const c={};32&o&&(c.content=s[5]),67109184&o&&(c.$$scope={dirty:o,ctx:s}),t.$set(c)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){m(t.$$.fragment,s),n=!1},d(s){r[17](null),x(t,s)}}}function Jt(r){let t,n;return t=new Bt({props:{filepath:r[23].path}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};2&s&&(o.filepath=e[23].path),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ot(r){let t,n;function e(){return r[18](r[23])}return t=new A.Item({props:{onSelect:e,highlight:r[9]===r[25],$$slots:{default:[Jt]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){r=s;const c={};2&o&&(c.onSelect=e),512&o&&(c.highlight=r[9]===r[25]),67108866&o&&(c.$$scope={dirty:o,ctx:r}),t.$set(c)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){m(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function rt(r){let t,n,e,s;return t=new A.Separator({}),e=new A.Label({props:{$$slots:{default:[Qt]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment),n=H(),h(e.$$.fragment)},m(o,c){v(t,o,c),R(o,n,c),v(e,o,c),s=!0},p(o,c){const u={};67109378&c&&(u.$$scope={dirty:c,ctx:o}),e.$set(u)},i(o){s||(p(t.$$.fragment,o),p(e.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(e.$$.fragment,o),s=!1},d(o){o&&b(n),x(t,o),x(e,o)}}}function Kt(r){let t,n=at(r[1][r[9]])+"";return{c(){t=D(n)},m(e,s){R(e,t,s)},p(e,s){514&s&&n!==(n=at(e[1][e[9]])+"")&&$t(t,n)},d(e){e&&b(t)}}}function Qt(r){let t,n;return t=new ut({props:{size:1,color:"neutral",$$slots:{default:[Kt]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};67109378&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Xt(r){let t,n,e,s=et(r[1]),o=[];for(let a=0;a<s.length;a+=1)o[a]=ot(nt(r,s,a));const c=a=>m(o[a],1,1,()=>{o[a]=null});let u=r[9]!==void 0&&r[1][r[9]]&&rt(r);return{c(){t=z("div");for(let a=0;a<o.length;a+=1)o[a].c();n=H(),u&&u.c(),S(t,"class","rules-dropdown-content svelte-18wohv")},m(a,l){R(a,t,l);for(let i=0;i<o.length;i+=1)o[i]&&o[i].m(t,null);T(t,n),u&&u.m(t,null),e=!0},p(a,l){if(8706&l){let i;for(s=et(a[1]),i=0;i<s.length;i+=1){const d=nt(a,s,i);o[i]?(o[i].p(d,l),p(o[i],1)):(o[i]=ot(d),o[i].c(),p(o[i],1),o[i].m(t,n))}for(k(),i=s.length;i<o.length;i+=1)c(i);q()}a[9]!==void 0&&a[1][a[9]]?u?(u.p(a,l),514&l&&p(u,1)):(u=rt(a),u.c(),p(u,1),u.m(t,null)):u&&(k(),m(u,1,1,()=>{u=null}),q())},i(a){if(!e){for(let l=0;l<s.length;l+=1)p(o[l]);p(u),e=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)m(o[l]);m(u),e=!1},d(a){a&&b(t),Mt(o,a),u&&u.d()}}}function Yt(r){let t,n,e,s;return t=new A.Trigger({props:{$$slots:{default:[Pt]},$$scope:{ctx:r}}}),e=new A.Content({props:{side:"bottom",align:"start",$$slots:{default:[Xt]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment),n=H(),h(e.$$.fragment)},m(o,c){v(t,o,c),R(o,n,c),v(e,o,c),s=!0},p(o,c){const u={};67109232&c&&(u.$$scope={dirty:c,ctx:o}),t.$set(u);const a={};67109378&c&&(a.$$scope={dirty:c,ctx:o}),e.$set(a)},i(o){s||(p(t.$$.fragment,o),p(e.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(e.$$.fragment,o),s=!1},d(o){o&&b(n),x(t,o),x(e,o)}}}function Zt(r){let t,n,e=!r[7]&&st(r);return{c(){e&&e.c(),t=ct()},m(s,o){e&&e.m(s,o),R(s,t,o),n=!0},p(s,[o]){s[7]?e&&(k(),m(e,1,1,()=>{e=null}),q()):e?(e.p(s,o),128&o&&p(e,1)):(e=st(s),e.c(),p(e,1),e.m(t.parentNode,t))},i(s){n||(p(e),n=!0)},o(s){m(e),n=!1},d(s){s&&b(t),e&&e.d(s)}}}function at(r){return`Move to ${r.path}`}function te(r,t,n){let e,s,o,c,u,a,l,i=M,d=()=>(i(),i=Ct(L,g=>n(9,l=g)),L);r.$$.on_destroy.push(()=>i());let{onRuleSelected:$}=t,{disabled:w=!1}=t;const y=N([]);O(r,y,g=>n(1,c=g));const f=N(!0);O(r,f,g=>n(7,u=g));const F=N(void 0);let L;O(r,F,g=>n(8,a=g)),d();let C,G=()=>{};function U(g){F.set(g),$(g),G()}return lt(()=>{f.set(!0),_.postMessage({type:V.getRulesListRequest,data:{query:"",maxResults:100}});const g=X=>{var Y;((Y=X.data)==null?void 0:Y.type)===V.getRulesListResponse&&(y.set(X.data.data||[]),f.set(!1))};return window.addEventListener("message",g),()=>{window.removeEventListener("message",g)}}),r.$$set=g=>{"onRuleSelected"in g&&n(15,$=g.onRuleSelected),"disabled"in g&&n(16,w=g.disabled)},r.$$.update=()=>{2&r.$$.dirty&&n(0,e=c.length>0),65537&r.$$.dirty&&n(6,s=w||!e),1&r.$$.dirty&&n(5,o=e?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")},[e,c,L,G,C,o,s,u,a,l,y,f,F,U,function(){C&&C.requestClose()},$,w,function(g){E[g?"unshift":"push"](()=>{C=g,n(4,C)})},g=>U(g),function(g){G=g,n(3,G)},function(g){L=g,d(n(2,L))},function(g){E[g?"unshift":"push"](()=>{C=g,n(4,C)})}]}class ee extends W{constructor(t){super(),j(this,t,te,Zt,P,{onRuleSelected:15,disabled:16})}}function ne(r){let t;return{c(){t=D("User Guidelines")},m(n,e){R(n,t,e)},d(n){n&&b(t)}}}function se(r){let t,n,e;return n=new Q({}),{c(){t=z("div"),h(n.$$.fragment),S(t,"slot","iconLeft"),S(t,"class","c-move-text-btn__left_icon svelte-1yddhs6")},m(s,o){R(s,t,o),v(n,t,null),e=!0},p:M,i(s){e||(p(n.$$.fragment,s),e=!0)},o(s){m(n.$$.fragment,s),e=!1},d(s){s&&b(t),x(n)}}}function it(r){let t,n,e;return n=new ee({props:{onRuleSelected:r[10],disabled:!r[2]}}),{c(){t=z("div"),h(n.$$.fragment),S(t,"class","c-move-text-btn svelte-1yddhs6")},m(s,o){R(s,t,o),v(n,t,null),e=!0},p(s,o){const c={};4&o&&(c.disabled=!s[2]),n.$set(c)},i(s){e||(p(n.$$.fragment,s),e=!0)},o(s){m(n.$$.fragment,s),e=!1},d(s){s&&b(t),x(n)}}}function oe(r){let t;return{c(){t=D("Open Memories")},m(n,e){R(n,t,e)},d(n){n&&b(t)}}}function re(r){let t,n;return t=new ut({props:{slot:"text",size:1,$$slots:{default:[oe]},$$scope:{ctx:r}}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1048576&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ae(r){let t,n,e,s,o,c,u,a,l;s=new Ft({props:{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:r[12],disabled:!r[2],stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,$$slots:{iconLeft:[se],default:[ne]},$$scope:{ctx:r}}});let i=r[5]&&it(r);return a=new Gt({props:{size:1,path:r[1],variant:"soft",onOpenLocalFile:r[13],$$slots:{text:[re]},$$scope:{ctx:r}}}),{c(){t=z("div"),n=z("div"),e=z("div"),h(s.$$.fragment),o=H(),i&&i.c(),c=H(),u=z("div"),h(a.$$.fragment),S(e,"class","c-move-text-btn svelte-1yddhs6"),S(n,"class","l-file-controls-left svelte-1yddhs6"),S(u,"class","l-file-controls-right svelte-1yddhs6"),S(t,"class","l-file-controls svelte-1yddhs6"),S(t,"slot","header")},m(d,$){R(d,t,$),T(t,n),T(n,e),v(s,e,null),T(n,o),i&&i.m(n,null),T(t,c),T(t,u),v(a,u,null),l=!0},p(d,$){const w={};4&$&&(w.disabled=!d[2]),1048576&$&&(w.$$scope={dirty:$,ctx:d}),s.$set(w),d[5]?i?(i.p(d,$),32&$&&p(i,1)):(i=it(d),i.c(),p(i,1),i.m(n,null)):i&&(k(),m(i,1,1,()=>{i=null}),q());const y={};2&$&&(y.path=d[1]),2&$&&(y.onOpenLocalFile=d[13]),1048576&$&&(y.$$scope={dirty:$,ctx:d}),a.$set(y)},i(d){l||(p(s.$$.fragment,d),p(i),p(a.$$.fragment,d),l=!0)},o(d){m(s.$$.fragment,d),m(i),m(a.$$.fragment,d),l=!1},d(d){d&&b(t),x(s),i&&i.d(),x(a)}}}function ie(r){let t,n,e,s,o,c;function u($){r[14]($)}function a($){r[15]($)}function l($){r[16]($)}function i($){r[17]($)}let d={saveFunction:r[8],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[ae]},$$scope:{ctx:r}};return r[2]!==void 0&&(d.selectedText=r[2]),r[3]!==void 0&&(d.selectionStart=r[3]),r[4]!==void 0&&(d.selectionEnd=r[4]),r[0]!==void 0&&(d.value=r[0]),t=new Tt({props:d}),E.push(()=>I(t,"selectedText",u)),E.push(()=>I(t,"selectionStart",a)),E.push(()=>I(t,"selectionEnd",l)),E.push(()=>I(t,"value",i)),{c(){h(t.$$.fragment)},m($,w){v(t,$,w),c=!0},p($,[w]){const y={};1048614&w&&(y.$$scope={dirty:w,ctx:$}),!n&&4&w&&(n=!0,y.selectedText=$[2],B(()=>n=!1)),!e&&8&w&&(e=!0,y.selectionStart=$[3],B(()=>e=!1)),!s&&16&w&&(s=!0,y.selectionEnd=$[4],B(()=>s=!1)),!o&&1&w&&(o=!0,y.value=$[0],B(()=>o=!1)),t.$set(y)},i($){c||(p(t.$$.fragment,$),c=!0)},o($){m(t.$$.fragment,$),c=!1},d($){x(t,$)}}}function ce(r,t,n){let e,s,{text:o}=t,{path:c}=t;const u=new pt(_),a=new Et;O(r,a,f=>n(11,s=f));const l=new zt(_,u,a);(async function(){try{const f=await l.getChatInitData();a.update({enableRules:f.enableRules??!1,enableDebugFeatures:f.enableDebugFeatures??!1})}catch(f){console.error("Failed to initialize flags:",f)}})();let i="",d=0,$=0;const w=async()=>{c&&l.saveFile({repoRoot:"",pathName:c,content:o})};async function y(f){if(!i)return;let F,L,C;const G=i.slice(0,20);if(f==="userGuidelines"?(F="Move Content to User Guidelines",L=`Are you sure you want to move the selected content "${G}" to your user guidelines?`,C=J.userGuidelines):f==="augmentGuidelines"?(F="Move Content to Workspace Guidelines",L=`Are you sure you want to move the selected content "${G}" to workspace guidelines?`,C=J.augmentGuidelines):(F="Move Content to Rule",L=`Are you sure you want to move the selected content "${G}" to rule file "${f.rule.path}"?`,C=J.rules),!await l.openConfirmationModal({title:F,message:L,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;f==="userGuidelines"?l.updateUserGuidelines(i):f==="augmentGuidelines"?l.updateWorkspaceGuidelines(i):l.updateRuleFile(f.rule.path,i);const U=o.substring(0,d)+o.substring($);return n(0,o=U),await w(),l.reportAgentSessionEvent({eventName:It.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:C}}}),"success"}return r.$$set=f=>{"text"in f&&n(0,o=f.text),"path"in f&&n(1,c=f.path)},r.$$.update=()=>{2048&r.$$.dirty&&n(5,e=s.enableRules)},[o,c,i,d,$,e,a,l,w,y,async function(f){await y({type:"rule",rule:f})},s,()=>y("userGuidelines"),async()=>(l.openFile({repoRoot:"",pathName:c}),"success"),function(f){i=f,n(2,i)},function(f){d=f,n(3,d)},function(f){$=f,n(4,$)},function(f){o=f,n(0,o)}]}class le extends W{constructor(t){super(),j(this,t,ce,ie,P,{text:0,path:1})}}function ue(r){let t;return{c(){t=D("Loading memories...")},m(n,e){R(n,t,e)},p:M,i:M,o:M,d(n){n&&b(t)}}}function $e(r){let t,n;return t=new le({props:{text:r[0],path:r[1]}}),{c(){h(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1&s&&(o.text=e[0]),2&s&&(o.path=e[1]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){m(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function de(r){let t,n,e,s,o,c;const u=[$e,ue],a=[];function l(i,d){return i[0]!==null&&i[1]!==null?0:1}return n=l(r),e=a[n]=u[n](r),{c(){t=z("div"),e.c(),S(t,"class","c-memories-container svelte-1vchs21")},m(i,d){R(i,t,d),a[n].m(t,null),s=!0,o||(c=Lt(window,"message",r[2].onMessageFromExtension),o=!0)},p(i,[d]){let $=n;n=l(i),n===$?a[n].p(i,d):(k(),m(a[$],1,1,()=>{a[$]=null}),q(),e=a[n],e?e.p(i,d):(e=a[n]=u[n](i),e.c()),p(e,1),e.m(t,null))},i(i){s||(p(e),s=!0)},o(i){m(e),s=!1},d(i){i&&b(t),a[n].d(),o=!1,c()}}}function pe(r,t,n){let e,s;const o=new pt(_),c=N(null);O(r,c,l=>n(0,e=l));const u=N(null);O(r,u,l=>n(1,s=l));const a={handleMessageFromExtension(l){const i=l.data;if(i&&i.type===V.loadFile){if(i.data.content!==void 0){const d=i.data.content.replace(/^\n+/,"");c.set(d)}i.data.pathName&&u.set(i.data.pathName)}return!0}};return lt(()=>{o.registerConsumer(a),_.postMessage({type:V.memoriesLoaded})}),St(()=>{o.dispose()}),[e,s,o,c,u]}new class extends W{constructor(r){super(),j(this,r,pe,de,P,{})}}({target:document.getElementById("app")});
