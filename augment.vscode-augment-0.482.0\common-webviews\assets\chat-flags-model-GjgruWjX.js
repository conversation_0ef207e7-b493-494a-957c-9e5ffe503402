var Ye=Object.defineProperty;var Ze=(t,e,s)=>e in t?Ye(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var o=(t,e,s)=>Ze(t,typeof e!="symbol"?e+"":e,s);import{T as x,p as Je,a as Qe,b as et,W as tt,d as _,e as he,g as st,r as ge,s as nt,h as me,S as at,i as rt}from"./types-8LwCBeyq.js";import{W as d,S as it}from"./BaseButton-BqzdgpkK.js";import{P as B,C as ot}from"./chat-types-B-te1sXh.js";import{n as lt}from"./file-paths-BcSg4gks.js";var C,u;function ee(t,e){return!(t===null||typeof t!="object"||!("$typeName"in t)||typeof t.$typeName!="string")&&(e===void 0||e.typeName===t.$typeName)}function ut(){let t=0,e=0;for(let n=0;n<28;n+=7){let a=this.buf[this.pos++];if(t|=(127&a)<<n,!(128&a))return this.assertBounds(),[t,e]}let s=this.buf[this.pos++];if(t|=(15&s)<<28,e=(112&s)>>4,!(128&s))return this.assertBounds(),[t,e];for(let n=3;n<=31;n+=7){let a=this.buf[this.pos++];if(e|=(127&a)<<n,!(128&a))return this.assertBounds(),[t,e]}throw new Error("invalid varint")}(function(t){t[t.Canceled=1]="Canceled",t[t.Unknown=2]="Unknown",t[t.InvalidArgument=3]="InvalidArgument",t[t.DeadlineExceeded=4]="DeadlineExceeded",t[t.NotFound=5]="NotFound",t[t.AlreadyExists=6]="AlreadyExists",t[t.PermissionDenied=7]="PermissionDenied",t[t.ResourceExhausted=8]="ResourceExhausted",t[t.FailedPrecondition=9]="FailedPrecondition",t[t.Aborted=10]="Aborted",t[t.OutOfRange=11]="OutOfRange",t[t.Unimplemented=12]="Unimplemented",t[t.Internal=13]="Internal",t[t.Unavailable=14]="Unavailable",t[t.DataLoss=15]="DataLoss",t[t.Unauthenticated=16]="Unauthenticated"})(C||(C={})),function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"}(u||(u={}));const j=4294967296;function fe(t){const e=t[0]==="-";e&&(t=t.slice(1));const s=1e6;let n=0,a=0;function r(i,l){const c=Number(t.slice(i,l));a*=s,n=n*s+c,n>=j&&(a+=n/j|0,n%=j)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),e?Re(n,a):te(n,a)}function ye(t,e){if({lo:t,hi:e}=function(c,g){return{lo:c>>>0,hi:g>>>0}}(t,e),e<=2097151)return String(j*e+t);const s=16777215&(t>>>24|e<<8),n=e>>16&65535;let a=(16777215&t)+6777216*s+6710656*n,r=s+8147497*n,i=2*n;const l=1e7;return a>=l&&(r+=Math.floor(a/l),a%=l),r>=l&&(i+=Math.floor(r/l),r%=l),i.toString()+pe(r)+pe(a)}function te(t,e){return{lo:0|t,hi:0|e}}function Re(t,e){return e=~e,t?t=1+~t:e+=1,te(t,e)}const pe=t=>{const e=String(t);return"0000000".slice(e.length)+e};function ct(){let t=this.buf[this.pos++],e=127&t;if(!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<7,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<14,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<21,!(128&t))return this.assertBounds(),e;t=this.buf[this.pos++],e|=(15&t)<<28;for(let s=5;128&t&&s<10;s++)t=this.buf[this.pos++];if(128&t)throw new Error("invalid varint");return this.assertBounds(),e>>>0}var be={};const v=dt();function dt(){const t=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"&&(typeof process!="object"||typeof be!="object"||be.BUF_BIGINT_DISABLE!=="1")){const e=BigInt("-9223372036854775808"),s=BigInt("9223372036854775807"),n=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(r){const i=typeof r=="bigint"?r:BigInt(r);if(i>s||i<e)throw new Error(`invalid int64: ${r}`);return i},uParse(r){const i=typeof r=="bigint"?r:BigInt(r);if(i>a||i<n)throw new Error(`invalid uint64: ${r}`);return i},enc(r){return t.setBigInt64(0,this.parse(r),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},uEnc(r){return t.setBigInt64(0,this.uParse(r),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},dec:(r,i)=>(t.setInt32(0,r,!0),t.setInt32(4,i,!0),t.getBigInt64(0,!0)),uDec:(r,i)=>(t.setInt32(0,r,!0),t.setInt32(4,i,!0),t.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:e=>(typeof e!="string"&&(e=e.toString()),_e(e),e),uParse:e=>(typeof e!="string"&&(e=e.toString()),Se(e),e),enc:e=>(typeof e!="string"&&(e=e.toString()),_e(e),fe(e)),uEnc:e=>(typeof e!="string"&&(e=e.toString()),Se(e),fe(e)),dec:(e,s)=>function(n,a){let r=te(n,a);const i=2147483648&r.hi;i&&(r=Re(r.lo,r.hi));const l=ye(r.lo,r.hi);return i?"-"+l:l}(e,s),uDec:(e,s)=>ye(e,s)}}function _e(t){if(!/^-?[0-9]+$/.test(t))throw new Error("invalid int64: "+t)}function Se(t){if(!/^[0-9]+$/.test(t))throw new Error("invalid uint64: "+t)}function O(t,e){switch(t){case u.STRING:return"";case u.BOOL:return!1;case u.DOUBLE:case u.FLOAT:return 0;case u.INT64:case u.UINT64:case u.SFIXED64:case u.FIXED64:case u.SINT64:return e?"0":v.zero;case u.BYTES:return new Uint8Array(0);default:return 0}}const N=Symbol.for("reflect unsafe local");function Ae(t,e){const s=t[e.localName].case;return s===void 0?s:e.fields.find(n=>n.localName===s)}function ht(t,e){const s=e.localName;if(e.oneof)return t[e.oneof.localName].case===s;if(e.presence!=2)return t[s]!==void 0&&Object.prototype.hasOwnProperty.call(t,s);switch(e.fieldKind){case"list":return t[s].length>0;case"map":return Object.keys(t[s]).length>0;case"scalar":return!function(n,a){switch(n){case u.BOOL:return a===!1;case u.STRING:return a==="";case u.BYTES:return a instanceof Uint8Array&&!a.byteLength;default:return a==0}}(e.scalar,t[s]);case"enum":return t[s]!==e.enum.values[0].number}throw new Error("message field with implicit presence")}function Fe(t,e){if(e.oneof){const s=t[e.oneof.localName];return s.case===e.localName?s.value:void 0}return t[e.localName]}function Pe(t,e,s){e.oneof?t[e.oneof.localName]={case:e.localName,value:s}:t[e.localName]=s}function E(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}function J(t,e){var s,n,a,r;if(E(t)&&N in t&&"add"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const i=e,l=t.field();return i.listKind==l.listKind&&i.scalar===l.scalar&&((s=i.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((a=i.enum)===null||a===void 0?void 0:a.typeName)===((r=l.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function Q(t,e){var s,n,a,r;if(E(t)&&N in t&&"has"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const i=e,l=t.field();return i.mapKey===l.mapKey&&i.mapKind==l.mapKind&&i.scalar===l.scalar&&((s=i.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((a=i.enum)===null||a===void 0?void 0:a.typeName)===((r=l.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function se(t,e){return E(t)&&N in t&&"desc"in t&&E(t.desc)&&t.desc.kind==="message"&&(e===void 0||t.desc.typeName==e.typeName)}function xe(t){const e=t.fields[0];return De(t.typeName)&&e!==void 0&&e.fieldKind=="scalar"&&e.name=="value"&&e.number==1}function De(t){return t.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(t.substring(16))}const gt=999,mt=998,q=2;function ne(t,e){if(ee(e,t))return e;const s=function(n){let a;if(function(r){switch(r.file.edition){case gt:return!1;case mt:return!0;default:return r.fields.some(i=>i.presence!=q&&i.fieldKind!="message"&&!i.oneof)}}(n)){const r=we.get(n);let i,l;if(r)({prototype:i,members:l}=r);else{i={},l=new Set;for(const c of n.members)c.kind!="oneof"&&(c.fieldKind!="scalar"&&c.fieldKind!="enum"||c.presence!=q&&(l.add(c),i[c.localName]=G(c)));we.set(n,{prototype:i,members:l})}a=Object.create(i),a.$typeName=n.typeName;for(const c of n.members)if(!l.has(c)){if(c.kind=="field"&&(c.fieldKind=="message"||(c.fieldKind=="scalar"||c.fieldKind=="enum")&&c.presence!=q))continue;a[c.localName]=G(c)}}else{a={$typeName:n.typeName};for(const r of n.members)r.kind!="oneof"&&r.presence!=q||(a[r.localName]=G(r))}return a}(t);return e!==void 0&&function(n,a,r){for(const i of n.members){let l,c=r[i.localName];if(c!=null){if(i.kind=="oneof"){const g=Ae(r,i);if(!g)continue;l=g,c=Fe(r,g)}else l=i;switch(l.fieldKind){case"message":c=ae(l,c);break;case"scalar":c=Ue(l,c);break;case"list":c=yt(l,c);break;case"map":c=ft(l,c)}Pe(a,l,c)}}}(t,s,e),s}function Ue(t,e){return t.scalar==u.BYTES?re(e):e}function ft(t,e){if(E(e)){if(t.scalar==u.BYTES)return ve(e,re);if(t.mapKind=="message")return ve(e,s=>ae(t,s))}return e}function yt(t,e){if(Array.isArray(e)){if(t.scalar==u.BYTES)return e.map(re);if(t.listKind=="message")return e.map(s=>ae(t,s))}return e}function ae(t,e){if(t.fieldKind=="message"&&!t.oneof&&xe(t.message))return Ue(t.message.fields[0],e);if(E(e)){if(t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!=="google.protobuf.Value")return e;if(!ee(e,t.message))return ne(t.message,e)}return e}function re(t){return Array.isArray(t)?new Uint8Array(t):t}function ve(t,e){const s={};for(const n of Object.entries(t))s[n[0]]=e(n[1]);return s}const pt=Symbol(),we=new WeakMap;function G(t){if(t.kind=="oneof")return{case:void 0};if(t.fieldKind=="list")return[];if(t.fieldKind=="map")return{};if(t.fieldKind=="message")return pt;const e=t.getDefaultValue();return e!==void 0?t.fieldKind=="scalar"&&t.longAsString?e.toString():e:t.fieldKind=="scalar"?O(t.scalar,t.longAsString):t.enum.values[0].number}class A extends Error{constructor(e,s,n="FieldValueInvalidError"){super(s),this.name=n,this.field=()=>e}}const X=Symbol.for("@bufbuild/protobuf/text-encoding");function Oe(){if(globalThis[X]==null){const t=new globalThis.TextEncoder,e=new globalThis.TextDecoder;globalThis[X]={encodeUtf8:s=>t.encode(s),decodeUtf8:s=>e.decode(s),checkUtf8(s){try{return encodeURIComponent(s),!0}catch{return!1}}}}return globalThis[X]}var T;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(T||(T={}));const bt=34028234663852886e22,_t=-34028234663852886e22,St=4294967295,vt=2147483647,wt=-2147483648;class Tt{constructor(e,s=Oe().decodeUtf8){this.decodeUtf8=s,this.varint64=ut,this.uint32=ct,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength)}tag(){let e=this.uint32(),s=e>>>3,n=7&e;if(s<=0||n<0||n>5)throw new Error("illegal tag: field no "+s+" wire type "+n);return[s,n]}skip(e,s){let n=this.pos;switch(e){case T.Varint:for(;128&this.buf[this.pos++];);break;case T.Bit64:this.pos+=4;case T.Bit32:this.pos+=4;break;case T.LengthDelimited:let a=this.uint32();this.pos+=a;break;case T.StartGroup:for(;;){const[r,i]=this.tag();if(i===T.EndGroup){if(s!==void 0&&r!==s)throw new Error("invalid end group tag");break}this.skip(i,r)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return v.dec(...this.varint64())}uint64(){return v.uDec(...this.varint64())}sint64(){let[e,s]=this.varint64(),n=-(1&e);return e=(e>>>1|(1&s)<<31)^n,s=s>>>1^n,v.dec(e,s)}bool(){let[e,s]=this.varint64();return e!==0||s!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return v.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return v.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),s=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(s,s+e)}string(){return this.decodeUtf8(this.bytes())}}function Te(t,e,s){const n=ie(t,s);if(n!==!0)return new A(t,`list item #${e+1}: ${K(t,s,n)}`)}function ie(t,e){return t.scalar!==void 0?Le(e,t.scalar):t.enum!==void 0?t.enum.open?Number.isInteger(e):t.enum.values.some(s=>s.number===e):se(e,t.message)}function Le(t,e){switch(e){case u.DOUBLE:return typeof t=="number";case u.FLOAT:return typeof t=="number"&&(!(!Number.isNaN(t)&&Number.isFinite(t))||!(t>bt||t<_t)||`${t.toFixed()} out of range`);case u.INT32:case u.SFIXED32:case u.SINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>vt||t<wt)||`${t.toFixed()} out of range`);case u.FIXED32:case u.UINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>St||t<0)||`${t.toFixed()} out of range`);case u.BOOL:return typeof t=="boolean";case u.STRING:return typeof t=="string"&&(Oe().checkUtf8(t)||"invalid UTF8");case u.BYTES:return t instanceof Uint8Array;case u.INT64:case u.SFIXED64:case u.SINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return v.parse(t),!0}catch{return`${t} out of range`}return!1;case u.FIXED64:case u.UINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return v.uParse(t),!0}catch{return`${t} out of range`}return!1}}function K(t,e,s){return s=typeof s=="string"?`: ${s}`:`, got ${H(e)}`,t.scalar!==void 0?`expected ${function(n){switch(n){case u.STRING:return"string";case u.BOOL:return"boolean";case u.INT64:case u.SINT64:case u.SFIXED64:return"bigint (int64)";case u.UINT64:case u.FIXED64:return"bigint (uint64)";case u.BYTES:return"Uint8Array";case u.DOUBLE:return"number (float64)";case u.FLOAT:return"number (float32)";case u.FIXED32:case u.UINT32:return"number (uint32)";case u.INT32:case u.SFIXED32:case u.SINT32:return"number (int32)"}}(t.scalar)}`+s:t.enum!==void 0?`expected ${t.enum.toString()}`+s:`expected ${Be(t.message)}`+s}function H(t){switch(typeof t){case"object":return t===null?"null":t instanceof Uint8Array?`Uint8Array(${t.length})`:Array.isArray(t)?`Array(${t.length})`:J(t)?qe(t.field()):Q(t)?$e(t.field()):se(t)?Be(t.desc):ee(t)?`message ${t.$typeName}`:"object";case"string":return t.length>30?"string":`"${t.split('"').join('\\"')}"`;case"boolean":case"number":return String(t);case"bigint":return String(t)+"n";default:return typeof t}}function Be(t){return`ReflectMessage (${t.typeName})`}function qe(t){switch(t.listKind){case"message":return`ReflectList (${t.message.toString()})`;case"enum":return`ReflectList (${t.enum.toString()})`;case"scalar":return`ReflectList (${u[t.scalar]})`}}function $e(t){switch(t.mapKind){case"message":return`ReflectMap (${u[t.mapKey]}, ${t.message.toString()})`;case"enum":return`ReflectMap (${u[t.mapKey]}, ${t.enum.toString()})`;case"scalar":return`ReflectMap (${u[t.mapKey]}, ${u[t.scalar]})`}}function oe(t,e,s=!0){return new je(t,e,s)}class je{get sortedFields(){var e;return(e=this._sortedFields)!==null&&e!==void 0?e:this._sortedFields=this.desc.fields.concat().sort((s,n)=>s.number-n.number)}constructor(e,s,n=!0){this.lists=new Map,this.maps=new Map,this.check=n,this.desc=e,this.message=this[N]=s??ne(e),this.fields=e.fields,this.oneofs=e.oneofs,this.members=e.members}findNumber(e){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(s=>[s.number,s]))),this._fieldsByNumber.get(e)}oneofCase(e){return D(this.message,e),Ae(this.message,e)}isSet(e){return D(this.message,e),ht(this.message,e)}clear(e){D(this.message,e),function(s,n){const a=n.localName;if(n.oneof){const r=n.oneof.localName;s[r].case===a&&(s[r]={case:void 0})}else if(n.presence!=2)delete s[a];else switch(n.fieldKind){case"map":s[a]={};break;case"list":s[a]=[];break;case"enum":s[a]=n.enum.values[0].number;break;case"scalar":s[a]=O(n.scalar,n.longAsString)}}(this.message,e)}get(e){D(this.message,e);const s=Fe(this.message,e);switch(e.fieldKind){case"list":let n=this.lists.get(e);return n&&n[N]===s||this.lists.set(e,n=new Mt(e,s,this.check)),n;case"map":let a=this.maps.get(e);return a&&a[N]===s||this.maps.set(e,a=new It(e,s,this.check)),a;case"message":return ue(e,s,this.check);case"scalar":return s===void 0?O(e.scalar,!1):ce(e,s);case"enum":return s??e.enum.values[0].number}}set(e,s){if(D(this.message,e),this.check){const a=function(r,i){const l=r.fieldKind=="list"?J(i,r):r.fieldKind=="map"?Q(i,r):ie(r,i);if(l===!0)return;let c;switch(r.fieldKind){case"list":c=`expected ${qe(r)}, got ${H(i)}`;break;case"map":c=`expected ${$e(r)}, got ${H(i)}`;break;default:c=K(r,i,l)}return new A(r,c)}(e,s);if(a)throw a}let n;n=e.fieldKind=="message"?le(e,s):Q(s)||J(s)?s[N]:de(e,s),Pe(this.message,e,n)}getUnknown(){return this.message.$unknown}setUnknown(e){this.message.$unknown=e}}function D(t,e){if(e.parent.typeName!==t.$typeName)throw new A(e,`cannot use ${e.toString()} with message ${t.$typeName}`,"ForeignFieldError")}class Mt{field(){return this._field}get size(){return this._arr.length}constructor(e,s,n){this._field=e,this._arr=this[N]=s,this.check=n}get(e){const s=this._arr[e];return s===void 0?void 0:z(this._field,s,this.check)}set(e,s){if(e<0||e>=this._arr.length)throw new A(this._field,`list item #${e+1}: out of range`);if(this.check){const n=Te(this._field,e,s);if(n)throw n}this._arr[e]=Me(this._field,s)}add(e){if(this.check){const s=Te(this._field,this._arr.length,e);if(s)throw s}this._arr.push(Me(this._field,e))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const e of this._arr)yield z(this._field,e,this.check)}*entries(){for(let e=0;e<this._arr.length;e++)yield[e,z(this._field,this._arr[e],this.check)]}}class It{constructor(e,s,n=!0){this.obj=this[N]=s??{},this.check=n,this._field=e}field(){return this._field}set(e,s){if(this.check){const n=function(a,r,i){const l=Le(r,a.mapKey);if(l!==!0)return new A(a,`invalid map key: ${K({scalar:a.mapKey},r,l)}`);const c=ie(a,i);return c!==!0?new A(a,`map entry ${H(r)}: ${K(a,i,c)}`):void 0}(this._field,e,s);if(n)throw n}return this.obj[$(e)]=function(n,a){return n.mapKind=="message"?le(n,a):de(n,a)}(this._field,s),this}delete(e){const s=$(e),n=Object.prototype.hasOwnProperty.call(this.obj,s);return n&&delete this.obj[s],n}clear(){for(const e of Object.keys(this.obj))delete this.obj[e]}get(e){let s=this.obj[$(e)];return s!==void 0&&(s=W(this._field,s,this.check)),s}has(e){return Object.prototype.hasOwnProperty.call(this.obj,$(e))}*keys(){for(const e of Object.keys(this.obj))yield Ie(e,this._field.mapKey)}*entries(){for(const e of Object.entries(this.obj))yield[Ie(e[0],this._field.mapKey),W(this._field,e[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const e of Object.values(this.obj))yield W(this._field,e,this.check)}forEach(e,s){for(const n of this.entries())e.call(s,n[1],n[0],this)}}function le(t,e){return se(e)?De(e.message.$typeName)&&!t.oneof&&t.fieldKind=="message"?e.message.value:e.desc.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"?He(e.message):e.message:e}function ue(t,e,s){return e!==void 0&&(xe(t.message)&&!t.oneof&&t.fieldKind=="message"?e={$typeName:t.message.typeName,value:ce(t.message.fields[0],e)}:t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"&&E(e)&&(e=Ke(e))),new je(t.message,e,s)}function Me(t,e){return t.listKind=="message"?le(t,e):de(t,e)}function z(t,e,s){return t.listKind=="message"?ue(t,e,s):ce(t,e)}function W(t,e,s){return t.mapKind=="message"?ue(t,e,s):e}function $(t){return typeof t=="string"||typeof t=="number"?t:String(t)}function Ie(t,e){switch(e){case u.STRING:return t;case u.INT32:case u.FIXED32:case u.UINT32:case u.SFIXED32:case u.SINT32:{const s=Number.parseInt(t);if(Number.isFinite(s))return s;break}case u.BOOL:switch(t){case"true":return!0;case"false":return!1}break;case u.UINT64:case u.FIXED64:try{return v.uParse(t)}catch{}break;default:try{return v.parse(t)}catch{}}return t}function ce(t,e){switch(t.scalar){case u.INT64:case u.SFIXED64:case u.SINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=v.parse(e));break;case u.FIXED64:case u.UINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=v.uParse(e))}return e}function de(t,e){switch(t.scalar){case u.INT64:case u.SFIXED64:case u.SINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=v.parse(e));break;case u.FIXED64:case u.UINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=v.uParse(e))}return e}function Ke(t){const e={$typeName:"google.protobuf.Struct",fields:{}};if(E(t))for(const[s,n]of Object.entries(t))e.fields[s]=Ge(n);return e}function He(t){const e={};for(const[s,n]of Object.entries(t.fields))e[s]=Ve(n);return e}function Ve(t){switch(t.kind.case){case"structValue":return He(t.kind.value);case"listValue":return t.kind.value.values.map(Ve);case"nullValue":case void 0:return null;default:return t.kind.value}}function Ge(t){const e={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:0};else if(Array.isArray(t)){const s={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(t))for(const n of t)s.values.push(Ge(n));e.kind={case:"listValue",value:s}}else e.kind={case:"structValue",value:Ke(t)}}return e}const Ne={readUnknownFields:!0};function Nt(t,e,s){const n=oe(t,void 0,!1);return Xe(n,new Tt(e),function(a){return a?Object.assign(Object.assign({},Ne),a):Ne}(s),!1,e.byteLength),n.message}function Xe(t,e,s,n,a){var r;const i=n?e.len:e.pos+a;let l,c;const g=(r=t.getUnknown())!==null&&r!==void 0?r:[];for(;e.pos<i&&([l,c]=e.tag(),!n||c!=T.EndGroup);){const m=t.findNumber(l);if(m)kt(t,e,m,c,s);else{const h=e.skip(c,l);s.readUnknownFields&&g.push({no:l,wireType:c,data:h})}}if(n&&(c!=T.EndGroup||l!==a))throw new Error("invalid end group tag");g.length>0&&t.setUnknown(g)}function kt(t,e,s,n,a){switch(s.fieldKind){case"scalar":t.set(s,R(e,s.scalar));break;case"enum":t.set(s,R(e,u.INT32));break;case"message":t.set(s,Y(e,a,s,t.get(s)));break;case"list":(function(r,i,l,c){var g;const m=l.field();if(m.listKind==="message")return void l.add(Y(r,c,m));const h=(g=m.scalar)!==null&&g!==void 0?g:u.INT32;if(!(i==T.LengthDelimited&&h!=u.STRING&&h!=u.BYTES))return void l.add(R(r,h));const p=r.uint32()+r.pos;for(;r.pos<p;)l.add(R(r,h))})(e,n,t.get(s),a);break;case"map":(function(r,i,l){const c=i.field();let g,m;const h=r.pos+r.uint32();for(;r.pos<h;){const[f]=r.tag();switch(f){case 1:g=R(r,c.mapKey);break;case 2:switch(c.mapKind){case"scalar":m=R(r,c.scalar);break;case"enum":m=r.int32();break;case"message":m=Y(r,l,c)}}}if(g===void 0&&(g=O(c.mapKey,!1)),m===void 0)switch(c.mapKind){case"scalar":m=O(c.scalar,!1);break;case"enum":m=c.enum.values[0].number;break;case"message":m=oe(c.message,void 0,!1)}i.set(g,m)})(e,t.get(s),a)}}function Y(t,e,s,n){const a=s.delimitedEncoding,r=n??oe(s.message,void 0,!1);return Xe(r,t,e,a,a?s.number:t.uint32()),r}function R(t,e){switch(e){case u.STRING:return t.string();case u.BOOL:return t.bool();case u.DOUBLE:return t.double();case u.FLOAT:return t.float();case u.INT32:return t.int32();case u.INT64:return t.int64();case u.UINT64:return t.uint64();case u.FIXED64:return t.fixed64();case u.BYTES:return t.bytes();case u.FIXED32:return t.fixed32();case u.SFIXED32:return t.sfixed32();case u.SFIXED64:return t.sfixed64();case u.SINT64:return t.sint64();case u.UINT32:return t.uint32();case u.SINT32:return t.sint32()}}function ke(t){const e=C[t];return typeof e!="string"?t.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,s=>"_"+s.toLowerCase())}class k extends Error{constructor(e,s=C.Unknown,n,a,r){super(function(i,l){return i.length?`[${ke(l)}] ${i}`:`[${ke(l)}]`}(e,s)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=s,this.metadata=new Headers(n??{}),this.details=a??[],this.cause=r}static from(e,s=C.Unknown){return e instanceof k?e:e instanceof Error?e.name=="AbortError"?new k(e.message,C.Canceled):new k(e.message,s,void 0,void 0,e):new k(String(e),s,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===k.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const s=e.kind==="message"?{getMessage:a=>a===e.typeName?e:void 0}:e,n=[];for(const a of this.details){if("desc"in a){s.getMessage(a.desc.typeName)&&n.push(ne(a.desc,a.value));continue}const r=s.getMessage(a.type);if(r)try{n.push(Nt(r,a.value))}catch{}}return n}}var Ct=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(a){e[a]=t[a]&&function(r){return new Promise(function(i,l){(function(c,g,m,h){Promise.resolve(h).then(function(f){c({value:f,done:m})},g)})(i,l,(r=t[a](r)).done,r.value)})}}},L=function(t){return this instanceof L?(this.v=t,this):new L(t)},Et=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=s.apply(t,e||[]),r=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(h){return function(f){return Promise.resolve(f).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function i(h,f){a[h]&&(n[h]=function(p){return new Promise(function(b,I){r.push([h,p,b,I])>1||l(h,p)})},f&&(n[h]=f(n[h])))}function l(h,f){try{(p=a[h](f)).value instanceof L?Promise.resolve(p.value.v).then(c,g):m(r[0][2],p)}catch(b){m(r[0][3],b)}var p}function c(h){l("next",h)}function g(h){l("throw",h)}function m(h,f){h(f),r.shift(),r.length&&l(r[0][0],r[0][1])}},Rt=function(t){var e,s;return e={},n("next"),n("throw",function(a){throw a}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(a,r){e[a]=t[a]?function(i){return(s=!s)?{value:L(t[a](i)),done:!1}:r?r(i):i}:r}},ze=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(a){e[a]=t[a]&&function(r){return new Promise(function(i,l){(function(c,g,m,h){Promise.resolve(h).then(function(f){c({value:f,done:m})},g)})(i,l,(r=t[a](r)).done,r.value)})}}},F=function(t){return this instanceof F?(this.v=t,this):new F(t)},At=function(t){var e,s;return e={},n("next"),n("throw",function(a){throw a}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(a,r){e[a]=t[a]?function(i){return(s=!s)?{value:F(t[a](i)),done:!1}:r?r(i):i}:r}},Ft=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=s.apply(t,e||[]),r=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(h){return function(f){return Promise.resolve(f).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function i(h,f){a[h]&&(n[h]=function(p){return new Promise(function(b,I){r.push([h,p,b,I])>1||l(h,p)})},f&&(n[h]=f(n[h])))}function l(h,f){try{(p=a[h](f)).value instanceof F?Promise.resolve(p.value.v).then(c,g):m(r[0][2],p)}catch(b){m(r[0][3],b)}var p}function c(h){l("next",h)}function g(h){l("throw",h)}function m(h,f){h(f),r.shift(),r.length&&l(r[0][0],r[0][1])}};function Pt(t,e){return function(s,n){const a={};for(const r of s.methods){const i=n(r);i!=null&&(a[r.localName]=i)}return a}(t,s=>{switch(s.methodKind){case"unary":return function(n,a){return async function(r,i){var l,c;const g=await n.unary(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);return(l=i==null?void 0:i.onHeader)===null||l===void 0||l.call(i,g.header),(c=i==null?void 0:i.onTrailer)===null||c===void 0||c.call(i,g.trailer),g.message}}(e,s);case"server_streaming":return function(n,a){return function(r,i){return Ce(n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(l){return Et(this,arguments,function*(){yield L(yield*Rt(Ct(l)))})}([r]),i==null?void 0:i.contextValues),i)}}(e,s);case"client_streaming":return function(n,a){return async function(r,i){var l,c,g,m,h,f;const p=await n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);let b;(h=i==null?void 0:i.onHeader)===null||h===void 0||h.call(i,p.header);let I=0;try{for(var y,P=!0,V=ze(p.message);!(l=(y=await V.next()).done);P=!0)m=y.value,P=!1,b=m,I++}catch(We){c={error:We}}finally{try{P||l||!(g=V.return)||await g.call(V)}finally{if(c)throw c.error}}if(!b)throw new k("protocol error: missing response message",C.Unimplemented);if(I>1)throw new k("protocol error: received extra messages for client streaming method",C.Unimplemented);return(f=i==null?void 0:i.onTrailer)===null||f===void 0||f.call(i,p.trailer),b}}(e,s);case"bidi_streaming":return function(n,a){return function(r,i){return Ce(n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues),i)}}(e,s);default:return null}})}function Ce(t,e){const s=function(){return Ft(this,arguments,function*(){var n,a;const r=yield F(t);(n=e==null?void 0:e.onHeader)===null||n===void 0||n.call(e,r.header),yield F(yield*At(ze(r.message))),(a=e==null?void 0:e.onTrailer)===null||a===void 0||a.call(e,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>s.next()})}}const ts="augment-welcome";var S=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(S||{}),M=(t=>(t.seen="seen",t.unseen="unseen",t))(M||{}),xt=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.autofixMessage="autofix-message",t.autofixSteeringMessage="autofix-steering-message",t.autofixStage="autofix-stage",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t.historySummary="history-summary",t))(xt||{});function Dt(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function ss(t){return Dt(t)&&t.status==="success"}function ns(t){return t.chatItemType==="agentic-checkpoint-delimiter"}function as(t){return t.chatItemType==="history-summary"}async function*Ut(t,e=1e3){for(;t>0;)yield t,await new Promise(s=>setTimeout(s,Math.min(e,t))),t-=e}class Ot{constructor(e,s,n,a=5,r=4e3,i){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=s,this.startStreamFn=n,this.maxRetries=a,this.baseDelay=r,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let e=0,s=!1;try{for(;!this._isCancelled;){const n=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,r=!1,i="";for await(const c of n){if(c.status===S.failed){if(c.isRetriable!==!0||s)return yield c;r=!0,i=c.display_error_message||"Service is currently unavailable",a=c.request_id;break}s=!0,yield c}if(!r)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:a??this.requestId,seen_state:M.unseen,status:S.failed,display_error_message:i,isRetriable:!1});const l=this.baseDelay*2**(e-1);for await(const c of Ut(l))yield{request_id:this.requestId,status:S.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(c/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:S.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(n){yield{request_id:this.requestId,seen_state:M.unseen,status:S.failed,display_error_message:n instanceof Error?n.message:String(n)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:M.unseen,status:S.cancelled}}}class Lt{constructor(e){o(this,"getHydratedTask",async e=>{const s={type:x.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.task});o(this,"createTask",async(e,s,n)=>{const a={type:x.createTaskRequest,data:{name:e,description:s,parentTaskUuid:n}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});o(this,"updateTask",async(e,s,n)=>{const a={type:x.updateTaskRequest,data:{uuid:e,updates:s,updatedBy:n}};await this._asyncMsgSender.sendToSidecar(a,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const s={type:x.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(s)});o(this,"updateHydratedTask",async(e,s)=>{const n={type:x.updateHydratedTaskRequest,data:{task:e,updatedBy:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});this._asyncMsgSender=e}}function w(t,e){return e in t&&t[e]!==void 0}function Bt(t){return w(t,"file")}function qt(t){return w(t,"recentFile")}function $t(t){return w(t,"folder")}function jt(t){return w(t,"sourceFolder")}function rs(t){return w(t,"selection")}function Kt(t){return w(t,"externalSource")}function is(t){return w(t,"allDefaultContext")}function os(t){return w(t,"clearContext")}function ls(t){return w(t,"userGuidelines")}function Ht(t){return w(t,"personality")}function Vt(t){return w(t,"rule")}const us={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},cs={clearContext:!0,label:"Clear Context",id:"clearContext"},ds={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},hs={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Ee=[{personality:{type:B.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:B.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:B.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:B.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function gs(t){return w(t,"group")}function ms(t){const e=new Map;return t.forEach(s=>{Bt(s)?e.set("file",[...e.get("file")??[],s]):qt(s)?e.set("recentFile",[...e.get("recentFile")??[],s]):$t(s)?e.set("folder",[...e.get("folder")??[],s]):Kt(s)?e.set("externalSource",[...e.get("externalSource")??[],s]):jt(s)?e.set("sourceFolder",[...e.get("sourceFolder")??[],s]):Ht(s)?e.set("personality",[...e.get("personality")??[],s]):Vt(s)&&e.set("rule",[...e.get("rule")??[],s])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(s=>s.group.items.length>0)}function Gt(t){const e=Je({rootPath:t.repoRoot,relPath:t.pathName}),s={label:lt(t.pathName).split("/").filter(n=>n.trim()!=="").pop()||"",name:e,id:e};if(t.fullRange){const n=`:L${t.fullRange.startLineNumber}-${t.fullRange.endLineNumber}`;s.label+=n,s.name+=n,s.id+=n}else if(t.range){const n=`:L${t.range.start}-${t.range.stop}`;s.label+=n,s.name+=n,s.id+=n}return s}function Xt(t){const e=t.path.split("/"),s=e[e.length-1],n=s.endsWith(".md")?s.slice(0,-3):s,a=`${Qe}/${et}/${t.path}`;return{label:n,name:a,id:a}}class fs{constructor(e,s,n){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:d.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const s=await async function(n){return(await Pt(rt,new at({sendMessage:r=>{n.postMessage(r)},onReceiveMessage:r=>{const i=l=>{r(l.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",s)}catch(s){console.error("Hello world error:",s)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:d.reportWebviewClientMetric,data:{webviewName:tt.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:_.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:_.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,s=!1)=>{const n={rootPath:"",relPath:e},a=this.findFiles(n,6),r=this.findRecentlyOpenedFiles(n,6),i=this.findFolders(n,3),l=this.findExternalSources(e,s),c=this.findRules(e,6),[g,m,h,f,p]=await Promise.all([U(a,[]),U(r,[]),U(i,[]),U(l,[]),U(c,[])]),b=(y,P)=>({...Gt(y),[P]:y}),I=[...g.map(y=>b(y,"file")),...h.map(y=>b(y,"folder")),...m.map(y=>b(y,"recentFile")),...f.map(y=>({label:y.name,name:y.name,id:y.id,externalSource:y})),...p.map(y=>({...Xt(y),rule:y}))];if(this._flags.enablePersonalities){const y=this.getPersonalities(e);y.length>0&&I.push(...y)}return I});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return Ee;const s=e.toLowerCase();return Ee.filter(n=>{const a=n.personality.description.toLowerCase(),r=n.label.toLowerCase();return a.includes(s)||r.includes(s)})});o(this,"sendAction",e=>{this._host.postMessage({type:d.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:d.showAugmentPanel})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:d.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:d.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,s=void 0)=>{const n=await this._asyncMsgSender.send({type:d.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:s}},5e3);if(n.data)return n.data});o(this,"resolveSymbols",async(e,s)=>(await this._asyncMsgSender.send({type:d.findSymbolRequest,data:{query:e,searchScope:s}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:d.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFileRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findFolders",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFolderRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findRecentlyOpenedFilesRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findExternalSources",async(e,s=!1)=>this._flags.enableExternalSourcesInChat?s?[]:(await this._asyncMsgSender.send({type:d.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,s=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:d.getRulesListRequest,data:{query:e,maxResults:s}},5e3)).data:[]);o(this,"openFile",e=>{this._host.postMessage({type:d.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:d.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:d.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:d.openMemoriesFile})});o(this,"createFile",(e,s)=>{this._host.postMessage({type:d.chatCreateFile,data:{code:e,relPath:s}})});o(this,"openScratchFile",async(e,s="shellscript")=>{await this._asyncMsgSender.send({type:d.openScratchFileRequest,data:{content:e,language:s}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:d.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:d.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,s)=>this._taskClient.updateHydratedTask(e,s));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,s,n)=>this._taskClient.createTask(e,s,n));o(this,"updateTask",async(e,s,n)=>this._taskClient.updateTask(e,s,n));o(this,"saveChat",async(e,s,n)=>this._asyncMsgSender.send({type:d.saveChat,data:{conversationId:e,chatHistory:s,title:n}}));o(this,"launchAutofixPanel",async(e,s,n)=>this._asyncMsgSender.send({type:d.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:s,stage:n}}));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:d.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:d.updateWorkspaceGuidelines,data:e})});o(this,"updateRuleFile",(e,s)=>{this._host.postMessage({type:d.updateRuleFile,data:{rulePath:e,content:s}})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:d.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var s;(s=this._activeRetryStreams.get(e))==null||s.cancel(),await this._asyncMsgSender.send({type:d.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,s,n,a="")=>{const r={requestId:e,rating:n,note:a,mode:s},i={type:d.chatRating,data:r};return(await this._asyncMsgSender.send(i,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:d.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:d.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,s,n,a,r,i)=>{const l={type:d.callTool,data:{chatRequestId:e,toolUseId:s,name:n,input:a,chatHistory:r,conversationId:i}};return(await this._asyncMsgSender.send(l,0)).data});o(this,"cancelToolRun",async(e,s)=>{const n={type:d.cancelToolRun,data:{requestId:e,toolUseId:s}};await this._asyncMsgSender.send(n,0)});o(this,"checkSafe",async(e,s)=>{const n={type:d.toolCheckSafe,data:{name:e,input:s}};return(await this._asyncMsgSender.send(n,0)).data.isSafe});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:he.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const s={type:he.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(s,0)).data});o(this,"executeCommand",async(e,s,n)=>{try{const a=await this._asyncMsgSender.send({type:d.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:s,args:n}},6e5);return{output:a.data.output,returnCode:a.data.returnCode}}catch(a){throw console.error("[ExtensionClient] Execute command failed:",a),a}});o(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:d.chatAutofixStateUpdate,data:e})});o(this,"autofixPlan",async(e,s)=>(await this._asyncMsgSender.send({type:d.chatAutofixPlanRequest,data:{command:e,steeringHistory:s}},6e4)).data.plan);o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:d.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,s)=>{const n={type:_.getEditListRequest,data:{fromTimestamp:e,toTimestamp:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});o(this,"hasChangesSince",async e=>{const s={type:_.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.edits.filter(n=>{var a,r;return((a=n.changesSummary)==null?void 0:a.totalAddedLines)||((r=n.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const s={type:d.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(s,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:_.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,s)=>{await this._asyncMsgSender.sendToSidecar({type:_.migrateConversationId,data:{oldConversationId:e,newConversationId:s}},3e4)});o(this,"showAgentReview",(e,s,n,a=!0,r)=>{this._asyncMsgSender.sendToSidecar({type:_.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:s,toTimestamp:n,retainFocus:a,useNativeDiffIfAvailable:r}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:_.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,s)=>(await this._asyncMsgSender.sendToSidecar({type:_.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:s}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:d.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const s={type:_.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const s={type:_.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:d.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:d.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:d.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:d.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:d.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:_.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:_.setHasEverUsedAgent,data:e},5e3)});o(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:_.checkHasEverUsedRemoteAgent},5e3)).data);o(this,"setHasEverUsedRemoteAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:_.setHasEverUsedRemoteAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:d.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:d.reportError,data:e})});this._host=e,this._asyncMsgSender=s,this._flags=n,this._taskClient=new Lt(s)}async*generateCommitMessage(){const e={type:d.generateCommitMessage},s=this._asyncMsgSender.stream(e,3e4,6e4);yield*Z(s)}async*sendInstructionMessage(e,s){const n={instruction:e.request_message??"",selectedCodeDetails:s,requestId:e.request_id},a={type:d.chatInstructionMessage,data:n},r=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(i){let l;try{for await(const c of i)l=c.data.requestId,yield{request_id:l,response_text:c.data.text,seen_state:M.unseen,status:S.sent};yield{request_id:l,seen_state:M.unseen,status:S.success}}catch{yield{request_id:l,seen_state:M.unseen,status:S.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:d.openGuidelines,data:e})}async*getExistingChatStream(e,s){if(!e.request_id)return;const n=s==null?void 0:s.flags.enablePreferenceCollection,a=n?1e9:6e4,r=n?1e9:3e5,i={type:d.chatGetStreamRequest,data:{requestId:e.request_id}},l=this._asyncMsgSender.stream(i,a,r);yield*Z(l,this.reportError)}async*startChatStream(e,s){const n=s==null?void 0:s.flags.enablePreferenceCollection,a=n?1e9:6e4,r=n?1e9:3e5,i={type:d.chatUserMessage,data:e},l=this._asyncMsgSender.stream(i,a,r);yield*Z(l,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:d.checkToolExists,toolName:e},0)).exists}async saveImage(e,s){const n=st(await ge(e)),a=s??`${await nt(await me(n))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveImageRequest,data:{filename:a,data:n}},1e4)).data}async loadImage(e){const s=await this._asyncMsgSender.send({type:d.chatLoadImageRequest,data:e},1e4),n=s.data?await me(s.data):void 0;if(!n)return;let a="application/octet-stream";const r=e.split(".").at(-1);r==="png"?a="image/png":r!=="jpg"&&r!=="jpeg"||(a="image/jpeg");const i=new File([n],e,{type:a});return await ge(i)}async deleteImage(e){await this._asyncMsgSender.send({type:d.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,s,n){const a=new Ot(e,s,(r,i)=>this.startChatStream(r,i),(n==null?void 0:n.maxRetries)??5,4e3,n==null?void 0:n.flags);this._activeRetryStreams.set(e,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:d.getSubscriptionInfo},5e3)}}async function*Z(t,e=()=>{}){let s;try{for await(const n of t){if(s=n.data.requestId,n.data.error)return yield{request_id:s,seen_state:M.unseen,status:S.failed,display_error_message:n.data.error.displayErrorMessage,isRetriable:n.data.error.isRetriable};const a={request_id:s,response_text:n.data.text,workspace_file_chunks:n.data.workspaceFileChunks,structured_output_nodes:zt(n.data.nodes),seen_state:M.unseen,status:S.sent};n.data.stop_reason!=null&&(a.stop_reason=n.data.stop_reason),yield a}yield{request_id:s,seen_state:M.unseen,status:S.success}}catch(n){e({originalRequestId:s||"",sanitizedMessage:n instanceof Error?n.message:String(n),stackTrace:n instanceof Error&&n.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:s,seen_state:M.unseen,status:S.failed}}}async function U(t,e){try{return await t}catch(s){return console.warn(`Error while resolving promise: ${s}`),e}}function zt(t){if(!t)return t;let e=!1;return t.filter(s=>s.type!==ot.TOOL_USE||!e&&(e=!0,!0))}const ys=15,ps=1e3,Wt=25e4;class bs{constructor(e){o(this,"_enableEditableHistory",!1);o(this,"_enablePreferenceCollection",!1);o(this,"_enableRetrievalDataCollection",!1);o(this,"_enableDebugFeatures",!1);o(this,"_enableRichTextHistory",!1);o(this,"_modelDisplayNameToId",{});o(this,"_fullFeatured",!0);o(this,"_enableExternalSourcesInChat",!1);o(this,"_smallSyncThreshold",15);o(this,"_bigSyncThreshold",1e3);o(this,"_enableSmartPaste",!1);o(this,"_enableDirectApply",!1);o(this,"_summaryTitles",!1);o(this,"_suggestedEditsAvailable",!1);o(this,"_enableShareService",!1);o(this,"_maxTrackableFileCount",Wt);o(this,"_enableDesignSystemRichTextEditor",!1);o(this,"_enableSources",!1);o(this,"_enableChatMermaidDiagrams",!1);o(this,"_smartPastePrecomputeMode",it.visibleHover);o(this,"_useNewThreadsMenu",!1);o(this,"_enableChatMermaidDiagramsMinVersion",!1);o(this,"_enablePromptEnhancer",!1);o(this,"_idleNewSessionNotificationTimeoutMs");o(this,"_idleNewSessionMessageTimeoutMs");o(this,"_enableChatMultimodal",!1);o(this,"_enableAgentMode",!1);o(this,"_enableAgentAutoMode",!1);o(this,"_enableRichCheckpointInfo",!1);o(this,"_agentMemoriesFilePathName");o(this,"_userTier","unknown");o(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});o(this,"_truncateChatHistory",!1);o(this,"_enableBackgroundAgents",!1);o(this,"_enableNewThreadsList",!1);o(this,"_enableVirtualizedMessageList",!1);o(this,"_customPersonalityPrompts",{});o(this,"_enablePersonalities",!1);o(this,"_enableRules",!1);o(this,"_memoryClassificationOnFirstToken",!1);o(this,"_enableGenerateCommitMessage",!1);o(this,"_doUseNewDraftFunctionality",!1);o(this,"_modelRegistry",{});o(this,"_enableModelRegistry",!1);o(this,"_enableTaskList",!1);o(this,"_clientAnnouncement","");o(this,"_useHistorySummary",!1);o(this,"_historySummaryMaxChars",0);o(this,"_historySummaryLowerChars",0);o(this,"_historySummaryPrompt","");o(this,"_subscribers",new Set);o(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));o(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=e.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=e.enableNewThreadsList??this._enableNewThreadsList,this._enableVirtualizedMessageList=e.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._doUseNewDraftFunctionality=e.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=e.useHistorySummary??this._useHistorySummary,this._historySummaryMaxChars=e.historySummaryMaxChars??this._historySummaryMaxChars,this._historySummaryLowerChars=e.historySummaryLowerChars??this._historySummaryLowerChars,this._historySummaryPrompt=e.historySummaryPrompt??this._historySummaryPrompt,this._subscribers.forEach(s=>s(this))});o(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));o(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(s=>this._modelDisplayNameToId[s]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,s)=>{const n=e.toLowerCase(),a=s.toLowerCase();return n==="default"&&a!=="default"?-1:a==="default"&&n!=="default"?1:e.localeCompare(s)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary&&this._historySummaryMaxChars>0}get historySummaryMaxChars(){return this._historySummaryMaxChars}get historySummaryLowerChars(){return this._historySummaryLowerChars}get historySummaryPrompt(){return this._historySummaryPrompt}}export{hs as A,bs as C,ys as D,fs as E,M as S,us as U,ss as a,S as b,ns as c,as as d,xt as e,Ht as f,ps as g,Wt as h,Dt as i,Bt as j,qt as k,rs as l,$t as m,jt as n,Kt as o,ls as p,Vt as q,Gt as r,ts as s,Xt as t,ms as u,gs as v,is as w,cs as x,ds as y,os as z};
