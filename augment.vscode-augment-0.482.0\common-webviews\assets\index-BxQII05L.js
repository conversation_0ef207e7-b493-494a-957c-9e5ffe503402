import{N as y,O as N}from"./SpinnerAugment-Cx9dt_ox.js";var E="Expected a function",w=NaN,W="[object Symbol]",M=/^\s+|\s+$/g,S=/^[-+]0x[0-9a-f]+$/i,D=/^0b[01]+$/i,F=/^0o[0-7]+$/i,I=parseInt,k=typeof y=="object"&&y&&y.Object===Object&&y,q=typeof self=="object"&&self&&self.Object===Object&&self,z=k||q||Function("return this")(),A=Object.prototype.toString,B=Math.max,C=Math.min,O=function(){return z.Date.now()};function G(e,n,t){var r,o,v,l,u,a,s=0,h=!1,p=!1,b=!0;if(typeof e!="function")throw new TypeError(E);function g(i){var f=r,c=o;return r=o=void 0,s=i,l=e.apply(c,f)}function x(i){var f=i-a;return a===void 0||f>=n||f<0||p&&i-s>=v}function m(){var i=O();if(x(i))return T(i);u=setTimeout(m,function(f){var c=n-(f-a);return p?C(c,v-(f-s)):c}(i))}function T(i){return u=void 0,b&&r?g(i):(r=o=void 0,l)}function j(){var i=O(),f=x(i);if(r=arguments,o=this,a=i,f){if(u===void 0)return function(c){return s=c,u=setTimeout(m,n),h?g(c):l}(a);if(p)return u=setTimeout(m,n),g(a)}return u===void 0&&(u=setTimeout(m,n)),l}return n=$(n)||0,d(t)&&(h=!!t.leading,v=(p="maxWait"in t)?B($(t.maxWait)||0,n):v,b="trailing"in t?!!t.trailing:b),j.cancel=function(){u!==void 0&&clearTimeout(u),s=0,r=a=o=u=void 0},j.flush=function(){return u===void 0?l:T(O())},j}function d(e){var n=typeof e;return!!e&&(n=="object"||n=="function")}function $(e){if(typeof e=="number")return e;if(function(r){return typeof r=="symbol"||function(o){return!!o&&typeof o=="object"}(r)&&A.call(r)==W}(e))return w;if(d(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=d(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=e.replace(M,"");var t=D.test(e);return t||F.test(e)?I(e.slice(2),t?2:8):S.test(e)?w:+e}const J=N(function(e,n,t){var r=!0,o=!0;if(typeof e!="function")throw new TypeError(E);return d(t)&&(r="leading"in t?!!t.leading:r,o="trailing"in t?!!t.trailing:o),G(e,n,{leading:r,maxWait:n,trailing:o})});export{J as t};
