/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.ide.util.PropertiesComponent
 *  com.intellij.openapi.diagnostic.Logger
 *  org.jetbrains.annotations.NotNull
 */
package com.wangzai;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.diagnostic.Logger;
import com.wangzai.TrialSessionManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.jetbrains.annotations.NotNull;

public class AuthenticationManager {
    private static final Logger LOG = Logger.getInstance(AuthenticationManager.class);
    private static final String FORMAL_VERIFICATION_STATUS_KEY = "augment.formal.verification.status";
    private static final String FIXED_TRIAL_CODE = "1024";
    private static final String FORMAL_VERIFICATION_API_URL = "https://m1.apifoxmock.com/m1/6468831-6167795-default/query/code";
    private static final AuthenticationManager INSTANCE = new AuthenticationManager();

    private AuthenticationManager() {
    }

    @NotNull
    public static AuthenticationManager getInstance() {
        AuthenticationManager authenticationManager = INSTANCE;
        if (authenticationManager == null) {
            AuthenticationManager.$$$reportNull$$$0(0);
        }
        return authenticationManager;
    }

    public boolean verifyTrialCode(@NotNull String trialCode) {
        if (trialCode == null) {
            AuthenticationManager.$$$reportNull$$$0(1);
        }
        boolean isValid = FIXED_TRIAL_CODE.equals(trialCode.trim());
        LOG.info("\u8bd5\u7528\u9a8c\u8bc1\u7801\u9a8c\u8bc1\u7ed3\u679c: " + isValid + " (\u8f93\u5165: " + trialCode + ", \u671f\u671b: 1024)");
        return isValid;
    }

    public boolean activateTrialMode() {
        LOG.info("\u76f4\u63a5\u6fc0\u6d3b\u8bd5\u7528\u6a21\u5f0f\uff0c\u4f7f\u7528\u56fa\u5b9a\u9a8c\u8bc1\u7801: 1024");
        return this.verifyTrialCode(FIXED_TRIAL_CODE);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public boolean verifyFormalCode(@NotNull String formalCode) throws Exception {
        if (formalCode == null) {
            AuthenticationManager.$$$reportNull$$$0(2);
        }
        String urlString = "https://m1.apifoxmock.com/m1/6468831-6167795-default/query/code?code=" + URLEncoder.encode(formalCode, StandardCharsets.UTF_8.toString());
        LOG.info("\u8c03\u7528\u6b63\u5f0f\u9a8c\u8bc1API: " + urlString);
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection)url.openConnection();
        try {
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("User-Agent", "Augment-Helper-Plugin");
            int responseCode = connection.getResponseCode();
            LOG.info("\u6b63\u5f0f\u9a8c\u8bc1API\u54cd\u5e94\u7801: " + responseCode);
            if (responseCode == 200) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));){
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    String responseBody = response.toString();
                    LOG.info("\u6b63\u5f0f\u9a8c\u8bc1API\u54cd\u5e94: " + responseBody);
                    boolean isValid = responseBody.contains("true");
                    LOG.info("\u6b63\u5f0f\u9a8c\u8bc1\u7801\u9a8c\u8bc1\u7ed3\u679c: " + isValid);
                    boolean bl = isValid;
                    return bl;
                }
            }
            LOG.warn("\u6b63\u5f0f\u9a8c\u8bc1API\u8c03\u7528\u5931\u8d25\uff0c\u54cd\u5e94\u7801: " + responseCode);
            boolean bl = false;
            return bl;
        }
        finally {
            connection.disconnect();
        }
    }

    public void saveFormalVerificationStatus(boolean verified) {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.setValue(FORMAL_VERIFICATION_STATUS_KEY, verified);
        LOG.info("\u4fdd\u5b58\u6b63\u5f0f\u9a8c\u8bc1\u72b6\u6001: " + verified);
        if (verified) {
            TrialSessionManager.getInstance().clearTrialData();
            LOG.info("\u6b63\u5f0f\u9a8c\u8bc1\u6210\u529f\uff0c\u5df2\u6e05\u7406\u8bd5\u7528\u6570\u636e");
        }
    }

    public boolean isFormallyVerified() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getBoolean(FORMAL_VERIFICATION_STATUS_KEY, false);
    }

    public void clearFormalVerificationStatus() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue(FORMAL_VERIFICATION_STATUS_KEY);
        LOG.info("\u5df2\u6e05\u9664\u6b63\u5f0f\u9a8c\u8bc1\u72b6\u6001");
    }

    @NotNull
    public String getAuthenticationStatus() {
        if (this.isFormallyVerified()) {
            return "\u6b63\u5f0f\u8ba4\u8bc1";
        }
        if (TrialSessionManager.getInstance().hasValidTrialSession()) {
            int remainingDays = TrialSessionManager.getInstance().getRemainingDays();
            String string = "\u8bd5\u7528\u8ba4\u8bc1 (\u5269\u4f59 " + remainingDays + " \u5929)";
            if (string == null) {
                AuthenticationManager.$$$reportNull$$$0(3);
            }
            return string;
        }
        return "\u672a\u8ba4\u8bc1";
    }

    public boolean hasValidAuthentication() {
        return this.isFormallyVerified() || TrialSessionManager.getInstance().hasValidTrialSession();
    }

    private static /* synthetic */ void $$$reportNull$$$0(int n) {
        Object[] objectArray;
        Object[] objectArray2;
        Object[] objectArray3 = new Object[switch (n) {
            default -> 2;
            case 1, 2 -> 3;
        }];
        switch (n) {
            default: {
                objectArray2 = objectArray3;
                objectArray3[0] = "com/wangzai/AuthenticationManager";
                break;
            }
            case 1: {
                objectArray2 = objectArray3;
                objectArray3[0] = "trialCode";
                break;
            }
            case 2: {
                objectArray2 = objectArray3;
                objectArray3[0] = "formalCode";
                break;
            }
        }
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[1] = "getInstance";
                break;
            }
            case 1: 
            case 2: {
                objectArray = objectArray2;
                objectArray2[1] = "com/wangzai/AuthenticationManager";
                break;
            }
            case 3: {
                objectArray = objectArray2;
                objectArray2[1] = "getAuthenticationStatus";
                break;
            }
        }
        switch (n) {
            default: {
                break;
            }
            case 1: {
                objectArray = objectArray;
                objectArray[2] = "verifyTrialCode";
                break;
            }
            case 2: {
                objectArray = objectArray;
                objectArray[2] = "verifyFormalCode";
                break;
            }
        }
        String string = String.format(v0, objectArray);
        throw switch (n) {
            default -> new IllegalStateException(string);
            case 1, 2 -> new IllegalArgumentException(string);
        };
    }
}

