const express = require('express');
const { safeQuery } = require('../database');
const router = express.Router();

// 获取所有账号列表
router.get('/accounts', async (req, res) => {
    try {
        const [accounts] = await safeQuery(`
            SELECT 
                id, account_name, email, account_type, quota_total, 
                quota_used, quota_remaining, status, last_used_at, 
                last_quota_check, created_at, notes
            FROM augment_accounts 
            ORDER BY 
                CASE status 
                    WHEN 'active' THEN 1 
                    WHEN 'exhausted' THEN 2 
                    WHEN 'disabled' THEN 3 
                    WHEN 'error' THEN 4 
                END,
                quota_remaining DESC,
                created_at ASC
        `);

        res.json({
            success: true,
            data: accounts,
            total: accounts.length
        });
    } catch (error) {
        console.error('获取账号列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取账号列表失败',
            error: error.message
        });
    }
});

// 获取当前可用的账号（用于自动切换）
router.get('/accounts/available', async (req, res) => {
    try {
        const [accounts] = await safeQuery(`
            SELECT 
                id, account_name, email, user_id, session_id, 
                quota_remaining, account_type
            FROM augment_accounts 
            WHERE status = 'active' AND quota_remaining > 0
            ORDER BY quota_remaining DESC, last_used_at ASC
            LIMIT 10
        `);

        res.json({
            success: true,
            data: accounts
        });
    } catch (error) {
        console.error('获取可用账号失败:', error);
        res.status(500).json({
            success: false,
            message: '获取可用账号失败',
            error: error.message
        });
    }
});

// 添加新账号
router.post('/accounts', async (req, res) => {
    try {
        const {
            account_name,
            email,
            user_id,
            session_id,
            account_type = 'regular',
            quota_total = 50,
            notes
        } = req.body;

        // 验证必填字段
        if (!account_name || !email || !user_id) {
            return res.status(400).json({
                success: false,
                message: '账号名称、邮箱和用户ID为必填项'
            });
        }

        // 检查是否已存在
        const [existing] = await safeQuery(
            'SELECT id FROM augment_accounts WHERE email = ? OR user_id = ?',
            [email, user_id]
        );

        if (existing.length > 0) {
            return res.status(400).json({
                success: false,
                message: '该邮箱或用户ID已存在'
            });
        }

        // 插入新账号
        const [result] = await safeQuery(`
            INSERT INTO augment_accounts 
            (account_name, email, user_id, session_id, account_type, quota_total, quota_remaining, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [account_name, email, user_id, session_id, account_type, quota_total, quota_total, notes]);

        res.json({
            success: true,
            message: '账号添加成功',
            data: {
                id: result.insertId,
                account_name,
                email,
                user_id,
                account_type,
                quota_total
            }
        });
    } catch (error) {
        console.error('添加账号失败:', error);
        res.status(500).json({
            success: false,
            message: '添加账号失败',
            error: error.message
        });
    }
});

// 更新账号信息
router.put('/accounts/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const {
            account_name,
            email,
            user_id,
            session_id,
            account_type,
            quota_total,
            quota_used,
            status,
            notes
        } = req.body;

        // 构建更新字段
        const updateFields = [];
        const updateValues = [];

        if (account_name !== undefined) {
            updateFields.push('account_name = ?');
            updateValues.push(account_name);
        }
        if (email !== undefined) {
            updateFields.push('email = ?');
            updateValues.push(email);
        }
        if (user_id !== undefined) {
            updateFields.push('user_id = ?');
            updateValues.push(user_id);
        }
        if (session_id !== undefined) {
            updateFields.push('session_id = ?');
            updateValues.push(session_id);
        }
        if (account_type !== undefined) {
            updateFields.push('account_type = ?');
            updateValues.push(account_type);
        }
        if (quota_total !== undefined) {
            updateFields.push('quota_total = ?');
            updateValues.push(quota_total);
        }
        if (quota_used !== undefined) {
            updateFields.push('quota_used = ?');
            updateValues.push(quota_used);
            // 同时更新剩余额度
            updateFields.push('quota_remaining = quota_total - ?');
            updateValues.push(quota_used);
        }
        if (status !== undefined) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        if (notes !== undefined) {
            updateFields.push('notes = ?');
            updateValues.push(notes);
        }

        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有要更新的字段'
            });
        }

        updateValues.push(id);

        const [result] = await safeQuery(`
            UPDATE augment_accounts 
            SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, updateValues);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '账号不存在'
            });
        }

        res.json({
            success: true,
            message: '账号更新成功'
        });
    } catch (error) {
        console.error('更新账号失败:', error);
        res.status(500).json({
            success: false,
            message: '更新账号失败',
            error: error.message
        });
    }
});

// 删除账号
router.delete('/accounts/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const [result] = await safeQuery(
            'DELETE FROM augment_accounts WHERE id = ?',
            [id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '账号不存在'
            });
        }

        res.json({
            success: true,
            message: '账号删除成功'
        });
    } catch (error) {
        console.error('删除账号失败:', error);
        res.status(500).json({
            success: false,
            message: '删除账号失败',
            error: error.message
        });
    }
});

// 检查账号额度（模拟API调用）
router.post('/accounts/:id/check-quota', async (req, res) => {
    try {
        const { id } = req.params;

        // 获取账号信息
        const [accounts] = await safeQuery(
            'SELECT * FROM augment_accounts WHERE id = ?',
            [id]
        );

        if (accounts.length === 0) {
            return res.status(404).json({
                success: false,
                message: '账号不存在'
            });
        }

        const account = accounts[0];

        // 这里应该调用实际的Augment API来检查额度
        // 现在先模拟返回数据
        const mockQuotaData = {
            usageUnitsAvailable: Math.max(0, account.quota_remaining - Math.floor(Math.random() * 5)),
            usageUnitsUsedThisBillingCycle: account.quota_used + Math.floor(Math.random() * 3),
            usageUnitsPending: 0
        };

        // 更新数据库中的额度信息
        const newUsed = mockQuotaData.usageUnitsUsedThisBillingCycle;
        const newRemaining = mockQuotaData.usageUnitsAvailable;
        const newStatus = newRemaining <= 0 ? 'exhausted' : 'active';

        await safeQuery(`
            UPDATE augment_accounts 
            SET quota_used = ?, quota_remaining = ?, status = ?, last_quota_check = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [newUsed, newRemaining, newStatus, id]);

        // 记录检查日志
        await safeQuery(`
            INSERT INTO augment_usage_logs 
            (account_id, account_name, action_type, quota_before, quota_after, success)
            VALUES (?, ?, 'quota_check', ?, ?, TRUE)
        `, [id, account.account_name, account.quota_remaining, newRemaining]);

        res.json({
            success: true,
            data: {
                account_name: account.account_name,
                quota_total: account.quota_total,
                quota_used: newUsed,
                quota_remaining: newRemaining,
                status: newStatus,
                raw_response: mockQuotaData
            }
        });
    } catch (error) {
        console.error('检查账号额度失败:', error);
        res.status(500).json({
            success: false,
            message: '检查账号额度失败',
            error: error.message
        });
    }
});

// 记录账号切换
router.post('/accounts/:id/switch', async (req, res) => {
    try {
        const { id } = req.params;
        const { client_info } = req.body;

        // 获取账号信息
        const [accounts] = await safeQuery(
            'SELECT * FROM augment_accounts WHERE id = ?',
            [id]
        );

        if (accounts.length === 0) {
            return res.status(404).json({
                success: false,
                message: '账号不存在'
            });
        }

        const account = accounts[0];

        if (account.status !== 'active' || account.quota_remaining <= 0) {
            return res.status(400).json({
                success: false,
                message: '账号不可用或额度已用完'
            });
        }

        // 更新最后使用时间
        await safeQuery(
            'UPDATE augment_accounts SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?',
            [id]
        );

        // 记录切换日志
        await safeQuery(`
            INSERT INTO augment_usage_logs
            (account_id, account_name, action_type, quota_before, quota_after, client_info, success)
            VALUES (?, ?, 'switch', ?, ?, ?, TRUE)
        `, [id, account.account_name, account.quota_remaining, account.quota_remaining, client_info || '']);

        res.json({
            success: true,
            message: '账号切换成功',
            data: {
                account_name: account.account_name,
                email: account.email,
                user_id: account.user_id,
                session_id: account.session_id,
                quota_remaining: account.quota_remaining,
                account_type: account.account_type
            }
        });
    } catch (error) {
        console.error('账号切换失败:', error);
        res.status(500).json({
            success: false,
            message: '账号切换失败',
            error: error.message
        });
    }
});

// 获取使用记录
router.get('/logs', async (req, res) => {
    try {
        const { page = 1, limit = 50, account_id, action_type } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = '';
        const params = [];

        if (account_id) {
            whereClause += ' WHERE account_id = ?';
            params.push(account_id);
        }

        if (action_type) {
            whereClause += account_id ? ' AND action_type = ?' : ' WHERE action_type = ?';
            params.push(action_type);
        }

        // 获取总数
        const [countResult] = await safeQuery(`
            SELECT COUNT(*) as total FROM augment_usage_logs${whereClause}
        `, params);

        // 获取记录
        params.push(parseInt(limit), offset);
        const [logs] = await safeQuery(`
            SELECT
                l.*,
                a.email as account_email
            FROM augment_usage_logs l
            LEFT JOIN augment_accounts a ON l.account_id = a.id
            ${whereClause}
            ORDER BY l.created_at DESC
            LIMIT ? OFFSET ?
        `, params);

        res.json({
            success: true,
            data: logs,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult[0].total,
                pages: Math.ceil(countResult[0].total / limit)
            }
        });
    } catch (error) {
        console.error('获取使用记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取使用记录失败',
            error: error.message
        });
    }
});

// 获取账号统计信息
router.get('/stats', async (req, res) => {
    try {
        // 账号状态统计
        const [statusStats] = await safeQuery(`
            SELECT
                status,
                COUNT(*) as count,
                SUM(quota_remaining) as total_remaining_quota
            FROM augment_accounts
            GROUP BY status
        `);

        // 账号类型统计
        const [typeStats] = await safeQuery(`
            SELECT
                account_type,
                COUNT(*) as count,
                AVG(quota_remaining) as avg_remaining_quota
            FROM augment_accounts
            GROUP BY account_type
        `);

        // 今日使用统计
        const [todayStats] = await safeQuery(`
            SELECT
                action_type,
                COUNT(*) as count
            FROM augment_usage_logs
            WHERE DATE(created_at) = CURDATE()
            GROUP BY action_type
        `);

        // 可用账号数量
        const [availableCount] = await safeQuery(`
            SELECT COUNT(*) as count
            FROM augment_accounts
            WHERE status = 'active' AND quota_remaining > 0
        `);

        res.json({
            success: true,
            data: {
                status_stats: statusStats,
                type_stats: typeStats,
                today_stats: todayStats,
                available_accounts: availableCount[0].count
            }
        });
    } catch (error) {
        console.error('获取统计信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取统计信息失败',
            error: error.message
        });
    }
});

// 批量导入账号
router.post('/accounts/batch', async (req, res) => {
    try {
        const { accounts } = req.body;

        if (!Array.isArray(accounts) || accounts.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请提供有效的账号数组'
            });
        }

        const results = {
            success: 0,
            failed: 0,
            errors: []
        };

        for (const account of accounts) {
            try {
                const {
                    account_name,
                    email,
                    user_id,
                    session_id,
                    account_type = 'regular',
                    quota_total = 50
                } = account;

                if (!account_name || !email || !user_id) {
                    results.failed++;
                    results.errors.push(`账号 ${account_name || email} 缺少必填字段`);
                    continue;
                }

                // 检查是否已存在
                const [existing] = await safeQuery(
                    'SELECT id FROM augment_accounts WHERE email = ? OR user_id = ?',
                    [email, user_id]
                );

                if (existing.length > 0) {
                    results.failed++;
                    results.errors.push(`账号 ${email} 已存在`);
                    continue;
                }

                // 插入账号
                await safeQuery(`
                    INSERT INTO augment_accounts
                    (account_name, email, user_id, session_id, account_type, quota_total, quota_remaining)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `, [account_name, email, user_id, session_id, account_type, quota_total, quota_total]);

                results.success++;
            } catch (error) {
                results.failed++;
                results.errors.push(`账号 ${account.email} 导入失败: ${error.message}`);
            }
        }

        res.json({
            success: true,
            message: `批量导入完成：成功 ${results.success} 个，失败 ${results.failed} 个`,
            data: results
        });
    } catch (error) {
        console.error('批量导入账号失败:', error);
        res.status(500).json({
            success: false,
            message: '批量导入账号失败',
            error: error.message
        });
    }
});

module.exports = router;
