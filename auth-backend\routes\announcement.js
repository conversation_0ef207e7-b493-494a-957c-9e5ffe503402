const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4'
};

// 创建公告表（如果不存在）
async function createAnnouncementTable() {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS announcements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                content TEXT NOT NULL COMMENT '公告内容',
                type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info' COMMENT '公告类型',
                is_active BOOLEAN DEFAULT true COMMENT '是否启用',
                priority INT DEFAULT 0 COMMENT '优先级，数字越大越靠前',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表'
        `);
        
        await connection.end();
        console.log('✅ 公告表创建成功');
    } catch (error) {
        console.error('❌ 创建公告表失败:', error);
    }
}

// 初始化时创建表
createAnnouncementTable();

// 获取所有公告（客户端API）
router.get('/list', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [rows] = await connection.execute(`
            SELECT id, content, type, created_at, updated_at
            FROM announcements
            WHERE is_active = true
            ORDER BY priority DESC, created_at DESC
        `);
        
        await connection.end();
        
        res.json({
            success: true,
            data: rows
        });
    } catch (error) {
        console.error('获取公告列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取公告失败'
        });
    }
});

// 获取所有公告（管理端API）
router.get('/admin/list', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [rows] = await connection.execute(`
            SELECT * FROM announcements 
            ORDER BY priority DESC, created_at DESC
        `);
        
        await connection.end();
        
        res.json({
            success: true,
            data: rows
        });
    } catch (error) {
        console.error('获取公告列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取公告失败'
        });
    }
});

// 创建公告
router.post('/admin/create', async (req, res) => {
    try {
        const { content, type = 'info', priority = 0 } = req.body;

        if (!content) {
            return res.status(400).json({
                success: false,
                message: '内容不能为空'
            });
        }

        const connection = await mysql.createConnection(dbConfig);

        const [result] = await connection.execute(`
            INSERT INTO announcements (content, type, priority)
            VALUES (?, ?, ?)
        `, [content, type, priority]);
        
        await connection.end();
        
        res.json({
            success: true,
            message: '公告创建成功',
            data: { id: result.insertId }
        });
    } catch (error) {
        console.error('创建公告失败:', error);
        res.status(500).json({
            success: false,
            message: '创建公告失败'
        });
    }
});

// 更新公告
router.put('/admin/update/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { content, type, priority, is_active } = req.body;

        const connection = await mysql.createConnection(dbConfig);

        await connection.execute(`
            UPDATE announcements
            SET content = ?, type = ?, priority = ?, is_active = ?
            WHERE id = ?
        `, [content, type, priority, is_active, id]);
        
        await connection.end();
        
        res.json({
            success: true,
            message: '公告更新成功'
        });
    } catch (error) {
        console.error('更新公告失败:', error);
        res.status(500).json({
            success: false,
            message: '更新公告失败'
        });
    }
});

// 删除公告
router.delete('/admin/delete/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute('DELETE FROM announcements WHERE id = ?', [id]);
        
        await connection.end();
        
        res.json({
            success: true,
            message: '公告删除成功'
        });
    } catch (error) {
        console.error('删除公告失败:', error);
        res.status(500).json({
            success: false,
            message: '删除公告失败'
        });
    }
});

module.exports = router;
