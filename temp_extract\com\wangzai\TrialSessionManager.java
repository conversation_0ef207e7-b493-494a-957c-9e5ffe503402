/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.ide.util.PropertiesComponent
 *  com.intellij.openapi.diagnostic.Logger
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.wangzai;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.diagnostic.Logger;
import com.wangzai.SessionId;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TrialSessionManager {
    private static final Logger LOG = Logger.getInstance(TrialSessionManager.class);
    private static final String TRIAL_CODE_KEY = "augment.trial.code";
    private static final String TRIAL_START_TIME_KEY = "augment.trial.start.time";
    private static final String TRIAL_SESSION_ID_KEY = "augment.trial.session.id";
    private static final String ORIGINAL_SESSION_ID_KEY = "augment.original.session.id";
    private static final int TRIAL_DAYS = 3;
    private static final TrialSessionManager INSTANCE = new TrialSessionManager();

    private TrialSessionManager() {
    }

    @NotNull
    public static TrialSessionManager getInstance() {
        TrialSessionManager trialSessionManager = INSTANCE;
        if (trialSessionManager == null) {
            TrialSessionManager.$$$reportNull$$$0(0);
        }
        return trialSessionManager;
    }

    public boolean activateTrialCode(@NotNull String trialCode) {
        if (trialCode == null) {
            TrialSessionManager.$$$reportNull$$$0(1);
        }
        if (trialCode.trim().isEmpty()) {
            return false;
        }
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String existingCode = properties.getValue(TRIAL_CODE_KEY);
        if (existingCode != null && !existingCode.trim().isEmpty()) {
            LOG.info("\u8bd5\u7528\u9a8c\u8bc1\u7801\u5df2\u5b58\u5728\uff0c\u65e0\u9700\u91cd\u590d\u6fc0\u6d3b");
            return true;
        }
        String currentSessionId = SessionId.INSTANCE.getSessionId();
        properties.setValue(ORIGINAL_SESSION_ID_KEY, currentSessionId);
        String trialSessionId = UUID.randomUUID().toString();
        properties.setValue(TRIAL_SESSION_ID_KEY, trialSessionId);
        properties.setValue(TRIAL_CODE_KEY, trialCode);
        properties.setValue(TRIAL_START_TIME_KEY, String.valueOf(Instant.now().toEpochMilli()));
        LOG.info("\u8bd5\u7528\u9a8c\u8bc1\u7801\u6fc0\u6d3b\u6210\u529f: " + trialCode + ", \u8bd5\u7528SessionId: " + trialSessionId);
        return true;
    }

    public boolean hasValidTrialSession() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String trialCode = properties.getValue(TRIAL_CODE_KEY);
        String startTimeStr = properties.getValue(TRIAL_START_TIME_KEY);
        if (trialCode == null || trialCode.trim().isEmpty() || startTimeStr == null) {
            return false;
        }
        try {
            boolean isValid;
            long startTime = Long.parseLong(startTimeStr);
            Instant startInstant = Instant.ofEpochMilli(startTime);
            Instant now = Instant.now();
            long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
            boolean bl = isValid = daysPassed < 3L;
            if (!isValid) {
                LOG.info("\u8bd5\u7528\u671f\u5df2\u8fc7\u671f\uff0c\u5df2\u8fc7\u53bb " + daysPassed + " \u5929");
                this.clearTrialData();
            }
            return isValid;
        }
        catch (NumberFormatException e) {
            LOG.error("\u89e3\u6790\u8bd5\u7528\u5f00\u59cb\u65f6\u95f4\u5931\u8d25", (Throwable)e);
            return false;
        }
    }

    @Nullable
    public String getTrialSessionId() {
        if (!this.hasValidTrialSession()) {
            return null;
        }
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getValue(TRIAL_SESSION_ID_KEY);
    }

    @Nullable
    public String getOriginalSessionId() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getValue(ORIGINAL_SESSION_ID_KEY);
    }

    @Nullable
    public String getTrialCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getValue(TRIAL_CODE_KEY);
    }

    public int getRemainingDays() {
        if (!this.hasValidTrialSession()) {
            return 0;
        }
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String startTimeStr = properties.getValue(TRIAL_START_TIME_KEY);
        if (startTimeStr == null) {
            return 0;
        }
        try {
            long startTime = Long.parseLong(startTimeStr);
            Instant startInstant = Instant.ofEpochMilli(startTime);
            Instant now = Instant.now();
            long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
            return Math.max(0, 3 - (int)daysPassed);
        }
        catch (NumberFormatException e) {
            LOG.error("\u89e3\u6790\u8bd5\u7528\u5f00\u59cb\u65f6\u95f4\u5931\u8d25", (Throwable)e);
            return 0;
        }
    }

    public void clearTrialData() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue(TRIAL_CODE_KEY);
        properties.unsetValue(TRIAL_START_TIME_KEY);
        properties.unsetValue(TRIAL_SESSION_ID_KEY);
        properties.unsetValue(ORIGINAL_SESSION_ID_KEY);
        LOG.info("\u8bd5\u7528\u6570\u636e\u5df2\u6e05\u7406");
    }

    public boolean hasTrialCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String trialCode = properties.getValue(TRIAL_CODE_KEY);
        return trialCode != null && !trialCode.trim().isEmpty();
    }

    private static /* synthetic */ void $$$reportNull$$$0(int n) {
        Object[] objectArray;
        Object[] objectArray2;
        Object[] objectArray3 = new Object[switch (n) {
            default -> 2;
            case 1 -> 3;
        }];
        switch (n) {
            default: {
                objectArray2 = objectArray3;
                objectArray3[0] = "com/wangzai/TrialSessionManager";
                break;
            }
            case 1: {
                objectArray2 = objectArray3;
                objectArray3[0] = "trialCode";
                break;
            }
        }
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[1] = "getInstance";
                break;
            }
            case 1: {
                objectArray = objectArray2;
                objectArray2[1] = "com/wangzai/TrialSessionManager";
                break;
            }
        }
        switch (n) {
            default: {
                break;
            }
            case 1: {
                objectArray = objectArray;
                objectArray[2] = "activateTrialCode";
                break;
            }
        }
        String string = String.format(v0, objectArray);
        throw switch (n) {
            default -> new IllegalStateException(string);
            case 1 -> new IllegalArgumentException(string);
        };
    }
}

