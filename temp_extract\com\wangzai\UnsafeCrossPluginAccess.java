/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.openapi.application.Application
 *  com.intellij.openapi.application.ApplicationManager
 *  com.intellij.openapi.diagnostic.Logger
 */
package com.wangzai;

import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Field;
import java.util.Map;

public class UnsafeCrossPluginAccess {
    private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);

    public static Object tryGetService(String pluginId, String serviceClassName) {
        try {
            Application app = ApplicationManager.getApplication();
            Field servicesField = app.getClass().getDeclaredField("myServices");
            servicesField.setAccessible(true);
            Map services = (Map)servicesField.get(app);
            for (Map.Entry entry : services.entrySet()) {
                Object service = entry.getValue();
                LOG.info("services: " + (service != null ? service.getClass().getName() : "none"));
                if (service == null || !service.getClass().getName().equals(serviceClassName)) continue;
                return service;
            }
        }
        catch (Exception e) {
            LOG.error("\u83b7\u53d6Service\u5f02\u5e38");
            e.printStackTrace();
        }
        return null;
    }
}

