const express = require('express');
const crypto = require('crypto');
const { safeQuery } = require('../database');
const router = express.Router();

// 登录中间件 - 检查是否已登录
function requireAuth(req, res, next) {
    if (req.session && req.session.adminId) {
        return next();
    } else {
        return res.status(401).json({
            success: false,
            message: '请先登录'
        });
    }
}

// 管理员登录
router.post('/login', async (req, res) => {
    console.log('🔐 收到登录请求');
    console.log('请求体:', req.body);
    console.log('请求头:', req.headers);

    try {
        const { username, password } = req.body;
        console.log('用户名:', username, '密码长度:', password ? password.length : 0);

        if (!username || !password) {
            console.log('❌ 用户名或密码为空');
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 密码加密
        const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');

        // 查询管理员
        const [users] = await safeQuery(
            'SELECT id, username, email, status, login_count FROM admin_users WHERE username = ? AND password = ? AND status = "active"',
            [username, hashedPassword]
        );

        if (users.length === 0) {
            console.log('❌ 用户名或密码错误');
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        const user = users[0];
        console.log('✅ 找到用户:', user.username);

        // 更新登录信息
        const clientIp = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
        await safeQuery(
            'UPDATE admin_users SET last_login_at = NOW(), last_login_ip = ?, login_count = login_count + 1 WHERE id = ?',
            [clientIp, user.id]
        );

        // 设置session
        req.session.adminId = user.id;
        req.session.username = user.username;
        console.log('✅ Session设置完成:', req.session);

        const response = {
            success: true,
            message: '登录成功',
            data: {
                username: user.username,
                email: user.email,
                loginCount: user.login_count + 1
            }
        };

        console.log('✅ 发送响应:', response);
        res.json(response);

    } catch (error) {
        console.error('管理员登录失败:', error);
        res.status(500).json({
            success: false,
            message: '登录失败，请稍后重试'
        });
    }
});

// 检查登录状态
router.get('/check', (req, res) => {
    if (req.session && req.session.adminId) {
        res.json({
            success: true,
            data: {
                isLoggedIn: true,
                username: req.session.username
            }
        });
    } else {
        res.json({
            success: true,
            data: {
                isLoggedIn: false
            }
        });
    }
});

// 管理员登出
router.post('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: '登出失败'
            });
        }
        res.json({
            success: true,
            message: '登出成功'
        });
    });
});

// 获取管理员信息
router.get('/profile', requireAuth, async (req, res) => {
    try {
        const [users] = await safeQuery(
            'SELECT id, username, email, status, last_login_at, last_login_ip, login_count, created_at FROM admin_users WHERE id = ?',
            [req.session.adminId]
        );

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        res.json({
            success: true,
            data: users[0]
        });

    } catch (error) {
        console.error('获取管理员信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户信息失败'
        });
    }
});

module.exports = { router, requireAuth };
