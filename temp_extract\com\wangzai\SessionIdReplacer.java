/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.intellij.ide.plugins.IdeaPluginDescriptor
 *  com.intellij.ide.plugins.PluginManager
 *  com.intellij.openapi.application.Application
 *  com.intellij.openapi.application.ApplicationManager
 *  com.intellij.openapi.diagnostic.Logger
 */
package com.wangzai;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManager;
import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.wangzai.SessionId;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;

public class SessionIdReplacer {
    private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);

    private boolean reinitializeHttpClient() {
        try {
            ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
            ClassLoader targetClassLoader = null;
            try {
                PluginManager pluginManager = PluginManager.getInstance();
                IdeaPluginDescriptor targetPlugin = Arrays.stream(pluginManager.getPlugins()).filter(plugin -> "com.augmentcode".equals(plugin.getPluginId().getIdString())).findFirst().orElse(null);
                if (targetPlugin != null) {
                    targetClassLoader = targetPlugin.getPluginClassLoader();
                    LOG.info("\u6210\u529f\u83b7\u53d6\u76ee\u6807\u63d2\u4ef6\u7684\u7c7b\u52a0\u8f7d\u5668");
                }
            }
            catch (Exception e) {
                LOG.warn("\u65e0\u6cd5\u83b7\u53d6\u76ee\u6807\u63d2\u4ef6\u7c7b\u52a0\u8f7d\u5668\uff0c\u5c06\u4f7f\u7528\u5f53\u524d\u7c7b\u52a0\u8f7d\u5668: " + e.getMessage());
            }
            if (targetClassLoader == null) {
                targetClassLoader = this.getClass().getClassLoader();
            }
            Thread.currentThread().setContextClassLoader(targetClassLoader);
            Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);
            Application app = ApplicationManager.getApplication();
            Method method = app.getClass().getMethod("getService", Class.class);
            Object invoke = method.invoke(app, apiImplClass);
            Field httpClientField = invoke.getClass().getDeclaredField("httpClient");
            httpClientField.setAccessible(true);
            String sessionId = SessionId.INSTANCE.getSessionId();
            LOG.info("\u4f7f\u7528\u914d\u7f6e\u7684SessionId: " + sessionId + " (\u6765\u6e90: " + SessionId.INSTANCE.getSessionIdSource() + ")");
            Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
            Constructor<?> constructor = httpClientClass.getConstructor(String.class);
            Object newHttpClient = constructor.newInstance(sessionId);
            httpClientField.set(invoke, newHttpClient);
            LOG.info("\u6210\u529f\u91cd\u65b0\u521d\u59cb\u5316httpClient\u5b9e\u4f8b");
            Thread.currentThread().setContextClassLoader(originalClassLoader);
            return true;
        }
        catch (Exception e) {
            LOG.error("\u91cd\u65b0\u521d\u59cb\u5316httpClient\u5b9e\u4f8b\u5931\u8d25", (Throwable)e);
            return false;
        }
    }

    public boolean replaceSessionIdClass() {
        try {
            if (this.reinitializeHttpClient()) {
                return true;
            }
            LOG.warn("\u6240\u6709\u66ff\u6362\u65b9\u6cd5\u90fd\u5931\u8d25");
            return false;
        }
        catch (Exception e) {
            LOG.error("\u66ff\u6362SessionId\u7c7b\u65f6\u51fa\u9519", (Throwable)e);
            return false;
        }
    }
}

