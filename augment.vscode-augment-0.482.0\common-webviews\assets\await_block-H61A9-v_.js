import{m as v,o as d,p as s,q as p,t as m,r as g,u as x,v as q}from"./SpinnerAugment-Cx9dt_ox.js";function C(o,c){const u=c.token={};function n(r,t,k,f){if(c.token!==u)return;c.resolved=f;let e=c.ctx;k!==void 0&&(e=e.slice(),e[k]=f);const l=r&&(c.current=r)(e);let b=!1;c.block&&(c.blocks?c.blocks.forEach((h,i)=>{i!==t&&h&&(p(),m(h,1,1,()=>{c.blocks[i]===h&&(c.blocks[i]=null)}),g())}):c.block.d(1),l.c(),x(l,1),l.m(c.mount(),c.anchor),b=!0),c.block=l,c.blocks&&(c.blocks[t]=l),b&&q()}if(v(o)){const r=d();if(o.then(t=>{s(r),n(c.then,1,c.value,t),s(null)},t=>{if(s(r),n(c.catch,2,c.error,t),s(null),!c.hasCatch)throw t}),c.current!==c.pending)return n(c.pending,0),!0}else{if(c.current!==c.then)return n(c.then,1,c.value,o),!0;c.resolved=o}}function E(o,c,u){const n=c.slice(),{resolved:r}=o;o.current===o.then&&(n[o.value]=r),o.current===o.catch&&(n[o.error]=r),o.block.p(n,u)}export{C as h,E as u};
