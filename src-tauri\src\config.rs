use std::env;

pub struct Config {
    pub api_base_url: String,
}

impl Config {
    pub fn new() -> Self {
        // 尝试加载.env文件（开发模式）
        dotenv::dotenv().ok();

        // 从环境变量获取API地址，如果获取不到则使用本地服务器
        let api_base_url = env::var("API_BASE_URL")
            .unwrap_or_else(|_| {
                // 只在调试模式下输出日志
                #[cfg(debug_assertions)]
                println!("未找到API_BASE_URL环境变量，使用本地服务器地址");
                "http://localhost:7761".to_string()
            });

        // 只在调试模式下输出配置信息
        #[cfg(debug_assertions)]
        println!("配置加载完成，API地址: {}", api_base_url);

        Config {
            api_base_url,
        }
    }
    
    pub fn get_update_url(&self, current_version: &str, installation_type: &str) -> String {
        format!("{}/api/update/x86_64-pc-windows-msvc/{}?type={}", 
                self.api_base_url, current_version, installation_type)
    }
    
    pub fn get_portable_update_url(&self, current_version: &str) -> String {
        format!("{}/api/update/x86_64-pc-windows-msvc/{}?type=portable", 
                self.api_base_url, current_version)
    }
}
