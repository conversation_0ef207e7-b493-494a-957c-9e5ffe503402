<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Autofix</title>
    <script nonce="nonce-hAolFNc71jnKc95TcryusQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <script type="module" crossorigin src="./assets/autofix-B_2xrCkN.js" nonce="nonce-hAolFNc71jnKc95TcryusQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Cx9dt_ox.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-BqzdgpkK.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DhtPLzGu.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-FVMxq7uD.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BjDqXmYl.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-B4JAagx1.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-B3px2_jp.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/resize-observer-DdAtcrRr.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BxQII05L.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-8X-F_Twk.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BWVRxMGM.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-reader-BdhEMA38.js" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/file-reader-BZAZY_XQ.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
    <link rel="stylesheet" crossorigin href="./assets/autofix-Dr7nxBLG.css" nonce="nonce-hAolFNc71jnKc95TcryusQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
