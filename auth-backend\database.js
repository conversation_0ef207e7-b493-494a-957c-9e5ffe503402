require('dotenv').config();
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jiaoben',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

// 创建连接池 - 优化的稳定性配置
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,     // 增加连接数以减少竞争
    queueLimit: 0,
    // 优化的连接管理
    idleTimeout: 300000,     // 空闲连接超时时间（5分钟）
    maxIdle: 5,              // 最大空闲连接数（保留5个）
    // 添加连接保活设置
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
});

// 添加连接池事件监听 - 减少日志输出
pool.on('connection', function (connection) {
    // 只在开发环境输出连接日志
    if (process.env.NODE_ENV === 'development') {
        console.log('🔗 新的数据库连接建立: ' + connection.threadId);
    }
});

pool.on('error', function(err) {
    console.error('❌ 数据库连接池错误:', err);
    if(err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('🔄 检测到连接丢失，连接池将自动重连');
    }
});

// 数据库表结构定义
const tables = {
    // 授权码表
    auth_codes: `
        CREATE TABLE IF NOT EXISTS auth_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(32) UNIQUE NOT NULL COMMENT '授权码',
            name VARCHAR(100) NOT NULL COMMENT '授权码名称/描述',
            status ENUM('active', 'disabled', 'expired') DEFAULT 'active' COMMENT '状态',
            max_uses INT DEFAULT 1 COMMENT '最大使用次数，-1为无限制',
            used_count INT DEFAULT 0 COMMENT '已使用次数',
            expire_date DATETIME NULL COMMENT '过期时间，NULL为永不过期',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            creator VARCHAR(50) DEFAULT 'admin' COMMENT '创建者',
            notes TEXT COMMENT '备注信息',
            device_binding_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用设备绑定',
            max_devices INT DEFAULT 1 COMMENT '最大设备数量',
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_expire_date (expire_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='授权码表'
    `,
    
    // 授权使用记录表
    auth_usage_logs: `
        CREATE TABLE IF NOT EXISTS auth_usage_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            auth_code VARCHAR(32) NOT NULL COMMENT '授权码',
            client_ip VARCHAR(45) COMMENT '客户端IP',
            client_info TEXT COMMENT '客户端信息',
            used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
            success BOOLEAN DEFAULT TRUE COMMENT '验证是否成功',
            error_message TEXT COMMENT '错误信息',
            INDEX idx_auth_code (auth_code),
            INDEX idx_used_at (used_at),
            INDEX idx_client_ip (client_ip)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='授权使用记录表'
    `,
    
    // 设备绑定表
    auth_devices: `
        CREATE TABLE IF NOT EXISTS auth_devices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            auth_code VARCHAR(32) NOT NULL COMMENT '授权码',
            device_id VARCHAR(255) NOT NULL COMMENT '设备指纹ID',
            device_name VARCHAR(100) COMMENT '设备名称',
            client_ip VARCHAR(45) COMMENT '客户端IP',
            user_agent TEXT COMMENT '用户代理',
            first_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次使用时间',
            last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后使用时间',
            status ENUM('active', 'disabled') DEFAULT 'active' COMMENT '设备状态',
            UNIQUE KEY unique_auth_device (auth_code, device_id),
            INDEX idx_auth_code (auth_code),
            INDEX idx_device_id (device_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备绑定表'
    `,

    // 系统配置表
    system_config: `
        CREATE TABLE IF NOT EXISTS system_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
            config_value TEXT COMMENT '配置值',
            config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
            description VARCHAR(255) COMMENT '配置描述',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_config_key (config_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
    `,

    // 版本管理表
    app_versions: `
        CREATE TABLE IF NOT EXISTS app_versions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            version VARCHAR(50) NOT NULL COMMENT '版本号',
            description TEXT COMMENT '版本描述',
            features JSON COMMENT '更新内容列表',
            file_name VARCHAR(255) COMMENT '文件名',
            file_size BIGINT COMMENT '文件大小(字节)',
            download_url VARCHAR(500) COMMENT '下载链接',
            mandatory BOOLEAN DEFAULT FALSE COMMENT '是否强制更新',
            is_current BOOLEAN DEFAULT FALSE COMMENT '是否为当前版本',
            release_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_version (version),
            INDEX idx_is_current (is_current),
            INDEX idx_release_date (release_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='版本管理表'
    `,

    // 管理员表
    admin_users: `
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
            password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
            email VARCHAR(100) COMMENT '邮箱',
            status ENUM('active', 'disabled') DEFAULT 'active' COMMENT '状态',
            last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
            last_login_ip VARCHAR(45) COMMENT '最后登录IP',
            login_count INT DEFAULT 0 COMMENT '登录次数',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_username (username),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表'
    `,

    // Augment账号池表
    augment_accounts: `
        CREATE TABLE IF NOT EXISTS augment_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_name VARCHAR(100) NOT NULL COMMENT '账号名称/标识',
            email VARCHAR(255) NOT NULL COMMENT '账号邮箱',
            user_id VARCHAR(255) NOT NULL COMMENT '浏览器用户ID (ajs_user_id)',
            session_id VARCHAR(255) COMMENT 'VSCode Session ID',
            account_type ENUM('trial', 'regular') DEFAULT 'regular' COMMENT '账号类型：trial=试用300额度，regular=正式50额度',
            quota_total INT DEFAULT 50 COMMENT '总额度',
            quota_used INT DEFAULT 0 COMMENT '已使用额度',
            quota_remaining INT DEFAULT 50 COMMENT '剩余额度',
            status ENUM('active', 'disabled', 'exhausted', 'error') DEFAULT 'active' COMMENT '账号状态',
            last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
            last_quota_check TIMESTAMP NULL COMMENT '最后额度检查时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            notes TEXT COMMENT '备注信息',
            UNIQUE KEY unique_user_id (user_id),
            UNIQUE KEY unique_email (email),
            INDEX idx_account_name (account_name),
            INDEX idx_status (status),
            INDEX idx_account_type (account_type),
            INDEX idx_quota_remaining (quota_remaining)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Augment账号池表'
    `,

    // Augment账号使用记录表
    augment_usage_logs: `
        CREATE TABLE IF NOT EXISTS augment_usage_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL COMMENT '账号ID',
            account_name VARCHAR(100) NOT NULL COMMENT '账号名称',
            action_type ENUM('switch', 'quota_check', 'api_call', 'error') DEFAULT 'api_call' COMMENT '操作类型',
            quota_before INT COMMENT '操作前额度',
            quota_after INT COMMENT '操作后额度',
            client_info TEXT COMMENT '客户端信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
            success BOOLEAN DEFAULT TRUE COMMENT '操作是否成功',
            error_message TEXT COMMENT '错误信息',
            INDEX idx_account_id (account_id),
            INDEX idx_action_type (action_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (account_id) REFERENCES augment_accounts(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Augment账号使用记录表'
    `
};

// 初始化数据库 - 添加重试机制
async function initDatabase() {
    let retryCount = 0;
    const maxRetries = 5;

    while (retryCount < maxRetries) {
        try {
            console.log(`🔄 正在连接数据库... (第${retryCount + 1}/${maxRetries}次)`);

            // 测试连接
            const connection = await pool.getConnection();
            console.log('✅ 数据库连接成功');

            // 创建表
            console.log('🔄 正在创建数据库表...');
            for (const [tableName, createSQL] of Object.entries(tables)) {
                await connection.execute(createSQL);
                console.log(`✅ 表 ${tableName} 创建/检查完成`);
            }

            // 执行数据库迁移
            await runMigrations(connection);

            // 插入默认配置
            await insertDefaultConfig(connection);

            connection.release();
            console.log('✅ 数据库初始化完成');
            return; // 成功，退出重试循环

        } catch (error) {
            retryCount++;
            console.error(`❌ 数据库初始化失败 (第${retryCount}/${maxRetries}次):`, error.message);

            if (retryCount < maxRetries) {
                const waitTime = retryCount * 2; // 递增等待时间：2s, 4s, 6s, 8s
                console.log(`🔄 等待${waitTime}秒后重试...`);
                await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
            } else {
                console.error(`❌ 数据库初始化最终失败，已重试${maxRetries}次`);
                throw error;
            }
        }
    }
}

// 执行数据库迁移
async function runMigrations(connection) {
    console.log('🔄 正在执行数据库迁移...');

    try {
        // 检查auth_codes表是否有新字段
        const [columns] = await connection.execute(
            "SHOW COLUMNS FROM auth_codes LIKE 'max_devices'"
        );

        if (columns.length === 0) {
            console.log('🔄 添加 max_devices 字段...');
            await connection.execute(
                'ALTER TABLE auth_codes ADD COLUMN max_devices INT DEFAULT 1 COMMENT "最大设备数量"'
            );
            console.log('✅ max_devices 字段添加成功');
        }

        // 检查device_binding_enabled字段
        const [bindingColumns] = await connection.execute(
            "SHOW COLUMNS FROM auth_codes LIKE 'device_binding_enabled'"
        );

        if (bindingColumns.length === 0) {
            console.log('🔄 添加 device_binding_enabled 字段...');
            await connection.execute(
                'ALTER TABLE auth_codes ADD COLUMN device_binding_enabled BOOLEAN DEFAULT TRUE COMMENT "是否启用设备绑定"'
            );
            console.log('✅ device_binding_enabled 字段添加成功');
        }

        console.log('✅ 数据库迁移完成');

    } catch (error) {
        console.error('❌ 数据库迁移失败:', error.message);
        // 不抛出错误，继续执行
    }
}

// 插入默认配置
async function insertDefaultConfig(connection) {
    const defaultConfigs = [
        {
            config_key: 'system_name',
            config_value: 'AmpCode 授权管理系统',
            config_type: 'string',
            description: '系统名称'
        },
        {
            config_key: 'default_expire_days',
            config_value: '30',
            config_type: 'number',
            description: '默认授权码过期天数'
        },
        {
            config_key: 'max_uses_default',
            config_value: '1',
            config_type: 'number',
            description: '默认最大使用次数'
        },
        {
            config_key: 'enable_ip_restriction',
            config_value: 'false',
            config_type: 'boolean',
            description: '是否启用IP限制'
        }
    ];

    for (const config of defaultConfigs) {
        try {
            await connection.execute(
                'INSERT IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES (?, ?, ?, ?)',
                [config.config_key, config.config_value, config.config_type, config.description]
            );
        } catch (error) {
            console.log(`配置 ${config.config_key} 已存在，跳过插入`);
        }
    }

    // 插入默认管理员账号
    try {
        // 简单的密码加密（实际项目中应该使用bcrypt）
        const crypto = require('crypto');
        const password = 'zhangying';
        const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');

        await connection.execute(
            'INSERT IGNORE INTO admin_users (username, password, email, status) VALUES (?, ?, ?, ?)',
            ['zhangying', hashedPassword, '<EMAIL>', 'active']
        );
        console.log('✅ 默认管理员账号创建完成 (用户名: zhangying, 密码: zhangying)');
    } catch (error) {
        console.log('管理员账号已存在，跳过创建');
    }
}

// 获取数据库连接 - 添加连接验证
function getConnection() {
    return pool;
}

// 安全的数据库查询包装器 - 优化版本
async function safeQuery(sql, params = []) {
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
        try {
            // 直接使用连接池执行查询，不手动获取连接
            const [result] = await pool.execute(sql, params);
            return [result];

        } catch (error) {
            retryCount++;
            console.error(`❌ 数据库查询失败 (第${retryCount}/${maxRetries}次):`, error.message);

            // 检查是否是连接相关的错误
            if (error.code === 'PROTOCOL_CONNECTION_LOST' ||
                error.code === 'ECONNRESET' ||
                error.code === 'ETIMEDOUT') {
                console.log(`🔄 检测到连接问题，等待2秒后重试...`);
            }

            if (retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                throw error;
            }
        }
    }
}

// 连接健康检查
async function checkConnectionHealth() {
    try {
        const [rows] = await pool.execute('SELECT 1 as health_check');
        console.log('✅ 数据库连接健康检查通过');
        return true;
    } catch (error) {
        console.error('❌ 数据库连接健康检查失败:', error.message);
        return false;
    }
}

// 连接池状态监控
function getPoolStatus() {
    const status = {
        totalConnections: pool._allConnections ? pool._allConnections.length : 0,
        freeConnections: pool._freeConnections ? pool._freeConnections.length : 0,
        acquiringConnections: pool._acquiringConnections ? pool._acquiringConnections.length : 0,
        connectionQueue: pool._connectionQueue ? pool._connectionQueue.length : 0
    };

    console.log('📊 连接池状态:', status);
    return status;
}

// 定期输出连接池状态（仅在开发环境）
if (process.env.NODE_ENV !== 'production') {
    setInterval(() => {
        getPoolStatus();
    }, 30000); // 每30秒输出一次状态
}

// 移除定期健康检查 - 改为按需检查
// 健康检查现在只在 safeQuery 中进行，避免不必要的连接

// 关闭数据库连接
async function closeDatabase() {
    await pool.end();
    console.log('✅ 数据库连接已关闭');
}

module.exports = {
    initDatabase,
    getConnection,
    closeDatabase,
    checkConnectionHealth,
    safeQuery,
    getPoolStatus,
    dbConfig
};
