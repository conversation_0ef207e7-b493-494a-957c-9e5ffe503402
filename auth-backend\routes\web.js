const express = require('express');
const path = require('path');
const { requireAuth } = require('./admin');

const router = express.Router();

// 主页重定向到管理界面
router.get('/', (req, res) => {
    res.redirect('/admin');
});

// 登录页面
router.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/login.html'));
});

// 管理界面 - 需要登录
router.get('/admin', (req, res) => {
    // 检查是否已登录
    if (req.session && req.session.adminId) {
        res.sendFile(path.join(__dirname, '../public/admin.html'));
    } else {
        // 检查是否是AJAX请求
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            res.status(401).json({
                success: false,
                message: '未登录',
                redirect: '/login'
            });
        } else {
            res.redirect('/login');
        }
    }
});

// API文档页面
router.get('/docs', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/docs.html'));
});

module.exports = router;
